/**
 * Advanced List Optimization Hook
 * 
 * Provides comprehensive optimization for large lists and data sets,
 * including virtualization, intelligent pagination, and performance monitoring.
 * 
 * Features:
 * - Virtual scrolling for large datasets
 * - Intelligent item rendering
 * - Predictive data loading
 * - Memory-efficient caching
 * - Performance monitoring
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, Platform } from 'react-native';
import { performanceMonitor } from '../utils/performance';
import { globalCache } from '../utils/caching';

// List optimization configuration
interface ListOptimizationConfig {
  itemHeight?: number;
  estimatedItemHeight?: number;
  windowSize?: number;
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
  updateCellsBatchingPeriod?: number;
  enableVirtualization?: boolean;
  enablePredictiveLoading?: boolean;
  enableMemoryOptimization?: boolean;
  cacheStrategy?: 'lru' | 'fifo' | 'adaptive';
}

// List performance metrics
interface ListPerformanceMetrics {
  renderTime: number;
  scrollPerformance: number;
  memoryUsage: number;
  cacheHitRate: number;
  visibleItems: number;
  totalItems: number;
}

// Item render state
interface ItemRenderState {
  index: number;
  isVisible: boolean;
  isRendered: boolean;
  lastRenderTime: number;
  renderCount: number;
}

// Default configuration
const DEFAULT_CONFIG: Required<ListOptimizationConfig> = {
  itemHeight: 60,
  estimatedItemHeight: 60,
  windowSize: 10,
  initialNumToRender: 10,
  maxToRenderPerBatch: 10,
  updateCellsBatchingPeriod: 50,
  enableVirtualization: true,
  enablePredictiveLoading: true,
  enableMemoryOptimization: true,
  cacheStrategy: 'adaptive',
};

/**
 * Advanced List Optimization Hook
 */
export const useAdvancedListOptimization = <T>(
  data: T[],
  config: ListOptimizationConfig = {}
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // State management
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: finalConfig.initialNumToRender });
  const [scrollOffset, setScrollOffset] = useState(0);
  const [containerHeight, setContainerHeight] = useState(Dimensions.get('window').height);
  const [metrics, setMetrics] = useState<ListPerformanceMetrics>({
    renderTime: 0,
    scrollPerformance: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    visibleItems: 0,
    totalItems: data.length,
  });

  // Refs for performance tracking
  const renderStates = useRef<Map<number, ItemRenderState>>(new Map());
  const scrollStartTime = useRef<number>(0);
  const renderStartTime = useRef<number>(0);
  const itemCache = useRef<Map<number, T>>(new Map());
  const predictiveLoadingQueue = useRef<Set<number>>(new Set());

  /**
   * Calculate visible range based on scroll position
   */
  const calculateVisibleRange = useCallback((offset: number, height: number) => {
    const { itemHeight, windowSize } = finalConfig;
    
    const startIndex = Math.max(0, Math.floor(offset / itemHeight) - windowSize);
    const endIndex = Math.min(
      data.length - 1,
      Math.ceil((offset + height) / itemHeight) + windowSize
    );

    return { start: startIndex, end: endIndex };
  }, [finalConfig, data.length]);

  /**
   * Intelligent item rendering with performance optimization
   */
  const shouldRenderItem = useCallback((index: number): boolean => {
    if (!finalConfig.enableVirtualization) return true;

    const renderState = renderStates.current.get(index);
    const now = performance.now();

    // Always render if within visible range
    if (index >= visibleRange.start && index <= visibleRange.end) {
      return true;
    }

    // Skip rendering if item was recently rendered and is outside visible range
    if (renderState && (now - renderState.lastRenderTime) < 1000) {
      return false;
    }

    return false;
  }, [finalConfig.enableVirtualization, visibleRange]);

  /**
   * Update item render state
   */
  const updateRenderState = useCallback((index: number, isVisible: boolean) => {
    const now = performance.now();
    const currentState = renderStates.current.get(index);

    const newState: ItemRenderState = {
      index,
      isVisible,
      isRendered: true,
      lastRenderTime: now,
      renderCount: (currentState?.renderCount || 0) + 1,
    };

    renderStates.current.set(index, newState);
  }, []);

  /**
   * Predictive data loading
   */
  const predictiveDataLoading = useCallback(async (direction: 'up' | 'down') => {
    if (!finalConfig.enablePredictiveLoading) return;

    const { start, end } = visibleRange;
    const loadSize = Math.min(finalConfig.maxToRenderPerBatch, 5);
    
    let indicesToLoad: number[] = [];

    if (direction === 'down') {
      // Load items below current range
      for (let i = end + 1; i < Math.min(end + loadSize + 1, data.length); i++) {
        if (!itemCache.current.has(i) && !predictiveLoadingQueue.current.has(i)) {
          indicesToLoad.push(i);
        }
      }
    } else {
      // Load items above current range
      for (let i = Math.max(start - loadSize, 0); i < start; i++) {
        if (!itemCache.current.has(i) && !predictiveLoadingQueue.current.has(i)) {
          indicesToLoad.push(i);
        }
      }
    }

    // Add to loading queue and cache
    indicesToLoad.forEach(index => {
      predictiveLoadingQueue.current.add(index);
      
      // Simulate async data loading (replace with actual data fetching)
      setTimeout(() => {
        if (index < data.length) {
          itemCache.current.set(index, data[index]);
        }
        predictiveLoadingQueue.current.delete(index);
      }, 10);
    });
  }, [finalConfig, visibleRange, data]);

  /**
   * Memory optimization for large lists
   */
  const optimizeMemory = useCallback(() => {
    if (!finalConfig.enableMemoryOptimization) return;

    const { start, end } = visibleRange;
    const cacheSize = itemCache.current.size;
    const maxCacheSize = finalConfig.windowSize * 4; // Keep 4x window size in cache

    if (cacheSize > maxCacheSize) {
      // Remove items far from visible range
      const itemsToRemove: number[] = [];
      
      itemCache.current.forEach((_, index) => {
        const distance = Math.min(
          Math.abs(index - start),
          Math.abs(index - end)
        );
        
        if (distance > finalConfig.windowSize * 2) {
          itemsToRemove.push(index);
        }
      });

      // Remove oldest items based on cache strategy
      if (finalConfig.cacheStrategy === 'lru') {
        // Remove least recently used items
        const sortedByUsage = Array.from(renderStates.current.entries())
          .filter(([index]) => itemsToRemove.includes(index))
          .sort(([, a], [, b]) => a.lastRenderTime - b.lastRenderTime)
          .slice(0, Math.ceil(itemsToRemove.length / 2));

        sortedByUsage.forEach(([index]) => {
          itemCache.current.delete(index);
          renderStates.current.delete(index);
        });
      } else {
        // Simple removal for other strategies
        itemsToRemove.slice(0, Math.ceil(itemsToRemove.length / 2)).forEach(index => {
          itemCache.current.delete(index);
          renderStates.current.delete(index);
        });
      }
    }
  }, [finalConfig, visibleRange]);

  /**
   * Performance monitoring
   */
  const trackPerformance = useCallback(() => {
    const now = performance.now();
    const renderTime = renderStartTime.current ? now - renderStartTime.current : 0;
    const scrollPerformance = scrollStartTime.current ? now - scrollStartTime.current : 0;

    const newMetrics: ListPerformanceMetrics = {
      renderTime,
      scrollPerformance,
      memoryUsage: itemCache.current.size * 0.1, // Estimated KB per item
      cacheHitRate: itemCache.current.size / Math.max(data.length, 1),
      visibleItems: visibleRange.end - visibleRange.start + 1,
      totalItems: data.length,
    };

    setMetrics(newMetrics);

    // Record performance metrics
    performanceMonitor.recordMetric({
      name: 'list-render-performance',
      value: renderTime,
      timestamp: Date.now(),
      type: 'timing',
      tags: {
        listSize: data.length.toString(),
        visibleItems: newMetrics.visibleItems.toString(),
        cacheStrategy: finalConfig.cacheStrategy,
      },
    });

    return newMetrics;
  }, [data.length, visibleRange, finalConfig.cacheStrategy]);

  /**
   * Handle scroll events with optimization
   */
  const handleScroll = useCallback((event: any) => {
    const offset = event.nativeEvent.contentOffset.y;
    const height = event.nativeEvent.layoutMeasurement.height;
    
    scrollStartTime.current = performance.now();
    setScrollOffset(offset);
    setContainerHeight(height);

    // Calculate new visible range
    const newRange = calculateVisibleRange(offset, height);
    
    // Only update if range changed significantly
    if (Math.abs(newRange.start - visibleRange.start) > 2 || 
        Math.abs(newRange.end - visibleRange.end) > 2) {
      setVisibleRange(newRange);
      
      // Trigger predictive loading
      const direction = newRange.start > visibleRange.start ? 'down' : 'up';
      predictiveDataLoading(direction);
    }

    // Optimize memory usage
    optimizeMemory();
    
    // Track performance
    trackPerformance();
  }, [calculateVisibleRange, visibleRange, predictiveDataLoading, optimizeMemory, trackPerformance]);

  /**
   * Get optimized item layout
   */
  const getItemLayout = useCallback((data: any, index: number) => ({
    length: finalConfig.itemHeight,
    offset: finalConfig.itemHeight * index,
    index,
  }), [finalConfig.itemHeight]);

  /**
   * Key extractor with caching
   */
  const keyExtractor = useCallback((item: T, index: number): string => {
    // Use cached key if available
    const cacheKey = `key-${index}`;
    const cached = itemCache.current.get(index);
    
    if (cached) {
      return cacheKey;
    }

    // Generate new key
    const key = typeof item === 'object' && item !== null && 'id' in item
      ? String((item as any).id)
      : `item-${index}`;
    
    return key;
  }, []);

  /**
   * Render item with optimization
   */
  const renderOptimizedItem = useCallback((renderItem: (info: any) => React.ReactElement) => {
    return (info: any) => {
      const { index } = info;
      
      renderStartTime.current = performance.now();
      
      // Check if item should be rendered
      if (!shouldRenderItem(index)) {
        return null;
      }

      // Update render state
      updateRenderState(index, true);
      
      // Render item
      const element = renderItem(info);
      
      // Track render completion
      trackPerformance();
      
      return element;
    };
  }, [shouldRenderItem, updateRenderState, trackPerformance]);

  // Initialize cache with initial data
  useEffect(() => {
    const { start, end } = visibleRange;
    for (let i = start; i <= Math.min(end, data.length - 1); i++) {
      itemCache.current.set(i, data[i]);
    }
  }, [data, visibleRange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      itemCache.current.clear();
      renderStates.current.clear();
      predictiveLoadingQueue.current.clear();
    };
  }, []);

  return {
    // Optimized props for FlatList/VirtualizedList
    data: data.slice(visibleRange.start, visibleRange.end + 1),
    getItemLayout,
    keyExtractor,
    renderItem: renderOptimizedItem,
    onScroll: handleScroll,
    
    // Configuration
    initialNumToRender: finalConfig.initialNumToRender,
    maxToRenderPerBatch: finalConfig.maxToRenderPerBatch,
    updateCellsBatchingPeriod: finalConfig.updateCellsBatchingPeriod,
    windowSize: finalConfig.windowSize,
    
    // Performance metrics
    metrics,
    visibleRange,
    
    // Utility functions
    shouldRenderItem,
    optimizeMemory,
    trackPerformance,
    
    // Cache management
    clearCache: () => {
      itemCache.current.clear();
      renderStates.current.clear();
    },
    getCacheSize: () => itemCache.current.size,
    getCacheHitRate: () => itemCache.current.size / Math.max(data.length, 1),
  };
};

export default useAdvancedListOptimization;
