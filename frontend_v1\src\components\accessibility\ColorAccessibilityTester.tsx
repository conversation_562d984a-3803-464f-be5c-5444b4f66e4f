/**
 * Color Accessibility Tester Component
 *
 * Development tool for testing color contrast and accessibility compliance
 * during development and design review processes.
 *
 * Features:
 * - Real-time contrast ratio calculation
 * - WCAG compliance validation
 * - Color blindness simulation
 * - Accessibility recommendations
 * - Visual contrast preview
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import {
  checkContrastCompliance,
  validateColorPalette,
  simulateColorBlindness,
  generateAccessibilityReport,
  WCAG_THRESHOLDS,
} from '../../utils/colorContrastUtils';

// Component props
interface ColorAccessibilityTesterProps {
  visible?: boolean;
  onClose?: () => void;
}

export const ColorAccessibilityTester: React.FC<ColorAccessibilityTesterProps> = ({
  visible = false,
  onClose,
}) => {
  // State
  const [selectedForeground, setSelectedForeground] = useState('#000000');
  const [selectedBackground, setSelectedBackground] = useState('#FFFFFF');
  const [isLargeText, setIsLargeText] = useState(false);
  const [colorBlindnessType, setColorBlindnessType] = useState<'none' | 'protanopia' | 'deuteranopia' | 'tritanopia'>('none');

  // Test current color combination
  const testCurrentColors = useCallback(() => {
    try {
      const result = checkContrastCompliance(selectedForeground, selectedBackground, isLargeText);
      const report = generateAccessibilityReport(selectedForeground, selectedBackground, isLargeText);
      
      Alert.alert('Accessibility Test Results', report);
    } catch (error) {
      Alert.alert('Error', 'Failed to test color combination');
    }
  }, [selectedForeground, selectedBackground, isLargeText]);

  // Test entire color palette
  const testColorPalette = useCallback(() => {
    const paletteResult = validateColorPalette(Colors as any);
    
    let message = paletteResult.valid 
      ? 'All color combinations pass WCAG AA requirements!' 
      : 'Some color combinations need improvement:';
    
    if (paletteResult.issues.length > 0) {
      message += '\n\nIssues:\n' + paletteResult.issues.join('\n');
    }
    
    if (paletteResult.recommendations.length > 0) {
      message += '\n\nRecommendations:\n' + paletteResult.recommendations.join('\n');
    }
    
    Alert.alert('Color Palette Test Results', message);
  }, []);

  // Get contrast result for current colors
  const contrastResult = React.useMemo(() => {
    try {
      return checkContrastCompliance(selectedForeground, selectedBackground, isLargeText);
    } catch {
      return null;
    }
  }, [selectedForeground, selectedBackground, isLargeText]);

  // Get color blindness simulation
  const simulatedColors = React.useMemo(() => {
    if (colorBlindnessType === 'none') {
      return { foreground: selectedForeground, background: selectedBackground };
    }
    
    return {
      foreground: simulateColorBlindness(selectedForeground, colorBlindnessType),
      background: simulateColorBlindness(selectedBackground, colorBlindnessType),
    };
  }, [selectedForeground, selectedBackground, colorBlindnessType]);

  // Predefined color options
  const colorOptions = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF', '#808080', '#800000',
    '#008000', '#000080', '#808000', '#800080', '#008080',
    '#C0C0C0', '#5A7A63', '#FF6B6B', '#4ECDC4', '#45B7D1',
  ];

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Color Accessibility Tester</Text>
          {onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Colors</Text>
          
          {/* Foreground Color */}
          <Text style={styles.label}>Foreground (Text) Color:</Text>
          <View style={styles.colorGrid}>
            {colorOptions.map((color) => (
              <TouchableOpacity
                key={`fg-${color}`}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedForeground === color && styles.selectedColor,
                ]}
                onPress={() => setSelectedForeground(color)}
                accessibilityLabel={`Select foreground color ${color}`}
              />
            ))}
          </View>

          {/* Background Color */}
          <Text style={styles.label}>Background Color:</Text>
          <View style={styles.colorGrid}>
            {colorOptions.map((color) => (
              <TouchableOpacity
                key={`bg-${color}`}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedBackground === color && styles.selectedColor,
                ]}
                onPress={() => setSelectedBackground(color)}
                accessibilityLabel={`Select background color ${color}`}
              />
            ))}
          </View>
        </View>

        {/* Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Options</Text>
          
          <TouchableOpacity
            style={styles.option}
            onPress={() => setIsLargeText(!isLargeText)}
            accessibilityRole="checkbox"
            accessibilityState={{ checked: isLargeText }}
          >
            <Text style={styles.optionText}>
              {isLargeText ? '☑' : '☐'} Large Text (18pt+)
            </Text>
          </TouchableOpacity>

          <Text style={styles.label}>Color Blindness Simulation:</Text>
          <View style={styles.buttonGroup}>
            {[
              { key: 'none', label: 'None' },
              { key: 'protanopia', label: 'Protanopia' },
              { key: 'deuteranopia', label: 'Deuteranopia' },
              { key: 'tritanopia', label: 'Tritanopia' },
            ].map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.button,
                  colorBlindnessType === option.key && styles.selectedButton,
                ]}
                onPress={() => setColorBlindnessType(option.key as any)}
              >
                <Text
                  style={[
                    styles.buttonText,
                    colorBlindnessType === option.key && styles.selectedButtonText,
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Preview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preview</Text>
          
          <View
            style={[
              styles.preview,
              { backgroundColor: simulatedColors.background },
            ]}
          >
            <Text
              style={[
                styles.previewText,
                { color: simulatedColors.foreground },
                isLargeText && styles.largeText,
              ]}
            >
              Sample Text
            </Text>
          </View>
        </View>

        {/* Results */}
        {contrastResult && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contrast Results</Text>
            
            <Text style={styles.resultText}>
              Contrast Ratio: {contrastResult.ratio}:1
            </Text>
            
            <View style={styles.complianceGrid}>
              <View style={[styles.complianceItem, contrastResult.wcagAA && styles.pass]}>
                <Text style={styles.complianceText}>
                  WCAG AA: {contrastResult.wcagAA ? 'PASS' : 'FAIL'}
                </Text>
              </View>
              
              <View style={[styles.complianceItem, contrastResult.wcagAAA && styles.pass]}>
                <Text style={styles.complianceText}>
                  WCAG AAA: {contrastResult.wcagAAA ? 'PASS' : 'FAIL'}
                </Text>
              </View>
            </View>

            {contrastResult.recommendation && (
              <Text style={styles.recommendation}>
                {contrastResult.recommendation}
              </Text>
            )}
          </View>
        )}

        {/* Actions */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.actionButton} onPress={testCurrentColors}>
            <Text style={styles.actionButtonText}>Generate Full Report</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={testColorPalette}>
            <Text style={styles.actionButtonText}>Test App Color Palette</Text>
          </TouchableOpacity>
        </View>

        {/* WCAG Guidelines */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>WCAG Guidelines</Text>
          <Text style={styles.guidelineText}>
            • AA Normal Text: {WCAG_THRESHOLDS.AA_NORMAL}:1 minimum{'\n'}
            • AA Large Text: {WCAG_THRESHOLDS.AA_LARGE}:1 minimum{'\n'}
            • AAA Normal Text: {WCAG_THRESHOLDS.AAA_NORMAL}:1 minimum{'\n'}
            • AAA Large Text: {WCAG_THRESHOLDS.AAA_LARGE}:1 minimum
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    marginTop: 12,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColor: {
    borderColor: '#007AFF',
    borderWidth: 3,
  },
  option: {
    paddingVertical: 8,
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  buttonGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#F0F0F0',
    borderWidth: 1,
    borderColor: '#DDD',
  },
  selectedButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  buttonText: {
    fontSize: 14,
    color: '#333',
  },
  selectedButtonText: {
    color: '#FFFFFF',
  },
  preview: {
    padding: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DDD',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  previewText: {
    fontSize: 16,
    fontWeight: '500',
  },
  largeText: {
    fontSize: 20,
  },
  resultText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  complianceGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  complianceItem: {
    flex: 1,
    padding: 12,
    borderRadius: 6,
    backgroundColor: '#FFE6E6',
    alignItems: 'center',
  },
  pass: {
    backgroundColor: '#E6F7E6',
  },
  complianceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  recommendation: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  guidelineText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default ColorAccessibilityTester;
