b26fdca0b68bdb1ee33cfa966f323262
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = processBackgroundImage;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var processColor = require("./processColor").default;
var DIRECTION_KEYWORD_REGEX = /^to\s+(?:top|bottom|left|right)(?:\s+(?:top|bottom|left|right))?/i;
var ANGLE_UNIT_REGEX = /^([+-]?\d*\.?\d+)(deg|grad|rad|turn)$/i;
var DEFAULT_DIRECTION = {
  type: 'angle',
  value: 180
};
function processBackgroundImage(backgroundImage) {
  var result = [];
  if (backgroundImage == null) {
    return result;
  }
  if (typeof backgroundImage === 'string') {
    result = parseCSSLinearGradient(backgroundImage.replace(/\n/g, ' '));
  } else if (Array.isArray(backgroundImage)) {
    for (var bgImage of backgroundImage) {
      var processedColorStops = [];
      for (var index = 0; index < bgImage.colorStops.length; index++) {
        var colorStop = bgImage.colorStops[index];
        var positions = colorStop.positions;
        if (colorStop.color == null && Array.isArray(positions) && positions.length === 1) {
          var position = positions[0];
          if (typeof position === 'number' || typeof position === 'string' && position.endsWith('%')) {
            processedColorStops.push({
              color: null,
              position: position
            });
          } else {
            return [];
          }
        } else {
          var processedColor = processColor(colorStop.color);
          if (processedColor == null) {
            return [];
          }
          if (positions != null && positions.length > 0) {
            for (var _position of positions) {
              if (typeof _position === 'number' || typeof _position === 'string' && _position.endsWith('%')) {
                processedColorStops.push({
                  color: processedColor,
                  position: _position
                });
              } else {
                return [];
              }
            }
          } else {
            processedColorStops.push({
              color: processedColor,
              position: null
            });
          }
        }
      }
      var direction = DEFAULT_DIRECTION;
      var bgDirection = bgImage.direction != null ? bgImage.direction.toLowerCase() : null;
      if (bgDirection != null) {
        if (ANGLE_UNIT_REGEX.test(bgDirection)) {
          var parsedAngle = getAngleInDegrees(bgDirection);
          if (parsedAngle != null) {
            direction = {
              type: 'angle',
              value: parsedAngle
            };
          } else {
            return [];
          }
        } else if (DIRECTION_KEYWORD_REGEX.test(bgDirection)) {
          var parsedDirection = getDirectionForKeyword(bgDirection);
          if (parsedDirection != null) {
            direction = parsedDirection;
          } else {
            return [];
          }
        } else {
          return [];
        }
      }
      result = result.concat({
        type: 'linearGradient',
        direction: direction,
        colorStops: processedColorStops
      });
    }
  }
  return result;
}
function parseCSSLinearGradient(cssString) {
  var gradients = [];
  var match;
  var linearGradientRegex = /linear-gradient\s*\(((?:\([^)]*\)|[^())])*)\)/gi;
  while (match = linearGradientRegex.exec(cssString)) {
    var gradientContent = match[1];
    var parts = gradientContent.split(',');
    var direction = DEFAULT_DIRECTION;
    var trimmedDirection = parts[0].trim().toLowerCase();
    if (ANGLE_UNIT_REGEX.test(trimmedDirection)) {
      var parsedAngle = getAngleInDegrees(trimmedDirection);
      if (parsedAngle != null) {
        direction = {
          type: 'angle',
          value: parsedAngle
        };
        parts.shift();
      } else {
        return [];
      }
    } else if (DIRECTION_KEYWORD_REGEX.test(trimmedDirection)) {
      var parsedDirection = getDirectionForKeyword(trimmedDirection);
      if (parsedDirection != null) {
        direction = parsedDirection;
        parts.shift();
      } else {
        return [];
      }
    }
    var colorStopsString = parts.join(',');
    var colorStops = [];
    var stops = colorStopsString.split(/,(?![^(]*\))/);
    var prevStop = null;
    for (var i = 0; i < stops.length; i++) {
      var stop = stops[i];
      var trimmedStop = stop.trim().toLowerCase();
      var colorStopParts = trimmedStop.match(/\S+\([^)]*\)|\S+/g);
      if (colorStopParts == null) {
        return [];
      }
      if (colorStopParts.length === 3) {
        var color = colorStopParts[0];
        var position1 = getPositionFromCSSValue(colorStopParts[1]);
        var position2 = getPositionFromCSSValue(colorStopParts[2]);
        var processedColor = processColor(color);
        if (processedColor == null) {
          return [];
        }
        if (position1 == null || position2 == null) {
          return [];
        }
        colorStops.push({
          color: processedColor,
          position: position1
        });
        colorStops.push({
          color: processedColor,
          position: position2
        });
      } else if (colorStopParts.length === 2) {
        var _color = colorStopParts[0];
        var position = getPositionFromCSSValue(colorStopParts[1]);
        var _processedColor = processColor(_color);
        if (_processedColor == null) {
          return [];
        }
        if (position == null) {
          return [];
        }
        colorStops.push({
          color: _processedColor,
          position: position
        });
      } else if (colorStopParts.length === 1) {
        var _position2 = getPositionFromCSSValue(colorStopParts[0]);
        if (_position2 != null) {
          if (prevStop != null && prevStop.length === 1 && getPositionFromCSSValue(prevStop[0]) != null || i === stops.length - 1 || i === 0) {
            return [];
          }
          colorStops.push({
            color: null,
            position: _position2
          });
        } else {
          var _processedColor2 = processColor(colorStopParts[0]);
          if (_processedColor2 == null) {
            return [];
          }
          colorStops.push({
            color: _processedColor2,
            position: null
          });
        }
      } else {
        return [];
      }
      prevStop = colorStopParts;
    }
    gradients.push({
      type: 'linearGradient',
      direction: direction,
      colorStops: colorStops
    });
  }
  return gradients;
}
function getDirectionForKeyword(direction) {
  if (direction == null) {
    return null;
  }
  var normalized = direction.replace(/\s+/g, ' ').toLowerCase();
  switch (normalized) {
    case 'to top':
      return {
        type: 'angle',
        value: 0
      };
    case 'to right':
      return {
        type: 'angle',
        value: 90
      };
    case 'to bottom':
      return {
        type: 'angle',
        value: 180
      };
    case 'to left':
      return {
        type: 'angle',
        value: 270
      };
    case 'to top right':
    case 'to right top':
      return {
        type: 'keyword',
        value: 'to top right'
      };
    case 'to bottom right':
    case 'to right bottom':
      return {
        type: 'keyword',
        value: 'to bottom right'
      };
    case 'to top left':
    case 'to left top':
      return {
        type: 'keyword',
        value: 'to top left'
      };
    case 'to bottom left':
    case 'to left bottom':
      return {
        type: 'keyword',
        value: 'to bottom left'
      };
    default:
      return null;
  }
}
function getAngleInDegrees(angle) {
  if (angle == null) {
    return null;
  }
  var match = angle.match(ANGLE_UNIT_REGEX);
  if (!match) {
    return null;
  }
  var _match = (0, _slicedToArray2.default)(match, 3),
    value = _match[1],
    unit = _match[2];
  var numericValue = parseFloat(value);
  switch (unit) {
    case 'deg':
      return numericValue;
    case 'grad':
      return numericValue * 0.9;
    case 'rad':
      return numericValue * 180 / Math.PI;
    case 'turn':
      return numericValue * 360;
    default:
      return null;
  }
}
function getPositionFromCSSValue(position) {
  if (position.endsWith('px')) {
    return parseFloat(position);
  }
  if (position.endsWith('%')) {
    return position;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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