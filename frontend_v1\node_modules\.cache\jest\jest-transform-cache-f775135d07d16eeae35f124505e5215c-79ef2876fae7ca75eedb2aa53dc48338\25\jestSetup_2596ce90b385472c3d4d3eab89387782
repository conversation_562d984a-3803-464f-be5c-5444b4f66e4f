62a28b8b234c2d4c3aa8ab4b2f2f2b5a
_getJestObj().mock("./src/RNGestureHandlerModule", function () {
  return require("./src/mocks");
});
_getJestObj().mock("./lib/commonjs/RNGestureHandlerModule", function () {
  return require("./lib/commonjs/mocks");
});
_getJestObj().mock("./lib/module/RNGestureHandlerModule", function () {
  return require("./lib/module/mocks");
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZ2V0SmVzdE9iaiIsIm1vY2siLCJyZXF1aXJlIiwiX3JlcXVpcmUiLCJqZXN0Il0sInNvdXJjZXMiOlsiamVzdFNldHVwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImplc3QubW9jaygnLi9zcmMvUk5HZXN0dXJlSGFuZGxlck1vZHVsZScsICgpID0+IHJlcXVpcmUoJy4vc3JjL21vY2tzJykpO1xuamVzdC5tb2NrKCcuL2xpYi9jb21tb25qcy9STkdlc3R1cmVIYW5kbGVyTW9kdWxlJywgKCkgPT5cbiAgcmVxdWlyZSgnLi9saWIvY29tbW9uanMvbW9ja3MnKVxuKTtcbmplc3QubW9jaygnLi9saWIvbW9kdWxlL1JOR2VzdHVyZUhhbmRsZXJNb2R1bGUnLCAoKSA9PlxuICByZXF1aXJlKCcuL2xpYi9tb2R1bGUvbW9ja3MnKVxuKTtcbiJdLCJtYXBwaW5ncyI6IkFBQUFBLFdBQUEsR0FBS0MsSUFBSSxpQ0FBaUM7RUFBQSxPQUFNQyxPQUFPLGNBQWMsQ0FBQztBQUFBLEVBQUM7QUFDdkVGLFdBQUEsR0FBS0MsSUFBSSwwQ0FBMEM7RUFBQSxPQUNqREMsT0FBTyx1QkFBdUIsQ0FBQztBQUFBLENBQ2pDLENBQUM7QUFDREYsV0FBQSxHQUFLQyxJQUFJLHdDQUF3QztFQUFBLE9BQy9DQyxPQUFPLHFCQUFxQixDQUFDO0FBQUEsQ0FDL0IsQ0FBQztBQUFDLFNBQUFGLFlBQUE7RUFBQSxJQUFBRyxRQUFBLEdBQUFELE9BQUE7SUFBQUUsSUFBQSxHQUFBRCxRQUFBLENBQUFDLElBQUE7RUFBQUosV0FBQSxZQUFBQSxZQUFBO0lBQUEsT0FBQUksSUFBQTtFQUFBO0VBQUEsT0FBQUEsSUFBQTtBQUFBIiwiaWdub3JlTGlzdCI6W119