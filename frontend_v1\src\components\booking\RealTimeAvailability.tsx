/**
 * Real-time Availability Component
 * Shows live availability updates with instant booking capability
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useRealTimeBooking } from '../../hooks/useRealTimeBooking';
import { TimeSlot, AvailabilityRequest } from '../../services/realTimeBookingService';
import { Ionicons } from '@expo/vector-icons';

interface RealTimeAvailabilityProps {
  providerId: string;
  serviceId: string;
  selectedDate: string;
  onSlotSelect: (slot: TimeSlot) => void;
  onBookingCreate: (slot: TimeSlot) => void;
  enableInstantBooking?: boolean;
  showReservationTimer?: boolean;
}

const RealTimeAvailability: React.FC<RealTimeAvailabilityProps> = ({
  providerId,
  serviceId,
  selectedDate,
  onSlotSelect,
  onBookingCreate,
  enableInstantBooking = true,
  showReservationTimer = true,
}) => {
  const { colors } = useTheme();
  const [reservationTimeLeft, setReservationTimeLeft] = useState<number | null>(null);

  const {
    timeSlots,
    selectedSlot,
    isLoading,
    isBooking,
    error,
    reservation,
    lastUpdate,
    loadAvailability,
    selectTimeSlot,
    createBooking,
    cancelReservation,
    refreshAvailability,
    clearError,
  } = useRealTimeBooking({
    autoRefresh: true,
    refreshInterval: 15000, // 15 seconds
    enableReservations: true,
  });

  // Load availability when props change
  useEffect(() => {
    if (providerId && serviceId && selectedDate) {
      const request: AvailabilityRequest = {
        providerId,
        serviceId,
        date: selectedDate,
      };
      loadAvailability(request);
    }
  }, [providerId, serviceId, selectedDate, loadAvailability]);

  // Update reservation timer
  useEffect(() => {
    if (reservation && showReservationTimer) {
      const updateTimer = () => {
        const expiresAt = new Date(reservation.expiresAt).getTime();
        const now = Date.now();
        const timeLeft = Math.max(0, Math.floor((expiresAt - now) / 1000));
        setReservationTimeLeft(timeLeft);

        if (timeLeft === 0) {
          setReservationTimeLeft(null);
        }
      };

      updateTimer();
      const interval = setInterval(updateTimer, 1000);

      return () => clearInterval(interval);
    } else {
      setReservationTimeLeft(null);
    }
  }, [reservation, showReservationTimer]);

  const handleSlotPress = useCallback(async (slot: TimeSlot) => {
    if (!slot.isAvailable) {
      Alert.alert('Unavailable', 'This time slot is no longer available');
      return;
    }

    try {
      await selectTimeSlot(slot);
      onSlotSelect(slot);
    } catch (error) {
      Alert.alert('Error', 'Failed to select time slot');
    }
  }, [selectTimeSlot, onSlotSelect]);

  const handleInstantBooking = useCallback(async (slot: TimeSlot) => {
    if (!enableInstantBooking) return;

    try {
      Alert.alert(
        'Instant Booking',
        `Book ${slot.startTime} - ${slot.endTime} for $${slot.price}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Book Now',
            onPress: async () => {
              try {
                await createBooking({
                  providerId,
                  serviceId,
                  date: selectedDate,
                });
                onBookingCreate(slot);
                Alert.alert('Success', 'Booking confirmed instantly!');
              } catch (error) {
                Alert.alert('Error', 'Failed to create booking');
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to process instant booking');
    }
  }, [enableInstantBooking, createBooking, providerId, serviceId, selectedDate, onBookingCreate]);

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatReservationTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderTimeSlot = (slot: TimeSlot) => {
    const isSelected = selectedSlot?.id === slot.id;
    const availableSpots = slot.maxBookings - slot.currentBookings;

    return (
      <TouchableOpacity
        key={slot.id}
        style={[
          styles.timeSlot,
          {
            backgroundColor: isSelected
              ? colors?.primary?.main || Colors.primary?.main
              : slot.isAvailable
              ? colors?.background?.secondary || Colors.background?.secondary
              : colors?.background?.tertiary || Colors.background?.tertiary,
            borderColor: isSelected
              ? colors?.primary?.dark || Colors.primary?.dark
              : colors?.border?.primary || Colors.border?.primary,
          },
        ]}
        onPress={() => handleSlotPress(slot)}
        disabled={!slot.isAvailable || isLoading}
        activeOpacity={0.7}
      >
        <View style={styles.timeSlotHeader}>
          <Text
            style={[
              styles.timeText,
              {
                color: isSelected
                  ? Colors.white
                  : slot.isAvailable
                  ? colors?.text?.primary || Colors.text?.primary
                  : colors?.text?.tertiary || Colors.text?.tertiary,
              },
            ]}
          >
            {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
          </Text>
          
          {slot.isAvailable && (
            <Text
              style={[
                styles.priceText,
                {
                  color: isSelected
                    ? Colors.white
                    : colors?.primary?.main || Colors.primary?.main,
                },
              ]}
            >
              ${slot.price}
            </Text>
          )}
        </View>

        <View style={styles.timeSlotDetails}>
          <Text
            style={[
              styles.availabilityText,
              {
                color: isSelected
                  ? Colors.white
                  : slot.isAvailable
                  ? colors?.text?.secondary || Colors.text?.secondary
                  : colors?.text?.tertiary || Colors.text?.tertiary,
              },
            ]}
          >
            {slot.isAvailable
              ? `${availableSpots} spot${availableSpots !== 1 ? 's' : ''} available`
              : 'Fully booked'}
          </Text>

          {slot.isAvailable && enableInstantBooking && (
            <TouchableOpacity
              style={[
                styles.instantBookButton,
                {
                  backgroundColor: isSelected
                    ? 'rgba(255, 255, 255, 0.2)'
                    : colors?.primary?.light || Colors.primary?.light,
                },
              ]}
              onPress={() => handleInstantBooking(slot)}
              disabled={isBooking}
            >
              <Ionicons
                name="flash"
                size={12}
                color={isSelected ? Colors.white : Colors.white}
              />
              <Text
                style={[
                  styles.instantBookText,
                  { color: isSelected ? Colors.white : Colors.white },
                ]}
              >
                Book Now
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderReservationTimer = () => {
    if (!reservation || !reservationTimeLeft || !showReservationTimer) return null;

    return (
      <View
        style={[
          styles.reservationTimer,
          {
            backgroundColor: colors?.warning?.light || Colors.warning?.light,
            borderColor: colors?.warning?.main || Colors.warning?.main,
          },
        ]}
      >
        <Ionicons
          name="timer-outline"
          size={16}
          color={colors?.warning?.dark || Colors.warning?.dark}
        />
        <Text
          style={[
            styles.reservationText,
            { color: colors?.warning?.dark || Colors.warning?.dark },
          ]}
        >
          Slot reserved for {formatReservationTime(reservationTimeLeft)}
        </Text>
        <TouchableOpacity onPress={cancelReservation} style={styles.cancelReservation}>
          <Ionicons
            name="close"
            size={16}
            color={colors?.warning?.dark || Colors.warning?.dark}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Text
          style={[
            styles.headerTitle,
            { color: colors?.text?.primary || Colors.text?.primary },
          ]}
        >
          Available Times
        </Text>
        {lastUpdate && (
          <Text
            style={[
              styles.lastUpdateText,
              { color: colors?.text?.secondary || Colors.text?.secondary },
            ]}
          >
            Updated {new Date(lastUpdate).toLocaleTimeString()}
          </Text>
        )}
      </View>

      <TouchableOpacity
        style={[
          styles.refreshButton,
          { borderColor: colors?.border?.primary || Colors.border?.primary },
        ]}
        onPress={refreshAvailability}
        disabled={isLoading}
      >
        <Ionicons
          name="refresh"
          size={16}
          color={colors?.primary?.main || Colors.primary?.main}
        />
      </TouchableOpacity>
    </View>
  );

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={[styles.errorText, { color: colors?.error?.main || Colors.error?.main }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[
            styles.retryButton,
            { backgroundColor: colors?.primary?.main || Colors.primary?.main },
          ]}
          onPress={() => {
            clearError();
            refreshAvailability();
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderHeader()}
      {renderReservationTimer()}

      {isLoading && timeSlots.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={colors?.primary?.main || Colors.primary?.main}
          />
          <Text
            style={[
              styles.loadingText,
              { color: colors?.text?.secondary || Colors.text?.secondary },
            ]}
          >
            Loading availability...
          </Text>
        </View>
      ) : timeSlots.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="calendar-outline"
            size={48}
            color={colors?.text?.tertiary || Colors.text?.tertiary}
          />
          <Text
            style={[
              styles.emptyText,
              { color: colors?.text?.secondary || Colors.text?.secondary },
            ]}
          >
            No available times for this date
          </Text>
        </View>
      ) : (
        <ScrollView
          style={styles.slotsContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.slotsContent}
        >
          {timeSlots.map(renderTimeSlot)}
        </ScrollView>
      )}

      {isBooking && (
        <View style={styles.bookingOverlay}>
          <ActivityIndicator
            size="large"
            color={colors?.primary?.main || Colors.primary?.main}
          />
          <Text
            style={[
              styles.bookingText,
              { color: colors?.text?.primary || Colors.text?.primary },
            ]}
          >
            Creating booking...
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(3),
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
  },
  lastUpdateText: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(0.5),
  },
  refreshButton: {
    padding: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(1),
    borderWidth: 1,
  },
  reservationTimer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(2),
    marginHorizontal: getResponsiveSpacing(4),
    marginBottom: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(2),
    borderWidth: 1,
  },
  reservationText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    marginLeft: getResponsiveSpacing(2),
    flex: 1,
  },
  cancelReservation: {
    padding: getResponsiveSpacing(1),
  },
  slotsContainer: {
    flex: 1,
  },
  slotsContent: {
    padding: getResponsiveSpacing(4),
    gap: getResponsiveSpacing(3),
  },
  timeSlot: {
    padding: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(3),
    borderWidth: 2,
  },
  timeSlotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  timeText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
  },
  priceText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
  },
  timeSlotDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availabilityText: {
    fontSize: getResponsiveFontSize(14),
    flex: 1,
  },
  instantBookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(1.5),
    borderRadius: getResponsiveSpacing(4),
    gap: getResponsiveSpacing(1),
  },
  instantBookText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(8),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    marginTop: getResponsiveSpacing(3),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(8),
  },
  emptyText: {
    fontSize: getResponsiveFontSize(16),
    textAlign: 'center',
    marginTop: getResponsiveSpacing(3),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(8),
  },
  errorText: {
    fontSize: getResponsiveFontSize(16),
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(4),
  },
  retryButton: {
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(2),
  },
  retryButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
  },
  bookingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bookingText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginTop: getResponsiveSpacing(3),
  },
});

export default RealTimeAvailability;
