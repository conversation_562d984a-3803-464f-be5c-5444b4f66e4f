9485719f21c94da7bfbf2e9facadb86a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = usePressability;
var _Pressability = _interopRequireDefault(require("./Pressability"));
var _react = require("react");
function usePressability(config) {
  var pressabilityRef = (0, _react.useRef)(null);
  if (config != null && pressabilityRef.current == null) {
    pressabilityRef.current = new _Pressability.default(config);
  }
  var pressability = pressabilityRef.current;
  (0, _react.useEffect)(function () {
    if (config != null && pressability != null) {
      pressability.configure(config);
    }
  }, [config, pressability]);
  (0, _react.useEffect)(function () {
    if (pressability != null) {
      return function () {
        pressability.reset();
      };
    }
  }, [pressability]);
  return pressability == null ? null : pressability.getEventHandlers();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfUHJlc3NhYmlsaXR5IiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJfcmVhY3QiLCJ1c2VQcmVzc2FiaWxpdHkiLCJjb25maWciLCJwcmVzc2FiaWxpdHlSZWYiLCJ1c2VSZWYiLCJjdXJyZW50IiwiUHJlc3NhYmlsaXR5IiwicHJlc3NhYmlsaXR5IiwidXNlRWZmZWN0IiwiY29uZmlndXJlIiwicmVzZXQiLCJnZXRFdmVudEhhbmRsZXJzIl0sInNvdXJjZXMiOlsidXNlUHJlc3NhYmlsaXR5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqIEBmb3JtYXRcbiAqL1xuXG5pbXBvcnQgUHJlc3NhYmlsaXR5LCB7XG4gIHR5cGUgRXZlbnRIYW5kbGVycyxcbiAgdHlwZSBQcmVzc2FiaWxpdHlDb25maWcsXG59IGZyb20gJy4vUHJlc3NhYmlsaXR5JztcbmltcG9ydCB7dXNlRWZmZWN0LCB1c2VSZWZ9IGZyb20gJ3JlYWN0JztcblxuZGVjbGFyZSBmdW5jdGlvbiB1c2VQcmVzc2FiaWxpdHkoY29uZmlnOiBQcmVzc2FiaWxpdHlDb25maWcpOiBFdmVudEhhbmRsZXJzO1xuZGVjbGFyZSBmdW5jdGlvbiB1c2VQcmVzc2FiaWxpdHkoY29uZmlnOiBudWxsIHwgdm9pZCk6IG51bGwgfCBFdmVudEhhbmRsZXJzO1xuXG4vKipcbiAqIENyZWF0ZXMgYSBwZXJzaXN0ZW50IGluc3RhbmNlIG9mIGBQcmVzc2FiaWxpdHlgIHRoYXQgYXV0b21hdGljYWxseSBjb25maWd1cmVzXG4gKiBpdHNlbGYgYW5kIHJlc2V0cy4gQWNjZXB0cyBudWxsIGBjb25maWdgIHRvIHN1cHBvcnQgbGF6eSBpbml0aWFsaXphdGlvbi4gT25jZVxuICogaW5pdGlhbGl6ZWQsIHdpbGwgbm90IHVuLWluaXRpYWxpemUgdW50aWwgdGhlIGNvbXBvbmVudCBoYXMgYmVlbiB1bm1vdW50ZWQuXG4gKlxuICogSW4gb3JkZXIgdG8gdXNlIGB1c2VQcmVzc2FiaWxpdHlgLCBkbyB0aGUgZm9sbG93aW5nOlxuICpcbiAqICAgY29uc3QgY29uZmlnID0gdXNlTWVtbyguLi4pO1xuICogICBjb25zdCBldmVudEhhbmRsZXJzID0gdXNlUHJlc3NhYmlsaXR5KGNvbmZpZyk7XG4gKiAgIGNvbnN0IHByZXNzYWJsZVZpZXcgPSA8VmlldyB7Li4uZXZlbnRIYW5kbGVyc30gLz47XG4gKlxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VQcmVzc2FiaWxpdHkoXG4gIGNvbmZpZzogP1ByZXNzYWJpbGl0eUNvbmZpZyxcbik6IG51bGwgfCBFdmVudEhhbmRsZXJzIHtcbiAgY29uc3QgcHJlc3NhYmlsaXR5UmVmID0gdXNlUmVmPD9QcmVzc2FiaWxpdHk+KG51bGwpO1xuICBpZiAoY29uZmlnICE9IG51bGwgJiYgcHJlc3NhYmlsaXR5UmVmLmN1cnJlbnQgPT0gbnVsbCkge1xuICAgIHByZXNzYWJpbGl0eVJlZi5jdXJyZW50ID0gbmV3IFByZXNzYWJpbGl0eShjb25maWcpO1xuICB9XG4gIGNvbnN0IHByZXNzYWJpbGl0eSA9IHByZXNzYWJpbGl0eVJlZi5jdXJyZW50O1xuXG4gIC8vIE9uIHRoZSBpbml0aWFsIG1vdW50LCB0aGlzIGlzIGEgbm8tb3AuIE9uIHVwZGF0ZXMsIGBwcmVzc2FiaWxpdHlgIHdpbGwgYmVcbiAgLy8gcmUtY29uZmlndXJlZCB0byB1c2UgdGhlIG5ldyBjb25maWd1cmF0aW9uLlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChjb25maWcgIT0gbnVsbCAmJiBwcmVzc2FiaWxpdHkgIT0gbnVsbCkge1xuICAgICAgcHJlc3NhYmlsaXR5LmNvbmZpZ3VyZShjb25maWcpO1xuICAgIH1cbiAgfSwgW2NvbmZpZywgcHJlc3NhYmlsaXR5XSk7XG5cbiAgLy8gT24gdW5tb3VudCwgcmVzZXQgcGVuZGluZyBzdGF0ZSBhbmQgdGltZXJzIGluc2lkZSBgcHJlc3NhYmlsaXR5YC4gVGhpcyBpc1xuICAvLyBhIHNlcGFyYXRlIGVmZmVjdCBiZWNhdXNlIHdlIGRvIG5vdCB3YW50IHRvIHJlc2V0IHdoZW4gYGNvbmZpZ2AgY2hhbmdlcy5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJlc3NhYmlsaXR5ICE9IG51bGwpIHtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIHByZXNzYWJpbGl0eS5yZXNldCgpO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtwcmVzc2FiaWxpdHldKTtcblxuICByZXR1cm4gcHJlc3NhYmlsaXR5ID09IG51bGwgPyBudWxsIDogcHJlc3NhYmlsaXR5LmdldEV2ZW50SGFuZGxlcnMoKTtcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7QUFVQSxJQUFBQSxhQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFJQSxJQUFBQyxNQUFBLEdBQUFELE9BQUE7QUFpQmUsU0FBU0UsZUFBZUEsQ0FDckNDLE1BQTJCLEVBQ0w7RUFDdEIsSUFBTUMsZUFBZSxHQUFHLElBQUFDLGFBQU0sRUFBZ0IsSUFBSSxDQUFDO0VBQ25ELElBQUlGLE1BQU0sSUFBSSxJQUFJLElBQUlDLGVBQWUsQ0FBQ0UsT0FBTyxJQUFJLElBQUksRUFBRTtJQUNyREYsZUFBZSxDQUFDRSxPQUFPLEdBQUcsSUFBSUMscUJBQVksQ0FBQ0osTUFBTSxDQUFDO0VBQ3BEO0VBQ0EsSUFBTUssWUFBWSxHQUFHSixlQUFlLENBQUNFLE9BQU87RUFJNUMsSUFBQUcsZ0JBQVMsRUFBQyxZQUFNO0lBQ2QsSUFBSU4sTUFBTSxJQUFJLElBQUksSUFBSUssWUFBWSxJQUFJLElBQUksRUFBRTtNQUMxQ0EsWUFBWSxDQUFDRSxTQUFTLENBQUNQLE1BQU0sQ0FBQztJQUNoQztFQUNGLENBQUMsRUFBRSxDQUFDQSxNQUFNLEVBQUVLLFlBQVksQ0FBQyxDQUFDO0VBSTFCLElBQUFDLGdCQUFTLEVBQUMsWUFBTTtJQUNkLElBQUlELFlBQVksSUFBSSxJQUFJLEVBQUU7TUFDeEIsT0FBTyxZQUFNO1FBQ1hBLFlBQVksQ0FBQ0csS0FBSyxDQUFDLENBQUM7TUFDdEIsQ0FBQztJQUNIO0VBQ0YsQ0FBQyxFQUFFLENBQUNILFlBQVksQ0FBQyxDQUFDO0VBRWxCLE9BQU9BLFlBQVksSUFBSSxJQUFJLEdBQUcsSUFBSSxHQUFHQSxZQUFZLENBQUNJLGdCQUFnQixDQUFDLENBQUM7QUFDdEUiLCJpZ25vcmVMaXN0IjpbXX0=