/**
 * Vierla Design System - Comprehensive Design Tokens
 *
 * Implements a consistent design system following Material Design 3 and
 * Human Interface Guidelines principles with WCAG 2.2 AA compliance.
 *
 * Features:
 * - Consistent spacing scale (8pt grid system)
 * - Typography hierarchy with accessibility
 * - Component sizing standards
 * - Animation and timing constants
 * - Responsive breakpoints
 * - Elevation and shadow system
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Base unit for spacing (8pt grid system)
const BASE_UNIT = 8;

// Spacing scale based on 8pt grid system
export const Spacing = {
  // Micro spacing
  xs: BASE_UNIT * 0.5, // 4px
  sm: BASE_UNIT, // 8px
  md: BASE_UNIT * 2, // 16px
  lg: BASE_UNIT * 3, // 24px
  xl: BASE_UNIT * 4, // 32px
  xxl: BASE_UNIT * 6, // 48px
  xxxl: BASE_UNIT * 8, // 64px

  // Semantic spacing
  none: 0,
  hairline: 1,
  tiny: 2,
  small: 4,
  base: 8,
  medium: 12,
  large: 16,
  xlarge: 20,
  xxlarge: 24,
  huge: 32,
  massive: 40,

  // Component-specific spacing
  component: {
    padding: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
    margin: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
    gap: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
    },
  },

  // Layout spacing
  layout: {
    screenPadding: 16,
    sectionSpacing: 24,
    cardPadding: 16,
    listItemPadding: 12,
    buttonPadding: 16,
    inputPadding: 12,
  },
} as const;

// Typography system with accessibility considerations
export const Typography = {
  // Font families
  fontFamily: {
    primary: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    secondary: Platform.select({
      ios: 'SF Pro Text',
      android: 'Roboto',
      default: 'System',
    }),
    mono: Platform.select({
      ios: 'SF Mono',
      android: 'Roboto Mono',
      default: 'monospace',
    }),
  },

  // Font weights
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    heavy: '800' as const,
  },

  // Font sizes (accessible scale)
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
  },

  // Line heights (1.5 ratio for accessibility)
  lineHeight: {
    xs: 18, // 12 * 1.5
    sm: 21, // 14 * 1.5
    base: 24, // 16 * 1.5
    lg: 27, // 18 * 1.5
    xl: 30, // 20 * 1.5
    '2xl': 36, // 24 * 1.5
    '3xl': 45, // 30 * 1.5
    '4xl': 54, // 36 * 1.5
    '5xl': 72, // 48 * 1.5
    '6xl': 90, // 60 * 1.5
  },

  // Text styles for common use cases
  styles: {
    // Headings
    h1: {
      fontSize: 36,
      lineHeight: 54,
      fontWeight: '700' as const,
      letterSpacing: -0.5,
    },
    h2: {
      fontSize: 30,
      lineHeight: 45,
      fontWeight: '700' as const,
      letterSpacing: -0.25,
    },
    h3: {
      fontSize: 24,
      lineHeight: 36,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    h4: {
      fontSize: 20,
      lineHeight: 30,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    h5: {
      fontSize: 18,
      lineHeight: 27,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    h6: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },

    // Body text
    body1: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    body2: {
      fontSize: 14,
      lineHeight: 21,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },

    // UI text
    button: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '600' as const,
      letterSpacing: 0.5,
    },
    caption: {
      fontSize: 12,
      lineHeight: 18,
      fontWeight: '400' as const,
      letterSpacing: 0.4,
    },
    overline: {
      fontSize: 12,
      lineHeight: 18,
      fontWeight: '600' as const,
      letterSpacing: 1.5,
      textTransform: 'uppercase' as const,
    },
  },
} as const;

// Border radius system
export const BorderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,

  // Component-specific radius
  component: {
    button: 8,
    card: 12,
    input: 8,
    modal: 16,
    avatar: 9999,
    badge: 12,
  },
} as const;

// Elevation and shadow system
export const Elevation = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 16,
  },
} as const;

// Animation and timing
export const Animation = {
  // Duration (in milliseconds)
  duration: {
    instant: 0,
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 800,
  },

  // Easing curves
  easing: {
    linear: 'linear',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // Common animation configs
  configs: {
    fadeIn: {
      duration: 300,
      easing: 'ease-out',
    },
    slideIn: {
      duration: 300,
      easing: 'ease-out',
    },
    bounce: {
      duration: 500,
      easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    },
  },
} as const;

// Responsive breakpoints
export const Breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,

  // Current screen size helpers
  current: {
    width: screenWidth,
    height: screenHeight,
    isSmall: screenWidth < 576,
    isMedium: screenWidth >= 576 && screenWidth < 768,
    isLarge: screenWidth >= 768,
  },
} as const;

// Component sizing standards
export const ComponentSizes = {
  // Touch targets (minimum 44x44 for accessibility)
  touchTarget: {
    minimum: 44,
    comfortable: 48,
    large: 56,
  },

  // Button sizes
  button: {
    small: {
      height: 32,
      paddingHorizontal: 12,
      fontSize: 14,
    },
    medium: {
      height: 40,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    large: {
      height: 48,
      paddingHorizontal: 24,
      fontSize: 16,
    },
  },

  // Input sizes
  input: {
    small: {
      height: 32,
      paddingHorizontal: 8,
      fontSize: 14,
    },
    medium: {
      height: 40,
      paddingHorizontal: 12,
      fontSize: 16,
    },
    large: {
      height: 48,
      paddingHorizontal: 16,
      fontSize: 16,
    },
  },

  // Avatar sizes
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
    xxl: 80,
  },

  // Icon sizes
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    xxl: 40,
  },
} as const;

// Z-index scale
export const ZIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Opacity scale
export const Opacity = {
  invisible: 0,
  disabled: 0.4,
  secondary: 0.6,
  primary: 0.8,
  visible: 1,
} as const;

// Design system utilities
export const getSpacing = (size: keyof typeof Spacing): number => {
  return Spacing[size];
};

export const getTypographyStyle = (variant: keyof typeof Typography.styles) => {
  return Typography.styles[variant];
};

export const getElevation = (level: keyof typeof Elevation) => {
  return Elevation[level];
};

export const getComponentSize = (component: keyof typeof ComponentSizes, size: string) => {
  return ComponentSizes[component][size as keyof typeof ComponentSizes[typeof component]];
};

// Responsive helpers
export const isSmallScreen = (): boolean => {
  return Breakpoints.current.isSmall;
};

export const isMediumScreen = (): boolean => {
  return Breakpoints.current.isMedium;
};

export const isLargeScreen = (): boolean => {
  return Breakpoints.current.isLarge;
};

// Accessibility helpers
export const getAccessibleTouchTarget = (size: 'minimum' | 'comfortable' | 'large' = 'minimum'): number => {
  return ComponentSizes.touchTarget[size];
};

export const getAccessibleFontSize = (variant: keyof typeof Typography.styles): number => {
  return Typography.styles[variant].fontSize;
};

export default {
  Spacing,
  Typography,
  BorderRadius,
  Elevation,
  Animation,
  Breakpoints,
  ComponentSizes,
  ZIndex,
  Opacity,
  // Utilities
  getSpacing,
  getTypographyStyle,
  getElevation,
  getComponentSize,
  isSmallScreen,
  isMediumScreen,
  isLargeScreen,
  getAccessibleTouchTarget,
  getAccessibleFontSize,
};
