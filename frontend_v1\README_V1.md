# Vierla Frontend v1 - Canadian Service Marketplace

A comprehensive React Native application for the Canadian service marketplace, built with accessibility, performance, and user experience as core principles.

## 🚀 Overview

Vierla Frontend v1 is a modern, accessible, and performant mobile application designed specifically for the Canadian market. It connects service providers with customers across Canada, with full support for Canadian regulations, currencies, and cultural preferences.

### ✨ Key Features

- **🇨🇦 Canadian Market Focus**: Localized for all Canadian provinces and territories
- **♿ Accessibility First**: WCAG 2.1 AA compliant with comprehensive accessibility features
- **🚀 Performance Optimized**: Code splitting, lazy loading, and bundle optimization
- **🌐 Bilingual Support**: English and French-Canadian (Quebec French) localization
- **🎨 Design System**: Comprehensive component library with consistent styling
- **🧪 Testing Infrastructure**: Unit, integration, accessibility, and performance testing
- **📱 Cross-Platform**: iOS, Android, and web support

## 🛠 Technology Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: Zustand
- **Navigation**: React Navigation v6
- **Styling**: StyleSheet with theme system
- **Testing**: Jest + React Native Testing Library
- **Internationalization**: react-i18next
- **Icons**: Expo Vector Icons
- **Animations**: React Native Reanimated

## 📋 Prerequisites

- Node.js 18+ and npm/yarn
- Expo CLI (`npm install -g @expo/cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## 🚀 Getting Started

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd frontend_v1

# Install dependencies
npm install

# Start the development server
npm start
```

### Environment Setup

Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_API_BASE_URL=http://************:8000
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_ENABLE_ANALYTICS=false
```

### Running the Application

```bash
# Start development server
npm start

# Run on specific platforms
npm run android    # Android device/emulator
npm run ios        # iOS device/simulator  
npm run web        # Web browser
```

## 📁 Project Structure

```
frontend_v1/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── animation/       # Animation components
│   │   ├── error/          # Error handling components
│   │   ├── forms/          # Form components
│   │   ├── loading/        # Loading state components
│   │   ├── localization/   # Language components
│   │   ├── navigation/     # Navigation components
│   │   ├── payment/        # Payment components
│   │   └── typography/     # Typography components
│   ├── contexts/           # React contexts
│   ├── hooks/              # Custom React hooks
│   ├── localization/       # i18n configuration
│   ├── screens/            # Screen components
│   ├── services/           # API and external services
│   ├── store/              # State management
│   ├── types/              # TypeScript definitions
│   ├── utils/              # Utility functions
│   └── __tests__/          # Test files
├── assets/                 # Static assets
├── docs/                   # Documentation
└── package.json
```

## 🎯 Available Scripts

```bash
# Development
npm start              # Start Expo development server
npm run android        # Run on Android device/emulator
npm run ios           # Run on iOS device/simulator
npm run web           # Run on web browser

# Testing
npm test              # Run test suite
npm run test:watch    # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
npm run test:a11y     # Run accessibility tests
npm run test:perf     # Run performance tests

# Code Quality
npm run lint          # Run ESLint
npm run lint:fix      # Fix ESLint issues
npm run type-check    # Run TypeScript type checking
npm run format        # Format code with Prettier

# Build
npm run build         # Build for production
npm run build:web     # Build for web
npm run build:android # Build Android APK
npm run build:ios     # Build iOS IPA
```

## 🏗 Architecture

### Design Patterns

- **Atomic Design**: Components organized by complexity (atoms, molecules, organisms)
- **Composition over Inheritance**: Favor component composition
- **Context API**: For theme, accessibility, and configuration
- **Custom Hooks**: Reusable logic extraction

### State Management

- **Zustand**: Lightweight global state management
- **React Context**: Theme and accessibility settings
- **Local State**: Component-specific state with hooks

### Error Handling

- **Error Boundaries**: Graceful error catching and recovery
- **Centralized Error Utils**: Consistent error classification
- **User-Friendly Messages**: Contextual error messages

## ♿ Accessibility Features

### WCAG 2.1 AA Compliance

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Comprehensive ARIA labels and roles
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Touch Targets**: 44x44 point minimum size
- **Focus Management**: Logical focus order and indicators

### Accessibility Contexts

```typescript
// High contrast support
const { colors } = useHighContrastColors();

// Motor accessibility
const touchTargetStyles = useTouchTargetStyles();

// Animation preferences
const { shouldAnimate } = useAnimation();
```

## 🌐 Internationalization

### Supported Languages

- **English (Canada)**: Primary language with Canadian terminology
- **French (Canada)**: Quebec French with proper cultural adaptations

### Usage

```typescript
const { t } = useTranslation();

// Simple translation
<Text>{t('common.loading')}</Text>

// With interpolation
<Text>{t('greeting', { name: 'John' })}</Text>

// Pluralization
<Text>{t('items_count', { count: items.length })}</Text>
```

## 🇨🇦 Canadian Market Features

### Provincial Compliance

- **Tax Calculations**: GST/HST/PST by province
- **Regulatory Compliance**: Provincial service regulations
- **Address Validation**: Canadian postal code validation
- **Currency Formatting**: Canadian dollar formatting

### Cultural Adaptations

- **Regional Preferences**: Communication styles by region
- **Business Hours**: Provincial business hour standards
- **Seasonal Considerations**: Weather and cultural factors
- **Local Terminology**: Regional language variations

## 🧪 Testing Strategy

### Test Types

- **Unit Tests**: Component and utility testing
- **Integration Tests**: Component interaction testing
- **Accessibility Tests**: WCAG compliance verification
- **Performance Tests**: Render time and memory usage

### Testing Tools

```typescript
// Accessibility testing
const result = accessibilityTestUtils.auditAccessibility(component);
expect(result.passed).toBe(true);

// Performance testing
await testAssertions.assertPerformant(component, {
  maxRenderTime: 50,
  maxMemoryUsage: 10,
  maxComponentCount: 20,
});
```

## 🚀 Performance Optimization

### Bundle Optimization

- **Code Splitting**: Route-based and component-based splitting
- **Lazy Loading**: On-demand component loading
- **Tree Shaking**: Unused code elimination
- **Asset Optimization**: Image and resource optimization

### Runtime Performance

- **Memoization**: React.memo and useMemo optimization
- **Callback Optimization**: useCallback for event handlers
- **List Optimization**: FlatList for large datasets
- **Memory Management**: Automatic cleanup and monitoring

## 📊 Performance Monitoring

```typescript
// Bundle analysis
const { metrics, recommendations } = usePerformanceMonitoring();

// Component tracking
trackComponentMetrics('ComponentName', size, loadTime);

// Bundle size analysis
analyzeBundleSize();
```

## 🔧 Development Tools

### Bundle Analyzer (Development Only)

```typescript
import { BundleAnalyzerButton } from '@/components/performance/BundleAnalyzer';

// Floating button appears in development builds
<BundleAnalyzerButton />
```

### Debug Features

- **Performance Metrics**: Real-time performance monitoring
- **Accessibility Audit**: Live accessibility checking
- **Bundle Analysis**: Bundle size and optimization recommendations
- **Error Tracking**: Comprehensive error logging

## 📚 Documentation

- **[Development Guide](./DEVELOPMENT_GUIDE.md)**: Comprehensive development documentation
- **[Component Documentation](./docs/COMPONENT_DOCUMENTATION_TEMPLATE.md)**: Component documentation template
- **[API Documentation](./docs/API.md)**: API integration guide
- **[Testing Guide](./docs/TESTING.md)**: Testing best practices

## 🤝 Contributing

1. **Read Documentation**: Review development guide and coding standards
2. **Setup Environment**: Follow installation and setup instructions
3. **Run Tests**: Ensure all tests pass before submitting
4. **Accessibility Check**: Verify WCAG compliance
5. **Performance Check**: Ensure performance benchmarks are met

### Code Quality Standards

- **TypeScript**: Strict type checking enabled
- **ESLint**: Comprehensive linting rules
- **Prettier**: Consistent code formatting
- **Test Coverage**: Minimum 80% coverage required

## 📄 License

This project is proprietary software developed for Vierla Inc.

## 📞 Support

For technical support or questions:
- **Documentation**: Check the docs/ directory
- **Issues**: Create detailed issue reports
- **Performance**: Use built-in performance monitoring tools
- **Accessibility**: Run accessibility audits regularly

---

**Built with ❤️ for the Canadian market**
