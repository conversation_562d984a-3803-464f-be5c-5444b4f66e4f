/**
 * Canadian Location Utilities
 *
 * Comprehensive utilities for Canadian address validation, postal code
 * processing, and location services with proper Canadian standards.
 *
 * Features:
 * - Canadian postal code validation
 * - Address standardization
 * - Province/territory handling
 * - Geographic calculations
 * - Location services integration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { CanadianProvince, CANADIAN_PROVINCES } from './canadianPaymentUtils';

// Canadian address components
export interface CanadianAddress {
  streetNumber?: string;
  streetName: string;
  streetType?: string;
  streetDirection?: string;
  unitNumber?: string;
  unitType?: string;
  city: string;
  province: CanadianProvince;
  postalCode: string;
  country: 'Canada' | 'CA';
  formatted?: string;
}

// Geographic coordinates
export interface Coordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

// Location with address and coordinates
export interface Location extends CanadianAddress {
  coordinates?: Coordinates;
  timezone?: string;
  region?: string;
}

// Postal code information
export interface PostalCodeInfo {
  postalCode: string;
  province: CanadianProvince;
  city?: string;
  region?: string;
  coordinates?: Coordinates;
  deliveryMode?: 'urban' | 'rural' | 'suburban';
  forwardSortationArea: string; // First 3 characters
  localDeliveryUnit: string; // Last 3 characters
}

// Canadian street types (common abbreviations)
export const CANADIAN_STREET_TYPES = {
  // English
  'AVENUE': 'AVE',
  'BOULEVARD': 'BLVD',
  'CIRCLE': 'CIR',
  'COURT': 'CT',
  'CRESCENT': 'CRES',
  'DRIVE': 'DR',
  'LANE': 'LANE',
  'PLACE': 'PL',
  'ROAD': 'RD',
  'STREET': 'ST',
  'TERRACE': 'TERR',
  'TRAIL': 'TRAIL',
  'WAY': 'WAY',
  
  // French (Quebec)
  'RUE': 'RUE',
  'AVENUE': 'AV',
  'BOULEVARD': 'BOUL',
  'CHEMIN': 'CH',
  'PLACE': 'PL',
  'ROUTE': 'RTE',
  'AUTOROUTE': 'AUT',
  'RANG': 'RG',
  'CÔTE': 'CÔTE',
  'MONTÉE': 'MTÉE',
} as const;

// Canadian unit types
export const CANADIAN_UNIT_TYPES = {
  'APARTMENT': 'APT',
  'SUITE': 'SUITE',
  'UNIT': 'UNIT',
  'FLOOR': 'FL',
  'ROOM': 'RM',
  'BASEMENT': 'BSMT',
  'UPPER': 'UPPR',
  'LOWER': 'LOWR',
  'REAR': 'REAR',
  'FRONT': 'FRNT',
} as const;

// Directional abbreviations
export const CANADIAN_DIRECTIONS = {
  'NORTH': 'N',
  'SOUTH': 'S',
  'EAST': 'E',
  'WEST': 'W',
  'NORTHEAST': 'NE',
  'NORTHWEST': 'NW',
  'SOUTHEAST': 'SE',
  'SOUTHWEST': 'SW',
} as const;

/**
 * Validate Canadian postal code format
 */
export const validateCanadianPostalCode = (postalCode: string): boolean => {
  if (!postalCode) return false;
  
  // Remove spaces and convert to uppercase
  const cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  
  // Canadian postal code pattern: A1A1A1
  const pattern = /^[A-Z]\d[A-Z]\d[A-Z]\d$/;
  
  return pattern.test(cleaned);
};

/**
 * Format Canadian postal code
 */
export const formatCanadianPostalCode = (postalCode: string): string => {
  if (!postalCode) return '';
  
  // Remove all non-alphanumeric characters and convert to uppercase
  const cleaned = postalCode.replace(/[^A-Z0-9]/gi, '').toUpperCase();
  
  if (cleaned.length === 6) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  }
  
  return cleaned;
};

/**
 * Get province from postal code
 */
export const getProvinceFromPostalCode = (postalCode: string): CanadianProvince | null => {
  if (!validateCanadianPostalCode(postalCode)) return null;
  
  const firstChar = postalCode.charAt(0).toUpperCase();
  
  const postalCodeMap: Record<string, CanadianProvince> = {
    'A': 'NL', // Newfoundland and Labrador
    'B': 'NS', // Nova Scotia
    'C': 'PE', // Prince Edward Island
    'E': 'NB', // New Brunswick
    'G': 'QC', // Quebec (Eastern Quebec)
    'H': 'QC', // Quebec (Montreal)
    'J': 'QC', // Quebec (Western Quebec)
    'K': 'ON', // Ontario (Eastern Ontario)
    'L': 'ON', // Ontario (Central Ontario)
    'M': 'ON', // Ontario (Toronto)
    'N': 'ON', // Ontario (Southwestern Ontario)
    'P': 'ON', // Ontario (Northern Ontario)
    'R': 'MB', // Manitoba
    'S': 'SK', // Saskatchewan
    'T': 'AB', // Alberta
    'V': 'BC', // British Columbia
    'X': 'NT', // Northwest Territories and Nunavut
    'Y': 'YT', // Yukon
  };
  
  return postalCodeMap[firstChar] || null;
};

/**
 * Parse postal code information
 */
export const parsePostalCodeInfo = (postalCode: string): PostalCodeInfo | null => {
  if (!validateCanadianPostalCode(postalCode)) return null;
  
  const formatted = formatCanadianPostalCode(postalCode);
  const province = getProvinceFromPostalCode(formatted);
  
  if (!province) return null;
  
  const forwardSortationArea = formatted.slice(0, 3);
  const localDeliveryUnit = formatted.slice(4, 7);
  
  // Determine delivery mode based on second character
  const secondChar = formatted.charAt(1);
  let deliveryMode: 'urban' | 'rural' | 'suburban' = 'urban';
  
  if (secondChar === '0') {
    deliveryMode = 'rural';
  } else if (['1', '2', '3', '4', '5'].includes(secondChar)) {
    deliveryMode = 'suburban';
  } else {
    deliveryMode = 'urban';
  }
  
  return {
    postalCode: formatted,
    province,
    forwardSortationArea,
    localDeliveryUnit,
    deliveryMode,
  };
};

/**
 * Standardize Canadian address
 */
export const standardizeCanadianAddress = (address: Partial<CanadianAddress>): CanadianAddress => {
  const standardized: CanadianAddress = {
    streetName: address.streetName || '',
    city: address.city || '',
    province: address.province || 'ON',
    postalCode: formatCanadianPostalCode(address.postalCode || ''),
    country: 'Canada',
  };
  
  // Standardize street number
  if (address.streetNumber) {
    standardized.streetNumber = address.streetNumber.trim();
  }
  
  // Standardize street type
  if (address.streetType) {
    const upperType = address.streetType.toUpperCase();
    standardized.streetType = CANADIAN_STREET_TYPES[upperType as keyof typeof CANADIAN_STREET_TYPES] || address.streetType;
  }
  
  // Standardize direction
  if (address.streetDirection) {
    const upperDirection = address.streetDirection.toUpperCase();
    standardized.streetDirection = CANADIAN_DIRECTIONS[upperDirection as keyof typeof CANADIAN_DIRECTIONS] || address.streetDirection;
  }
  
  // Standardize unit information
  if (address.unitNumber) {
    standardized.unitNumber = address.unitNumber.trim();
  }
  
  if (address.unitType) {
    const upperUnitType = address.unitType.toUpperCase();
    standardized.unitType = CANADIAN_UNIT_TYPES[upperUnitType as keyof typeof CANADIAN_UNIT_TYPES] || address.unitType;
  }
  
  // Standardize city name
  standardized.city = standardizeCanadianCityName(address.city || '', address.province || 'ON');
  
  return standardized;
};

/**
 * Standardize Canadian city names
 */
export const standardizeCanadianCityName = (city: string, province: CanadianProvince): string => {
  if (!city) return '';
  
  // Common city name standardizations by province
  const cityStandardizations: Partial<Record<CanadianProvince, Record<string, string>>> = {
    QC: {
      'MONTREAL': 'Montréal',
      'QUEBEC': 'Québec',
      'QUEBEC CITY': 'Québec',
      'TROIS-RIVIERES': 'Trois-Rivières',
      'CHICOUTIMI': 'Saguenay',
    },
    ON: {
      'TORONTO': 'Toronto',
      'OTTAWA': 'Ottawa',
      'MISSISSAUGA': 'Mississauga',
      'HAMILTON': 'Hamilton',
      'LONDON': 'London',
      'KITCHENER': 'Kitchener',
      'WINDSOR': 'Windsor',
    },
    BC: {
      'VANCOUVER': 'Vancouver',
      'VICTORIA': 'Victoria',
      'BURNABY': 'Burnaby',
      'RICHMOND': 'Richmond',
      'SURREY': 'Surrey',
    },
    AB: {
      'CALGARY': 'Calgary',
      'EDMONTON': 'Edmonton',
      'RED DEER': 'Red Deer',
      'LETHBRIDGE': 'Lethbridge',
    },
  };
  
  const upperCity = city.toUpperCase();
  const provinceStandardizations = cityStandardizations[province];
  
  if (provinceStandardizations && provinceStandardizations[upperCity]) {
    return provinceStandardizations[upperCity];
  }
  
  // Default: Title case
  return city.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Format Canadian address for display
 */
export const formatCanadianAddress = (
  address: CanadianAddress,
  format: 'single-line' | 'multi-line' | 'mailing' = 'multi-line'
): string => {
  const parts: string[] = [];
  
  // Street address line
  let streetLine = '';
  
  if (address.unitNumber && address.unitType) {
    streetLine += `${address.unitType} ${address.unitNumber}, `;
  } else if (address.unitNumber) {
    streetLine += `Unit ${address.unitNumber}, `;
  }
  
  if (address.streetNumber) {
    streetLine += `${address.streetNumber} `;
  }
  
  streetLine += address.streetName;
  
  if (address.streetType) {
    streetLine += ` ${address.streetType}`;
  }
  
  if (address.streetDirection) {
    streetLine += ` ${address.streetDirection}`;
  }
  
  parts.push(streetLine.trim());
  
  // City, Province, Postal Code line
  const cityLine = `${address.city}, ${address.province} ${address.postalCode}`;
  parts.push(cityLine);
  
  // Country (for mailing format)
  if (format === 'mailing') {
    parts.push('CANADA');
  }
  
  // Join based on format
  if (format === 'single-line') {
    return parts.join(', ');
  } else {
    return parts.join('\n');
  }
};

/**
 * Calculate distance between two coordinates (Haversine formula)
 */
export const calculateDistance = (
  coord1: Coordinates,
  coord2: Coordinates,
  unit: 'km' | 'miles' = 'km'
): number => {
  const R = unit === 'km' ? 6371 : 3959; // Earth's radius
  
  const dLat = toRadians(coord2.latitude - coord1.latitude);
  const dLon = toRadians(coord2.longitude - coord1.longitude);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  return R * c;
};

/**
 * Convert degrees to radians
 */
const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Get timezone for Canadian province
 */
export const getCanadianTimezone = (province: CanadianProvince): string => {
  const timezones: Record<CanadianProvince, string> = {
    BC: 'America/Vancouver',
    AB: 'America/Edmonton',
    SK: 'America/Regina',
    MB: 'America/Winnipeg',
    ON: 'America/Toronto',
    QC: 'America/Montreal',
    NB: 'America/Moncton',
    PE: 'America/Halifax',
    NS: 'America/Halifax',
    NL: 'America/St_Johns',
    YT: 'America/Whitehorse',
    NT: 'America/Yellowknife',
    NU: 'America/Iqaluit',
  };
  
  return timezones[province];
};

/**
 * Validate Canadian address completeness
 */
export const validateCanadianAddressCompleteness = (address: Partial<CanadianAddress>): {
  isComplete: boolean;
  missingFields: string[];
  warnings: string[];
} => {
  const missingFields: string[] = [];
  const warnings: string[] = [];
  
  // Required fields
  if (!address.streetName?.trim()) {
    missingFields.push('Street name');
  }
  
  if (!address.city?.trim()) {
    missingFields.push('City');
  }
  
  if (!address.province) {
    missingFields.push('Province');
  }
  
  if (!address.postalCode?.trim()) {
    missingFields.push('Postal code');
  } else if (!validateCanadianPostalCode(address.postalCode)) {
    missingFields.push('Valid postal code');
  }
  
  // Warnings for optional but recommended fields
  if (!address.streetNumber?.trim()) {
    warnings.push('Street number is recommended for accurate delivery');
  }
  
  // Validate postal code matches province
  if (address.postalCode && address.province) {
    const detectedProvince = getProvinceFromPostalCode(address.postalCode);
    if (detectedProvince && detectedProvince !== address.province) {
      warnings.push(`Postal code suggests ${CANADIAN_PROVINCES[detectedProvince].name}, but ${CANADIAN_PROVINCES[address.province].name} is selected`);
    }
  }
  
  return {
    isComplete: missingFields.length === 0,
    missingFields,
    warnings,
  };
};

/**
 * Search for Canadian addresses (mock implementation)
 */
export const searchCanadianAddresses = async (
  query: string,
  province?: CanadianProvince
): Promise<CanadianAddress[]> => {
  // This would integrate with a real Canadian address API like Canada Post
  // For now, return mock results
  
  const mockResults: CanadianAddress[] = [
    {
      streetNumber: '123',
      streetName: 'Main',
      streetType: 'ST',
      city: 'Toronto',
      province: 'ON',
      postalCode: 'M5V 3A8',
      country: 'Canada',
    },
    {
      streetNumber: '456',
      streetName: 'Queen',
      streetType: 'ST',
      streetDirection: 'W',
      city: 'Toronto',
      province: 'ON',
      postalCode: 'M5V 2A1',
      country: 'Canada',
    },
  ];
  
  // Filter by province if specified
  if (province) {
    return mockResults.filter(addr => addr.province === province);
  }
  
  return mockResults;
};

export default {
  CANADIAN_STREET_TYPES,
  CANADIAN_UNIT_TYPES,
  CANADIAN_DIRECTIONS,
  validateCanadianPostalCode,
  formatCanadianPostalCode,
  getProvinceFromPostalCode,
  parsePostalCodeInfo,
  standardizeCanadianAddress,
  standardizeCanadianCityName,
  formatCanadianAddress,
  calculateDistance,
  getCanadianTimezone,
  validateCanadianAddressCompleteness,
  searchCanadianAddresses,
};
