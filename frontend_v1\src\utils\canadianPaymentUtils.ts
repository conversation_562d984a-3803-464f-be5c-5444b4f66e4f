/**
 * Canadian Payment Utilities
 *
 * Comprehensive utilities for Canadian payment processing, currency formatting,
 * and integration with Canadian financial institutions and payment methods.
 *
 * Features:
 * - Canadian currency formatting
 * - Tax calculations (GST/HST/PST)
 * - Payment method validation
 * - Canadian banking integration
 * - Provincial tax compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Canadian provinces and territories
export const CANADIAN_PROVINCES = {
  AB: { name: 'Alberta', gst: 0.05, pst: 0, hst: 0 },
  BC: { name: 'British Columbia', gst: 0.05, pst: 0.07, hst: 0 },
  MB: { name: 'Manitoba', gst: 0.05, pst: 0.07, hst: 0 },
  NB: { name: 'New Brunswick', gst: 0, pst: 0, hst: 0.15 },
  NL: { name: 'Newfoundland and Labrador', gst: 0, pst: 0, hst: 0.15 },
  NS: { name: 'Nova Scotia', gst: 0, pst: 0, hst: 0.15 },
  NT: { name: 'Northwest Territories', gst: 0.05, pst: 0, hst: 0 },
  NU: { name: 'Nunavut', gst: 0.05, pst: 0, hst: 0 },
  ON: { name: 'Ontario', gst: 0, pst: 0, hst: 0.13 },
  PE: { name: 'Prince Edward Island', gst: 0, pst: 0, hst: 0.15 },
  QC: { name: 'Quebec', gst: 0.05, pst: 0.09975, hst: 0 },
  SK: { name: 'Saskatchewan', gst: 0.05, pst: 0.06, hst: 0 },
  YT: { name: 'Yukon', gst: 0.05, pst: 0, hst: 0 },
} as const;

export type CanadianProvince = keyof typeof CANADIAN_PROVINCES;

// Canadian payment methods
export const CANADIAN_PAYMENT_METHODS = {
  CREDIT_CARD: {
    id: 'credit_card',
    name: 'Credit Card',
    nameFr: 'Carte de crédit',
    supported: ['visa', 'mastercard', 'amex'],
    processingFee: 0.029, // 2.9%
  },
  DEBIT_CARD: {
    id: 'debit_card',
    name: 'Debit Card',
    nameFr: 'Carte de débit',
    supported: ['interac'],
    processingFee: 0.015, // 1.5%
  },
  INTERAC_ETRANSFER: {
    id: 'interac_etransfer',
    name: 'Interac e-Transfer',
    nameFr: 'Virement Interac',
    processingFee: 1.50, // Fixed fee
  },
  PAYPAL: {
    id: 'paypal',
    name: 'PayPal',
    nameFr: 'PayPal',
    processingFee: 0.029, // 2.9%
  },
  APPLE_PAY: {
    id: 'apple_pay',
    name: 'Apple Pay',
    nameFr: 'Apple Pay',
    processingFee: 0.029, // 2.9%
  },
  GOOGLE_PAY: {
    id: 'google_pay',
    name: 'Google Pay',
    nameFr: 'Google Pay',
    processingFee: 0.029, // 2.9%
  },
} as const;

// Currency formatting options
export interface CurrencyFormatOptions {
  locale?: 'en-CA' | 'fr-CA';
  showCents?: boolean;
  showSymbol?: boolean;
  compact?: boolean;
}

// Tax calculation result
export interface TaxCalculation {
  subtotal: number;
  gst: number;
  pst: number;
  hst: number;
  totalTax: number;
  total: number;
  breakdown: {
    gstRate: number;
    pstRate: number;
    hstRate: number;
  };
}

// Payment calculation result
export interface PaymentCalculation extends TaxCalculation {
  processingFee: number;
  finalTotal: number;
}

/**
 * Format Canadian currency
 */
export const formatCanadianCurrency = (
  amount: number,
  options: CurrencyFormatOptions = {}
): string => {
  const {
    locale = 'en-CA',
    showCents = true,
    showSymbol = true,
    compact = false,
  } = options;

  if (compact && amount >= 1000) {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'CAD',
      notation: 'compact',
      maximumFractionDigits: 1,
    });
    return formatter.format(amount);
  }

  const formatter = new Intl.NumberFormat(locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency: 'CAD',
    minimumFractionDigits: showCents ? 2 : 0,
    maximumFractionDigits: showCents ? 2 : 0,
  });

  let formatted = formatter.format(amount);

  // Quebec French specific formatting adjustments
  if (locale === 'fr-CA') {
    // Ensure proper spacing for Quebec French: "1 234,56 $"
    formatted = formatted.replace(/\s/g, ' '); // Ensure non-breaking spaces
  }

  return formatted;
};

/**
 * Parse Canadian currency string to number
 */
export const parseCanadianCurrency = (currencyString: string): number => {
  // Remove currency symbols and spaces
  const cleaned = currencyString
    .replace(/[$CAD\s]/g, '')
    .replace(/,/g, '.')
    .replace(/[^\d.-]/g, '');
  
  return parseFloat(cleaned) || 0;
};

/**
 * Calculate Canadian taxes
 */
export const calculateCanadianTaxes = (
  subtotal: number,
  province: CanadianProvince
): TaxCalculation => {
  const provinceInfo = CANADIAN_PROVINCES[province];
  
  let gst = 0;
  let pst = 0;
  let hst = 0;

  if (provinceInfo.hst > 0) {
    // HST provinces (combines GST and PST)
    hst = subtotal * provinceInfo.hst;
  } else {
    // GST + PST provinces
    gst = subtotal * provinceInfo.gst;
    
    // PST is calculated on subtotal + GST in some provinces
    if (province === 'QC') {
      // Quebec PST is calculated on subtotal + GST
      pst = (subtotal + gst) * provinceInfo.pst;
    } else {
      // Other provinces calculate PST on subtotal only
      pst = subtotal * provinceInfo.pst;
    }
  }

  const totalTax = gst + pst + hst;
  const total = subtotal + totalTax;

  return {
    subtotal,
    gst: Math.round(gst * 100) / 100,
    pst: Math.round(pst * 100) / 100,
    hst: Math.round(hst * 100) / 100,
    totalTax: Math.round(totalTax * 100) / 100,
    total: Math.round(total * 100) / 100,
    breakdown: {
      gstRate: provinceInfo.gst,
      pstRate: provinceInfo.pst,
      hstRate: provinceInfo.hst,
    },
  };
};

/**
 * Calculate payment processing fees
 */
export const calculateProcessingFee = (
  amount: number,
  paymentMethod: keyof typeof CANADIAN_PAYMENT_METHODS
): number => {
  const method = CANADIAN_PAYMENT_METHODS[paymentMethod];
  
  if (typeof method.processingFee === 'number') {
    if (method.processingFee < 1) {
      // Percentage fee
      return Math.round(amount * method.processingFee * 100) / 100;
    } else {
      // Fixed fee
      return method.processingFee;
    }
  }
  
  return 0;
};

/**
 * Calculate total payment including taxes and fees
 */
export const calculateTotalPayment = (
  subtotal: number,
  province: CanadianProvince,
  paymentMethod: keyof typeof CANADIAN_PAYMENT_METHODS
): PaymentCalculation => {
  const taxCalculation = calculateCanadianTaxes(subtotal, province);
  const processingFee = calculateProcessingFee(taxCalculation.total, paymentMethod);
  const finalTotal = taxCalculation.total + processingFee;

  return {
    ...taxCalculation,
    processingFee: Math.round(processingFee * 100) / 100,
    finalTotal: Math.round(finalTotal * 100) / 100,
  };
};

/**
 * Validate Canadian postal code
 */
export const validateCanadianPostalCode = (postalCode: string): boolean => {
  const canadianPostalRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/;
  return canadianPostalRegex.test(postalCode.trim());
};

/**
 * Format Canadian postal code
 */
export const formatCanadianPostalCode = (postalCode: string): string => {
  const cleaned = postalCode.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
  
  if (cleaned.length === 6) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  }
  
  return cleaned;
};

/**
 * Get province from postal code
 */
export const getProvinceFromPostalCode = (postalCode: string): CanadianProvince | null => {
  const firstChar = postalCode.charAt(0).toUpperCase();
  
  const postalCodeMap: Record<string, CanadianProvince> = {
    A: 'NL', // Newfoundland and Labrador
    B: 'NS', // Nova Scotia
    C: 'PE', // Prince Edward Island
    E: 'NB', // New Brunswick
    G: 'QC', // Quebec (Eastern)
    H: 'QC', // Quebec (Montreal)
    J: 'QC', // Quebec (Western)
    K: 'ON', // Ontario (Eastern)
    L: 'ON', // Ontario (Central)
    M: 'ON', // Ontario (Toronto)
    N: 'ON', // Ontario (Southwestern)
    P: 'ON', // Ontario (Northern)
    R: 'MB', // Manitoba
    S: 'SK', // Saskatchewan
    T: 'AB', // Alberta
    V: 'BC', // British Columbia
    X: 'NT', // Northwest Territories/Nunavut
    Y: 'YT', // Yukon
  };
  
  return postalCodeMap[firstChar] || null;
};

/**
 * Validate Canadian credit card number (basic Luhn algorithm)
 */
export const validateCreditCard = (cardNumber: string): boolean => {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned.charAt(i), 10);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
};

/**
 * Get credit card type
 */
export const getCreditCardType = (cardNumber: string): string | null => {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (/^4/.test(cleaned)) return 'visa';
  if (/^5[1-5]/.test(cleaned)) return 'mastercard';
  if (/^3[47]/.test(cleaned)) return 'amex';
  if (/^6(?:011|5)/.test(cleaned)) return 'discover';
  
  return null;
};

/**
 * Format credit card number for display
 */
export const formatCreditCardNumber = (cardNumber: string): string => {
  const cleaned = cardNumber.replace(/\D/g, '');
  const type = getCreditCardType(cleaned);
  
  if (type === 'amex') {
    // American Express: XXXX XXXXXX XXXXX
    return cleaned.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
  } else {
    // Visa, MasterCard, etc.: XXXX XXXX XXXX XXXX
    return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
  }
};

/**
 * Generate payment summary for display
 */
export const generatePaymentSummary = (
  subtotal: number,
  province: CanadianProvince,
  paymentMethod: keyof typeof CANADIAN_PAYMENT_METHODS,
  locale: 'en-CA' | 'fr-CA' = 'en-CA'
): {
  items: Array<{ label: string; amount: string; type: 'subtotal' | 'tax' | 'fee' | 'total' }>;
  total: string;
} => {
  const calculation = calculateTotalPayment(subtotal, province, paymentMethod);
  const provinceInfo = CANADIAN_PROVINCES[province];
  const paymentInfo = CANADIAN_PAYMENT_METHODS[paymentMethod];
  
  const items: Array<{ label: string; amount: string; type: 'subtotal' | 'tax' | 'fee' | 'total' }> = [];
  
  // Subtotal
  items.push({
    label: locale === 'fr-CA' ? 'Sous-total' : 'Subtotal',
    amount: formatCanadianCurrency(calculation.subtotal, { locale }),
    type: 'subtotal',
  });
  
  // Taxes
  if (calculation.gst > 0) {
    items.push({
      label: `GST/TPS (${(provinceInfo.gst * 100).toFixed(1)}%)`,
      amount: formatCanadianCurrency(calculation.gst, { locale }),
      type: 'tax',
    });
  }
  
  if (calculation.pst > 0) {
    const pstLabel = province === 'QC' 
      ? (locale === 'fr-CA' ? 'TVQ' : 'QST')
      : 'PST';
    items.push({
      label: `${pstLabel} (${(provinceInfo.pst * 100).toFixed(2)}%)`,
      amount: formatCanadianCurrency(calculation.pst, { locale }),
      type: 'tax',
    });
  }
  
  if (calculation.hst > 0) {
    items.push({
      label: `HST (${(provinceInfo.hst * 100).toFixed(1)}%)`,
      amount: formatCanadianCurrency(calculation.hst, { locale }),
      type: 'tax',
    });
  }
  
  // Processing fee
  if (calculation.processingFee > 0) {
    const feeLabel = locale === 'fr-CA' ? 'Frais de traitement' : 'Processing Fee';
    items.push({
      label: `${feeLabel} (${locale === 'fr-CA' ? paymentInfo.nameFr : paymentInfo.name})`,
      amount: formatCanadianCurrency(calculation.processingFee, { locale }),
      type: 'fee',
    });
  }
  
  // Total
  items.push({
    label: locale === 'fr-CA' ? 'Total' : 'Total',
    amount: formatCanadianCurrency(calculation.finalTotal, { locale }),
    type: 'total',
  });
  
  return {
    items,
    total: formatCanadianCurrency(calculation.finalTotal, { locale }),
  };
};

export default {
  CANADIAN_PROVINCES,
  CANADIAN_PAYMENT_METHODS,
  formatCanadianCurrency,
  parseCanadianCurrency,
  calculateCanadianTaxes,
  calculateProcessingFee,
  calculateTotalPayment,
  validateCanadianPostalCode,
  formatCanadianPostalCode,
  getProvinceFromPostalCode,
  validateCreditCard,
  getCreditCardType,
  formatCreditCardNumber,
  generatePaymentSummary,
};
