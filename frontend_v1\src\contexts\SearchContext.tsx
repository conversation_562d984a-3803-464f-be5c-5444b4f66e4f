/**
 * Search Context Provider
 *
 * Comprehensive search state management with intelligent filtering,
 * suggestions, and discovery features for enhanced user experience.
 *
 * Features:
 * - Global search state
 * - Search history
 * - Smart suggestions
 * - Filter management
 * - Search analytics
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Platform, AccessibilityInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  SearchResult,
  SearchSuggestion,
  FilterConfig,
  fuzzySearch,
  applyFilters,
  generateSearchSuggestions,
  createSearchAnalytics,
} from '../utils/searchUtils';

// Search state interface
export interface SearchState {
  query: string;
  results: SearchResult[];
  suggestions: SearchSuggestion[];
  filters: FilterConfig[];
  isSearching: boolean;
  hasSearched: boolean;
  resultCount: number;
  searchTime: number;
}

// Search context interface
interface SearchContextType {
  // State
  searchState: SearchState;
  
  // Search operations
  setQuery: (query: string) => void;
  performSearch: (query?: string) => Promise<void>;
  clearSearch: () => void;
  
  // Suggestions
  updateSuggestions: (query: string) => void;
  selectSuggestion: (suggestion: SearchSuggestion) => void;
  
  // Filters
  addFilter: (filter: FilterConfig) => void;
  removeFilter: (field: string) => void;
  clearFilters: () => void;
  updateFilter: (field: string, updates: Partial<FilterConfig>) => void;
  
  // History
  getSearchHistory: () => string[];
  clearSearchHistory: () => void;
  
  // Analytics
  getPopularQueries: () => string[];
  getSearchStats: () => any;
  
  // Data source
  setDataSource: (items: any[]) => void;
  updateDataSource: (items: any[]) => void;
  
  // Configuration
  setSearchConfig: (config: any) => void;
}

// Create context
const SearchContext = createContext<SearchContextType | undefined>(undefined);

// Storage keys
const SEARCH_HISTORY_KEY = '@vierla_search_history';
const SEARCH_ANALYTICS_KEY = '@vierla_search_analytics';

// Provider props
interface SearchProviderProps {
  children: React.ReactNode;
  initialData?: any[];
  searchFields?: string[];
  maxSuggestions?: number;
}

export const SearchProvider: React.FC<SearchProviderProps> = ({
  children,
  initialData = [],
  searchFields = ['name', 'title', 'description', 'category'],
  maxSuggestions = 8,
}) => {
  // State
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    results: [],
    suggestions: [],
    filters: [],
    isSearching: false,
    hasSearched: false,
    resultCount: 0,
    searchTime: 0,
  });

  const [dataSource, setDataSourceState] = useState(initialData);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [popularQueries, setPopularQueries] = useState<string[]>([]);

  // Refs
  const searchConfigRef = useRef({
    fields: searchFields,
    threshold: 0.3,
    maxResults: 100,
    includeMatches: true,
    sortByScore: true,
  });
  const analyticsRef = useRef(createSearchAnalytics());

  // Load search history and analytics
  useEffect(() => {
    const loadSearchData = async () => {
      try {
        const [savedHistory, savedAnalytics] = await Promise.all([
          AsyncStorage.getItem(SEARCH_HISTORY_KEY),
          AsyncStorage.getItem(SEARCH_ANALYTICS_KEY),
        ]);

        if (savedHistory) {
          const history = JSON.parse(savedHistory);
          setSearchHistory(history);
        }

        if (savedAnalytics) {
          const analytics = JSON.parse(savedAnalytics);
          setPopularQueries(analytics.popularQueries || []);
        }
      } catch (error) {
        console.warn('Failed to load search data:', error);
      }
    };

    loadSearchData();
  }, []);

  // Save search history
  const saveSearchHistory = useCallback(async (history: string[]) => {
    try {
      await AsyncStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
      console.warn('Failed to save search history:', error);
    }
  }, []);

  // Set query
  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({
      ...prev,
      query,
    }));
  }, []);

  // Perform search
  const performSearch = useCallback(async (query?: string) => {
    const searchQuery = query || searchState.query;
    if (!searchQuery.trim()) return;

    setSearchState(prev => ({
      ...prev,
      isSearching: true,
      hasSearched: true,
    }));

    const startTime = Date.now();

    try {
      // Apply filters to data source
      const filteredData = applyFilters(dataSource, searchState.filters);
      
      // Perform fuzzy search
      const searchResults = fuzzySearch(filteredData, searchQuery, searchConfigRef.current);
      
      const searchTime = Date.now() - startTime;

      setSearchState(prev => ({
        ...prev,
        results: searchResults,
        resultCount: searchResults.length,
        searchTime,
        isSearching: false,
      }));

      // Update search history
      const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 20);
      setSearchHistory(newHistory);
      saveSearchHistory(newHistory);

      // Record analytics
      analyticsRef.current.recordSearch(searchQuery, searchResults.length);

      // Announce results to screen readers
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        AccessibilityInfo.announceForAccessibility(
          `Search completed. Found ${searchResults.length} results for ${searchQuery}`
        );
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchState(prev => ({
        ...prev,
        isSearching: false,
      }));
    }
  }, [searchState.query, searchState.filters, dataSource, searchHistory, saveSearchHistory]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchState({
      query: '',
      results: [],
      suggestions: [],
      filters: [],
      isSearching: false,
      hasSearched: false,
      resultCount: 0,
      searchTime: 0,
    });
  }, []);

  // Update suggestions
  const updateSuggestions = useCallback((query: string) => {
    if (!query.trim()) {
      setSearchState(prev => ({ ...prev, suggestions: [] }));
      return;
    }

    const suggestions = generateSearchSuggestions(
      query,
      dataSource,
      searchHistory,
      popularQueries
    ).slice(0, maxSuggestions);

    setSearchState(prev => ({
      ...prev,
      suggestions,
    }));
  }, [dataSource, searchHistory, popularQueries, maxSuggestions]);

  // Select suggestion
  const selectSuggestion = useCallback((suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    performSearch(suggestion.text);
  }, [setQuery, performSearch]);

  // Add filter
  const addFilter = useCallback((filter: FilterConfig) => {
    setSearchState(prev => ({
      ...prev,
      filters: [...prev.filters.filter(f => f.field !== filter.field), filter],
    }));
  }, []);

  // Remove filter
  const removeFilter = useCallback((field: string) => {
    setSearchState(prev => ({
      ...prev,
      filters: prev.filters.filter(f => f.field !== field),
    }));
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      filters: [],
    }));
  }, []);

  // Update filter
  const updateFilter = useCallback((field: string, updates: Partial<FilterConfig>) => {
    setSearchState(prev => ({
      ...prev,
      filters: prev.filters.map(f => 
        f.field === field ? { ...f, ...updates } : f
      ),
    }));
  }, []);

  // Get search history
  const getSearchHistory = useCallback(() => {
    return [...searchHistory];
  }, [searchHistory]);

  // Clear search history
  const clearSearchHistory = useCallback(async () => {
    setSearchHistory([]);
    try {
      await AsyncStorage.removeItem(SEARCH_HISTORY_KEY);
    } catch (error) {
      console.warn('Failed to clear search history:', error);
    }
  }, []);

  // Get popular queries
  const getPopularQueries = useCallback(() => {
    return analyticsRef.current.getPopularQueries();
  }, []);

  // Get search stats
  const getSearchStats = useCallback(() => {
    return analyticsRef.current.getSearchStats();
  }, []);

  // Set data source
  const setDataSource = useCallback((items: any[]) => {
    setDataSourceState(items);
  }, []);

  // Update data source
  const updateDataSource = useCallback((items: any[]) => {
    setDataSourceState(prev => [...prev, ...items]);
  }, []);

  // Set search config
  const setSearchConfig = useCallback((config: any) => {
    searchConfigRef.current = { ...searchConfigRef.current, ...config };
  }, []);

  // Auto-update suggestions when query changes
  useEffect(() => {
    if (searchState.query) {
      updateSuggestions(searchState.query);
    }
  }, [searchState.query, updateSuggestions]);

  // Context value
  const contextValue: SearchContextType = {
    // State
    searchState,
    
    // Search operations
    setQuery,
    performSearch,
    clearSearch,
    
    // Suggestions
    updateSuggestions,
    selectSuggestion,
    
    // Filters
    addFilter,
    removeFilter,
    clearFilters,
    updateFilter,
    
    // History
    getSearchHistory,
    clearSearchHistory,
    
    // Analytics
    getPopularQueries,
    getSearchStats,
    
    // Data source
    setDataSource,
    updateDataSource,
    
    // Configuration
    setSearchConfig,
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};

// Hook to use search context
export const useSearch = (): SearchContextType => {
  const context = useContext(SearchContext);
  
  if (context === undefined) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  
  return context;
};

// Convenience hooks
export const useSearchQuery = () => {
  const { searchState, setQuery, performSearch } = useSearch();
  return {
    query: searchState.query,
    setQuery,
    search: performSearch,
  };
};

export const useSearchResults = () => {
  const { searchState } = useSearch();
  return {
    results: searchState.results,
    isSearching: searchState.isSearching,
    hasSearched: searchState.hasSearched,
    resultCount: searchState.resultCount,
    searchTime: searchState.searchTime,
  };
};

export const useSearchSuggestions = () => {
  const { searchState, selectSuggestion, updateSuggestions } = useSearch();
  return {
    suggestions: searchState.suggestions,
    selectSuggestion,
    updateSuggestions,
  };
};

export const useSearchFilters = () => {
  const { searchState, addFilter, removeFilter, clearFilters, updateFilter } = useSearch();
  return {
    filters: searchState.filters,
    addFilter,
    removeFilter,
    clearFilters,
    updateFilter,
  };
};

export default SearchProvider;
