/**
 * Enhanced Form Validation Hook
 * Comprehensive form validation with real-time feedback
 */

import { useState, useCallback, useEffect } from 'react';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface FieldConfig {
  rules: ValidationRule[];
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

export interface FormConfig {
  [fieldName: string]: FieldConfig;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState {
  values: { [key: string]: any };
  errors: { [key: string]: string[] };
  touched: { [key: string]: boolean };
  isValid: boolean;
  isSubmitting: boolean;
}

export interface FormActions {
  setValue: (field: string, value: any) => void;
  setError: (field: string, error: string) => void;
  clearError: (field: string) => void;
  clearAllErrors: () => void;
  validateField: (field: string) => boolean;
  validateForm: () => boolean;
  setTouched: (field: string, touched?: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  reset: (initialValues?: { [key: string]: any }) => void;
  getFieldProps: (field: string) => {
    value: any;
    onChangeText: (value: any) => void;
    onBlur: () => void;
    error: string[];
    hasError: boolean;
  };
}

export const useFormValidation = (
  config: FormConfig,
  initialValues: { [key: string]: any } = {}
): [FormState, FormActions] => {
  const [state, setState] = useState<FormState>({
    values: initialValues,
    errors: {},
    touched: {},
    isValid: false,
    isSubmitting: false,
  });

  // Validation functions
  const validateValue = useCallback((field: string, value: any): string[] => {
    const fieldConfig = config[field];
    if (!fieldConfig) return [];

    const errors: string[] = [];

    for (const rule of fieldConfig.rules) {
      // Required validation
      if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(rule.message || `${field} is required`);
        continue;
      }

      // Skip other validations if value is empty and not required
      if (!value || value.toString().trim() === '') continue;

      // Min length validation
      if (rule.minLength && value.toString().length < rule.minLength) {
        errors.push(rule.message || `${field} must be at least ${rule.minLength} characters`);
      }

      // Max length validation
      if (rule.maxLength && value.toString().length > rule.maxLength) {
        errors.push(rule.message || `${field} must be no more than ${rule.maxLength} characters`);
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value.toString())) {
        errors.push(rule.message || `${field} format is invalid`);
      }

      // Email validation
      if (rule.email) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(value.toString())) {
          errors.push(rule.message || 'Please enter a valid email address');
        }
      }

      // Phone validation
      if (rule.phone) {
        const phonePattern = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phonePattern.test(value.toString().replace(/[\s\-\(\)]/g, ''))) {
          errors.push(rule.message || 'Please enter a valid phone number');
        }
      }

      // Custom validation
      if (rule.custom) {
        const customError = rule.custom(value);
        if (customError) {
          errors.push(customError);
        }
      }
    }

    return errors;
  }, [config]);

  const validateField = useCallback((field: string): boolean => {
    const value = state.values[field];
    const errors = validateValue(field, value);
    
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: errors,
      },
    }));

    return errors.length === 0;
  }, [state.values, validateValue]);

  const validateForm = useCallback((): boolean => {
    const newErrors: { [key: string]: string[] } = {};
    let isValid = true;

    Object.keys(config).forEach(field => {
      const errors = validateValue(field, state.values[field]);
      if (errors.length > 0) {
        newErrors[field] = errors;
        isValid = false;
      }
    });

    setState(prev => ({
      ...prev,
      errors: newErrors,
      isValid,
    }));

    return isValid;
  }, [config, state.values, validateValue]);

  const setValue = useCallback((field: string, value: any) => {
    setState(prev => {
      const newState = {
        ...prev,
        values: {
          ...prev.values,
          [field]: value,
        },
      };

      // Validate on change if configured
      const fieldConfig = config[field];
      if (fieldConfig?.validateOnChange && prev.touched[field]) {
        const errors = validateValue(field, value);
        newState.errors = {
          ...prev.errors,
          [field]: errors,
        };
      }

      return newState;
    });
  }, [config, validateValue]);

  const setError = useCallback((field: string, error: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: [error],
      },
    }));
  }, []);

  const clearError = useCallback((field: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: [],
      },
    }));
  }, []);

  const clearAllErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: {},
    }));
  }, []);

  const setTouched = useCallback((field: string, touched: boolean = true) => {
    setState(prev => {
      const newState = {
        ...prev,
        touched: {
          ...prev.touched,
          [field]: touched,
        },
      };

      // Validate on blur if configured
      const fieldConfig = config[field];
      if (fieldConfig?.validateOnBlur && touched) {
        const errors = validateValue(field, prev.values[field]);
        newState.errors = {
          ...prev.errors,
          [field]: errors,
        };
      }

      return newState;
    });
  }, [config, validateValue]);

  const setSubmitting = useCallback((submitting: boolean) => {
    setState(prev => ({
      ...prev,
      isSubmitting: submitting,
    }));
  }, []);

  const reset = useCallback((newInitialValues?: { [key: string]: any }) => {
    setState({
      values: newInitialValues || initialValues,
      errors: {},
      touched: {},
      isValid: false,
      isSubmitting: false,
    });
  }, [initialValues]);

  const getFieldProps = useCallback((field: string) => {
    return {
      value: state.values[field] || '',
      onChangeText: (value: any) => setValue(field, value),
      onBlur: () => setTouched(field, true),
      error: state.errors[field] || [],
      hasError: (state.errors[field] || []).length > 0,
    };
  }, [state.values, state.errors, setValue, setTouched]);

  // Update form validity when errors change
  useEffect(() => {
    const hasErrors = Object.values(state.errors).some(errors => errors.length > 0);
    const hasAllRequiredFields = Object.keys(config).every(field => {
      const fieldConfig = config[field];
      const hasRequiredRule = fieldConfig.rules.some(rule => rule.required);
      if (!hasRequiredRule) return true;
      
      const value = state.values[field];
      return value && value.toString().trim() !== '';
    });

    setState(prev => ({
      ...prev,
      isValid: !hasErrors && hasAllRequiredFields,
    }));
  }, [state.errors, state.values, config]);

  const actions: FormActions = {
    setValue,
    setError,
    clearError,
    clearAllErrors,
    validateField,
    validateForm,
    setTouched,
    setSubmitting,
    reset,
    getFieldProps,
  };

  return [state, actions];
};
