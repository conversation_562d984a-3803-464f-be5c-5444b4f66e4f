/**
 * Design System Context Provider
 *
 * Provides comprehensive design system tokens including colors, typography,
 * spacing, and component standards throughout the application.
 *
 * Features:
 * - Centralized design tokens
 * - Theme switching support
 * - Accessibility helpers
 * - Responsive utilities
 * - Component styling standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useMemo, ReactNode } from 'react';
import { Colors, DarkModeColors, SemanticColors } from '../constants/Colors';
import DesignSystem, {
  Spacing,
  Typography,
  BorderRadius,
  Elevation,
  Animation,
  Breakpoints,
  ComponentSizes,
  ZIndex,
  Opacity,
} from '../constants/DesignSystem';

// Theme interface
export interface Theme {
  colors: typeof Colors;
  spacing: typeof Spacing;
  typography: typeof Typography;
  borderRadius: typeof BorderRadius;
  elevation: typeof Elevation;
  animation: typeof Animation;
  breakpoints: typeof Breakpoints;
  componentSizes: typeof ComponentSizes;
  zIndex: typeof ZIndex;
  opacity: typeof Opacity;
  semanticColors: typeof SemanticColors;
  isDark: boolean;
}

// Design system context interface
interface DesignSystemContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  
  // Utility functions
  getSpacing: (size: keyof typeof Spacing) => number;
  getTypographyStyle: (variant: keyof typeof Typography.styles) => any;
  getElevation: (level: keyof typeof Elevation) => any;
  getColor: (path: string, fallback?: string) => string;
  
  // Responsive helpers
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  
  // Accessibility helpers
  getAccessibleTouchTarget: (size?: 'minimum' | 'comfortable' | 'large') => number;
  getAccessibleTextColor: (backgroundColor: string) => string;
}

// Create context
const DesignSystemContext = createContext<DesignSystemContextType | undefined>(undefined);

// Provider props
interface DesignSystemProviderProps {
  children: ReactNode;
  initialTheme?: 'light' | 'dark';
}

// Provider component
export const DesignSystemProvider: React.FC<DesignSystemProviderProps> = ({
  children,
  initialTheme = 'light',
}) => {
  const [isDark, setIsDark] = React.useState(initialTheme === 'dark');

  // Create theme object
  const theme = useMemo((): Theme => ({
    colors: isDark ? DarkModeColors : Colors,
    spacing: Spacing,
    typography: Typography,
    borderRadius: BorderRadius,
    elevation: Elevation,
    animation: Animation,
    breakpoints: Breakpoints,
    componentSizes: ComponentSizes,
    zIndex: ZIndex,
    opacity: Opacity,
    semanticColors: SemanticColors,
    isDark,
  }), [isDark]);

  // Toggle theme function
  const toggleTheme = React.useCallback(() => {
    setIsDark(prev => !prev);
  }, []);

  // Utility functions
  const getSpacing = React.useCallback((size: keyof typeof Spacing): number => {
    return Spacing[size];
  }, []);

  const getTypographyStyle = React.useCallback((variant: keyof typeof Typography.styles) => {
    return Typography.styles[variant];
  }, []);

  const getElevation = React.useCallback((level: keyof typeof Elevation) => {
    return Elevation[level];
  }, []);

  // Safe color getter with dot notation support
  const getColor = React.useCallback((path: string, fallback: string = '#000000'): string => {
    try {
      const keys = path.split('.');
      let value: any = theme.colors;
      
      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return fallback;
        }
      }
      
      return typeof value === 'string' ? value : fallback;
    } catch (error) {
      console.warn(`Failed to get color for path: ${path}`, error);
      return fallback;
    }
  }, [theme.colors]);

  // Responsive helpers
  const isSmallScreen = useMemo(() => Breakpoints.current.isSmall, []);
  const isMediumScreen = useMemo(() => Breakpoints.current.isMedium, []);
  const isLargeScreen = useMemo(() => Breakpoints.current.isLarge, []);

  // Accessibility helpers
  const getAccessibleTouchTarget = React.useCallback((size: 'minimum' | 'comfortable' | 'large' = 'minimum'): number => {
    return ComponentSizes.touchTarget[size];
  }, []);

  const getAccessibleTextColor = React.useCallback((backgroundColor: string): string => {
    // Simple contrast calculation - in production, use a more sophisticated algorithm
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    return brightness > 128 ? theme.colors.text?.primary || '#000000' : theme.colors.text?.inverse || '#FFFFFF';
  }, [theme.colors]);

  // Context value
  const contextValue = useMemo((): DesignSystemContextType => ({
    theme,
    isDark,
    toggleTheme,
    getSpacing,
    getTypographyStyle,
    getElevation,
    getColor,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    getAccessibleTouchTarget,
    getAccessibleTextColor,
  }), [
    theme,
    isDark,
    toggleTheme,
    getSpacing,
    getTypographyStyle,
    getElevation,
    getColor,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    getAccessibleTouchTarget,
    getAccessibleTextColor,
  ]);

  return (
    <DesignSystemContext.Provider value={contextValue}>
      {children}
    </DesignSystemContext.Provider>
  );
};

// Hook to use design system
export const useDesignSystem = (): DesignSystemContextType => {
  const context = useContext(DesignSystemContext);
  
  if (context === undefined) {
    throw new Error('useDesignSystem must be used within a DesignSystemProvider');
  }
  
  return context;
};

// Convenience hooks for specific parts of the design system
export const useTheme = () => {
  const { theme, isDark, toggleTheme } = useDesignSystem();
  return { theme, isDark, toggleTheme };
};

export const useColors = () => {
  const { theme, getColor } = useDesignSystem();
  return { colors: theme.colors, getColor };
};

export const useSpacing = () => {
  const { theme, getSpacing } = useDesignSystem();
  return { spacing: theme.spacing, getSpacing };
};

export const useTypography = () => {
  const { theme, getTypographyStyle } = useDesignSystem();
  return { typography: theme.typography, getTypographyStyle };
};

export const useResponsive = () => {
  const { isSmallScreen, isMediumScreen, isLargeScreen } = useDesignSystem();
  return { isSmallScreen, isMediumScreen, isLargeScreen };
};

export const useAccessibility = () => {
  const { getAccessibleTouchTarget, getAccessibleTextColor } = useDesignSystem();
  return { getAccessibleTouchTarget, getAccessibleTextColor };
};

// Style helper functions
export const createThemedStyles = (styleFunction: (theme: Theme) => any) => {
  return (theme: Theme) => styleFunction(theme);
};

// Component style generators
export const getButtonStyles = (theme: Theme, variant: 'primary' | 'secondary' | 'ghost' = 'primary') => {
  const baseStyles = {
    borderRadius: theme.borderRadius.component.button,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minHeight: theme.componentSizes.touchTarget.minimum,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    ...theme.typography.styles.button,
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyles,
        backgroundColor: theme.semanticColors.primary,
        ...theme.elevation.sm,
      };
    case 'secondary':
      return {
        ...baseStyles,
        backgroundColor: theme.semanticColors.secondary,
        borderWidth: 1,
        borderColor: theme.semanticColors.borderPrimary,
      };
    case 'ghost':
      return {
        ...baseStyles,
        backgroundColor: 'transparent',
      };
    default:
      return baseStyles;
  }
};

export const getCardStyles = (theme: Theme, elevation: keyof typeof Elevation = 'sm') => ({
  backgroundColor: theme.colors.background?.primary || '#FFFFFF',
  borderRadius: theme.borderRadius.component.card,
  padding: theme.spacing.md,
  ...theme.elevation[elevation],
});

export const getInputStyles = (theme: Theme, size: 'small' | 'medium' | 'large' = 'medium') => ({
  ...theme.componentSizes.input[size],
  borderRadius: theme.borderRadius.component.input,
  borderWidth: 1,
  borderColor: theme.semanticColors.borderPrimary,
  backgroundColor: theme.colors.background?.primary || '#FFFFFF',
  ...theme.typography.styles.body1,
});

export default DesignSystemProvider;
