/**
 * Enhanced Error Handling Components
 * Comprehensive error display and user feedback system
 */

import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StandardizedButton } from './StandardizedButton';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { Colors } from '../../constants/Colors';

export interface ErrorDisplayProps {
  error: string | Error | null;
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  variant?: 'inline' | 'card' | 'banner' | 'modal';
  severity?: 'error' | 'warning' | 'info';
  style?: ViewStyle;
  showIcon?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  testID?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title,
  description,
  actionLabel = 'Try Again',
  onAction,
  variant = 'card',
  severity = 'error',
  style,
  showIcon = true,
  dismissible = false,
  onDismiss,
  testID = 'error-display',
}) => {
  if (!error) return null;

  const errorMessage = error instanceof Error ? error.message : error;
  const config = getErrorConfig(severity);

  const renderContent = () => (
    <>
      {showIcon && (
        <Ionicons
          name={config.icon}
          size={24}
          color={config.color}
          style={styles.icon}
        />
      )}
      <View style={styles.content}>
        {title && (
          <Text style={[styles.title, { color: config.color }]} accessibilityRole="header">
            {title}
          </Text>
        )}
        <Text style={[styles.message, { color: config.textColor }]} accessibilityRole="text">
          {errorMessage}
        </Text>
        {description && (
          <Text style={styles.description} accessibilityRole="text">
            {description}
          </Text>
        )}
        {onAction && (
          <StandardizedButton
            title={actionLabel}
            onPress={onAction}
            variant="secondary"
            size="small"
            style={styles.actionButton}
            testID={`${testID}-action`}
          />
        )}
      </View>
      {dismissible && onDismiss && (
        <StandardizedButton
          action="close"
          onPress={onDismiss}
          variant="ghost"
          size="small"
          style={styles.dismissButton}
          testID={`${testID}-dismiss`}
        />
      )}
    </>
  );

  const containerStyle = [
    styles.container,
    styles[variant],
    { backgroundColor: config.backgroundColor, borderColor: config.borderColor },
    style,
  ];

  return (
    <View style={containerStyle} testID={testID} accessibilityRole="alert">
      {renderContent()}
    </View>
  );
};

// Validation Error Component
export interface ValidationErrorProps {
  errors: string[];
  field?: string;
  style?: ViewStyle;
  testID?: string;
}

export const ValidationError: React.FC<ValidationErrorProps> = ({
  errors,
  field,
  style,
  testID = 'validation-error',
}) => {
  if (!errors || errors.length === 0) return null;

  return (
    <View style={[styles.validationContainer, style]} testID={testID}>
      {errors.map((error, index) => (
        <View key={index} style={styles.validationItem}>
          <Ionicons
            name="alert-circle"
            size={16}
            color={Colors.error}
            style={styles.validationIcon}
          />
          <Text style={styles.validationText} accessibilityRole="text">
            {field ? `${field}: ${error}` : error}
          </Text>
        </View>
      ))}
    </View>
  );
};

// Success Feedback Component
export interface SuccessFeedbackProps {
  message: string;
  title?: string;
  actionLabel?: string;
  onAction?: () => void;
  autoHide?: boolean;
  duration?: number;
  style?: ViewStyle;
  testID?: string;
}

export const SuccessFeedback: React.FC<SuccessFeedbackProps> = ({
  message,
  title,
  actionLabel,
  onAction,
  autoHide = false,
  duration = 3000,
  style,
  testID = 'success-feedback',
}) => {
  const [visible, setVisible] = React.useState(true);

  React.useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => setVisible(false), duration);
      return () => clearTimeout(timer);
    }
  }, [autoHide, duration]);

  if (!visible) return null;

  return (
    <View style={[styles.container, styles.card, styles.successContainer, style]} testID={testID}>
      <Ionicons
        name="checkmark-circle"
        size={24}
        color={Colors.success}
        style={styles.icon}
      />
      <View style={styles.content}>
        {title && (
          <Text style={[styles.title, { color: Colors.success }]} accessibilityRole="header">
            {title}
          </Text>
        )}
        <Text style={[styles.message, { color: Colors.text.primary }]} accessibilityRole="text">
          {message}
        </Text>
        {onAction && actionLabel && (
          <StandardizedButton
            title={actionLabel}
            onPress={onAction}
            variant="secondary"
            size="small"
            style={styles.actionButton}
            testID={`${testID}-action`}
          />
        )}
      </View>
    </View>
  );
};

// Info Banner Component
export interface InfoBannerProps {
  message: string;
  title?: string;
  actionLabel?: string;
  onAction?: () => void;
  dismissible?: boolean;
  onDismiss?: () => void;
  style?: ViewStyle;
  testID?: string;
}

export const InfoBanner: React.FC<InfoBannerProps> = ({
  message,
  title,
  actionLabel,
  onAction,
  dismissible = true,
  onDismiss,
  style,
  testID = 'info-banner',
}) => {
  return (
    <ErrorDisplay
      error={message}
      title={title}
      actionLabel={actionLabel}
      onAction={onAction}
      variant="banner"
      severity="info"
      dismissible={dismissible}
      onDismiss={onDismiss}
      style={style}
      testID={testID}
    />
  );
};

// Helper function to get error configuration
const getErrorConfig = (severity: 'error' | 'warning' | 'info') => {
  switch (severity) {
    case 'error':
      return {
        icon: 'alert-circle' as const,
        color: Colors.error,
        backgroundColor: Colors.error + '10',
        borderColor: Colors.error + '30',
        textColor: Colors.text.primary,
      };
    case 'warning':
      return {
        icon: 'warning' as const,
        color: Colors.warning,
        backgroundColor: Colors.warning + '10',
        borderColor: Colors.warning + '30',
        textColor: Colors.text.primary,
      };
    case 'info':
      return {
        icon: 'information-circle' as const,
        color: Colors.info,
        backgroundColor: Colors.info + '10',
        borderColor: Colors.info + '30',
        textColor: Colors.text.primary,
      };
  }
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: getResponsiveSpacing(12),
    borderRadius: 8,
    borderWidth: 1,
  },
  inline: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: getResponsiveSpacing(8),
  },
  card: {
    marginVertical: getResponsiveSpacing(8),
    marginHorizontal: getResponsiveSpacing(16),
  },
  banner: {
    borderRadius: 0,
    marginVertical: 0,
    marginHorizontal: 0,
  },
  modal: {
    margin: getResponsiveSpacing(24),
    borderRadius: 12,
  },
  icon: {
    marginRight: getResponsiveSpacing(12),
    marginTop: getResponsiveSpacing(2),
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(4),
  },
  message: {
    fontSize: getResponsiveFontSize(14),
    lineHeight: getResponsiveFontSize(20),
    marginBottom: getResponsiveSpacing(8),
  },
  description: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
    lineHeight: getResponsiveFontSize(18),
    marginBottom: getResponsiveSpacing(8),
  },
  actionButton: {
    alignSelf: 'flex-start',
    marginTop: getResponsiveSpacing(8),
  },
  dismissButton: {
    marginLeft: getResponsiveSpacing(8),
  },
  // Validation styles
  validationContainer: {
    marginTop: getResponsiveSpacing(4),
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(4),
  },
  validationIcon: {
    marginRight: getResponsiveSpacing(6),
  },
  validationText: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.error,
    flex: 1,
  },
  // Success styles
  successContainer: {
    backgroundColor: Colors.success + '10',
    borderColor: Colors.success + '30',
  },
});
