{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "React", "require", "TextAncestorContext", "createContext", "__DEV__", "displayName", "_default"], "sources": ["TextAncestor.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nconst React = require('react');\n\n/**\n * Whether the current element is the descendant of a <Text> element.\n */\nconst TextAncestorContext: React.Context<boolean> = React.createContext(false);\nif (__DEV__) {\n  TextAncestorContext.displayName = 'TextAncestorContext';\n}\nexport default TextAncestorContext;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAK9B,IAAMC,mBAA2C,GAAGF,KAAK,CAACG,aAAa,CAAC,KAAK,CAAC;AAC9E,IAAIC,OAAO,EAAE;EACXF,mBAAmB,CAACG,WAAW,GAAG,qBAAqB;AACzD;AAAC,IAAAC,QAAA,GAAAT,OAAA,CAAAE,OAAA,GACcG,mBAAmB", "ignoreList": []}