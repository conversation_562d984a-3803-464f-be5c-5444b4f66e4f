/**
 * Form Validation Utilities
 *
 * Comprehensive form validation utilities with enhanced UX patterns
 * following modern form design principles and accessibility guidelines.
 *
 * Features:
 * - Real-time validation
 * - Accessible error messaging
 * - Progressive enhancement
 * - Multi-language support
 * - Custom validation rules
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Validation rule types
export type ValidationRule = 
  | 'required'
  | 'email'
  | 'phone'
  | 'password'
  | 'confirmPassword'
  | 'minLength'
  | 'maxLength'
  | 'pattern'
  | 'custom';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Field validation configuration
export interface FieldValidation {
  rules: ValidationRule[];
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: string, formData?: any) => ValidationResult;
  required?: boolean;
  dependsOn?: string[];
}

// Form validation configuration
export interface FormValidationConfig {
  fields: Record<string, FieldValidation>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  showErrorsImmediately?: boolean;
  progressiveValidation?: boolean;
}

// Validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  postalCode: /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  mediumPassword: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,}$/,
  name: /^[a-zA-ZÀ-ÿ\s'-]{2,50}$/,
  username: /^[a-zA-Z0-9_-]{3,20}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  password: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  confirmPassword: 'Passwords do not match',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Must be no more than ${max} characters`,
  pattern: 'Please enter a valid format',
  name: 'Please enter a valid name (2-50 characters, letters only)',
  username: 'Username must be 3-20 characters (letters, numbers, underscore, hyphen)',
  postalCode: 'Please enter a valid postal code (e.g., K1A 0A6)',
  url: 'Please enter a valid URL',
} as const;

/**
 * Validate a single field
 */
export const validateField = (
  value: string,
  fieldName: string,
  validation: FieldValidation,
  formData?: Record<string, string>
): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  // Skip validation if field is empty and not required
  if (!value.trim() && !validation.required) {
    return result;
  }

  // Required validation
  if (validation.required && !value.trim()) {
    result.isValid = false;
    result.errors.push(ERROR_MESSAGES.required);
    return result;
  }

  // Apply validation rules
  for (const rule of validation.rules) {
    switch (rule) {
      case 'email':
        if (value && !VALIDATION_PATTERNS.email.test(value)) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.email);
        }
        break;

      case 'phone':
        if (value && !VALIDATION_PATTERNS.phone.test(value.replace(/\s|-|\(|\)/g, ''))) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.phone);
        }
        break;

      case 'password':
        if (value) {
          if (!VALIDATION_PATTERNS.strongPassword.test(value)) {
            if (!VALIDATION_PATTERNS.mediumPassword.test(value)) {
              result.isValid = false;
              result.errors.push(ERROR_MESSAGES.password);
            } else {
              result.warnings.push('Consider using a stronger password');
              result.suggestions.push('Add uppercase letters and special characters');
            }
          }
        }
        break;

      case 'confirmPassword':
        if (formData && value !== formData.password) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.confirmPassword);
        }
        break;

      case 'minLength':
        if (validation.minLength && value.length < validation.minLength) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.minLength(validation.minLength));
        }
        break;

      case 'maxLength':
        if (validation.maxLength && value.length > validation.maxLength) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.maxLength(validation.maxLength));
        }
        break;

      case 'pattern':
        if (validation.pattern && value && !validation.pattern.test(value)) {
          result.isValid = false;
          result.errors.push(ERROR_MESSAGES.pattern);
        }
        break;

      case 'custom':
        if (validation.customValidator) {
          const customResult = validation.customValidator(value, formData);
          result.isValid = result.isValid && customResult.isValid;
          result.errors.push(...customResult.errors);
          result.warnings.push(...customResult.warnings);
          result.suggestions.push(...customResult.suggestions);
        }
        break;
    }
  }

  return result;
};

/**
 * Validate entire form
 */
export const validateForm = (
  formData: Record<string, string>,
  config: FormValidationConfig
): Record<string, ValidationResult> => {
  const results: Record<string, ValidationResult> = {};

  Object.entries(config.fields).forEach(([fieldName, validation]) => {
    const value = formData[fieldName] || '';
    results[fieldName] = validateField(value, fieldName, validation, formData);
  });

  return results;
};

/**
 * Check if form is valid
 */
export const isFormValid = (validationResults: Record<string, ValidationResult>): boolean => {
  return Object.values(validationResults).every(result => result.isValid);
};

/**
 * Get form errors summary
 */
export const getFormErrorsSummary = (
  validationResults: Record<string, ValidationResult>
): string[] => {
  const errors: string[] = [];
  
  Object.entries(validationResults).forEach(([fieldName, result]) => {
    if (!result.isValid) {
      errors.push(`${fieldName}: ${result.errors.join(', ')}`);
    }
  });

  return errors;
};

/**
 * Format field name for display
 */
export const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

/**
 * Generate accessibility attributes for form fields
 */
export const getFieldAccessibilityProps = (
  fieldName: string,
  validationResult: ValidationResult,
  isRequired: boolean = false
) => {
  const props: any = {
    accessibilityLabel: formatFieldName(fieldName),
    accessibilityRequired: isRequired,
  };

  if (!validationResult.isValid) {
    props.accessibilityInvalid = true;
    props.accessibilityErrorMessage = validationResult.errors.join('. ');
  }

  if (validationResult.warnings.length > 0) {
    props.accessibilityHint = validationResult.warnings.join('. ');
  }

  return props;
};

/**
 * Progressive validation - validate as user types
 */
export const createProgressiveValidator = (
  fieldName: string,
  validation: FieldValidation,
  onValidation: (result: ValidationResult) => void
) => {
  let timeoutId: NodeJS.Timeout;
  
  return (value: string, formData?: Record<string, string>) => {
    clearTimeout(timeoutId);
    
    // Immediate validation for critical errors
    if (validation.required && !value.trim()) {
      const result = validateField(value, fieldName, validation, formData);
      onValidation(result);
      return;
    }

    // Debounced validation for other cases
    timeoutId = setTimeout(() => {
      const result = validateField(value, fieldName, validation, formData);
      onValidation(result);
    }, 300);
  };
};

/**
 * Smart validation suggestions
 */
export const generateValidationSuggestions = (
  value: string,
  fieldName: string,
  validation: FieldValidation
): string[] => {
  const suggestions: string[] = [];

  if (fieldName.toLowerCase().includes('email') && value) {
    if (!value.includes('@')) {
      suggestions.push('Email addresses need an @ symbol');
    } else if (!value.includes('.')) {
      suggestions.push('Email addresses need a domain (e.g., .com)');
    }
  }

  if (fieldName.toLowerCase().includes('password') && value) {
    if (value.length < 8) {
      suggestions.push('Try a longer password');
    }
    if (!/[A-Z]/.test(value)) {
      suggestions.push('Add an uppercase letter');
    }
    if (!/[0-9]/.test(value)) {
      suggestions.push('Add a number');
    }
    if (!/[^A-Za-z0-9]/.test(value)) {
      suggestions.push('Add a special character');
    }
  }

  if (fieldName.toLowerCase().includes('phone') && value) {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length < 10) {
      suggestions.push('Phone numbers need at least 10 digits');
    }
  }

  return suggestions;
};

/**
 * Form validation presets for common forms
 */
export const FORM_PRESETS = {
  login: {
    fields: {
      email: {
        rules: ['required', 'email'],
        required: true,
      },
      password: {
        rules: ['required', 'minLength'],
        minLength: 6,
        required: true,
      },
    },
    validateOnBlur: true,
    showErrorsImmediately: false,
  },
  
  registration: {
    fields: {
      firstName: {
        rules: ['required', 'pattern'],
        pattern: VALIDATION_PATTERNS.name,
        required: true,
      },
      lastName: {
        rules: ['required', 'pattern'],
        pattern: VALIDATION_PATTERNS.name,
        required: true,
      },
      email: {
        rules: ['required', 'email'],
        required: true,
      },
      password: {
        rules: ['required', 'password'],
        required: true,
      },
      confirmPassword: {
        rules: ['required', 'confirmPassword'],
        required: true,
        dependsOn: ['password'],
      },
    },
    validateOnChange: true,
    progressiveValidation: true,
  },
  
  profile: {
    fields: {
      firstName: {
        rules: ['required', 'pattern'],
        pattern: VALIDATION_PATTERNS.name,
        required: true,
      },
      lastName: {
        rules: ['required', 'pattern'],
        pattern: VALIDATION_PATTERNS.name,
        required: true,
      },
      phone: {
        rules: ['phone'],
        required: false,
      },
      postalCode: {
        rules: ['pattern'],
        pattern: VALIDATION_PATTERNS.postalCode,
        required: false,
      },
    },
    validateOnBlur: true,
    showErrorsImmediately: false,
  },
} as const;

/**
 * Auto-format input values
 */
export const formatInputValue = (value: string, type: string): string => {
  switch (type) {
    case 'phone':
      // Format as (XXX) XXX-XXXX
      const cleaned = value.replace(/\D/g, '');
      if (cleaned.length >= 10) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      }
      return value;

    case 'postalCode':
      // Format as XXX XXX
      const postal = value.replace(/\s/g, '').toUpperCase();
      if (postal.length >= 3) {
        return `${postal.slice(0, 3)} ${postal.slice(3, 6)}`;
      }
      return postal;

    case 'creditCard':
      // Format as XXXX XXXX XXXX XXXX
      const card = value.replace(/\D/g, '');
      return card.replace(/(\d{4})(?=\d)/g, '$1 ');

    default:
      return value;
  }
};

export default {
  VALIDATION_PATTERNS,
  ERROR_MESSAGES,
  FORM_PRESETS,
  validateField,
  validateForm,
  isFormValid,
  getFormErrorsSummary,
  formatFieldName,
  getFieldAccessibilityProps,
  createProgressiveValidator,
  generateValidationSuggestions,
  formatInputValue,
};
