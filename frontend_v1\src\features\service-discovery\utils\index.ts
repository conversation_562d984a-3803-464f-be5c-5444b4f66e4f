/**
 * Service Discovery Utilities - Export Index
 *
 * Provides clean imports for all service discovery utility functions
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Formatting utilities
export {
  formatPrice,
  formatDuration,
  formatRating,
  formatDistance,
} from './formatters';

// Validation utilities
export {
  validateSearchFilters,
  validateServiceData,
  validateProviderData,
} from './validators';

// Responsive design utilities
export {
  getResponsiveFontSize,
  getResponsiveSpacing,
  getScreenDimensions,
} from '../../../utils/responsiveUtils';

// Search utilities
export {
  buildSearchQuery,
  parseSearchFilters,
  normalizeSearchResults,
} from './search';

// Date and time utilities
export {
  formatDate,
  formatTime,
  parseAvailability,
  isTimeSlotAvailable,
} from './datetime';

// Image utilities
export { getImageUrl, getPlaceholderImage, optimizeImageUrl } from './images';

// Navigation utilities
export {
  buildServiceUrl,
  buildProviderUrl,
  parseNavigationParams,
} from './navigation';

// Performance utilities
export { memoizeApiCall, throttleFunction, cacheResult } from './performance';
