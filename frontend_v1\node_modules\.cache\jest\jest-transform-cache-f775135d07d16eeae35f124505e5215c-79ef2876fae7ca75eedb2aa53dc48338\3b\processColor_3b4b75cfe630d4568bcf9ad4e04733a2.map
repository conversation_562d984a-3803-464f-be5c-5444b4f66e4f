{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "Platform", "require", "normalizeColor", "processColor", "color", "undefined", "normalizedColor", "processColorObject", "processedColorObj", "OS", "_default"], "sources": ["processColor.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n'use strict';\n\nimport type {ColorValue, NativeColorValue} from './StyleSheet';\n\nconst Platform = require('../Utilities/Platform').default;\nconst normalizeColor = require('./normalizeColor').default;\n\nexport type ProcessedColorValue = number | NativeColorValue;\n\n/* eslint no-bitwise: 0 */\nfunction processColor(color?: ?(number | ColorValue)): ?ProcessedColorValue {\n  if (color === undefined || color === null) {\n    return color;\n  }\n\n  let normalizedColor = normalizeColor(color);\n  if (normalizedColor === null || normalizedColor === undefined) {\n    return undefined;\n  }\n\n  if (typeof normalizedColor === 'object') {\n    const processColorObject =\n      require('./PlatformColorValueTypes').processColorObject;\n\n    const processedColorObj = processColorObject(normalizedColor);\n\n    if (processedColorObj != null) {\n      return processedColorObj;\n    }\n  }\n\n  if (typeof normalizedColor !== 'number') {\n    return null;\n  }\n\n  // Converts 0xrrggbbaa into 0xaarrggbb\n  normalizedColor = ((normalizedColor << 24) | (normalizedColor >>> 8)) >>> 0;\n\n  if (Platform.OS === 'android') {\n    // Android use 32 bit *signed* integer to represent the color\n    // We utilize the fact that bitwise operations in JS also operates on\n    // signed 32 bit integers, so that we can use those to convert from\n    // *unsigned* to *signed* 32bit int that way.\n    normalizedColor = normalizedColor | 0x0;\n  }\n  return normalizedColor;\n}\n\nexport default processColor;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAIb,IAAMC,QAAQ,GAAGC,OAAO,wBAAwB,CAAC,CAACF,OAAO;AACzD,IAAMG,cAAc,GAAGD,OAAO,mBAAmB,CAAC,CAACF,OAAO;AAK1D,SAASI,YAAYA,CAACC,KAA8B,EAAwB;EAC1E,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAOA,KAAK;EACd;EAEA,IAAIE,eAAe,GAAGJ,cAAc,CAACE,KAAK,CAAC;EAC3C,IAAIE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKD,SAAS,EAAE;IAC7D,OAAOA,SAAS;EAClB;EAEA,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;IACvC,IAAMC,kBAAkB,GACtBN,OAAO,4BAA4B,CAAC,CAACM,kBAAkB;IAEzD,IAAMC,iBAAiB,GAAGD,kBAAkB,CAACD,eAAe,CAAC;IAE7D,IAAIE,iBAAiB,IAAI,IAAI,EAAE;MAC7B,OAAOA,iBAAiB;IAC1B;EACF;EAEA,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EAGAA,eAAe,GAAG,CAAEA,eAAe,IAAI,EAAE,GAAKA,eAAe,KAAK,CAAE,MAAM,CAAC;EAE3E,IAAIN,QAAQ,CAACS,EAAE,KAAK,SAAS,EAAE;IAK7BH,eAAe,GAAGA,eAAe,GAAG,GAAG;EACzC;EACA,OAAOA,eAAe;AACxB;AAAC,IAAAI,QAAA,GAAAb,OAAA,CAAAE,OAAA,GAEcI,YAAY", "ignoreList": []}