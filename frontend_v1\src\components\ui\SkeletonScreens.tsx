/**
 * Skeleton Screen Components
 * Pre-built skeleton screens for common layouts
 */

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { 
  SkeletonLoader, 
  SkeletonText, 
  SkeletonImage, 
  SkeletonButton 
} from './LoadingStates';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { Colors } from '../../constants/Colors';

interface SkeletonScreenProps {
  style?: ViewStyle;
}

// Home Screen Skeleton
export const HomeScreenSkeleton: React.FC<SkeletonScreenProps> = ({ style }) => {
  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <View style={styles.header}>
        <SkeletonText lines={1} width="40%" height={24} />
        <SkeletonImage width={40} height={40} borderRadius={20} />
      </View>

      {/* Greeting */}
      <View style={styles.greetingSection}>
        <SkeletonText lines={1} width="60%" height={28} />
        <SkeletonText lines={1} width="80%" height={16} />
      </View>

      {/* Categories */}
      <View style={styles.categoriesSection}>
        <SkeletonText lines={1} width="50%" height={20} />
        <View style={styles.categoriesRow}>
          {Array.from({ length: 4 }, (_, index) => (
            <View key={index} style={styles.categoryCard}>
              <SkeletonImage width={60} height={60} borderRadius={12} />
              <SkeletonText lines={1} width="80%" height={14} />
              <SkeletonText lines={1} width="60%" height={12} />
            </View>
          ))}
        </View>
      </View>

      {/* Featured Providers */}
      <View style={styles.providersSection}>
        <View style={styles.sectionHeader}>
          <SkeletonText lines={1} width="60%" height={20} />
          <SkeletonButton width={80} height={32} />
        </View>
        <View style={styles.providersRow}>
          {Array.from({ length: 3 }, (_, index) => (
            <View key={index} style={styles.providerCard}>
              <SkeletonImage width={120} height={80} borderRadius={8} />
              <SkeletonText lines={1} width="90%" height={16} />
              <SkeletonText lines={1} width="70%" height={14} />
              <SkeletonText lines={1} width="50%" height={12} />
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

// Search Screen Skeleton
export const SearchScreenSkeleton: React.FC<SkeletonScreenProps> = ({ style }) => {
  return (
    <View style={[styles.container, style]}>
      {/* Search Header */}
      <View style={styles.searchHeader}>
        <SkeletonLoader width="100%" height={48} borderRadius={24} />
        <View style={styles.searchButtons}>
          <SkeletonButton width={80} height={36} />
          <SkeletonButton width={80} height={36} />
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersSection}>
        <View style={styles.filterChips}>
          {Array.from({ length: 5 }, (_, index) => (
            <SkeletonButton key={index} width={60} height={32} />
          ))}
        </View>
      </View>

      {/* Results */}
      <View style={styles.resultsSection}>
        <SkeletonText lines={1} width="40%" height={16} />
        {Array.from({ length: 6 }, (_, index) => (
          <View key={index} style={styles.resultCard}>
            <SkeletonImage width={80} height={80} borderRadius={8} />
            <View style={styles.resultContent}>
              <SkeletonText lines={1} width="80%" height={18} />
              <SkeletonText lines={1} width="60%" height={14} />
              <SkeletonText lines={1} width="40%" height={12} />
              <View style={styles.resultActions}>
                <SkeletonButton width={60} height={28} />
                <SkeletonButton width={80} height={28} />
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

// Profile Screen Skeleton
export const ProfileScreenSkeleton: React.FC<SkeletonScreenProps> = ({ style }) => {
  return (
    <View style={[styles.container, style]}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <SkeletonImage width={100} height={100} borderRadius={50} />
        <View style={styles.profileInfo}>
          <SkeletonText lines={1} width="70%" height={24} />
          <SkeletonText lines={1} width="50%" height={16} />
          <SkeletonText lines={1} width="60%" height={14} />
        </View>
        <SkeletonButton width={80} height={36} />
      </View>

      {/* Profile Sections */}
      {Array.from({ length: 4 }, (_, index) => (
        <View key={index} style={styles.profileSection}>
          <SkeletonText lines={1} width="40%" height={18} />
          <View style={styles.profileSectionContent}>
            <SkeletonText lines={2} width="100%" height={14} />
          </View>
        </View>
      ))}
    </View>
  );
};

// List Screen Skeleton
export const ListScreenSkeleton: React.FC<SkeletonScreenProps & { itemCount?: number }> = ({ 
  style, 
  itemCount = 8 
}) => {
  return (
    <View style={[styles.container, style]}>
      {Array.from({ length: itemCount }, (_, index) => (
        <View key={index} style={styles.listItem}>
          <SkeletonImage width={60} height={60} borderRadius={8} />
          <View style={styles.listItemContent}>
            <SkeletonText lines={1} width="80%" height={16} />
            <SkeletonText lines={1} width="60%" height={14} />
            <SkeletonText lines={1} width="40%" height={12} />
          </View>
          <SkeletonButton width={24} height={24} />
        </View>
      ))}
    </View>
  );
};

// Booking Screen Skeleton
export const BookingScreenSkeleton: React.FC<SkeletonScreenProps> = ({ style }) => {
  return (
    <View style={[styles.container, style]}>
      {/* Service Info */}
      <View style={styles.bookingSection}>
        <SkeletonImage width="100%" height={200} borderRadius={12} />
        <View style={styles.bookingInfo}>
          <SkeletonText lines={1} width="80%" height={24} />
          <SkeletonText lines={2} width="100%" height={14} />
          <SkeletonText lines={1} width="50%" height={16} />
        </View>
      </View>

      {/* Calendar */}
      <View style={styles.calendarSection}>
        <SkeletonText lines={1} width="40%" height={18} />
        <View style={styles.calendarGrid}>
          {Array.from({ length: 35 }, (_, index) => (
            <SkeletonLoader key={index} width={32} height={32} borderRadius={16} />
          ))}
        </View>
      </View>

      {/* Time Slots */}
      <View style={styles.timeSlotsSection}>
        <SkeletonText lines={1} width="40%" height={18} />
        <View style={styles.timeSlots}>
          {Array.from({ length: 8 }, (_, index) => (
            <SkeletonButton key={index} width={80} height={36} />
          ))}
        </View>
      </View>

      {/* Booking Button */}
      <View style={styles.bookingActions}>
        <SkeletonButton width="100%" height={48} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    padding: getResponsiveSpacing(16),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(24),
  },
  greetingSection: {
    marginBottom: getResponsiveSpacing(32),
  },
  categoriesSection: {
    marginBottom: getResponsiveSpacing(32),
  },
  categoriesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: getResponsiveSpacing(16),
  },
  categoryCard: {
    alignItems: 'center',
    width: '22%',
  },
  providersSection: {
    marginBottom: getResponsiveSpacing(32),
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(16),
  },
  providersRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  providerCard: {
    width: '30%',
  },
  searchHeader: {
    marginBottom: getResponsiveSpacing(16),
  },
  searchButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: getResponsiveSpacing(12),
  },
  filtersSection: {
    marginBottom: getResponsiveSpacing(16),
  },
  filterChips: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resultsSection: {
    flex: 1,
  },
  resultCard: {
    flexDirection: 'row',
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(12),
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
  },
  resultContent: {
    flex: 1,
    marginLeft: getResponsiveSpacing(12),
  },
  resultActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: getResponsiveSpacing(8),
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(32),
  },
  profileInfo: {
    alignItems: 'center',
    marginVertical: getResponsiveSpacing(16),
  },
  profileSection: {
    marginBottom: getResponsiveSpacing(24),
  },
  profileSectionContent: {
    marginTop: getResponsiveSpacing(12),
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(12),
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
  },
  listItemContent: {
    flex: 1,
    marginLeft: getResponsiveSpacing(12),
  },
  bookingSection: {
    marginBottom: getResponsiveSpacing(24),
  },
  bookingInfo: {
    marginTop: getResponsiveSpacing(16),
  },
  calendarSection: {
    marginBottom: getResponsiveSpacing(24),
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: getResponsiveSpacing(12),
  },
  timeSlotsSection: {
    marginBottom: getResponsiveSpacing(24),
  },
  timeSlots: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: getResponsiveSpacing(12),
  },
  bookingActions: {
    marginTop: 'auto',
    paddingTop: getResponsiveSpacing(16),
  },
});
