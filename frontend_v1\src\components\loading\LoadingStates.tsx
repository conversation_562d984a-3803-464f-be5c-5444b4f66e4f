/**
 * Loading States Components
 *
 * Comprehensive loading state components for various loading scenarios
 * with accessibility features and customizable animations.
 *
 * Features:
 * - Multiple loading variants
 * - Skeleton loading screens
 * - Progress indicators
 * - Accessibility compliance
 * - Customizable animations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Body } from '../typography/Typography';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useAnimation } from '../../contexts/AnimationContext';

// Loading variant types
export type LoadingVariant = 
  | 'spinner'
  | 'dots'
  | 'pulse'
  | 'skeleton'
  | 'progress'
  | 'shimmer';

// Component props
export interface LoadingStateProps {
  // Configuration
  variant?: LoadingVariant;
  size?: 'small' | 'medium' | 'large';
  message?: string;
  progress?: number; // 0-100 for progress variant
  
  // Styling
  color?: string;
  backgroundColor?: string;
  overlay?: boolean;
  style?: any;
  
  // Accessibility
  accessibilityLabel?: string;
  
  // Testing
  testID?: string;
}

// Spinner loading component
export const SpinnerLoading: React.FC<LoadingStateProps> = ({
  size = 'medium',
  message,
  color,
  overlay = false,
  style,
  accessibilityLabel,
  testID,
}) => {
  const { colors } = useHighContrastColors();
  const { shouldAnimate } = useAnimation();
  const { t } = useTranslation();
  
  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (shouldAnimate) {
      const spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();
      return () => spinAnimation.stop();
    }
  }, [shouldAnimate, spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getSize = () => {
    switch (size) {
      case 'small': return 20;
      case 'large': return 48;
      default: return 32;
    }
  };

  const containerStyles = [
    styles.container,
    overlay && styles.overlay,
    overlay && { backgroundColor: colors?.background?.overlay || 'rgba(0, 0, 0, 0.5)' },
    style,
  ];

  return (
    <View style={containerStyles} testID={testID}>
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.spinner,
            { transform: [{ rotate: spin }] },
          ]}
          accessibilityRole="progressbar"
          accessibilityLabel={accessibilityLabel || t('common.loading')}
        >
          <Ionicons
            name="refresh"
            size={getSize()}
            color={color || colors?.primary?.default || '#5A7A63'}
          />
        </Animated.View>
        
        {message && (
          <Body
            color={overlay ? colors?.text?.inverse : colors?.text?.secondary}
            align="center"
            style={styles.message}
          >
            {message}
          </Body>
        )}
      </View>
    </View>
  );
};

// Dots loading component
export const DotsLoading: React.FC<LoadingStateProps> = ({
  size = 'medium',
  message,
  color,
  style,
  accessibilityLabel,
  testID,
}) => {
  const { colors } = useHighContrastColors();
  const { shouldAnimate } = useAnimation();
  const { t } = useTranslation();
  
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (shouldAnimate) {
      const createDotAnimation = (dot: Animated.Value, delay: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dot, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ])
        );
      };

      const animations = [
        createDotAnimation(dot1, 0),
        createDotAnimation(dot2, 200),
        createDotAnimation(dot3, 400),
      ];

      animations.forEach(animation => animation.start());
      
      return () => animations.forEach(animation => animation.stop());
    }
  }, [shouldAnimate, dot1, dot2, dot3]);

  const getDotSize = () => {
    switch (size) {
      case 'small': return 6;
      case 'large': return 12;
      default: return 8;
    }
  };

  const dotSize = getDotSize();
  const dotColor = color || colors?.primary?.default || '#5A7A63';

  return (
    <View style={[styles.container, style]} testID={testID}>
      <View style={styles.content}>
        <View
          style={styles.dotsContainer}
          accessibilityRole="progressbar"
          accessibilityLabel={accessibilityLabel || t('common.loading')}
        >
          {[dot1, dot2, dot3].map((dot, index) => (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  width: dotSize,
                  height: dotSize,
                  backgroundColor: dotColor,
                  opacity: dot,
                },
              ]}
            />
          ))}
        </View>
        
        {message && (
          <Body
            color={colors?.text?.secondary}
            align="center"
            style={styles.message}
          >
            {message}
          </Body>
        )}
      </View>
    </View>
  );
};

// Skeleton loading component
export const SkeletonLoading: React.FC<{
  lines?: number;
  height?: number;
  style?: any;
  testID?: string;
}> = ({
  lines = 3,
  height = 16,
  style,
  testID,
}) => {
  const { colors } = useHighContrastColors();
  const { shouldAnimate } = useAnimation();
  
  const pulseValue = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    if (shouldAnimate) {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 0.7,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 0.3,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    }
  }, [shouldAnimate, pulseValue]);

  return (
    <View style={[styles.skeletonContainer, style]} testID={testID}>
      {Array.from({ length: lines }).map((_, index) => (
        <Animated.View
          key={index}
          style={[
            styles.skeletonLine,
            {
              height,
              backgroundColor: colors?.background?.secondary || '#F0F0F0',
              opacity: pulseValue,
              width: index === lines - 1 ? '70%' : '100%', // Last line shorter
            },
          ]}
        />
      ))}
    </View>
  );
};

// Progress loading component
export const ProgressLoading: React.FC<LoadingStateProps> = ({
  progress = 0,
  message,
  color,
  style,
  accessibilityLabel,
  testID,
}) => {
  const { colors } = useHighContrastColors();
  const { t } = useTranslation();
  
  const progressValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(progressValue, {
      toValue: progress / 100,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress, progressValue]);

  const progressWidth = progressValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={[styles.container, style]} testID={testID}>
      <View style={styles.content}>
        <View
          style={[styles.progressContainer, { backgroundColor: colors?.background?.secondary }]}
          accessibilityRole="progressbar"
          accessibilityLabel={accessibilityLabel || `${t('common.loading')} ${Math.round(progress)}%`}
          accessibilityValue={{ min: 0, max: 100, now: progress }}
        >
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: progressWidth,
                backgroundColor: color || colors?.primary?.default || '#5A7A63',
              },
            ]}
          />
        </View>
        
        <Typography
          variant="caption"
          color={colors?.text?.secondary}
          align="center"
          style={styles.progressText}
        >
          {Math.round(progress)}%
        </Typography>
        
        {message && (
          <Body
            color={colors?.text?.secondary}
            align="center"
            style={styles.message}
          >
            {message}
          </Body>
        )}
      </View>
    </View>
  );
};

// Main loading component that switches between variants
export const LoadingState: React.FC<LoadingStateProps> = ({
  variant = 'spinner',
  ...props
}) => {
  switch (variant) {
    case 'dots':
      return <DotsLoading {...props} />;
    case 'skeleton':
      return <SkeletonLoading {...props} />;
    case 'progress':
      return <ProgressLoading {...props} />;
    case 'spinner':
    default:
      return <SpinnerLoading {...props} />;
  }
};

// Full screen loading overlay
export const LoadingOverlay: React.FC<LoadingStateProps> = (props) => (
  <LoadingState {...props} overlay />
);

// Inline loading for content areas
export const InlineLoading: React.FC<LoadingStateProps> = (props) => (
  <LoadingState {...props} style={[styles.inlineLoading, props.style]} />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinner: {
    marginBottom: 16,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 4,
  },
  message: {
    marginTop: 8,
    maxWidth: 200,
  },
  skeletonContainer: {
    width: '100%',
  },
  skeletonLine: {
    borderRadius: 4,
    marginBottom: 8,
  },
  progressContainer: {
    width: 200,
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    marginBottom: 8,
  },
  inlineLoading: {
    minHeight: 100,
  },
});

export default LoadingState;
