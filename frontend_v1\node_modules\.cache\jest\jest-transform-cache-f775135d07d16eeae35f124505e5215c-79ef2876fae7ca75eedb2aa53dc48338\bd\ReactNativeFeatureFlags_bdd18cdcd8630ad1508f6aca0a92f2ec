f4de1329ef794ebe975956c788e4dc3e
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var ReactNativeFeatureFlags = {
  shouldEmitW3CPointerEvents: function shouldEmitW3CPointerEvents() {
    return false;
  },
  shouldPressibilityUseW3CPointerEventsForHover: function shouldPressibilityUseW3CPointerEventsForHover() {
    return false;
  }
};
var _default = exports.default = ReactNativeFeatureFlags;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdE5hdGl2ZUZlYXR1cmVGbGFncyIsInNob3VsZEVtaXRXM0NQb2ludGVyRXZlbnRzIiwic2hvdWxkUHJlc3NpYmlsaXR5VXNlVzNDUG9pbnRlckV2ZW50c0ZvckhvdmVyIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbIlJlYWN0TmF0aXZlRmVhdHVyZUZsYWdzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3RcbiAqIEBmb3JtYXRcbiAqL1xuXG5leHBvcnQgdHlwZSBGZWF0dXJlRmxhZ3MgPSB7XG4gIC8qKlxuICAgKiBGdW5jdGlvbiB1c2VkIHRvIGVuYWJsZSAvIGRpc2FibGUgVzNDIHBvaW50ZXIgZXZlbnQgZW1pdHRpbmcgaW4gUmVhY3QgTmF0aXZlLlxuICAgKiBJZiBlbmFibGVkIHlvdSBtdXN0IGFsc28gZmxpcCB0aGUgZXF1aXZhbGVudCBuYXRpdmUgZmxhZ3Mgb24gZWFjaCBwbGF0Zm9ybTpcbiAgICogaU9TIC0+IFJDVFNldERpc3BhdGNoVzNDUG9pbnRlckV2ZW50c1xuICAgKiBBbmRyb2lkIC0+IFJlYWN0RmVhdHVyZUZsYWdzLmRpc3BhdGNoUG9pbnRlckV2ZW50c1xuICAgKi9cbiAgc2hvdWxkRW1pdFczQ1BvaW50ZXJFdmVudHM6ICgpID0+IGJvb2xlYW4sXG4gIC8qKlxuICAgKiBGdW5jdGlvbiB1c2VkIHRvIGVuYWJsZSAvIGRpc2FibGUgUHJlc3NpYmlsaXR5IGZyb20gdXNpbmcgVzNDIFBvaW50ZXIgRXZlbnRzXG4gICAqIGZvciBpdHMgaG92ZXIgY2FsbGJhY2tzXG4gICAqL1xuICBzaG91bGRQcmVzc2liaWxpdHlVc2VXM0NQb2ludGVyRXZlbnRzRm9ySG92ZXI6ICgpID0+IGJvb2xlYW4sXG59O1xuXG5jb25zdCBSZWFjdE5hdGl2ZUZlYXR1cmVGbGFnczogRmVhdHVyZUZsYWdzID0ge1xuICBzaG91bGRFbWl0VzNDUG9pbnRlckV2ZW50czogKCkgPT4gZmFsc2UsXG4gIHNob3VsZFByZXNzaWJpbGl0eVVzZVczQ1BvaW50ZXJFdmVudHNGb3JIb3ZlcjogKCkgPT4gZmFsc2UsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBSZWFjdE5hdGl2ZUZlYXR1cmVGbGFncztcbiJdLCJtYXBwaW5ncyI6Ijs7OztBQXlCQSxJQUFNQSx1QkFBcUMsR0FBRztFQUM1Q0MsMEJBQTBCLEVBQUUsU0FBNUJBLDBCQUEwQkEsQ0FBQTtJQUFBLE9BQVEsS0FBSztFQUFBO0VBQ3ZDQyw2Q0FBNkMsRUFBRSxTQUEvQ0EsNkNBQTZDQSxDQUFBO0lBQUEsT0FBUSxLQUFLO0VBQUE7QUFDNUQsQ0FBQztBQUFDLElBQUFDLFFBQUEsR0FBQUMsT0FBQSxDQUFBQyxPQUFBLEdBRWFMLHVCQUF1QiIsImlnbm9yZUxpc3QiOltdfQ==