/**
 * Enhanced Loading State Hook
 * Provides comprehensive loading state management with progress tracking
 */

import { useState, useCallback, useRef } from 'react';

export interface LoadingState {
  isLoading: boolean;
  progress: number;
  message: string;
  error: string | null;
  step: number;
  totalSteps: number;
}

export interface LoadingOptions {
  initialMessage?: string;
  totalSteps?: number;
  enableProgress?: boolean;
  enableSteps?: boolean;
}

export interface LoadingActions {
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  setProgress: (progress: number, message?: string) => void;
  setStep: (step: number, message?: string) => void;
  setError: (error: string) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState: LoadingState = {
  isLoading: false,
  progress: 0,
  message: '',
  error: null,
  step: 0,
  totalSteps: 1,
};

export const useLoadingState = (options: LoadingOptions = {}): [LoadingState, LoadingActions] => {
  const {
    initialMessage = 'Loading...',
    totalSteps = 1,
    enableProgress = false,
    enableSteps = false,
  } = options;

  const [state, setState] = useState<LoadingState>({
    ...initialState,
    message: initialMessage,
    totalSteps,
  });

  const timeoutRef = useRef<NodeJS.Timeout>();

  const startLoading = useCallback((message?: string) => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      message: message || initialMessage,
      error: null,
      progress: 0,
      step: 0,
    }));
  }, [initialMessage]);

  const stopLoading = useCallback(() => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      progress: 100,
    }));
    
    // Clear any pending timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const setProgress = useCallback((progress: number, message?: string) => {
    setState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
      message: message || prev.message,
    }));
  }, []);

  const setStep = useCallback((step: number, message?: string) => {
    setState(prev => {
      const newStep = Math.max(0, Math.min(prev.totalSteps, step));
      const newProgress = enableProgress ? (newStep / prev.totalSteps) * 100 : prev.progress;
      
      return {
        ...prev,
        step: newStep,
        progress: newProgress,
        message: message || prev.message,
      };
    });
  }, [enableProgress]);

  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
    }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      ...initialState,
      message: initialMessage,
      totalSteps,
    });
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, [initialMessage, totalSteps]);

  const actions: LoadingActions = {
    startLoading,
    stopLoading,
    setProgress,
    setStep,
    setError,
    clearError,
    reset,
  };

  return [state, actions];
};

// Specialized hooks for common use cases
export const useApiLoadingState = () => {
  return useLoadingState({
    initialMessage: 'Loading data...',
    enableProgress: true,
  });
};

export const useFormLoadingState = () => {
  return useLoadingState({
    initialMessage: 'Submitting...',
    enableProgress: true,
  });
};

export const useSteppedLoadingState = (totalSteps: number, stepLabels?: string[]) => {
  return useLoadingState({
    initialMessage: 'Processing...',
    totalSteps,
    enableSteps: true,
    enableProgress: true,
  });
};

// Helper function for async operations with loading state
export const withLoadingState = async <T>(
  asyncFn: () => Promise<T>,
  loadingActions: LoadingActions,
  options: {
    startMessage?: string;
    successMessage?: string;
    errorMessage?: string;
    enableProgress?: boolean;
  } = {}
): Promise<T> => {
  const {
    startMessage = 'Loading...',
    successMessage,
    errorMessage = 'An error occurred',
    enableProgress = false,
  } = options;

  try {
    loadingActions.startLoading(startMessage);
    
    if (enableProgress) {
      // Simulate progress for better UX
      loadingActions.setProgress(25, 'Initializing...');
      await new Promise(resolve => setTimeout(resolve, 100));
      loadingActions.setProgress(50, 'Processing...');
    }

    const result = await asyncFn();
    
    if (enableProgress) {
      loadingActions.setProgress(100, successMessage || 'Complete!');
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    loadingActions.stopLoading();
    return result;
  } catch (error) {
    const message = error instanceof Error ? error.message : errorMessage;
    loadingActions.setError(message);
    throw error;
  }
};
