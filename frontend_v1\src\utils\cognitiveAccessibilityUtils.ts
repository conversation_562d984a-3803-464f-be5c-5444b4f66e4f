/**
 * Cognitive Accessibility Utilities
 *
 * Utilities for reducing cognitive load and improving comprehension
 * following WCAG 2.2 AA guidelines and cognitive accessibility best practices.
 *
 * Features:
 * - Plain language processing
 * - Content simplification
 * - Reading level analysis
 * - Cognitive load assessment
 * - Memory aid utilities
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Reading level thresholds
export const READING_LEVELS = {
  ELEMENTARY: 6,
  MIDDLE_SCHOOL: 8,
  HIGH_SCHOOL: 12,
  COLLEGE: 16,
} as const;

// Cognitive load levels
export type CognitiveLoadLevel = 'low' | 'medium' | 'high' | 'very-high';

// Content complexity interface
export interface ContentComplexity {
  readingLevel: number;
  cognitiveLoad: CognitiveLoadLevel;
  wordCount: number;
  sentenceCount: number;
  averageWordsPerSentence: number;
  complexWords: number;
  recommendations: string[];
}

// Memory aid types
export type MemoryAidType = 'visual' | 'textual' | 'audio' | 'interactive';

/**
 * Calculate Flesch Reading Ease score
 */
export const calculateFleschScore = (text: string): number => {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(w => w.length > 0);
  const syllables = words.reduce((total, word) => total + countSyllables(word), 0);
  
  if (sentences.length === 0 || words.length === 0) return 0;
  
  const avgSentenceLength = words.length / sentences.length;
  const avgSyllablesPerWord = syllables / words.length;
  
  return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
};

/**
 * Count syllables in a word
 */
export const countSyllables = (word: string): number => {
  word = word.toLowerCase();
  if (word.length <= 3) return 1;
  
  word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
  word = word.replace(/^y/, '');
  
  const matches = word.match(/[aeiouy]{1,2}/g);
  return matches ? matches.length : 1;
};

/**
 * Convert Flesch score to reading level
 */
export const fleschToReadingLevel = (score: number): number => {
  if (score >= 90) return 5;
  if (score >= 80) return 6;
  if (score >= 70) return 7;
  if (score >= 60) return 8;
  if (score >= 50) return 9;
  if (score >= 30) return 13;
  return 16;
};

/**
 * Analyze content complexity
 */
export const analyzeContentComplexity = (text: string): ContentComplexity => {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(w => w.length > 0);
  const complexWords = words.filter(word => word.length > 6 || countSyllables(word) > 2);
  
  const fleschScore = calculateFleschScore(text);
  const readingLevel = fleschToReadingLevel(fleschScore);
  const averageWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
  
  // Determine cognitive load
  let cognitiveLoad: CognitiveLoadLevel = 'low';
  if (readingLevel > 12 || averageWordsPerSentence > 20 || complexWords.length / words.length > 0.3) {
    cognitiveLoad = 'very-high';
  } else if (readingLevel > 8 || averageWordsPerSentence > 15 || complexWords.length / words.length > 0.2) {
    cognitiveLoad = 'high';
  } else if (readingLevel > 6 || averageWordsPerSentence > 10 || complexWords.length / words.length > 0.1) {
    cognitiveLoad = 'medium';
  }
  
  // Generate recommendations
  const recommendations: string[] = [];
  if (readingLevel > 8) {
    recommendations.push('Consider simplifying language for better comprehension');
  }
  if (averageWordsPerSentence > 15) {
    recommendations.push('Break long sentences into shorter ones');
  }
  if (complexWords.length / words.length > 0.2) {
    recommendations.push('Replace complex words with simpler alternatives');
  }
  
  return {
    readingLevel,
    cognitiveLoad,
    wordCount: words.length,
    sentenceCount: sentences.length,
    averageWordsPerSentence,
    complexWords: complexWords.length,
    recommendations,
  };
};

/**
 * Simplify text for better comprehension
 */
export const simplifyText = (text: string): string => {
  // Common word replacements for simpler language
  const replacements: Record<string, string> = {
    'utilize': 'use',
    'facilitate': 'help',
    'demonstrate': 'show',
    'approximately': 'about',
    'assistance': 'help',
    'commence': 'start',
    'terminate': 'end',
    'purchase': 'buy',
    'location': 'place',
    'additional': 'more',
    'previous': 'last',
    'subsequent': 'next',
    'numerous': 'many',
    'sufficient': 'enough',
    'immediately': 'now',
    'currently': 'now',
    'subsequently': 'then',
    'consequently': 'so',
    'furthermore': 'also',
    'nevertheless': 'but',
    'therefore': 'so',
    'however': 'but',
    'although': 'though',
    'because': 'since',
    'regarding': 'about',
    'concerning': 'about',
    'pertaining': 'about',
  };
  
  let simplified = text;
  
  // Apply word replacements
  Object.entries(replacements).forEach(([complex, simple]) => {
    const regex = new RegExp(`\\b${complex}\\b`, 'gi');
    simplified = simplified.replace(regex, simple);
  });
  
  // Break long sentences
  simplified = simplified.replace(/([.!?])\s*([A-Z])/g, '$1\n\n$2');
  
  return simplified;
};

/**
 * Generate memory aids for content
 */
export const generateMemoryAids = (
  content: string,
  type: MemoryAidType = 'textual'
): string[] => {
  const aids: string[] = [];
  
  switch (type) {
    case 'textual':
      // Generate bullet points
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
      aids.push(...sentences.slice(0, 3).map(s => `• ${s.trim()}`));
      break;
      
    case 'visual':
      // Suggest visual elements
      aids.push('Consider adding icons or images to support the text');
      aids.push('Use consistent visual patterns throughout');
      aids.push('Highlight important information with visual cues');
      break;
      
    case 'interactive':
      // Suggest interactive elements
      aids.push('Add progress indicators for multi-step processes');
      aids.push('Provide confirmation messages for user actions');
      aids.push('Use consistent button placement and styling');
      break;
      
    case 'audio':
      // Suggest audio aids
      aids.push('Consider providing audio descriptions');
      aids.push('Add sound cues for important actions');
      aids.push('Provide text-to-speech options');
      break;
  }
  
  return aids;
};

/**
 * Check for cognitive accessibility patterns
 */
export const checkCognitivePatterns = (content: {
  headings: string[];
  buttons: string[];
  links: string[];
  instructions: string[];
}): {
  consistent: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check heading consistency
  const headingPatterns = content.headings.map(h => h.toLowerCase().trim());
  const uniquePatterns = new Set(headingPatterns);
  if (headingPatterns.length > uniquePatterns.size * 2) {
    issues.push('Inconsistent heading patterns detected');
    recommendations.push('Use consistent heading structure and language');
  }
  
  // Check button text clarity
  const vagueButttonTexts = ['click here', 'read more', 'go', 'submit', 'ok'];
  const vagueButtons = content.buttons.filter(btn => 
    vagueButttonTexts.some(vague => btn.toLowerCase().includes(vague))
  );
  if (vagueButtons.length > 0) {
    issues.push('Vague button text found');
    recommendations.push('Use descriptive button text that explains the action');
  }
  
  // Check instruction clarity
  const complexInstructions = content.instructions.filter(inst => {
    const complexity = analyzeContentComplexity(inst);
    return complexity.cognitiveLoad === 'high' || complexity.cognitiveLoad === 'very-high';
  });
  if (complexInstructions.length > 0) {
    issues.push('Complex instructions detected');
    recommendations.push('Simplify instructions and break them into smaller steps');
  }
  
  return {
    consistent: issues.length === 0,
    issues,
    recommendations,
  };
};

/**
 * Generate cognitive accessibility guidelines
 */
export const generateCognitiveGuidelines = (): string[] => {
  return [
    // Language and Content
    'Use simple, clear language that is easy to understand',
    'Keep sentences short (under 20 words when possible)',
    'Use active voice instead of passive voice',
    'Define technical terms and acronyms',
    'Use familiar words instead of complex alternatives',
    
    // Structure and Layout
    'Organize content in a logical, predictable order',
    'Use consistent navigation and layout patterns',
    'Provide clear headings and subheadings',
    'Use bullet points and numbered lists for clarity',
    'Group related information together',
    
    // Instructions and Help
    'Provide clear, step-by-step instructions',
    'Include examples and illustrations when helpful',
    'Offer multiple ways to complete tasks',
    'Provide help and support options',
    'Use progress indicators for multi-step processes',
    
    // Error Prevention and Recovery
    'Prevent errors with clear labels and instructions',
    'Provide helpful error messages with solutions',
    'Allow users to review and correct information',
    'Offer undo options for important actions',
    'Save user progress automatically when possible',
    
    // Memory and Attention
    'Minimize cognitive load by reducing unnecessary information',
    'Provide reminders and confirmations for important actions',
    'Use visual cues to highlight important information',
    'Allow users to customize their experience',
    'Provide search and filtering options for large amounts of content',
  ];
};

/**
 * Validate cognitive accessibility compliance
 */
export const validateCognitiveAccessibility = (content: {
  text: string;
  headings: string[];
  buttons: string[];
  links: string[];
  instructions: string[];
}): {
  score: number;
  level: 'excellent' | 'good' | 'fair' | 'poor';
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  let score = 100;
  
  // Analyze text complexity
  const complexity = analyzeContentComplexity(content.text);
  if (complexity.cognitiveLoad === 'very-high') {
    score -= 30;
    issues.push('Text complexity is very high');
    recommendations.push(...complexity.recommendations);
  } else if (complexity.cognitiveLoad === 'high') {
    score -= 20;
    issues.push('Text complexity is high');
    recommendations.push(...complexity.recommendations);
  } else if (complexity.cognitiveLoad === 'medium') {
    score -= 10;
  }
  
  // Check patterns
  const patternCheck = checkCognitivePatterns(content);
  if (!patternCheck.consistent) {
    score -= 20;
    issues.push(...patternCheck.issues);
    recommendations.push(...patternCheck.recommendations);
  }
  
  // Determine level
  let level: 'excellent' | 'good' | 'fair' | 'poor';
  if (score >= 90) level = 'excellent';
  else if (score >= 75) level = 'good';
  else if (score >= 60) level = 'fair';
  else level = 'poor';
  
  return {
    score: Math.max(0, score),
    level,
    issues,
    recommendations,
  };
};

export default {
  READING_LEVELS,
  calculateFleschScore,
  countSyllables,
  fleschToReadingLevel,
  analyzeContentComplexity,
  simplifyText,
  generateMemoryAids,
  checkCognitivePatterns,
  generateCognitiveGuidelines,
  validateCognitiveAccessibility,
};
