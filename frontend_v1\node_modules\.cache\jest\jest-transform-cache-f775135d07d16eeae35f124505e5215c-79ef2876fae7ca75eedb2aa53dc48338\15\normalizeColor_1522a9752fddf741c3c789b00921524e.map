{"version": 3, "names": ["_normalizeColors", "_interopRequireDefault", "require", "normalizeColor", "color", "_require", "normalizeColorObject", "normalizedColor", "_normalizeColor", "_default", "exports", "default"], "sources": ["normalizeColor.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n/* eslint no-bitwise: 0 */\n\nimport type {ProcessedColorValue} from './processColor';\nimport type {ColorValue} from './StyleSheet';\n\nimport _normalizeColor from '@react-native/normalize-colors';\n\nfunction normalizeColor(\n  color: ?(ColorValue | ProcessedColorValue),\n): ?ProcessedColorValue {\n  if (typeof color === 'object' && color != null) {\n    const {normalizeColorObject} = require('./PlatformColorValueTypes');\n    const normalizedColor = normalizeColorObject(color);\n    if (normalizedColor != null) {\n      return normalizedColor;\n    }\n  }\n\n  if (typeof color === 'string' || typeof color === 'number') {\n    return _normalizeColor(color);\n  }\n}\n\nexport default normalizeColor;\n"], "mappings": ";;;;;AAeA,IAAAA,gBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,cAAcA,CACrBC,KAA0C,EACpB;EACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC9C,IAAAC,QAAA,GAA+BH,OAAO,4BAA4B,CAAC;MAA5DI,oBAAoB,GAAAD,QAAA,CAApBC,oBAAoB;IAC3B,IAAMC,eAAe,GAAGD,oBAAoB,CAACF,KAAK,CAAC;IACnD,IAAIG,eAAe,IAAI,IAAI,EAAE;MAC3B,OAAOA,eAAe;IACxB;EACF;EAEA,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1D,OAAO,IAAAI,wBAAe,EAACJ,KAAK,CAAC;EAC/B;AACF;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcR,cAAc", "ignoreList": []}