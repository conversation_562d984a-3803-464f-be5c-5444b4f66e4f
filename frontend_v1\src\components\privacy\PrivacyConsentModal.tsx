/**
 * Privacy Consent Modal Component
 *
 * Comprehensive privacy consent modal for Canadian privacy law compliance
 * including PIPEDA and Quebec privacy legislation.
 *
 * Features:
 * - Granular consent options
 * - Canadian privacy law compliance
 * - Clear privacy explanations
 * - Consent tracking
 * - Accessibility compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Modal,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Heading, Body } from '../typography/Typography';
import { AnimatedButton } from '../animation/AnimatedButton';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles } from '../../contexts/MotorAccessibilityContext';
import { PrivacySettings, DEFAULT_PRIVACY_SETTINGS } from '../../utils/securityUtils';

// Consent categories
export interface ConsentCategory {
  id: string;
  title: string;
  description: string;
  required: boolean;
  enabled: boolean;
  details: string[];
  legalBasis: string;
}

// Component props
export interface PrivacyConsentModalProps {
  visible: boolean;
  onClose: () => void;
  onConsentGiven: (settings: PrivacySettings) => void;
  initialSettings?: Partial<PrivacySettings>;
  userLocation?: 'canada' | 'quebec' | 'international';
  showDetailedOptions?: boolean;
}

export const PrivacyConsentModal: React.FC<PrivacyConsentModalProps> = ({
  visible,
  onClose,
  onConsentGiven,
  initialSettings = {},
  userLocation = 'canada',
  showDetailedOptions = true,
}) => {
  // Hooks
  const { t } = useTranslation();
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();

  // State
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    ...DEFAULT_PRIVACY_SETTINGS,
    ...initialSettings,
  });
  const [showDetails, setShowDetails] = useState<Record<string, boolean>>({});

  // Consent categories
  const getConsentCategories = (): ConsentCategory[] => {
    const baseCategories: ConsentCategory[] = [
      {
        id: 'essential',
        title: t('privacy.essential_services'),
        description: t('privacy.essential_description'),
        required: true,
        enabled: true,
        details: [
          t('privacy.essential_detail_1'),
          t('privacy.essential_detail_2'),
          t('privacy.essential_detail_3'),
        ],
        legalBasis: t('privacy.legal_basis_contract'),
      },
      {
        id: 'analytics',
        title: t('privacy.analytics_title'),
        description: t('privacy.analytics_description'),
        required: false,
        enabled: privacySettings.allowAnalytics,
        details: [
          t('privacy.analytics_detail_1'),
          t('privacy.analytics_detail_2'),
          t('privacy.analytics_detail_3'),
        ],
        legalBasis: t('privacy.legal_basis_consent'),
      },
      {
        id: 'location',
        title: t('privacy.location_title'),
        description: t('privacy.location_description'),
        required: false,
        enabled: privacySettings.allowLocationTracking,
        details: [
          t('privacy.location_detail_1'),
          t('privacy.location_detail_2'),
          t('privacy.location_detail_3'),
        ],
        legalBasis: t('privacy.legal_basis_consent'),
      },
      {
        id: 'notifications',
        title: t('privacy.notifications_title'),
        description: t('privacy.notifications_description'),
        required: false,
        enabled: privacySettings.allowNotifications,
        details: [
          t('privacy.notifications_detail_1'),
          t('privacy.notifications_detail_2'),
        ],
        legalBasis: t('privacy.legal_basis_consent'),
      },
      {
        id: 'marketing',
        title: t('privacy.marketing_title'),
        description: t('privacy.marketing_description'),
        required: false,
        enabled: privacySettings.allowDataSharing,
        details: [
          t('privacy.marketing_detail_1'),
          t('privacy.marketing_detail_2'),
        ],
        legalBasis: t('privacy.legal_basis_consent'),
      },
    ];

    // Add Quebec-specific categories
    if (userLocation === 'quebec') {
      baseCategories.push({
        id: 'quebec_specific',
        title: t('privacy.quebec_specific_title'),
        description: t('privacy.quebec_specific_description'),
        required: false,
        enabled: false,
        details: [
          t('privacy.quebec_detail_1'),
          t('privacy.quebec_detail_2'),
        ],
        legalBasis: t('privacy.legal_basis_quebec'),
      });
    }

    return baseCategories;
  };

  // Update privacy setting
  const updatePrivacySetting = useCallback((
    category: string,
    enabled: boolean
  ) => {
    setPrivacySettings(prev => {
      const updated = { ...prev };
      
      switch (category) {
        case 'analytics':
          updated.allowAnalytics = enabled;
          break;
        case 'location':
          updated.allowLocationTracking = enabled;
          break;
        case 'notifications':
          updated.allowNotifications = enabled;
          break;
        case 'marketing':
          updated.allowDataSharing = enabled;
          break;
      }
      
      return updated;
    });
  }, []);

  // Toggle details visibility
  const toggleDetails = useCallback((categoryId: string) => {
    setShowDetails(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  }, []);

  // Handle consent submission
  const handleConsentSubmit = useCallback(() => {
    onConsentGiven(privacySettings);
    onClose();
  }, [privacySettings, onConsentGiven, onClose]);

  // Handle accept all
  const handleAcceptAll = useCallback(() => {
    const allEnabled: PrivacySettings = {
      ...privacySettings,
      allowAnalytics: true,
      allowLocationTracking: true,
      allowNotifications: true,
      allowDataSharing: false, // Keep marketing opt-in explicit
    };
    
    onConsentGiven(allEnabled);
    onClose();
  }, [privacySettings, onConsentGiven, onClose]);

  // Handle reject optional
  const handleRejectOptional = useCallback(() => {
    const essentialOnly: PrivacySettings = {
      ...privacySettings,
      allowAnalytics: false,
      allowLocationTracking: false,
      allowNotifications: false,
      allowDataSharing: false,
    };
    
    onConsentGiven(essentialOnly);
    onClose();
  }, [privacySettings, onConsentGiven, onClose]);

  // Render consent category
  const renderConsentCategory = (category: ConsentCategory) => (
    <View
      key={category.id}
      style={[
        styles.categoryContainer,
        { backgroundColor: colors?.background?.secondary },
      ]}
    >
      <View style={styles.categoryHeader}>
        <View style={styles.categoryInfo}>
          <Typography
            variant="subtitle1"
            color={colors?.text?.primary}
            style={styles.categoryTitle}
          >
            {category.title}
            {category.required && (
              <Typography variant="caption" color={colors?.status?.error}>
                {' '}*{t('privacy.required')}
              </Typography>
            )}
          </Typography>
          
          <Body
            color={colors?.text?.secondary}
            style={styles.categoryDescription}
          >
            {category.description}
          </Body>
        </View>
        
        <Switch
          value={category.enabled}
          onValueChange={(enabled) => updatePrivacySetting(category.id, enabled)}
          disabled={category.required}
          trackColor={{
            false: colors?.background?.tertiary,
            true: colors?.primary?.light,
          }}
          thumbColor={category.enabled ? colors?.primary?.default : colors?.text?.tertiary}
          accessibilityLabel={`${category.title} ${category.enabled ? 'enabled' : 'disabled'}`}
          accessibilityRole="switch"
        />
      </View>
      
      {showDetailedOptions && (
        <TouchableOpacity
          style={[styles.detailsToggle, touchTargetStyles]}
          onPress={() => toggleDetails(category.id)}
          accessibilityRole="button"
          accessibilityLabel={`${showDetails[category.id] ? 'Hide' : 'Show'} details for ${category.title}`}
        >
          <Typography
            variant="caption"
            color={colors?.primary?.default}
            style={styles.detailsToggleText}
          >
            {showDetails[category.id] ? t('privacy.hide_details') : t('privacy.show_details')}
          </Typography>
          
          <Ionicons
            name={showDetails[category.id] ? 'chevron-up' : 'chevron-down'}
            size={16}
            color={colors?.primary?.default}
          />
        </TouchableOpacity>
      )}
      
      {showDetails[category.id] && (
        <View style={styles.categoryDetails}>
          <Typography
            variant="caption"
            color={colors?.text?.tertiary}
            style={styles.legalBasis}
          >
            {t('privacy.legal_basis')}: {category.legalBasis}
          </Typography>
          
          {category.details.map((detail, index) => (
            <View key={index} style={styles.detailItem}>
              <Typography
                variant="caption"
                color={colors?.text?.secondary}
                style={styles.detailBullet}
              >
                •
              </Typography>
              <Typography
                variant="caption"
                color={colors?.text?.secondary}
                style={styles.detailText}
              >
                {detail}
              </Typography>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  // Render privacy notice
  const renderPrivacyNotice = () => (
    <View style={[styles.noticeContainer, { backgroundColor: colors?.background?.tertiary }]}>
      <Ionicons
        name="shield-checkmark"
        size={24}
        color={colors?.primary?.default}
        style={styles.noticeIcon}
      />
      
      <View style={styles.noticeContent}>
        <Typography
          variant="subtitle2"
          color={colors?.text?.primary}
          style={styles.noticeTitle}
        >
          {t('privacy.your_privacy_matters')}
        </Typography>
        
        <Body
          color={colors?.text?.secondary}
          style={styles.noticeText}
        >
          {userLocation === 'quebec' 
            ? t('privacy.quebec_notice')
            : t('privacy.canada_notice')
          }
        </Body>
      </View>
    </View>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors?.background?.primary }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors?.border?.primary }]}>
          <Heading
            level={2}
            color={colors?.text?.primary}
            style={styles.headerTitle}
          >
            {t('privacy.privacy_settings')}
          </Heading>
          
          <TouchableOpacity
            style={[styles.closeButton, touchTargetStyles]}
            onPress={onClose}
            accessibilityRole="button"
            accessibilityLabel={t('common.close')}
          >
            <Ionicons
              name="close"
              size={24}
              color={colors?.text?.secondary}
            />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {renderPrivacyNotice()}
          
          <Typography
            variant="body1"
            color={colors?.text?.primary}
            style={styles.introText}
          >
            {t('privacy.consent_intro')}
          </Typography>
          
          {getConsentCategories().map(renderConsentCategory)}
          
          <View style={styles.additionalInfo}>
            <Typography
              variant="caption"
              color={colors?.text?.tertiary}
              style={styles.additionalInfoText}
            >
              {t('privacy.additional_info')}
            </Typography>
          </View>
        </ScrollView>

        {/* Actions */}
        <View style={[styles.actions, { borderTopColor: colors?.border?.primary }]}>
          <View style={styles.actionButtons}>
            <AnimatedButton
              title={t('privacy.essential_only')}
              onPress={handleRejectOptional}
              variant="outline"
              style={styles.actionButton}
              accessibilityLabel={t('privacy.essential_only_hint')}
            />
            
            <AnimatedButton
              title={t('privacy.accept_selected')}
              onPress={handleConsentSubmit}
              variant="primary"
              style={styles.actionButton}
              accessibilityLabel={t('privacy.accept_selected_hint')}
            />
          </View>
          
          <AnimatedButton
            title={t('privacy.accept_all')}
            onPress={handleAcceptAll}
            variant="ghost"
            style={styles.acceptAllButton}
            accessibilityLabel={t('privacy.accept_all_hint')}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    flex: 1,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  noticeContainer: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  noticeIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  noticeContent: {
    flex: 1,
  },
  noticeTitle: {
    marginBottom: 4,
    fontWeight: '600',
  },
  noticeText: {
    lineHeight: 18,
  },
  introText: {
    marginBottom: 24,
    lineHeight: 22,
  },
  categoryContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  categoryInfo: {
    flex: 1,
    marginRight: 16,
  },
  categoryTitle: {
    marginBottom: 4,
    fontWeight: '600',
  },
  categoryDescription: {
    lineHeight: 18,
  },
  detailsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingVertical: 4,
  },
  detailsToggleText: {
    marginRight: 4,
    fontWeight: '600',
  },
  categoryDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  legalBasis: {
    marginBottom: 8,
    fontStyle: 'italic',
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  detailBullet: {
    marginRight: 8,
    marginTop: 1,
  },
  detailText: {
    flex: 1,
    lineHeight: 16,
  },
  additionalInfo: {
    marginTop: 24,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },
  additionalInfoText: {
    textAlign: 'center',
    lineHeight: 16,
  },
  actions: {
    padding: 16,
    borderTopWidth: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  actionButton: {
    flex: 1,
  },
  acceptAllButton: {
    width: '100%',
  },
});

export default PrivacyConsentModal;
