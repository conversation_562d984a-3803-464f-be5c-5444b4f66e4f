73858954d410430d7826adc1c7714be2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("@testing-library/react-native");
var _AnimatedButton = require("../../components/animation/AnimatedButton");
var _jsxRuntime = require("react/jsx-runtime");
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
    children: children
  });
};
describe('AnimatedButton', function () {
  var defaultProps = {
    title: 'Test Button',
    onPress: jest.fn(),
    testID: 'animated-button'
  };
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Rendering', function () {
    it('renders correctly with default props', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, Object.assign({}, defaultProps))
        })),
        getByTestId = _render.getByTestId,
        getByText = _render.getByText;
      expect(getByTestId('animated-button')).toBeTruthy();
      expect(getByText('Test Button')).toBeTruthy();
    });
    it('renders with different variants', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, Object.assign({}, defaultProps, {
            variant: "primary",
            testID: "button-primary"
          }))
        })),
        getByTestId = _render2.getByTestId;
      expect(getByTestId('button-primary')).toBeTruthy();
    });
  });
  describe('Functionality', function () {
    it('calls onPress when pressed', function () {
      var onPressMock = jest.fn();
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, Object.assign({}, defaultProps, {
            onPress: onPressMock
          }))
        })),
        getByTestId = _render3.getByTestId;
      _reactNative.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
    it('does not call onPress when disabled', function () {
      var onPressMock = jest.fn();
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, Object.assign({}, defaultProps, {
            onPress: onPressMock,
            disabled: true
          }))
        })),
        getByTestId = _render4.getByTestId;
      _reactNative.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).not.toHaveBeenCalled();
    });
  });
  describe('Accessibility', function () {
    it('has proper accessibility properties', function () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, Object.assign({}, defaultProps, {
            accessibilityLabel: "Custom accessibility label"
          }))
        })),
        getByTestId = _render5.getByTestId;
      var button = getByTestId('animated-button');
      expect(button.props.accessibilityRole).toBe('button');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdE5hdGl2ZSIsIl9BbmltYXRlZEJ1dHRvbiIsIl9qc3hSdW50aW1lIiwiVGVzdFdyYXBwZXIiLCJfcmVmIiwiY2hpbGRyZW4iLCJqc3giLCJGcmFnbWVudCIsImRlc2NyaWJlIiwiZGVmYXVsdFByb3BzIiwidGl0bGUiLCJvblByZXNzIiwiamVzdCIsImZuIiwidGVzdElEIiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJpdCIsIl9yZW5kZXIiLCJyZW5kZXIiLCJBbmltYXRlZEJ1dHRvbiIsIk9iamVjdCIsImFzc2lnbiIsImdldEJ5VGVzdElkIiwiZ2V0QnlUZXh0IiwiZXhwZWN0IiwidG9CZVRydXRoeSIsIl9yZW5kZXIyIiwidmFyaWFudCIsIm9uUHJlc3NNb2NrIiwiX3JlbmRlcjMiLCJmaXJlRXZlbnQiLCJwcmVzcyIsInRvSGF2ZUJlZW5DYWxsZWRUaW1lcyIsIl9yZW5kZXI0IiwiZGlzYWJsZWQiLCJub3QiLCJ0b0hhdmVCZWVuQ2FsbGVkIiwiX3JlbmRlcjUiLCJhY2Nlc3NpYmlsaXR5TGFiZWwiLCJidXR0b24iLCJwcm9wcyIsImFjY2Vzc2liaWxpdHlSb2xlIiwidG9CZSJdLCJzb3VyY2VzIjpbIkFuaW1hdGVkQnV0dG9uLnRlc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQW5pbWF0ZWRCdXR0b24gQ29tcG9uZW50IFRlc3RzXG4gKlxuICogQmFzaWMgdGVzdCBzdWl0ZSBmb3IgdGhlIEFuaW1hdGVkQnV0dG9uIGNvbXBvbmVudC5cbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyByZW5kZXIsIGZpcmVFdmVudCB9IGZyb20gJ0B0ZXN0aW5nLWxpYnJhcnkvcmVhY3QtbmF0aXZlJztcbmltcG9ydCB7IEFuaW1hdGVkQnV0dG9uIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9hbmltYXRpb24vQW5pbWF0ZWRCdXR0b24nO1xuXG4vLyBNb2NrIHByb3ZpZGVyc1xuY29uc3QgVGVzdFdyYXBwZXI6IFJlYWN0LkZDPHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9PiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPjtcbn07XG5cbmRlc2NyaWJlKCdBbmltYXRlZEJ1dHRvbicsICgpID0+IHtcbiAgY29uc3QgZGVmYXVsdFByb3BzID0ge1xuICAgIHRpdGxlOiAnVGVzdCBCdXR0b24nLFxuICAgIG9uUHJlc3M6IGplc3QuZm4oKSxcbiAgICB0ZXN0SUQ6ICdhbmltYXRlZC1idXR0b24nLFxuICB9O1xuXG4gIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpO1xuICB9KTtcblxuICBkZXNjcmliZSgnUmVuZGVyaW5nJywgKCkgPT4ge1xuICAgIGl0KCdyZW5kZXJzIGNvcnJlY3RseSB3aXRoIGRlZmF1bHQgcHJvcHMnLCAoKSA9PiB7XG4gICAgICBjb25zdCB7IGdldEJ5VGVzdElkLCBnZXRCeVRleHQgfSA9IHJlbmRlcihcbiAgICAgICAgPFRlc3RXcmFwcGVyPlxuICAgICAgICAgIDxBbmltYXRlZEJ1dHRvbiB7Li4uZGVmYXVsdFByb3BzfSAvPlxuICAgICAgICA8L1Rlc3RXcmFwcGVyPlxuICAgICAgKTtcblxuICAgICAgZXhwZWN0KGdldEJ5VGVzdElkKCdhbmltYXRlZC1idXR0b24nKSkudG9CZVRydXRoeSgpO1xuICAgICAgZXhwZWN0KGdldEJ5VGV4dCgnVGVzdCBCdXR0b24nKSkudG9CZVRydXRoeSgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3JlbmRlcnMgd2l0aCBkaWZmZXJlbnQgdmFyaWFudHMnLCAoKSA9PiB7XG4gICAgICBjb25zdCB7IGdldEJ5VGVzdElkIH0gPSByZW5kZXIoXG4gICAgICAgIDxUZXN0V3JhcHBlcj5cbiAgICAgICAgICA8QW5pbWF0ZWRCdXR0b25cbiAgICAgICAgICAgIHsuLi5kZWZhdWx0UHJvcHN9XG4gICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXG4gICAgICAgICAgICB0ZXN0SUQ9XCJidXR0b24tcHJpbWFyeVwiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9UZXN0V3JhcHBlcj5cbiAgICAgICk7XG5cbiAgICAgIGV4cGVjdChnZXRCeVRlc3RJZCgnYnV0dG9uLXByaW1hcnknKSkudG9CZVRydXRoeSgpO1xuICAgIH0pO1xuXG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdGdW5jdGlvbmFsaXR5JywgKCkgPT4ge1xuICAgIGl0KCdjYWxscyBvblByZXNzIHdoZW4gcHJlc3NlZCcsICgpID0+IHtcbiAgICAgIGNvbnN0IG9uUHJlc3NNb2NrID0gamVzdC5mbigpO1xuICAgICAgY29uc3QgeyBnZXRCeVRlc3RJZCB9ID0gcmVuZGVyKFxuICAgICAgICA8VGVzdFdyYXBwZXI+XG4gICAgICAgICAgPEFuaW1hdGVkQnV0dG9uXG4gICAgICAgICAgICB7Li4uZGVmYXVsdFByb3BzfVxuICAgICAgICAgICAgb25QcmVzcz17b25QcmVzc01vY2t9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9UZXN0V3JhcHBlcj5cbiAgICAgICk7XG5cbiAgICAgIGZpcmVFdmVudC5wcmVzcyhnZXRCeVRlc3RJZCgnYW5pbWF0ZWQtYnV0dG9uJykpO1xuICAgICAgZXhwZWN0KG9uUHJlc3NNb2NrKS50b0hhdmVCZWVuQ2FsbGVkVGltZXMoMSk7XG4gICAgfSk7XG5cbiAgICBpdCgnZG9lcyBub3QgY2FsbCBvblByZXNzIHdoZW4gZGlzYWJsZWQnLCAoKSA9PiB7XG4gICAgICBjb25zdCBvblByZXNzTW9jayA9IGplc3QuZm4oKTtcbiAgICAgIGNvbnN0IHsgZ2V0QnlUZXN0SWQgfSA9IHJlbmRlcihcbiAgICAgICAgPFRlc3RXcmFwcGVyPlxuICAgICAgICAgIDxBbmltYXRlZEJ1dHRvblxuICAgICAgICAgICAgey4uLmRlZmF1bHRQcm9wc31cbiAgICAgICAgICAgIG9uUHJlc3M9e29uUHJlc3NNb2NrfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e3RydWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9UZXN0V3JhcHBlcj5cbiAgICAgICk7XG5cbiAgICAgIGZpcmVFdmVudC5wcmVzcyhnZXRCeVRlc3RJZCgnYW5pbWF0ZWQtYnV0dG9uJykpO1xuICAgICAgZXhwZWN0KG9uUHJlc3NNb2NrKS5ub3QudG9IYXZlQmVlbkNhbGxlZCgpO1xuICAgIH0pO1xuXG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdBY2Nlc3NpYmlsaXR5JywgKCkgPT4ge1xuICAgIGl0KCdoYXMgcHJvcGVyIGFjY2Vzc2liaWxpdHkgcHJvcGVydGllcycsICgpID0+IHtcbiAgICAgIGNvbnN0IHsgZ2V0QnlUZXN0SWQgfSA9IHJlbmRlcihcbiAgICAgICAgPFRlc3RXcmFwcGVyPlxuICAgICAgICAgIDxBbmltYXRlZEJ1dHRvblxuICAgICAgICAgICAgey4uLmRlZmF1bHRQcm9wc31cbiAgICAgICAgICAgIGFjY2Vzc2liaWxpdHlMYWJlbD1cIkN1c3RvbSBhY2Nlc3NpYmlsaXR5IGxhYmVsXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L1Rlc3RXcmFwcGVyPlxuICAgICAgKTtcblxuICAgICAgY29uc3QgYnV0dG9uID0gZ2V0QnlUZXN0SWQoJ2FuaW1hdGVkLWJ1dHRvbicpO1xuICAgICAgZXhwZWN0KGJ1dHRvbi5wcm9wcy5hY2Nlc3NpYmlsaXR5Um9sZSkudG9CZSgnYnV0dG9uJyk7XG4gICAgfSk7XG4gIH0pO1xufSk7XG4iXSwibWFwcGluZ3MiOiI7QUFTQSxJQUFBQSxNQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxZQUFBLEdBQUFELE9BQUE7QUFDQSxJQUFBRSxlQUFBLEdBQUFGLE9BQUE7QUFBMkUsSUFBQUcsV0FBQSxHQUFBSCxPQUFBO0FBRzNFLElBQU1JLFdBQW9ELEdBQUcsU0FBdkRBLFdBQW9EQSxDQUFBQyxJQUFBLEVBQXFCO0VBQUEsSUFBZkMsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7RUFDdEUsT0FBTyxJQUFBSCxXQUFBLENBQUFJLEdBQUEsRUFBQUosV0FBQSxDQUFBSyxRQUFBO0lBQUFGLFFBQUEsRUFBR0E7RUFBUSxDQUFHLENBQUM7QUFDeEIsQ0FBQztBQUVERyxRQUFRLENBQUMsZ0JBQWdCLEVBQUUsWUFBTTtFQUMvQixJQUFNQyxZQUFZLEdBQUc7SUFDbkJDLEtBQUssRUFBRSxhQUFhO0lBQ3BCQyxPQUFPLEVBQUVDLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7SUFDbEJDLE1BQU0sRUFBRTtFQUNWLENBQUM7RUFFREMsVUFBVSxDQUFDLFlBQU07SUFDZkgsSUFBSSxDQUFDSSxhQUFhLENBQUMsQ0FBQztFQUN0QixDQUFDLENBQUM7RUFFRlIsUUFBUSxDQUFDLFdBQVcsRUFBRSxZQUFNO0lBQzFCUyxFQUFFLENBQUMsc0NBQXNDLEVBQUUsWUFBTTtNQUMvQyxJQUFBQyxPQUFBLEdBQW1DLElBQUFDLG1CQUFNLEVBQ3ZDLElBQUFqQixXQUFBLENBQUFJLEdBQUEsRUFBQ0gsV0FBVztVQUFBRSxRQUFBLEVBQ1YsSUFBQUgsV0FBQSxDQUFBSSxHQUFBLEVBQUNMLGVBQUEsQ0FBQW1CLGNBQWMsRUFBQUMsTUFBQSxDQUFBQyxNQUFBLEtBQUtiLFlBQVksQ0FBRztRQUFDLENBQ3pCLENBQ2YsQ0FBQztRQUpPYyxXQUFXLEdBQUFMLE9BQUEsQ0FBWEssV0FBVztRQUFFQyxTQUFTLEdBQUFOLE9BQUEsQ0FBVE0sU0FBUztNQU05QkMsTUFBTSxDQUFDRixXQUFXLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDRyxVQUFVLENBQUMsQ0FBQztNQUNuREQsTUFBTSxDQUFDRCxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQ0UsVUFBVSxDQUFDLENBQUM7SUFDL0MsQ0FBQyxDQUFDO0lBRUZULEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxZQUFNO01BQzFDLElBQUFVLFFBQUEsR0FBd0IsSUFBQVIsbUJBQU0sRUFDNUIsSUFBQWpCLFdBQUEsQ0FBQUksR0FBQSxFQUFDSCxXQUFXO1VBQUFFLFFBQUEsRUFDVixJQUFBSCxXQUFBLENBQUFJLEdBQUEsRUFBQ0wsZUFBQSxDQUFBbUIsY0FBYyxFQUFBQyxNQUFBLENBQUFDLE1BQUEsS0FDVGIsWUFBWTtZQUNoQm1CLE9BQU8sRUFBQyxTQUFTO1lBQ2pCZCxNQUFNLEVBQUM7VUFBZ0IsRUFDeEI7UUFBQyxDQUNTLENBQ2YsQ0FBQztRQVJPUyxXQUFXLEdBQUFJLFFBQUEsQ0FBWEosV0FBVztNQVVuQkUsTUFBTSxDQUFDRixXQUFXLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDRyxVQUFVLENBQUMsQ0FBQztJQUNwRCxDQUFDLENBQUM7RUFFSixDQUFDLENBQUM7RUFFRmxCLFFBQVEsQ0FBQyxlQUFlLEVBQUUsWUFBTTtJQUM5QlMsRUFBRSxDQUFDLDRCQUE0QixFQUFFLFlBQU07TUFDckMsSUFBTVksV0FBVyxHQUFHakIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztNQUM3QixJQUFBaUIsUUFBQSxHQUF3QixJQUFBWCxtQkFBTSxFQUM1QixJQUFBakIsV0FBQSxDQUFBSSxHQUFBLEVBQUNILFdBQVc7VUFBQUUsUUFBQSxFQUNWLElBQUFILFdBQUEsQ0FBQUksR0FBQSxFQUFDTCxlQUFBLENBQUFtQixjQUFjLEVBQUFDLE1BQUEsQ0FBQUMsTUFBQSxLQUNUYixZQUFZO1lBQ2hCRSxPQUFPLEVBQUVrQjtVQUFZLEVBQ3RCO1FBQUMsQ0FDUyxDQUNmLENBQUM7UUFQT04sV0FBVyxHQUFBTyxRQUFBLENBQVhQLFdBQVc7TUFTbkJRLHNCQUFTLENBQUNDLEtBQUssQ0FBQ1QsV0FBVyxDQUFDLGlCQUFpQixDQUFDLENBQUM7TUFDL0NFLE1BQU0sQ0FBQ0ksV0FBVyxDQUFDLENBQUNJLHFCQUFxQixDQUFDLENBQUMsQ0FBQztJQUM5QyxDQUFDLENBQUM7SUFFRmhCLEVBQUUsQ0FBQyxxQ0FBcUMsRUFBRSxZQUFNO01BQzlDLElBQU1ZLFdBQVcsR0FBR2pCLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7TUFDN0IsSUFBQXFCLFFBQUEsR0FBd0IsSUFBQWYsbUJBQU0sRUFDNUIsSUFBQWpCLFdBQUEsQ0FBQUksR0FBQSxFQUFDSCxXQUFXO1VBQUFFLFFBQUEsRUFDVixJQUFBSCxXQUFBLENBQUFJLEdBQUEsRUFBQ0wsZUFBQSxDQUFBbUIsY0FBYyxFQUFBQyxNQUFBLENBQUFDLE1BQUEsS0FDVGIsWUFBWTtZQUNoQkUsT0FBTyxFQUFFa0IsV0FBWTtZQUNyQk0sUUFBUSxFQUFFO1VBQUssRUFDaEI7UUFBQyxDQUNTLENBQ2YsQ0FBQztRQVJPWixXQUFXLEdBQUFXLFFBQUEsQ0FBWFgsV0FBVztNQVVuQlEsc0JBQVMsQ0FBQ0MsS0FBSyxDQUFDVCxXQUFXLENBQUMsaUJBQWlCLENBQUMsQ0FBQztNQUMvQ0UsTUFBTSxDQUFDSSxXQUFXLENBQUMsQ0FBQ08sR0FBRyxDQUFDQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQzVDLENBQUMsQ0FBQztFQUVKLENBQUMsQ0FBQztFQUVGN0IsUUFBUSxDQUFDLGVBQWUsRUFBRSxZQUFNO0lBQzlCUyxFQUFFLENBQUMscUNBQXFDLEVBQUUsWUFBTTtNQUM5QyxJQUFBcUIsUUFBQSxHQUF3QixJQUFBbkIsbUJBQU0sRUFDNUIsSUFBQWpCLFdBQUEsQ0FBQUksR0FBQSxFQUFDSCxXQUFXO1VBQUFFLFFBQUEsRUFDVixJQUFBSCxXQUFBLENBQUFJLEdBQUEsRUFBQ0wsZUFBQSxDQUFBbUIsY0FBYyxFQUFBQyxNQUFBLENBQUFDLE1BQUEsS0FDVGIsWUFBWTtZQUNoQjhCLGtCQUFrQixFQUFDO1VBQTRCLEVBQ2hEO1FBQUMsQ0FDUyxDQUNmLENBQUM7UUFQT2hCLFdBQVcsR0FBQWUsUUFBQSxDQUFYZixXQUFXO01BU25CLElBQU1pQixNQUFNLEdBQUdqQixXQUFXLENBQUMsaUJBQWlCLENBQUM7TUFDN0NFLE1BQU0sQ0FBQ2UsTUFBTSxDQUFDQyxLQUFLLENBQUNDLGlCQUFpQixDQUFDLENBQUNDLElBQUksQ0FBQyxRQUFRLENBQUM7SUFDdkQsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxDQUFDO0FBQ0osQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119