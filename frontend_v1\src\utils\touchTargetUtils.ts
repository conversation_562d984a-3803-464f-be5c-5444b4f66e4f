/**
 * Touch Target Utilities
 *
 * Utilities for ensuring proper touch target sizes and motor accessibility
 * compliance following WCAG 2.2 AA guidelines and platform best practices.
 *
 * Features:
 * - Touch target size validation
 * - Motor accessibility helpers
 * - Gesture recognition utilities
 * - Adaptive touch areas
 * - Accessibility measurements
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Dimensions, Platform } from 'react-native';

// Touch target size constants (in dp/pt)
export const TOUCH_TARGET_SIZES = {
  // WCAG 2.2 AA minimum
  MINIMUM: 44,
  
  // Recommended comfortable sizes
  COMFORTABLE: 48,
  LARGE: 56,
  EXTRA_LARGE: 64,
  
  // Platform-specific recommendations
  IOS_MINIMUM: 44,
  ANDROID_MINIMUM: 48,
  
  // Context-specific sizes
  ICON_BUTTON: 44,
  TEXT_BUTTON: 44,
  FAB: 56,
  NAVIGATION_ITEM: 48,
  FORM_INPUT: 44,
} as const;

// Touch target spacing constants
export const TOUCH_TARGET_SPACING = {
  MINIMUM: 8,
  COMFORTABLE: 12,
  LARGE: 16,
} as const;

// Motor accessibility difficulty levels
export type MotorDifficultyLevel = 'none' | 'mild' | 'moderate' | 'severe';

// Touch target configuration
export interface TouchTargetConfig {
  minSize: number;
  preferredSize: number;
  spacing: number;
  adaptiveSize: boolean;
  motorAccessibility: MotorDifficultyLevel;
}

// Touch area interface
export interface TouchArea {
  width: number;
  height: number;
  paddingHorizontal?: number;
  paddingVertical?: number;
  minTouchableArea?: number;
}

/**
 * Get platform-specific minimum touch target size
 */
export const getPlatformMinimumTouchTarget = (): number => {
  return Platform.select({
    ios: TOUCH_TARGET_SIZES.IOS_MINIMUM,
    android: TOUCH_TARGET_SIZES.ANDROID_MINIMUM,
    default: TOUCH_TARGET_SIZES.MINIMUM,
  });
};

/**
 * Get recommended touch target size based on motor accessibility level
 */
export const getRecommendedTouchTargetSize = (
  motorLevel: MotorDifficultyLevel = 'none'
): number => {
  switch (motorLevel) {
    case 'none':
      return TOUCH_TARGET_SIZES.COMFORTABLE;
    case 'mild':
      return TOUCH_TARGET_SIZES.LARGE;
    case 'moderate':
      return TOUCH_TARGET_SIZES.EXTRA_LARGE;
    case 'severe':
      return TOUCH_TARGET_SIZES.EXTRA_LARGE + 8;
    default:
      return TOUCH_TARGET_SIZES.COMFORTABLE;
  }
};

/**
 * Get recommended spacing between touch targets
 */
export const getRecommendedSpacing = (
  motorLevel: MotorDifficultyLevel = 'none'
): number => {
  switch (motorLevel) {
    case 'none':
      return TOUCH_TARGET_SPACING.MINIMUM;
    case 'mild':
      return TOUCH_TARGET_SPACING.COMFORTABLE;
    case 'moderate':
    case 'severe':
      return TOUCH_TARGET_SPACING.LARGE;
    default:
      return TOUCH_TARGET_SPACING.MINIMUM;
  }
};

/**
 * Validate if a touch target meets accessibility requirements
 */
export const validateTouchTarget = (
  width: number,
  height: number,
  motorLevel: MotorDifficultyLevel = 'none'
): {
  valid: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  const minSize = getPlatformMinimumTouchTarget();
  const recommendedSize = getRecommendedTouchTargetSize(motorLevel);
  
  // Check minimum size requirements
  if (width < minSize || height < minSize) {
    issues.push(`Touch target too small: ${width}x${height}px (minimum: ${minSize}x${minSize}px)`);
    recommendations.push(`Increase touch target size to at least ${minSize}x${minSize}px`);
  }
  
  // Check recommended size for motor accessibility
  if (motorLevel !== 'none' && (width < recommendedSize || height < recommendedSize)) {
    recommendations.push(
      `For ${motorLevel} motor difficulty, consider increasing to ${recommendedSize}x${recommendedSize}px`
    );
  }
  
  // Check aspect ratio
  const aspectRatio = Math.max(width, height) / Math.min(width, height);
  if (aspectRatio > 3) {
    recommendations.push('Consider more balanced aspect ratio for better usability');
  }
  
  return {
    valid: issues.length === 0,
    issues,
    recommendations,
  };
};

/**
 * Calculate optimal touch area with padding
 */
export const calculateTouchArea = (
  contentWidth: number,
  contentHeight: number,
  motorLevel: MotorDifficultyLevel = 'none'
): TouchArea => {
  const minTouchSize = getRecommendedTouchTargetSize(motorLevel);
  
  // Calculate required padding to meet minimum touch target
  const paddingHorizontal = Math.max(0, (minTouchSize - contentWidth) / 2);
  const paddingVertical = Math.max(0, (minTouchSize - contentHeight) / 2);
  
  return {
    width: Math.max(contentWidth, minTouchSize),
    height: Math.max(contentHeight, minTouchSize),
    paddingHorizontal,
    paddingVertical,
    minTouchableArea: minTouchSize,
  };
};

/**
 * Generate touch target styles for React Native components
 */
export const generateTouchTargetStyles = (
  contentSize: { width?: number; height?: number } = {},
  motorLevel: MotorDifficultyLevel = 'none'
) => {
  const minSize = getRecommendedTouchTargetSize(motorLevel);
  const spacing = getRecommendedSpacing(motorLevel);
  
  return {
    minWidth: contentSize.width ? Math.max(contentSize.width, minSize) : minSize,
    minHeight: contentSize.height ? Math.max(contentSize.height, minSize) : minSize,
    paddingHorizontal: contentSize.width ? Math.max(0, (minSize - contentSize.width) / 2) : 0,
    paddingVertical: contentSize.height ? Math.max(0, (minSize - contentSize.height) / 2) : 0,
    marginHorizontal: spacing / 2,
    marginVertical: spacing / 2,
  };
};

/**
 * Check if device supports motor accessibility features
 */
export const getMotorAccessibilitySupport = () => {
  return {
    // Platform capabilities
    supportsHapticFeedback: Platform.OS === 'ios' || Platform.OS === 'android',
    supportsVoiceControl: Platform.OS === 'ios',
    supportsSwitchControl: Platform.OS === 'ios',
    supportsAssistiveTouch: Platform.OS === 'ios',
    
    // Screen size considerations
    screenSize: Dimensions.get('window'),
    isLargeScreen: Dimensions.get('window').width > 768,
    
    // Recommended adaptations
    recommendLargerTargets: true,
    recommendHapticFeedback: true,
    recommendVoiceAlternatives: true,
  };
};

/**
 * Calculate adaptive touch target size based on user interaction patterns
 */
export const calculateAdaptiveTouchTargetSize = (
  baseSize: number,
  userInteractionData: {
    missRate?: number;
    averageAccuracy?: number;
    preferredSize?: number;
  } = {}
): number => {
  let adaptedSize = baseSize;
  
  // Increase size based on miss rate
  if (userInteractionData.missRate && userInteractionData.missRate > 0.1) {
    adaptedSize += Math.min(16, userInteractionData.missRate * 80);
  }
  
  // Adjust based on accuracy
  if (userInteractionData.averageAccuracy && userInteractionData.averageAccuracy < 0.8) {
    adaptedSize += Math.min(12, (1 - userInteractionData.averageAccuracy) * 60);
  }
  
  // Use preferred size if available
  if (userInteractionData.preferredSize) {
    adaptedSize = Math.max(adaptedSize, userInteractionData.preferredSize);
  }
  
  // Ensure minimum requirements
  adaptedSize = Math.max(adaptedSize, getPlatformMinimumTouchTarget());
  
  return Math.round(adaptedSize);
};

/**
 * Generate gesture-friendly touch area
 */
export const generateGestureFriendlyArea = (
  gestureType: 'tap' | 'swipe' | 'pinch' | 'long-press' | 'drag',
  motorLevel: MotorDifficultyLevel = 'none'
) => {
  const baseSize = getRecommendedTouchTargetSize(motorLevel);
  
  switch (gestureType) {
    case 'tap':
      return {
        width: baseSize,
        height: baseSize,
        hitSlop: { top: 8, bottom: 8, left: 8, right: 8 },
      };
    
    case 'swipe':
      return {
        width: baseSize * 1.5,
        height: baseSize,
        hitSlop: { top: 12, bottom: 12, left: 16, right: 16 },
      };
    
    case 'pinch':
      return {
        width: baseSize * 2,
        height: baseSize * 2,
        hitSlop: { top: 16, bottom: 16, left: 16, right: 16 },
      };
    
    case 'long-press':
      return {
        width: baseSize * 1.2,
        height: baseSize * 1.2,
        hitSlop: { top: 10, bottom: 10, left: 10, right: 10 },
      };
    
    case 'drag':
      return {
        width: baseSize * 1.3,
        height: baseSize * 1.3,
        hitSlop: { top: 12, bottom: 12, left: 12, right: 12 },
      };
    
    default:
      return {
        width: baseSize,
        height: baseSize,
        hitSlop: { top: 8, bottom: 8, left: 8, right: 8 },
      };
  }
};

/**
 * Validate touch target spacing
 */
export const validateTouchTargetSpacing = (
  targets: Array<{ x: number; y: number; width: number; height: number }>,
  motorLevel: MotorDifficultyLevel = 'none'
): {
  valid: boolean;
  conflicts: Array<{ target1: number; target2: number; distance: number }>;
  recommendations: string[];
} => {
  const minSpacing = getRecommendedSpacing(motorLevel);
  const conflicts: Array<{ target1: number; target2: number; distance: number }> = [];
  const recommendations: string[] = [];
  
  for (let i = 0; i < targets.length; i++) {
    for (let j = i + 1; j < targets.length; j++) {
      const target1 = targets[i];
      const target2 = targets[j];
      
      // Calculate distance between target edges
      const horizontalDistance = Math.max(0, 
        Math.max(target1.x, target2.x) - Math.min(target1.x + target1.width, target2.x + target2.width)
      );
      const verticalDistance = Math.max(0,
        Math.max(target1.y, target2.y) - Math.min(target1.y + target1.height, target2.y + target2.height)
      );
      
      const distance = Math.sqrt(horizontalDistance ** 2 + verticalDistance ** 2);
      
      if (distance < minSpacing) {
        conflicts.push({ target1: i, target2: j, distance });
      }
    }
  }
  
  if (conflicts.length > 0) {
    recommendations.push(`Increase spacing between touch targets to at least ${minSpacing}px`);
    if (motorLevel !== 'none') {
      recommendations.push(`Consider additional spacing for ${motorLevel} motor accessibility`);
    }
  }
  
  return {
    valid: conflicts.length === 0,
    conflicts,
    recommendations,
  };
};

export default {
  TOUCH_TARGET_SIZES,
  TOUCH_TARGET_SPACING,
  getPlatformMinimumTouchTarget,
  getRecommendedTouchTargetSize,
  getRecommendedSpacing,
  validateTouchTarget,
  calculateTouchArea,
  generateTouchTargetStyles,
  getMotorAccessibilitySupport,
  calculateAdaptiveTouchTargetSize,
  generateGestureFriendlyArea,
  validateTouchTargetSpacing,
};
