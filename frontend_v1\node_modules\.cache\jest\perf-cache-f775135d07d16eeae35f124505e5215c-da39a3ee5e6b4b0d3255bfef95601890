{"C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\passwordlessAuthService.test.ts": [1, 197], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\testAccountsService.test.ts": [1, 160], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\enhancedTestingQA.test.ts": [1, 587], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\components\\AnimatedButton.test.tsx": [1, 402], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\imageAccessibilityValidation.test.ts": [1, 160], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\performance.test.ts": [1, 175], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\accessibility.test.ts": [1, 179], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\integration\\complete-implementation.test.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formAccessibilityValidation.test.ts": [1, 187], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\features\\authentication\\__tests__\\authSlice.test.ts": [1, 137], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formValidation.test.ts": [1, 121], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\focusIndicatorValidation.test.ts": [1, 371], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastValidation.test.ts": [1, 135], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\setup.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.test.ts": [1, 127], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastEnhancer.test.ts": [1, 127], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\store\\__tests__\\providersSlice.test.ts": [1, 1716], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\navigation\\__tests__\\types.test.ts": [1, 103], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\wcag-standards-fix.test.ts": [1, 104], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.integration.test.ts": [1, 102], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\jestSetup.js": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\errorHandler.test.ts": [1, 1530]}