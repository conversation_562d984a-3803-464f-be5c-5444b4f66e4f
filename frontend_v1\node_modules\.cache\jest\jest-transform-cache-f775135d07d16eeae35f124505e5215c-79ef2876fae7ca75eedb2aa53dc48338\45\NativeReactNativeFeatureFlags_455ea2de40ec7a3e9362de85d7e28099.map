{"version": 3, "names": ["TurboModuleRegistry", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeReactNativeFeatureFlags", "_default", "exports"], "sources": ["NativeReactNativeFeatureFlags.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @generated SignedSource<<2499cbe243c048d9e3647342ebec3544>>\n * @flow strict\n */\n\n/**\n * IMPORTANT: Do NOT modify this file directly.\n *\n * To change the definition of the flags, edit\n *   packages/react-native/scripts/featureflags/ReactNativeFeatureFlags.config.js.\n *\n * To regenerate this code, run the following script from the repo root:\n *   yarn featureflags --update\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\nexport interface Spec extends TurboModule {\n  +commonTestFlag?: () => boolean;\n  +commonTestFlagWithoutNativeImplementation?: () => boolean;\n  +disableMountItemReorderingAndroid?: () => boolean;\n  +enableAccumulatedUpdatesInRawPropsAndroid?: () => boolean;\n  +enableBridgelessArchitecture?: () => boolean;\n  +enableCppPropsIteratorSetter?: () => boolean;\n  +enableEagerRootViewAttachment?: () => boolean;\n  +enableFabricLogs?: () => boolean;\n  +enableFabricRenderer?: () => boolean;\n  +enableIOSViewClipToPaddingBox?: () => boolean;\n  +enableImagePrefetchingAndroid?: () => boolean;\n  +enableJSRuntimeGCOnMemoryPressureOnIOS?: () => boolean;\n  +enableLayoutAnimationsOnAndroid?: () => boolean;\n  +enableLayoutAnimationsOnIOS?: () => boolean;\n  +enableLongTaskAPI?: () => boolean;\n  +enableNativeCSSParsing?: () => boolean;\n  +enableNewBackgroundAndBorderDrawables?: () => boolean;\n  +enablePreciseSchedulingForPremountItemsOnAndroid?: () => boolean;\n  +enablePropsUpdateReconciliationAndroid?: () => boolean;\n  +enableReportEventPaintTime?: () => boolean;\n  +enableSynchronousStateUpdates?: () => boolean;\n  +enableUIConsistency?: () => boolean;\n  +enableViewCulling?: () => boolean;\n  +enableViewRecycling?: () => boolean;\n  +enableViewRecyclingForText?: () => boolean;\n  +enableViewRecyclingForView?: () => boolean;\n  +excludeYogaFromRawProps?: () => boolean;\n  +fixDifferentiatorEmittingUpdatesWithWrongParentTag?: () => boolean;\n  +fixMappingOfEventPrioritiesBetweenFabricAndReact?: () => boolean;\n  +fixMountingCoordinatorReportedPendingTransactionsOnAndroid?: () => boolean;\n  +fuseboxEnabledRelease?: () => boolean;\n  +fuseboxNetworkInspectionEnabled?: () => boolean;\n  +lazyAnimationCallbacks?: () => boolean;\n  +removeTurboModuleManagerDelegateMutex?: () => boolean;\n  +throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS?: () => boolean;\n  +traceTurboModulePromiseRejectionsOnAndroid?: () => boolean;\n  +updateRuntimeShadowNodeReferencesOnCommit?: () => boolean;\n  +useAlwaysAvailableJSErrorHandling?: () => boolean;\n  +useFabricInterop?: () => boolean;\n  +useNativeViewConfigsInBridgelessMode?: () => boolean;\n  +useOptimizedEventBatchingOnAndroid?: () => boolean;\n  +useRawPropsJsiValue?: () => boolean;\n  +useShadowNodeStateOnClone?: () => boolean;\n  +useTurboModuleInterop?: () => boolean;\n  +useTurboModules?: () => boolean;\n}\n\nconst NativeReactNativeFeatureFlags: ?Spec = TurboModuleRegistry.get<Spec>(\n  'NativeReactNativeFeatureFlagsCxx',\n);\n\nexport default NativeReactNativeFeatureFlags;\n"], "mappings": ";;;;AAsBA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA6F,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAkD7F,IAAMmB,6BAAoC,GAAGvB,mBAAmB,CAACe,GAAG,CAClE,kCACF,CAAC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAAZ,OAAA,GAEaU,6BAA6B", "ignoreList": []}