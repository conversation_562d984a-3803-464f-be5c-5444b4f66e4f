{"version": 3, "names": ["_TextAncestor", "_interopRequireDefault", "require", "_ViewNativeComponent", "React", "_interopRequireWildcard", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "View", "forwardRef", "_ref", "forwardedRef", "_ariaLabelledBy$split", "accessibilityElementsHidden", "accessibilityLabel", "accessibilityLabelledBy", "accessibilityLiveRegion", "accessibilityState", "accessibilityValue", "ariaBusy", "ariaChe<PERSON>", "ariaDisabled", "ariaExpanded", "ariaHidden", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaLive", "ariaSelected", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "focusable", "id", "importantForAccessibility", "nativeID", "tabIndex", "otherProps", "_objectWithoutProperties2", "hasTextAncestor", "useContext", "TextAncestor", "_accessibilityLabelledBy", "split", "_accessibilityState", "busy", "checked", "disabled", "expanded", "selected", "_accessibilityValue", "max", "min", "now", "text", "actualView", "jsx", "assign", "undefined", "ref", "Provider", "value", "children", "displayName", "_default", "exports"], "sources": ["View.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {ViewProps} from './ViewPropTypes';\n\nimport TextAncestor from '../../Text/TextAncestor';\nimport ViewNativeComponent from './ViewNativeComponent';\nimport * as React from 'react';\n\nexport type Props = ViewProps;\n\n/**\n * The most fundamental component for building a UI, View is a container that\n * supports layout with flexbox, style, some touch handling, and accessibility\n * controls.\n *\n * @see https://reactnative.dev/docs/view\n */\nconst View: component(\n  ref?: React.RefSetter<React.ElementRef<typeof ViewNativeComponent>>,\n  ...props: ViewProps\n) = React.forwardRef(\n  (\n    {\n      accessibilityElementsHidden,\n      accessibilityLabel,\n      accessibilityLabelledBy,\n      accessibilityLiveRegion,\n      accessibilityState,\n      accessibilityValue,\n      'aria-busy': ariaBusy,\n      'aria-checked': ariaChecked,\n      'aria-disabled': ariaDisabled,\n      'aria-expanded': ariaExpanded,\n      'aria-hidden': ariaHidden,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      'aria-live': ariaLive,\n      'aria-selected': ariaSelected,\n      'aria-valuemax': ariaValueMax,\n      'aria-valuemin': ariaValueMin,\n      'aria-valuenow': ariaValueNow,\n      'aria-valuetext': ariaValueText,\n      focusable,\n      id,\n      importantForAccessibility,\n      nativeID,\n      tabIndex,\n      ...otherProps\n    }: ViewProps,\n    forwardedRef,\n  ) => {\n    const hasTextAncestor = React.useContext(TextAncestor);\n    const _accessibilityLabelledBy =\n      ariaLabelledBy?.split(/\\s*,\\s*/g) ?? accessibilityLabelledBy;\n\n    let _accessibilityState;\n    if (\n      accessibilityState != null ||\n      ariaBusy != null ||\n      ariaChecked != null ||\n      ariaDisabled != null ||\n      ariaExpanded != null ||\n      ariaSelected != null\n    ) {\n      _accessibilityState = {\n        busy: ariaBusy ?? accessibilityState?.busy,\n        checked: ariaChecked ?? accessibilityState?.checked,\n        disabled: ariaDisabled ?? accessibilityState?.disabled,\n        expanded: ariaExpanded ?? accessibilityState?.expanded,\n        selected: ariaSelected ?? accessibilityState?.selected,\n      };\n    }\n    let _accessibilityValue;\n    if (\n      accessibilityValue != null ||\n      ariaValueMax != null ||\n      ariaValueMin != null ||\n      ariaValueNow != null ||\n      ariaValueText != null\n    ) {\n      _accessibilityValue = {\n        max: ariaValueMax ?? accessibilityValue?.max,\n        min: ariaValueMin ?? accessibilityValue?.min,\n        now: ariaValueNow ?? accessibilityValue?.now,\n        text: ariaValueText ?? accessibilityValue?.text,\n      };\n    }\n\n    const actualView = (\n      <ViewNativeComponent\n        {...otherProps}\n        accessibilityLiveRegion={\n          ariaLive === 'off' ? 'none' : ariaLive ?? accessibilityLiveRegion\n        }\n        accessibilityLabel={ariaLabel ?? accessibilityLabel}\n        focusable={tabIndex !== undefined ? !tabIndex : focusable}\n        accessibilityState={_accessibilityState}\n        accessibilityElementsHidden={ariaHidden ?? accessibilityElementsHidden}\n        accessibilityLabelledBy={_accessibilityLabelledBy}\n        accessibilityValue={_accessibilityValue}\n        importantForAccessibility={\n          ariaHidden === true\n            ? 'no-hide-descendants'\n            : importantForAccessibility\n        }\n        nativeID={id ?? nativeID}\n        ref={forwardedRef}\n      />\n    );\n\n    if (hasTextAncestor) {\n      return (\n        <TextAncestor.Provider value={false}>\n          {actualView}\n        </TextAncestor.Provider>\n      );\n    }\n\n    return actualView;\n  },\n);\n\nView.displayName = 'View';\n\nexport default View;\n"], "mappings": ";;;;;;AAYA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAC,uBAAA,CAAAH,OAAA;AAA+B,IAAAI,WAAA,GAAAJ,OAAA;AAAA,IAAAK,SAAA;AAAA,SAAAF,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAW/B,IAAMmB,IAGL,GAAGxB,KAAK,CAACyB,UAAU,CAClB,UAAAC,IAAA,EA4BEC,YAAY,EACT;EAAA,IAAAC,qBAAA;EAAA,IA3BDC,2BAA2B,GAAAH,IAAA,CAA3BG,2BAA2B;IAC3BC,kBAAkB,GAAAJ,IAAA,CAAlBI,kBAAkB;IAClBC,uBAAuB,GAAAL,IAAA,CAAvBK,uBAAuB;IACvBC,uBAAuB,GAAAN,IAAA,CAAvBM,uBAAuB;IACvBC,kBAAkB,GAAAP,IAAA,CAAlBO,kBAAkB;IAClBC,kBAAkB,GAAAR,IAAA,CAAlBQ,kBAAkB;IACLC,QAAQ,GAAAT,IAAA,CAArB,WAAW;IACKU,WAAW,GAAAV,IAAA,CAA3B,cAAc;IACGW,YAAY,GAAAX,IAAA,CAA7B,eAAe;IACEY,YAAY,GAAAZ,IAAA,CAA7B,eAAe;IACAa,UAAU,GAAAb,IAAA,CAAzB,aAAa;IACCc,SAAS,GAAAd,IAAA,CAAvB,YAAY;IACOe,cAAc,GAAAf,IAAA,CAAjC,iBAAiB;IACJgB,QAAQ,GAAAhB,IAAA,CAArB,WAAW;IACMiB,YAAY,GAAAjB,IAAA,CAA7B,eAAe;IACEkB,YAAY,GAAAlB,IAAA,CAA7B,eAAe;IACEmB,YAAY,GAAAnB,IAAA,CAA7B,eAAe;IACEoB,YAAY,GAAApB,IAAA,CAA7B,eAAe;IACGqB,aAAa,GAAArB,IAAA,CAA/B,gBAAgB;IAChBsB,SAAS,GAAAtB,IAAA,CAATsB,SAAS;IACTC,EAAE,GAAAvB,IAAA,CAAFuB,EAAE;IACFC,yBAAyB,GAAAxB,IAAA,CAAzBwB,yBAAyB;IACzBC,QAAQ,GAAAzB,IAAA,CAARyB,QAAQ;IACRC,QAAQ,GAAA1B,IAAA,CAAR0B,QAAQ;IACLC,UAAU,OAAAC,yBAAA,CAAAxC,OAAA,EAAAY,IAAA,EAAAvB,SAAA;EAIf,IAAMoD,eAAe,GAAGvD,KAAK,CAACwD,UAAU,CAACC,qBAAY,CAAC;EACtD,IAAMC,wBAAwB,IAAA9B,qBAAA,GAC5Ba,cAAc,oBAAdA,cAAc,CAAEkB,KAAK,CAAC,UAAU,CAAC,YAAA/B,qBAAA,GAAIG,uBAAuB;EAE9D,IAAI6B,mBAAmB;EACvB,IACE3B,kBAAkB,IAAI,IAAI,IAC1BE,QAAQ,IAAI,IAAI,IAChBC,WAAW,IAAI,IAAI,IACnBC,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,IACpBK,YAAY,IAAI,IAAI,EACpB;IACAiB,mBAAmB,GAAG;MACpBC,IAAI,EAAE1B,QAAQ,WAARA,QAAQ,GAAIF,kBAAkB,oBAAlBA,kBAAkB,CAAE4B,IAAI;MAC1CC,OAAO,EAAE1B,WAAW,WAAXA,WAAW,GAAIH,kBAAkB,oBAAlBA,kBAAkB,CAAE6B,OAAO;MACnDC,QAAQ,EAAE1B,YAAY,WAAZA,YAAY,GAAIJ,kBAAkB,oBAAlBA,kBAAkB,CAAE8B,QAAQ;MACtDC,QAAQ,EAAE1B,YAAY,WAAZA,YAAY,GAAIL,kBAAkB,oBAAlBA,kBAAkB,CAAE+B,QAAQ;MACtDC,QAAQ,EAAEtB,YAAY,WAAZA,YAAY,GAAIV,kBAAkB,oBAAlBA,kBAAkB,CAAEgC;IAChD,CAAC;EACH;EACA,IAAIC,mBAAmB;EACvB,IACEhC,kBAAkB,IAAI,IAAI,IAC1BU,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,IACpBC,aAAa,IAAI,IAAI,EACrB;IACAmB,mBAAmB,GAAG;MACpBC,GAAG,EAAEvB,YAAY,WAAZA,YAAY,GAAIV,kBAAkB,oBAAlBA,kBAAkB,CAAEiC,GAAG;MAC5CC,GAAG,EAAEvB,YAAY,WAAZA,YAAY,GAAIX,kBAAkB,oBAAlBA,kBAAkB,CAAEkC,GAAG;MAC5CC,GAAG,EAAEvB,YAAY,WAAZA,YAAY,GAAIZ,kBAAkB,oBAAlBA,kBAAkB,CAAEmC,GAAG;MAC5CC,IAAI,EAAEvB,aAAa,WAAbA,aAAa,GAAIb,kBAAkB,oBAAlBA,kBAAkB,CAAEoC;IAC7C,CAAC;EACH;EAEA,IAAMC,UAAU,GACd,IAAArE,WAAA,CAAAsE,GAAA,EAACzE,oBAAA,CAAAe,OAAmB,EAAAO,MAAA,CAAAoD,MAAA,KACdpB,UAAU;IACdrB,uBAAuB,EACrBU,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAGA,QAAQ,WAARA,QAAQ,GAAIV,uBAC3C;IACDF,kBAAkB,EAAEU,SAAS,WAATA,SAAS,GAAIV,kBAAmB;IACpDkB,SAAS,EAAEI,QAAQ,KAAKsB,SAAS,GAAG,CAACtB,QAAQ,GAAGJ,SAAU;IAC1Df,kBAAkB,EAAE2B,mBAAoB;IACxC/B,2BAA2B,EAAEU,UAAU,WAAVA,UAAU,GAAIV,2BAA4B;IACvEE,uBAAuB,EAAE2B,wBAAyB;IAClDxB,kBAAkB,EAAEgC,mBAAoB;IACxChB,yBAAyB,EACvBX,UAAU,KAAK,IAAI,GACf,qBAAqB,GACrBW,yBACL;IACDC,QAAQ,EAAEF,EAAE,WAAFA,EAAE,GAAIE,QAAS;IACzBwB,GAAG,EAAEhD;EAAa,EACnB,CACF;EAED,IAAI4B,eAAe,EAAE;IACnB,OACE,IAAArD,WAAA,CAAAsE,GAAA,EAAC5E,aAAA,CAAAkB,OAAY,CAAC8D,QAAQ;MAACC,KAAK,EAAE,KAAM;MAAAC,QAAA,EACjCP;IAAU,CACU,CAAC;EAE5B;EAEA,OAAOA,UAAU;AACnB,CACF,CAAC;AAED/C,IAAI,CAACuD,WAAW,GAAG,MAAM;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAnE,OAAA,GAEXU,IAAI", "ignoreList": []}