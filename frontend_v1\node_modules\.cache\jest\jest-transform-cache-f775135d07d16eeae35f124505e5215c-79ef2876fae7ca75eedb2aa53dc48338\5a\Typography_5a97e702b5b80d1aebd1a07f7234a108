fb4f51291b8d6fbb91a5eed877f384c3
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isTextSizeAccessible = exports.getPlatformAdjustedSize = exports.getOptimalLineHeight = exports.getAccessibleFontSize = exports.default = exports.TYPOGRAPHY_VARIANTS = exports.READING_WIDTH = exports.LINE_HEIGHTS = exports.LETTER_SPACING = exports.FONT_WEIGHTS = exports.FONT_SIZES = exports.FONT_FAMILIES = exports.ACCESSIBILITY_FONT_SIZES = void 0;
var _reactNative = require("react-native");
var BASE_FONT_SIZE = 16;
var SCALE_RATIO = 1.25;
var LINE_HEIGHT_RATIO = 1.5;
var FONT_FAMILIES = exports.FONT_FAMILIES = {
  primary: _reactNative.Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    default: 'System'
  }),
  secondary: _reactNative.Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    default: 'System'
  }),
  monospace: _reactNative.Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    web: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
    default: 'monospace'
  })
};
var FONT_WEIGHTS = exports.FONT_WEIGHTS = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800'
};
var getResponsiveFontSize = function getResponsiveFontSize(size) {
  var scale = _reactNative.PixelRatio.getFontScale();
  return Math.round(size * scale);
};
var generateScale = function generateScale(base, ratio, steps) {
  var scale = [];
  for (var i = -2; i <= steps; i++) {
    scale.push(Math.round(base * Math.pow(ratio, i)));
  }
  return scale;
};
var SCALE = generateScale(BASE_FONT_SIZE, SCALE_RATIO, 6);
var FONT_SIZES = exports.FONT_SIZES = {
  xs: getResponsiveFontSize(SCALE[0]),
  sm: getResponsiveFontSize(SCALE[1]),
  base: getResponsiveFontSize(SCALE[2]),
  lg: getResponsiveFontSize(SCALE[3]),
  xl: getResponsiveFontSize(SCALE[4]),
  '2xl': getResponsiveFontSize(SCALE[5]),
  '3xl': getResponsiveFontSize(SCALE[6]),
  '4xl': getResponsiveFontSize(SCALE[7]),
  '5xl': getResponsiveFontSize(SCALE[8])
};
var LINE_HEIGHTS = exports.LINE_HEIGHTS = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75,
  loose: 2
};
var LETTER_SPACING = exports.LETTER_SPACING = {
  tighter: -0.05,
  tight: -0.025,
  normal: 0,
  wide: 0.025,
  wider: 0.05,
  widest: 0.1
};
var TYPOGRAPHY_VARIANTS = exports.TYPOGRAPHY_VARIANTS = {
  h1: {
    fontSize: FONT_SIZES['4xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['4xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight
  },
  h2: {
    fontSize: FONT_SIZES['3xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['3xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight
  },
  h3: {
    fontSize: FONT_SIZES['2xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES['2xl'] * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h4: {
    fontSize: FONT_SIZES.xl,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.xl * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h5: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h6: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  body1: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  body2: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  subtitle1: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  subtitle2: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  caption: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  overline: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.widest,
    textTransform: 'uppercase'
  },
  button: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  buttonSmall: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  buttonLarge: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  input: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  helper: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  navItem: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.normal
  },
  tabItem: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  code: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.monospace,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.relaxed,
    letterSpacing: LETTER_SPACING.normal
  }
};
var ACCESSIBILITY_FONT_SIZES = exports.ACCESSIBILITY_FONT_SIZES = {
  minimum: 12,
  comfortable: 16,
  large: 20,
  extraLarge: 24
};
var READING_WIDTH = exports.READING_WIDTH = {
  optimal: 65,
  maximum: 75,
  minimum: 45
};
var getOptimalLineHeight = exports.getOptimalLineHeight = function getOptimalLineHeight(fontSize) {
  if (fontSize >= FONT_SIZES.xl) return fontSize * 1.2;
  if (fontSize >= FONT_SIZES.lg) return fontSize * 1.3;
  return fontSize * 1.5;
};
var getAccessibleFontSize = exports.getAccessibleFontSize = function getAccessibleFontSize(baseFontSize) {
  var userPreference = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
  var multipliers = {
    small: 0.875,
    normal: 1,
    large: 1.125,
    extraLarge: 1.25
  };
  return Math.max(ACCESSIBILITY_FONT_SIZES.minimum, Math.round(baseFontSize * multipliers[userPreference]));
};
var isTextSizeAccessible = exports.isTextSizeAccessible = function isTextSizeAccessible(fontSize) {
  return fontSize >= ACCESSIBILITY_FONT_SIZES.minimum;
};
var getPlatformAdjustedSize = exports.getPlatformAdjustedSize = function getPlatformAdjustedSize(size) {
  if (_reactNative.Platform.OS === 'android') {
    return Math.round(size * 1.05);
  }
  return size;
};
var _default = exports.default = {
  FONT_FAMILIES: FONT_FAMILIES,
  FONT_WEIGHTS: FONT_WEIGHTS,
  FONT_SIZES: FONT_SIZES,
  LINE_HEIGHTS: LINE_HEIGHTS,
  LETTER_SPACING: LETTER_SPACING,
  TYPOGRAPHY_VARIANTS: TYPOGRAPHY_VARIANTS,
  ACCESSIBILITY_FONT_SIZES: ACCESSIBILITY_FONT_SIZES,
  READING_WIDTH: READING_WIDTH,
  getOptimalLineHeight: getOptimalLineHeight,
  getAccessibleFontSize: getAccessibleFontSize,
  isTextSizeAccessible: isTextSizeAccessible,
  getPlatformAdjustedSize: getPlatformAdjustedSize
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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