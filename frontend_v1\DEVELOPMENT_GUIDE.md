# Vierla Frontend v1 - Development Guide

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Development Setup](#development-setup)
4. [Code Standards](#code-standards)
5. [Component Library](#component-library)
6. [Testing Strategy](#testing-strategy)
7. [Performance Guidelines](#performance-guidelines)
8. [Accessibility Standards](#accessibility-standards)
9. [Internationalization](#internationalization)
10. [Deployment](#deployment)

## Project Overview

Vierla Frontend v1 is a React Native application built with Expo, designed for the Canadian service marketplace. The application follows modern development practices with a focus on accessibility, performance, and user experience.

### Key Features

- **Canadian Market Focus**: Localized for Canadian provinces, currencies, and regulations
- **Accessibility First**: WCAG 2.1 AA compliant with comprehensive accessibility features
- **Performance Optimized**: Code splitting, lazy loading, and bundle optimization
- **Bilingual Support**: English and French-Canadian (Quebec French) localization
- **Design System**: Comprehensive component library with consistent styling
- **Testing Infrastructure**: Unit, integration, and accessibility testing

### Technology Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: Zustand
- **Navigation**: React Navigation v6
- **Styling**: StyleSheet with theme system
- **Testing**: Jest + React Native Testing Library
- **Internationalization**: react-i18next
- **Icons**: Expo Vector Icons
- **Animations**: React Native Reanimated

## Architecture

### Project Structure

```
frontend_v1/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── animation/       # Animation components
│   │   ├── error/          # Error handling components
│   │   ├── forms/          # Form components
│   │   ├── loading/        # Loading state components
│   │   ├── localization/   # Language components
│   │   ├── navigation/     # Navigation components
│   │   ├── payment/        # Payment components
│   │   └── typography/     # Typography components
│   ├── contexts/           # React contexts
│   │   ├── AnimationContext.tsx
│   │   ├── HighContrastContext.tsx
│   │   └── MotorAccessibilityContext.tsx
│   ├── hooks/              # Custom React hooks
│   ├── localization/       # i18n configuration and translations
│   ├── screens/            # Screen components
│   ├── services/           # API and external services
│   ├── store/              # State management
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions
│   └── __tests__/          # Test files
├── assets/                 # Static assets
├── docs/                   # Documentation
└── package.json
```

### Design Patterns

#### Component Architecture

- **Atomic Design**: Components are organized by complexity (atoms, molecules, organisms)
- **Composition over Inheritance**: Favor component composition
- **Props Interface**: All components have well-defined TypeScript interfaces
- **Accessibility First**: Every component includes accessibility properties

#### State Management

- **Zustand Stores**: Lightweight state management for global state
- **Local State**: React hooks for component-specific state
- **Context API**: For theme, accessibility, and configuration

#### Error Handling

- **Error Boundaries**: Catch and handle React errors gracefully
- **Error Utils**: Centralized error classification and handling
- **User-Friendly Messages**: Contextual error messages based on user location

## Development Setup

### Prerequisites

- Node.js 18+ and npm/yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd frontend_v1

# Install dependencies
npm install

# Start the development server
npm start

# Run on specific platform
npm run android
npm run ios
npm run web
```

### Environment Configuration

Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_API_BASE_URL=http://************:8000
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_ENABLE_ANALYTICS=false
```

## Code Standards

### TypeScript Guidelines

- **Strict Mode**: Enable strict TypeScript checking
- **Interface Definitions**: Define interfaces for all props and data structures
- **Type Safety**: Avoid `any` types, use proper type definitions
- **Generic Types**: Use generics for reusable components

```typescript
// Good: Well-defined interface
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

// Good: Generic component
interface ListProps<T> {
  data: T[];
  renderItem: (item: T) => React.ReactNode;
  keyExtractor: (item: T) => string;
}
```

### Component Guidelines

- **Functional Components**: Use function components with hooks
- **Props Destructuring**: Destructure props in function signature
- **Default Props**: Use default parameters instead of defaultProps
- **Memo Optimization**: Use React.memo for performance-critical components

```typescript
// Good: Functional component with proper typing
const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
}) => {
  // Component implementation
};

// Good: Memoized component
export default React.memo(Button);
```

### Styling Guidelines

- **StyleSheet**: Use React Native StyleSheet for performance
- **Theme System**: Use centralized theme for colors and spacing
- **Responsive Design**: Consider different screen sizes
- **Accessibility**: Include accessibility styles

```typescript
const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primary: {
    backgroundColor: '#5A7A63',
  },
  secondary: {
    backgroundColor: '#F0F0F0',
  },
});
```

## Component Library

### Core Components

#### Typography
- `Typography`: Base text component with theme integration
- `Heading`: Semantic heading component (h1-h6)
- `Body`: Body text with proper line height
- `Caption`: Small text for captions and labels

#### Buttons
- `AnimatedButton`: Primary button with animations and states
- `IconButton`: Button with icon support
- `LinkButton`: Text-based button for links

#### Forms
- `TextInput`: Enhanced text input with validation
- `CanadianAddressInput`: Specialized address input for Canadian addresses
- `CanadianPaymentForm`: Payment form with Canadian tax calculations

#### Navigation
- `TabBar`: Custom tab bar with accessibility
- `Header`: Screen header with navigation controls

### Usage Examples

```typescript
// Typography
<Heading level={1} color="primary">Welcome to Vierla</Heading>
<Body color="secondary">Find trusted service providers in your area.</Body>

// Buttons
<AnimatedButton
  title="Book Now"
  onPress={handleBooking}
  variant="primary"
  state="idle"
  accessibilityLabel="Book this service"
/>

// Forms
<CanadianAddressInput
  value={address}
  onChange={setAddress}
  required
  showUnitField
  defaultProvince="ON"
/>
```

## Testing Strategy

### Test Types

1. **Unit Tests**: Individual component and utility testing
2. **Integration Tests**: Component interaction testing
3. **Accessibility Tests**: WCAG compliance testing
4. **Performance Tests**: Render time and memory usage testing

### Testing Tools

- **Jest**: Test runner and assertion library
- **React Native Testing Library**: Component testing utilities
- **Accessibility Testing**: Custom accessibility audit utilities
- **Performance Testing**: Custom performance monitoring

### Writing Tests

```typescript
// Component test example
describe('AnimatedButton', () => {
  it('renders correctly with default props', () => {
    const { getByText } = render(
      <AnimatedButton title="Test" onPress={jest.fn()} />
    );
    expect(getByText('Test')).toBeTruthy();
  });

  it('passes accessibility audit', () => {
    const { getByTestId } = render(
      <AnimatedButton 
        title="Test" 
        onPress={jest.fn()} 
        testID="button"
        accessibilityLabel="Test button"
      />
    );
    
    expect(() => {
      testAssertions.assertElementAccessible(
        getByTestId('button'), 
        'button'
      );
    }).not.toThrow();
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run accessibility tests
npm run test:a11y

# Run performance tests
npm run test:performance
```

## Performance Guidelines

### Bundle Optimization

- **Code Splitting**: Use lazy loading for screens and heavy components
- **Tree Shaking**: Import only needed modules
- **Asset Optimization**: Optimize images and reduce bundle size

### Runtime Performance

- **Memo Usage**: Memoize expensive calculations and components
- **Callback Optimization**: Use useCallback for event handlers
- **List Optimization**: Use FlatList for large datasets
- **Image Optimization**: Use appropriate image sizes and formats

### Performance Monitoring

```typescript
// Use performance monitoring hook
const { metrics, recommendations } = usePerformanceMonitoring();

// Track component performance
trackComponentMetrics('ComponentName', size, loadTime);

// Monitor bundle size
analyzeBundleSize();
```

## Accessibility Standards

### WCAG 2.1 AA Compliance

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Reader Support**: Proper semantic markup and labels
- **Color Contrast**: Minimum 4.5:1 contrast ratio
- **Touch Targets**: Minimum 44x44 point touch targets
- **Focus Management**: Logical focus order and visible focus indicators

### Implementation

```typescript
// Accessible button example
<TouchableOpacity
  style={styles.button}
  onPress={handlePress}
  accessibilityRole="button"
  accessibilityLabel="Book this service"
  accessibilityHint="Navigate to booking screen"
  accessibilityState={{ disabled: isDisabled }}
>
  <Text>{title}</Text>
</TouchableOpacity>
```

### Testing Accessibility

```typescript
// Accessibility test
it('meets accessibility standards', () => {
  const { getByTestId } = render(<Component testID="test" />);
  const result = accessibilityTestUtils.auditAccessibility(
    getByTestId('test')
  );
  expect(result.passed).toBe(true);
});
```

## Internationalization

### Supported Languages

- **English (Canada)**: Primary language
- **French (Canada)**: Quebec French with proper terminology

### Implementation

```typescript
// Translation usage
const { t } = useTranslation();

// Simple translation
<Text>{t('common.loading')}</Text>

// Translation with interpolation
<Text>{t('greeting', { name: 'John' })}</Text>

// Pluralization
<Text>{t('items_count', { count: items.length })}</Text>
```

### Adding Translations

1. Add keys to translation files in `src/localization/translations/`
2. Use proper Quebec French terminology for fr-CA
3. Test with both languages
4. Consider cultural context and local preferences

## Deployment

### Build Process

```bash
# Development build
npm run build:dev

# Production build
npm run build:prod

# Platform-specific builds
npm run build:android
npm run build:ios
```

### Environment Configuration

- **Development**: Local API, debug enabled
- **Staging**: Staging API, limited analytics
- **Production**: Production API, full analytics

### Quality Checks

Before deployment, ensure:

- [ ] All tests pass
- [ ] Accessibility audit passes
- [ ] Performance benchmarks met
- [ ] Bundle size within limits
- [ ] Both languages tested
- [ ] Canadian compliance verified

---

For more detailed information, see the individual component documentation in the `docs/` directory.
