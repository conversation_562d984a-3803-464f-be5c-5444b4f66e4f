/**
 * Testing Utilities
 *
 * Comprehensive testing utilities for React Native applications
 * with accessibility testing, performance testing, and quality assurance.
 *
 * Features:
 * - Test helpers and utilities
 * - Accessibility testing
 * - Performance testing
 * - Mock data generation
 * - Test environment setup
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { ReactTestInstance } from 'react-test-renderer';

// Test environment configuration
export interface TestEnvironmentConfig {
  enableAccessibilityTesting: boolean;
  enablePerformanceTesting: boolean;
  mockNetworkRequests: boolean;
  mockLocationServices: boolean;
  mockNotifications: boolean;
  logLevel: 'silent' | 'error' | 'warn' | 'info' | 'debug';
}

// Accessibility test result
export interface AccessibilityTestResult {
  passed: boolean;
  violations: Array<{
    rule: string;
    severity: 'error' | 'warning' | 'info';
    message: string;
    element?: string;
  }>;
  warnings: string[];
  suggestions: string[];
}

// Performance test result
export interface PerformanceTestResult {
  renderTime: number;
  memoryUsage: number;
  componentCount: number;
  passed: boolean;
  thresholds: {
    maxRenderTime: number;
    maxMemoryUsage: number;
    maxComponentCount: number;
  };
}

// Mock data generators
export const mockDataGenerators = {
  // User data
  user: (overrides: Partial<any> = {}) => ({
    id: `user_${Math.random().toString(36).substr(2, 9)}`,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    avatar: 'https://example.com/avatar.jpg',
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  // Service data
  service: (overrides: Partial<any> = {}) => ({
    id: `service_${Math.random().toString(36).substr(2, 9)}`,
    title: 'House Cleaning',
    description: 'Professional house cleaning service',
    category: 'cleaning',
    price: 75.00,
    duration: 120,
    rating: 4.5,
    reviewCount: 128,
    images: ['https://example.com/service1.jpg'],
    ...overrides,
  }),

  // Provider data
  provider: (overrides: Partial<any> = {}) => ({
    id: `provider_${Math.random().toString(36).substr(2, 9)}`,
    name: 'Jane Smith',
    company: 'Smith Cleaning Services',
    rating: 4.8,
    reviewCount: 256,
    verified: true,
    experience: 5,
    location: 'Toronto, ON',
    avatar: 'https://example.com/provider.jpg',
    services: ['cleaning', 'maintenance'],
    ...overrides,
  }),

  // Booking data
  booking: (overrides: Partial<any> = {}) => ({
    id: `booking_${Math.random().toString(36).substr(2, 9)}`,
    serviceId: 'service_123',
    providerId: 'provider_456',
    customerId: 'user_789',
    date: new Date().toISOString(),
    status: 'confirmed',
    price: 75.00,
    address: '123 Main St, Toronto, ON M5V 3A8',
    notes: 'Please call when arriving',
    ...overrides,
  }),

  // Address data
  address: (overrides: Partial<any> = {}) => ({
    id: `address_${Math.random().toString(36).substr(2, 9)}`,
    streetNumber: '123',
    streetName: 'Main Street',
    city: 'Toronto',
    province: 'ON',
    postalCode: 'M5V 3A8',
    country: 'Canada',
    coordinates: { latitude: 43.6532, longitude: -79.3832 },
    ...overrides,
  }),

  // Review data
  review: (overrides: Partial<any> = {}) => ({
    id: `review_${Math.random().toString(36).substr(2, 9)}`,
    rating: 5,
    comment: 'Excellent service! Highly recommended.',
    authorName: 'Happy Customer',
    date: new Date().toISOString(),
    verified: true,
    helpful: 12,
    ...overrides,
  }),
};

// Test environment setup
export const setupTestEnvironment = (config: Partial<TestEnvironmentConfig> = {}) => {
  const defaultConfig: TestEnvironmentConfig = {
    enableAccessibilityTesting: true,
    enablePerformanceTesting: true,
    mockNetworkRequests: true,
    mockLocationServices: true,
    mockNotifications: true,
    logLevel: 'warn',
  };

  const finalConfig = { ...defaultConfig, ...config };

  // Setup console logging
  if (finalConfig.logLevel === 'silent') {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }

  // Mock network requests
  if (finalConfig.mockNetworkRequests) {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve(''),
      })
    ) as jest.Mock;
  }

  // Mock location services
  if (finalConfig.mockLocationServices) {
    const mockGeolocation = {
      getCurrentPosition: jest.fn((success) =>
        success({
          coords: {
            latitude: 43.6532,
            longitude: -79.3832,
            accuracy: 10,
          },
        })
      ),
      watchPosition: jest.fn(),
      clearWatch: jest.fn(),
    };

    Object.defineProperty(global.navigator, 'geolocation', {
      value: mockGeolocation,
      writable: true,
    });
  }

  // Mock notifications
  if (finalConfig.mockNotifications) {
    const mockNotifications = {
      requestPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
      scheduleNotificationAsync: jest.fn(() => Promise.resolve('notification-id')),
      cancelNotificationAsync: jest.fn(() => Promise.resolve()),
    };

    jest.doMock('expo-notifications', () => mockNotifications);
  }

  return finalConfig;
};

// Accessibility testing utilities
export const accessibilityTestUtils = {
  // Check if element has accessibility label
  hasAccessibilityLabel: (element: ReactTestInstance): boolean => {
    return !!(element.props.accessibilityLabel || element.props['aria-label']);
  },

  // Check if element has accessibility role
  hasAccessibilityRole: (element: ReactTestInstance): boolean => {
    return !!(element.props.accessibilityRole || element.props.role);
  },

  // Check if element has accessibility hint
  hasAccessibilityHint: (element: ReactTestInstance): boolean => {
    return !!element.props.accessibilityHint;
  },

  // Check if element is focusable
  isFocusable: (element: ReactTestInstance): boolean => {
    return element.props.accessible !== false && 
           (element.props.accessibilityRole === 'button' ||
            element.props.accessibilityRole === 'link' ||
            element.props.onPress ||
            element.props.onFocus);
  },

  // Check touch target size
  hasSufficientTouchTarget: (element: ReactTestInstance): boolean => {
    const style = element.props.style;
    if (!style) return false;

    const minSize = 44; // iOS HIG minimum
    const width = style.width || style.minWidth;
    const height = style.height || style.minHeight;

    return width >= minSize && height >= minSize;
  },

  // Run accessibility audit
  auditAccessibility: (component: ReactTestInstance): AccessibilityTestResult => {
    const violations: AccessibilityTestResult['violations'] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Find all interactive elements
    const interactiveElements = component.findAll((node) => {
      return node.props.onPress || 
             node.props.accessibilityRole === 'button' ||
             node.props.accessibilityRole === 'link';
    });

    interactiveElements.forEach((element, index) => {
      const elementId = `element-${index}`;

      // Check accessibility label
      if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
        violations.push({
          rule: 'accessibility-label-required',
          severity: 'error',
          message: 'Interactive element must have accessibility label',
          element: elementId,
        });
      }

      // Check accessibility role
      if (!accessibilityTestUtils.hasAccessibilityRole(element)) {
        warnings.push(`Element ${elementId} should have accessibility role`);
      }

      // Check touch target size
      if (!accessibilityTestUtils.hasSufficientTouchTarget(element)) {
        violations.push({
          rule: 'touch-target-size',
          severity: 'warning',
          message: 'Touch target should be at least 44x44 points',
          element: elementId,
        });
      }
    });

    // Find all images
    const images = component.findAll((node) => node.type === 'Image');
    images.forEach((image, index) => {
      if (!image.props.accessibilityLabel && !image.props.alt) {
        suggestions.push(`Image ${index} should have descriptive accessibility label`);
      }
    });

    return {
      passed: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      warnings,
      suggestions,
    };
  },
};

// Performance testing utilities
export const performanceTestUtils = {
  // Measure render time
  measureRenderTime: async (renderFunction: () => Promise<any>): Promise<number> => {
    const startTime = performance.now();
    await renderFunction();
    const endTime = performance.now();
    return endTime - startTime;
  },

  // Measure memory usage (mock implementation)
  measureMemoryUsage: (): number => {
    // In a real implementation, this would use native modules
    // to measure actual memory usage
    return Math.random() * 100; // Mock value in MB
  },

  // Count components in tree
  countComponents: (component: ReactTestInstance): number => {
    let count = 1;
    if (component.children) {
      component.children.forEach((child) => {
        if (typeof child === 'object' && 'type' in child) {
          count += performanceTestUtils.countComponents(child as ReactTestInstance);
        }
      });
    }
    return count;
  },

  // Run performance audit
  auditPerformance: async (
    component: ReactTestInstance,
    thresholds: PerformanceTestResult['thresholds'] = {
      maxRenderTime: 100,
      maxMemoryUsage: 50,
      maxComponentCount: 100,
    }
  ): Promise<PerformanceTestResult> => {
    const renderTime = await performanceTestUtils.measureRenderTime(async () => {
      // Simulate re-render
      await new Promise(resolve => setTimeout(resolve, 10));
    });

    const memoryUsage = performanceTestUtils.measureMemoryUsage();
    const componentCount = performanceTestUtils.countComponents(component);

    const passed = renderTime <= thresholds.maxRenderTime &&
                   memoryUsage <= thresholds.maxMemoryUsage &&
                   componentCount <= thresholds.maxComponentCount;

    return {
      renderTime,
      memoryUsage,
      componentCount,
      passed,
      thresholds,
    };
  },
};

// Test data utilities
export const testDataUtils = {
  // Generate array of mock data
  generateArray: <T>(generator: () => T, count: number): T[] => {
    return Array.from({ length: count }, generator);
  },

  // Generate mock API response
  generateApiResponse: <T>(data: T, success: boolean = true) => ({
    success,
    data: success ? data : null,
    error: success ? null : 'Mock error message',
    timestamp: new Date().toISOString(),
  }),

  // Generate mock pagination response
  generatePaginatedResponse: <T>(
    items: T[],
    page: number = 1,
    limit: number = 10
  ) => {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      pagination: {
        page,
        limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1,
      },
    };
  },
};

// Test assertion utilities
export const testAssertions = {
  // Assert accessibility compliance
  assertAccessible: (component: ReactTestInstance) => {
    const result = accessibilityTestUtils.auditAccessibility(component);
    
    if (!result.passed) {
      const errorMessages = result.violations
        .filter(v => v.severity === 'error')
        .map(v => `${v.rule}: ${v.message}`)
        .join('\n');
      
      throw new Error(`Accessibility violations found:\n${errorMessages}`);
    }
  },

  // Assert performance compliance
  assertPerformant: async (
    component: ReactTestInstance,
    thresholds?: PerformanceTestResult['thresholds']
  ) => {
    const result = await performanceTestUtils.auditPerformance(component, thresholds);
    
    if (!result.passed) {
      const issues = [];
      if (result.renderTime > result.thresholds.maxRenderTime) {
        issues.push(`Render time ${result.renderTime}ms exceeds threshold ${result.thresholds.maxRenderTime}ms`);
      }
      if (result.memoryUsage > result.thresholds.maxMemoryUsage) {
        issues.push(`Memory usage ${result.memoryUsage}MB exceeds threshold ${result.thresholds.maxMemoryUsage}MB`);
      }
      if (result.componentCount > result.thresholds.maxComponentCount) {
        issues.push(`Component count ${result.componentCount} exceeds threshold ${result.thresholds.maxComponentCount}`);
      }
      
      throw new Error(`Performance issues found:\n${issues.join('\n')}`);
    }
  },

  // Assert element exists and is accessible
  assertElementAccessible: (component: ReactTestInstance, testID: string) => {
    const element = component.findByProps({ testID });
    
    if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
      throw new Error(`Element with testID "${testID}" must have accessibility label`);
    }
    
    if (accessibilityTestUtils.isFocusable(element) && 
        !accessibilityTestUtils.hasSufficientTouchTarget(element)) {
      throw new Error(`Focusable element with testID "${testID}" must have sufficient touch target size`);
    }
  },
};

// Test helpers
export const testHelpers = {
  // Wait for element to appear
  waitForElement: async (
    component: ReactTestInstance,
    testID: string,
    timeout: number = 5000
  ): Promise<ReactTestInstance> => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        return component.findByProps({ testID });
      } catch {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    throw new Error(`Element with testID "${testID}" not found within ${timeout}ms`);
  },

  // Simulate user interaction
  simulatePress: async (element: ReactTestInstance) => {
    if (element.props.onPress) {
      await element.props.onPress();
    }
  },

  // Simulate text input
  simulateTextInput: async (element: ReactTestInstance, text: string) => {
    if (element.props.onChangeText) {
      await element.props.onChangeText(text);
    }
  },

  // Create test wrapper with providers
  createTestWrapper: (children: React.ReactNode) => {
    // This would wrap children with necessary providers
    // (Theme, Navigation, etc.) for testing
    return children;
  },
};

export default {
  mockDataGenerators,
  setupTestEnvironment,
  accessibilityTestUtils,
  performanceTestUtils,
  testDataUtils,
  testAssertions,
  testHelpers,
};
