/**
 * Provincial Compliance Utilities
 *
 * Comprehensive utilities for ensuring compliance with Canadian provincial
 * regulations, service standards, and legal requirements.
 *
 * Features:
 * - Provincial service regulations
 * - Licensing requirements
 * - Consumer protection laws
 * - Privacy compliance
 * - Service standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { CanadianProvince } from './canadianPaymentUtils';

// Service categories that may require licensing
export const REGULATED_SERVICE_CATEGORIES = {
  HEALTHCARE: 'healthcare',
  CHILDCARE: 'childcare',
  ELDERCARE: 'eldercare',
  EDUCATION: 'education',
  FINANCIAL: 'financial',
  LEGAL: 'legal',
  CONSTRUCTION: 'construction',
  AUTOMOTIVE: 'automotive',
  FOOD_SERVICE: 'food_service',
  BEAUTY_WELLNESS: 'beauty_wellness',
  TRANSPORTATION: 'transportation',
  SECURITY: 'security',
  REAL_ESTATE: 'real_estate',
  INSURANCE: 'insurance',
  PROFESSIONAL_SERVICES: 'professional_services',
} as const;

export type RegulatedServiceCategory = keyof typeof REGULATED_SERVICE_CATEGORIES;

// Provincial regulatory information
export interface ProvincialRegulation {
  province: CanadianProvince;
  serviceLicensingRequired: boolean;
  backgroundCheckRequired: boolean;
  insuranceRequired: boolean;
  minimumAge: number;
  consumerProtectionLaws: string[];
  privacyLaws: string[];
  serviceStandards: string[];
  regulatoryBodies: string[];
  licenseVerificationUrl?: string;
  complaintProcessUrl?: string;
}

// Service compliance requirements
export interface ServiceComplianceRequirements {
  category: RegulatedServiceCategory;
  province: CanadianProvince;
  licenseRequired: boolean;
  licenseTypes: string[];
  backgroundCheckRequired: boolean;
  insuranceRequired: boolean;
  minimumInsuranceAmount?: number;
  bondingRequired: boolean;
  certificationRequired: boolean;
  certificationTypes: string[];
  ageRestrictions: {
    minimum: number;
    maximum?: number;
  };
  additionalRequirements: string[];
  exemptions: string[];
}

// Provincial regulations database
export const PROVINCIAL_REGULATIONS: Record<CanadianProvince, ProvincialRegulation> = {
  AB: {
    province: 'AB',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Fair Trading Act',
      'Consumer Protection Act',
      'Personal Information Protection Act (PIPA)',
    ],
    privacyLaws: [
      'Personal Information Protection Act (PIPA)',
      'Health Information Act',
    ],
    serviceStandards: [
      'Alberta Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Service Alberta',
      'Alberta Health Services',
      'Alberta Securities Commission',
    ],
    licenseVerificationUrl: 'https://www.alberta.ca/license-verification',
    complaintProcessUrl: 'https://www.alberta.ca/consumer-complaints',
  },
  BC: {
    province: 'BC',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Business Practices and Consumer Protection Act',
      'Personal Information Protection Act (PIPA)',
    ],
    privacyLaws: [
      'Personal Information Protection Act (PIPA)',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'BC Building Code',
      'Workers Compensation Act',
    ],
    regulatoryBodies: [
      'Consumer Protection BC',
      'BC Housing',
      'BC Securities Commission',
    ],
    licenseVerificationUrl: 'https://www.consumerprotectionbc.ca/license-verification',
    complaintProcessUrl: 'https://www.consumerprotectionbc.ca/complaints',
  },
  MB: {
    province: 'MB',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Personal Health Information Act',
    ],
    privacyLaws: [
      'Personal Health Information Act',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'Manitoba Building Code',
      'Workplace Safety and Health Act',
    ],
    regulatoryBodies: [
      'Manitoba Consumer Protection Office',
      'Manitoba Health',
    ],
  },
  NB: {
    province: 'NB',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Product Warranty and Liability Act',
      'Personal Health Information Privacy and Access Act',
    ],
    privacyLaws: [
      'Personal Health Information Privacy and Access Act',
      'Right to Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'New Brunswick Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Financial and Consumer Services Commission',
      'Department of Health',
    ],
  },
  NL: {
    province: 'NL',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Protection and Business Practices Act',
      'Personal Health Information Act',
    ],
    privacyLaws: [
      'Personal Health Information Act',
      'Access to Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'National Building Code (NL)',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Service NL',
      'Department of Health and Community Services',
    ],
  },
  NS: {
    province: 'NS',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Personal Health Information Act',
    ],
    privacyLaws: [
      'Personal Health Information Act',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'Nova Scotia Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Access Nova Scotia',
      'Nova Scotia Health Authority',
    ],
  },
  NT: {
    province: 'NT',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Health Information Act',
    ],
    privacyLaws: [
      'Health Information Act',
      'Access to Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'National Building Code (NT)',
      'Safety Act',
    ],
    regulatoryBodies: [
      'Department of Municipal and Community Affairs',
      'Department of Health and Social Services',
    ],
  },
  NU: {
    province: 'NU',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Health Information Act',
    ],
    privacyLaws: [
      'Health Information Act',
      'Access to Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'National Building Code (NU)',
      'Safety Act',
    ],
    regulatoryBodies: [
      'Department of Community and Government Services',
      'Department of Health',
    ],
  },
  ON: {
    province: 'ON',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Personal Health Information Protection Act (PHIPA)',
    ],
    privacyLaws: [
      'Personal Health Information Protection Act (PHIPA)',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'Ontario Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Ministry of Government and Consumer Services',
      'Ministry of Health',
      'Ontario Securities Commission',
    ],
    licenseVerificationUrl: 'https://www.ontario.ca/page/verify-professional-licence',
    complaintProcessUrl: 'https://www.ontario.ca/page/file-consumer-complaint',
  },
  PE: {
    province: 'PE',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Health Information Act',
    ],
    privacyLaws: [
      'Health Information Act',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'Prince Edward Island Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Office of the Attorney General',
      'Department of Health and Wellness',
    ],
  },
  QC: {
    province: 'QC',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Act respecting the protection of personal information in the private sector',
      'Charter of the French Language (Bill 101)',
    ],
    privacyLaws: [
      'Act respecting the protection of personal information in the private sector',
      'Act respecting Access to documents held by public bodies',
    ],
    serviceStandards: [
      'Construction Code of Quebec',
      'Act respecting occupational health and safety',
      'Charter of the French Language requirements',
    ],
    regulatoryBodies: [
      'Office de la protection du consommateur',
      'Régie du bâtiment du Québec',
      'Commission des normes, de l\'équité, de la santé et de la sécurité du travail',
    ],
    licenseVerificationUrl: 'https://www.opc.gouv.qc.ca/verification-permis',
    complaintProcessUrl: 'https://www.opc.gouv.qc.ca/plaintes',
  },
  SK: {
    province: 'SK',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 18,
    consumerProtectionLaws: [
      'Consumer Protection and Business Practices Act',
      'Health Information Protection Act',
    ],
    privacyLaws: [
      'Health Information Protection Act',
      'Freedom of Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'Saskatchewan Building Code',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Financial and Consumer Affairs Authority',
      'Saskatchewan Health Authority',
    ],
  },
  YT: {
    province: 'YT',
    serviceLicensingRequired: true,
    backgroundCheckRequired: true,
    insuranceRequired: true,
    minimumAge: 19,
    consumerProtectionLaws: [
      'Consumer Protection Act',
      'Health Information Privacy and Management Act',
    ],
    privacyLaws: [
      'Health Information Privacy and Management Act',
      'Access to Information and Protection of Privacy Act',
    ],
    serviceStandards: [
      'National Building Code (YT)',
      'Occupational Health and Safety Act',
    ],
    regulatoryBodies: [
      'Department of Community Services',
      'Department of Health and Social Services',
    ],
  },
};

/**
 * Get compliance requirements for a service category in a specific province
 */
export const getServiceComplianceRequirements = (
  category: RegulatedServiceCategory,
  province: CanadianProvince
): ServiceComplianceRequirements => {
  const baseRegulation = PROVINCIAL_REGULATIONS[province];
  
  // Service-specific requirements (this would be expanded with detailed regulations)
  const serviceRequirements: Partial<Record<RegulatedServiceCategory, Partial<ServiceComplianceRequirements>>> = {
    HEALTHCARE: {
      licenseRequired: true,
      licenseTypes: ['Professional License', 'Practice Permit'],
      backgroundCheckRequired: true,
      insuranceRequired: true,
      minimumInsuranceAmount: 2000000, // $2M
      bondingRequired: false,
      certificationRequired: true,
      certificationTypes: ['Professional Certification', 'Continuing Education'],
      ageRestrictions: { minimum: 21 },
      additionalRequirements: [
        'Professional registration',
        'Malpractice insurance',
        'Continuing education requirements',
      ],
      exemptions: [],
    },
    CHILDCARE: {
      licenseRequired: true,
      licenseTypes: ['Childcare License', 'Early Childhood Education Certificate'],
      backgroundCheckRequired: true,
      insuranceRequired: true,
      minimumInsuranceAmount: 1000000, // $1M
      bondingRequired: true,
      certificationRequired: true,
      certificationTypes: ['First Aid/CPR', 'Child Development'],
      ageRestrictions: { minimum: 18 },
      additionalRequirements: [
        'Criminal background check',
        'Child abuse registry check',
        'Health clearance',
        'Reference checks',
      ],
      exemptions: ['Occasional babysitting (under 4 hours)'],
    },
    CONSTRUCTION: {
      licenseRequired: true,
      licenseTypes: ['Contractor License', 'Trade Certification'],
      backgroundCheckRequired: false,
      insuranceRequired: true,
      minimumInsuranceAmount: 2000000, // $2M
      bondingRequired: true,
      certificationRequired: true,
      certificationTypes: ['Trade Certification', 'Safety Training'],
      ageRestrictions: { minimum: 18 },
      additionalRequirements: [
        'Workers compensation coverage',
        'Building permits for major work',
        'Safety compliance',
      ],
      exemptions: ['Minor repairs under $1000'],
    },
  };

  const specificRequirements = serviceRequirements[category] || {};
  
  return {
    category,
    province,
    licenseRequired: specificRequirements.licenseRequired ?? baseRegulation.serviceLicensingRequired,
    licenseTypes: specificRequirements.licenseTypes ?? [],
    backgroundCheckRequired: specificRequirements.backgroundCheckRequired ?? baseRegulation.backgroundCheckRequired,
    insuranceRequired: specificRequirements.insuranceRequired ?? baseRegulation.insuranceRequired,
    minimumInsuranceAmount: specificRequirements.minimumInsuranceAmount,
    bondingRequired: specificRequirements.bondingRequired ?? false,
    certificationRequired: specificRequirements.certificationRequired ?? false,
    certificationTypes: specificRequirements.certificationTypes ?? [],
    ageRestrictions: specificRequirements.ageRestrictions ?? { minimum: baseRegulation.minimumAge },
    additionalRequirements: specificRequirements.additionalRequirements ?? [],
    exemptions: specificRequirements.exemptions ?? [],
  };
};

/**
 * Check if a service provider meets compliance requirements
 */
export const checkProviderCompliance = (
  provider: {
    age: number;
    hasLicense: boolean;
    licenseTypes: string[];
    hasBackgroundCheck: boolean;
    hasInsurance: boolean;
    insuranceAmount?: number;
    hasBonding: boolean;
    certifications: string[];
  },
  requirements: ServiceComplianceRequirements
): {
  compliant: boolean;
  violations: string[];
  warnings: string[];
} => {
  const violations: string[] = [];
  const warnings: string[] = [];

  // Age check
  if (provider.age < requirements.ageRestrictions.minimum) {
    violations.push(`Provider must be at least ${requirements.ageRestrictions.minimum} years old`);
  }
  
  if (requirements.ageRestrictions.maximum && provider.age > requirements.ageRestrictions.maximum) {
    violations.push(`Provider must be under ${requirements.ageRestrictions.maximum} years old`);
  }

  // License check
  if (requirements.licenseRequired && !provider.hasLicense) {
    violations.push('Valid license required');
  }
  
  if (requirements.licenseRequired && requirements.licenseTypes.length > 0) {
    const hasRequiredLicense = requirements.licenseTypes.some(
      requiredType => provider.licenseTypes.includes(requiredType)
    );
    if (!hasRequiredLicense) {
      violations.push(`Required license type: ${requirements.licenseTypes.join(' or ')}`);
    }
  }

  // Background check
  if (requirements.backgroundCheckRequired && !provider.hasBackgroundCheck) {
    violations.push('Background check required');
  }

  // Insurance check
  if (requirements.insuranceRequired && !provider.hasInsurance) {
    violations.push('Insurance coverage required');
  }
  
  if (requirements.minimumInsuranceAmount && provider.insuranceAmount) {
    if (provider.insuranceAmount < requirements.minimumInsuranceAmount) {
      violations.push(`Minimum insurance coverage: $${requirements.minimumInsuranceAmount.toLocaleString()}`);
    }
  }

  // Bonding check
  if (requirements.bondingRequired && !provider.hasBonding) {
    violations.push('Bonding required');
  }

  // Certification check
  if (requirements.certificationRequired && requirements.certificationTypes.length > 0) {
    const hasRequiredCertification = requirements.certificationTypes.some(
      requiredCert => provider.certifications.includes(requiredCert)
    );
    if (!hasRequiredCertification) {
      violations.push(`Required certification: ${requirements.certificationTypes.join(' or ')}`);
    }
  }

  return {
    compliant: violations.length === 0,
    violations,
    warnings,
  };
};

/**
 * Get provincial regulatory information
 */
export const getProvincialRegulation = (province: CanadianProvince): ProvincialRegulation => {
  return PROVINCIAL_REGULATIONS[province];
};

/**
 * Generate compliance checklist for service providers
 */
export const generateComplianceChecklist = (
  category: RegulatedServiceCategory,
  province: CanadianProvince
): Array<{
  requirement: string;
  mandatory: boolean;
  description: string;
  helpUrl?: string;
}> => {
  const requirements = getServiceComplianceRequirements(category, province);
  const regulation = getProvincialRegulation(province);
  
  const checklist: Array<{
    requirement: string;
    mandatory: boolean;
    description: string;
    helpUrl?: string;
  }> = [];

  // Age requirement
  checklist.push({
    requirement: 'Age Requirement',
    mandatory: true,
    description: `Must be at least ${requirements.ageRestrictions.minimum} years old`,
  });

  // License requirement
  if (requirements.licenseRequired) {
    checklist.push({
      requirement: 'Professional License',
      mandatory: true,
      description: `Valid license required: ${requirements.licenseTypes.join(', ')}`,
      helpUrl: regulation.licenseVerificationUrl,
    });
  }

  // Background check
  if (requirements.backgroundCheckRequired) {
    checklist.push({
      requirement: 'Background Check',
      mandatory: true,
      description: 'Criminal background check required',
    });
  }

  // Insurance
  if (requirements.insuranceRequired) {
    const insuranceDesc = requirements.minimumInsuranceAmount
      ? `Liability insurance required (minimum $${requirements.minimumInsuranceAmount.toLocaleString()})`
      : 'Liability insurance required';
    
    checklist.push({
      requirement: 'Insurance Coverage',
      mandatory: true,
      description: insuranceDesc,
    });
  }

  // Bonding
  if (requirements.bondingRequired) {
    checklist.push({
      requirement: 'Bonding',
      mandatory: true,
      description: 'Surety bond required',
    });
  }

  // Certifications
  if (requirements.certificationRequired) {
    checklist.push({
      requirement: 'Professional Certification',
      mandatory: true,
      description: `Required certifications: ${requirements.certificationTypes.join(', ')}`,
    });
  }

  // Additional requirements
  requirements.additionalRequirements.forEach(req => {
    checklist.push({
      requirement: req,
      mandatory: true,
      description: req,
    });
  });

  return checklist;
};

/**
 * Get consumer protection information for a province
 */
export const getConsumerProtectionInfo = (province: CanadianProvince) => {
  const regulation = getProvincialRegulation(province);
  
  return {
    laws: regulation.consumerProtectionLaws,
    regulatoryBodies: regulation.regulatoryBodies,
    complaintProcess: regulation.complaintProcessUrl,
    rights: [
      'Right to clear pricing information',
      'Right to cancel within cooling-off period',
      'Right to quality service delivery',
      'Right to privacy protection',
      'Right to file complaints',
    ],
  };
};

export default {
  REGULATED_SERVICE_CATEGORIES,
  PROVINCIAL_REGULATIONS,
  getServiceComplianceRequirements,
  checkProviderCompliance,
  getProvincialRegulation,
  generateComplianceChecklist,
  getConsumerProtectionInfo,
};
