/**
 * Enhanced Loading Indicator Component
 *
 * Comprehensive loading indicator with accessibility features,
 * progress tracking, and performance optimization.
 *
 * Features:
 * - Multiple loading states
 * - Progress indication
 * - Accessibility compliance
 * - Performance monitoring
 * - Customizable animations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  ActivityIndicator,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useCognitiveAccessibility } from '../../contexts/CognitiveAccessibilityContext';
import { LoadingState } from '../../utils/performanceUtils';

// Loading indicator types
export type LoadingIndicatorType = 
  | 'spinner' 
  | 'dots' 
  | 'pulse' 
  | 'skeleton' 
  | 'progress' 
  | 'custom';

// Component props
export interface EnhancedLoadingIndicatorProps {
  // Loading state
  isLoading: boolean;
  type?: LoadingIndicatorType;
  progress?: number;
  message?: string;
  
  // Behavior
  showProgress?: boolean;
  showMessage?: boolean;
  showTimeEstimate?: boolean;
  estimatedDuration?: number;
  
  // Styling
  size?: 'small' | 'medium' | 'large';
  color?: string;
  style?: any;
  overlayStyle?: any;
  
  // Accessibility
  accessibilityLabel?: string;
  announceProgress?: boolean;
  
  // Performance
  reduceMotion?: boolean;
  
  // Custom content
  customIndicator?: React.ReactNode;
  
  // Testing
  testID?: string;
}

export const EnhancedLoadingIndicator: React.FC<EnhancedLoadingIndicatorProps> = ({
  isLoading,
  type = 'spinner',
  progress = 0,
  message,
  showProgress = false,
  showMessage = true,
  showTimeEstimate = false,
  estimatedDuration,
  size = 'medium',
  color,
  style,
  overlayStyle,
  accessibilityLabel,
  announceProgress = true,
  reduceMotion = false,
  customIndicator,
  testID,
}) => {
  // State
  const [elapsedTime, setElapsedTime] = useState(0);
  const [lastAnnouncedProgress, setLastAnnouncedProgress] = useState(0);
  
  // Refs
  const startTimeRef = useRef<number>(Date.now());
  const animationRef = useRef<Animated.Value>(new Animated.Value(0));
  const pulseAnimationRef = useRef<Animated.Value>(new Animated.Value(1));
  const dotsAnimationRef = useRef<Animated.Value[]>([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]);
  
  // Hooks
  const { colors } = useHighContrastColors();
  const { settings, processText } = useCognitiveAccessibility();

  // Process message for cognitive accessibility
  const processedMessage = message ? processText(message) : undefined;

  // Get effective color
  const effectiveColor = color || colors?.primary?.default || '#5A7A63';

  // Update elapsed time
  useEffect(() => {
    if (!isLoading) return;

    startTimeRef.current = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Date.now() - startTimeRef.current);
    }, 1000);

    return () => clearInterval(interval);
  }, [isLoading]);

  // Announce progress changes to screen readers
  useEffect(() => {
    if (!announceProgress || !isLoading) return;

    const progressDiff = Math.abs(progress - lastAnnouncedProgress);
    if (progressDiff >= 25) { // Announce every 25% change
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        AccessibilityInfo.announceForAccessibility(
          `Loading progress: ${Math.round(progress)}% complete`
        );
      }
      setLastAnnouncedProgress(progress);
    }
  }, [progress, lastAnnouncedProgress, announceProgress, isLoading]);

  // Spinner animation
  useEffect(() => {
    if (!isLoading || reduceMotion) return;

    const spinAnimation = Animated.loop(
      Animated.timing(animationRef.current, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    );

    spinAnimation.start();
    return () => spinAnimation.stop();
  }, [isLoading, reduceMotion]);

  // Pulse animation
  useEffect(() => {
    if (!isLoading || reduceMotion || type !== 'pulse') return;

    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimationRef.current, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimationRef.current, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );

    pulseAnimation.start();
    return () => pulseAnimation.stop();
  }, [isLoading, reduceMotion, type]);

  // Dots animation
  useEffect(() => {
    if (!isLoading || reduceMotion || type !== 'dots') return;

    const createDotAnimation = (animatedValue: Animated.Value, delay: number) =>
      Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ])
      );

    const animations = dotsAnimationRef.current.map((dot, index) =>
      createDotAnimation(dot, index * 200)
    );

    animations.forEach(animation => animation.start());
    return () => animations.forEach(animation => animation.stop());
  }, [isLoading, reduceMotion, type]);

  // Format time
  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ${seconds % 60}s`;
  };

  // Get size values
  const getSizeValues = () => {
    switch (size) {
      case 'small':
        return { indicator: 20, text: 12, spacing: 8 };
      case 'large':
        return { indicator: 40, text: 18, spacing: 16 };
      default:
        return { indicator: 30, text: 14, spacing: 12 };
    }
  };

  const sizeValues = getSizeValues();

  // Render loading indicator based on type
  const renderIndicator = () => {
    if (customIndicator) {
      return customIndicator;
    }

    switch (type) {
      case 'spinner':
        return (
          <Animated.View
            style={{
              transform: [
                {
                  rotate: animationRef.current.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg'],
                  }),
                },
              ],
            }}
          >
            <Ionicons
              name="refresh"
              size={sizeValues.indicator}
              color={effectiveColor}
            />
          </Animated.View>
        );

      case 'dots':
        return (
          <View style={styles.dotsContainer}>
            {dotsAnimationRef.current.map((dot, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.dot,
                  {
                    backgroundColor: effectiveColor,
                    opacity: dot,
                    width: sizeValues.indicator / 3,
                    height: sizeValues.indicator / 3,
                  },
                ]}
              />
            ))}
          </View>
        );

      case 'pulse':
        return (
          <Animated.View
            style={{
              transform: [{ scale: pulseAnimationRef.current }],
            }}
          >
            <View
              style={[
                styles.pulseIndicator,
                {
                  backgroundColor: effectiveColor,
                  width: sizeValues.indicator,
                  height: sizeValues.indicator,
                },
              ]}
            />
          </Animated.View>
        );

      case 'progress':
        return (
          <View style={styles.progressContainer}>
            <View
              style={[
                styles.progressBar,
                { backgroundColor: colors?.background?.secondary || '#F0F0F0' },
              ]}
            >
              <View
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: effectiveColor,
                    width: `${Math.max(0, Math.min(100, progress))}%`,
                  },
                ]}
              />
            </View>
            {showProgress && (
              <Text style={[styles.progressText, { fontSize: sizeValues.text }]}>
                {Math.round(progress)}%
              </Text>
            )}
          </View>
        );

      case 'skeleton':
        return (
          <View style={styles.skeletonContainer}>
            {[...Array(3)].map((_, index) => (
              <View
                key={index}
                style={[
                  styles.skeletonLine,
                  {
                    backgroundColor: colors?.background?.secondary || '#F0F0F0',
                    height: sizeValues.text,
                    width: `${100 - index * 20}%`,
                  },
                ]}
              />
            ))}
          </View>
        );

      default:
        return (
          <ActivityIndicator
            size={size === 'small' ? 'small' : 'large'}
            color={effectiveColor}
          />
        );
    }
  };

  if (!isLoading) return null;

  return (
    <View
      style={[styles.container, overlayStyle]}
      testID={testID}
      accessibilityRole="progressbar"
      accessibilityLabel={
        accessibilityLabel ||
        `Loading${processedMessage ? `: ${processedMessage}` : ''}${
          showProgress ? `. ${Math.round(progress)}% complete` : ''
        }`
      }
      accessibilityValue={
        showProgress
          ? {
              min: 0,
              max: 100,
              now: Math.round(progress),
            }
          : undefined
      }
      importantForAccessibility="yes"
    >
      <View style={[styles.content, style]}>
        {/* Loading Indicator */}
        <View style={[styles.indicatorContainer, { marginBottom: sizeValues.spacing }]}>
          {renderIndicator()}
        </View>

        {/* Loading Message */}
        {showMessage && processedMessage && (
          <Text
            style={[
              styles.message,
              {
                fontSize: sizeValues.text,
                color: colors?.text?.primary || '#333',
                marginBottom: sizeValues.spacing / 2,
              },
            ]}
          >
            {processedMessage}
          </Text>
        )}

        {/* Progress Text */}
        {showProgress && type !== 'progress' && (
          <Text
            style={[
              styles.progressText,
              {
                fontSize: sizeValues.text - 2,
                color: colors?.text?.secondary || '#666',
                marginBottom: sizeValues.spacing / 2,
              },
            ]}
          >
            {Math.round(progress)}% complete
          </Text>
        )}

        {/* Time Estimate */}
        {showTimeEstimate && (
          <View style={styles.timeContainer}>
            <Text
              style={[
                styles.timeText,
                {
                  fontSize: sizeValues.text - 2,
                  color: colors?.text?.tertiary || '#999',
                },
              ]}
            >
              Elapsed: {formatTime(elapsedTime)}
            </Text>
            {estimatedDuration && elapsedTime < estimatedDuration && (
              <Text
                style={[
                  styles.timeText,
                  {
                    fontSize: sizeValues.text - 2,
                    color: colors?.text?.tertiary || '#999',
                  },
                ]}
              >
                Remaining: {formatTime(estimatedDuration - elapsedTime)}
              </Text>
            )}
          </View>
        )}

        {/* Cognitive accessibility aids */}
        {settings.memoryAids && (
          <Text
            style={[
              styles.helpText,
              {
                fontSize: sizeValues.text - 4,
                color: colors?.text?.tertiary || '#999',
              },
            ]}
          >
            Please wait while we load your content
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  indicatorContainer: {
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 2,
  },
  pulseIndicator: {
    borderRadius: 50,
  },
  progressContainer: {
    alignItems: 'center',
    width: 200,
  },
  progressBar: {
    width: '100%',
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  skeletonContainer: {
    width: 200,
  },
  skeletonLine: {
    borderRadius: 4,
    marginBottom: 8,
  },
  message: {
    textAlign: 'center',
    fontWeight: '500',
  },
  progressText: {
    textAlign: 'center',
    fontWeight: '600',
  },
  timeContainer: {
    alignItems: 'center',
  },
  timeText: {
    textAlign: 'center',
    marginBottom: 2,
  },
  helpText: {
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 8,
  },
});

export default EnhancedLoadingIndicator;
