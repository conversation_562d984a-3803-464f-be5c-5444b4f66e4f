/**
 * Animation Utilities
 *
 * Comprehensive animation utilities for creating smooth, performant
 * animations and micro-interactions throughout the application.
 *
 * Features:
 * - Predefined animation presets
 * - Easing functions
 * - Performance optimization
 * - Accessibility compliance
 * - Gesture animations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Animated, Easing, Platform } from 'react-native';

// Animation durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  instant: 0,
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
  slowest: 750,
} as const;

// Easing presets
export const EASING_PRESETS = {
  linear: Easing.linear,
  ease: Easing.ease,
  easeIn: Easing.in(Easing.ease),
  easeOut: Easing.out(Easing.ease),
  easeInOut: Easing.inOut(Easing.ease),
  
  // Cubic bezier equivalents
  easeInQuad: Easing.in(Easing.quad),
  easeOutQuad: Easing.out(Easing.quad),
  easeInOutQuad: Easing.inOut(Easing.quad),
  
  easeInCubic: Easing.in(Easing.cubic),
  easeOutCubic: Easing.out(Easing.cubic),
  easeInOutCubic: Easing.inOut(Easing.cubic),
  
  // Spring-like easing
  easeOutBack: Easing.out(Easing.back(1.7)),
  easeInBack: Easing.in(Easing.back(1.7)),
  easeInOutBack: Easing.inOut(Easing.back(1.7)),
  
  // Bounce
  easeOutBounce: Easing.bounce,
  easeInBounce: Easing.in(Easing.bounce),
  easeInOutBounce: Easing.inOut(Easing.bounce),
} as const;

// Animation configuration interface
export interface AnimationConfig {
  duration?: number;
  easing?: keyof typeof EASING_PRESETS;
  delay?: number;
  useNativeDriver?: boolean;
}

// Spring animation configuration
export interface SpringConfig {
  tension?: number;
  friction?: number;
  speed?: number;
  bounciness?: number;
  useNativeDriver?: boolean;
}

/**
 * Create a timing animation with presets
 */
export const createTimingAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  const {
    duration = ANIMATION_DURATIONS.normal,
    easing = 'easeInOut',
    delay = 0,
    useNativeDriver = true,
  } = config;

  const animation = Animated.timing(animatedValue, {
    toValue,
    duration,
    easing: EASING_PRESETS[easing],
    useNativeDriver,
    delay,
  });

  return animation;
};

/**
 * Create a spring animation
 */
export const createSpringAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  config: SpringConfig = {}
): Animated.CompositeAnimation => {
  const {
    tension = 100,
    friction = 8,
    speed = 12,
    bounciness = 8,
    useNativeDriver = true,
  } = config;

  return Animated.spring(animatedValue, {
    toValue,
    tension,
    friction,
    speed,
    bounciness,
    useNativeDriver,
  });
};

/**
 * Fade in animation
 */
export const fadeIn = (
  animatedValue: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(animatedValue, 1, {
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeOut',
    ...config,
  });
};

/**
 * Fade out animation
 */
export const fadeOut = (
  animatedValue: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(animatedValue, 0, {
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeIn',
    ...config,
  });
};

/**
 * Scale in animation
 */
export const scaleIn = (
  animatedValue: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createSpringAnimation(animatedValue, 1, {
    tension: 150,
    friction: 8,
    ...config,
  });
};

/**
 * Scale out animation
 */
export const scaleOut = (
  animatedValue: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(animatedValue, 0, {
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeIn',
    ...config,
  });
};

/**
 * Slide in from direction
 */
export const slideIn = (
  animatedValue: Animated.Value,
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance: number = 50,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(animatedValue, 0, {
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeOut',
    ...config,
  });
};

/**
 * Slide out to direction
 */
export const slideOut = (
  animatedValue: Animated.Value,
  direction: 'up' | 'down' | 'left' | 'right' = 'down',
  distance: number = 50,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  const targetValue = direction === 'up' ? -distance :
                     direction === 'down' ? distance :
                     direction === 'left' ? -distance : distance;

  return createTimingAnimation(animatedValue, targetValue, {
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeIn',
    ...config,
  });
};

/**
 * Pulse animation (scale up and down)
 */
export const pulse = (
  animatedValue: Animated.Value,
  scale: number = 1.1,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return Animated.sequence([
    createTimingAnimation(animatedValue, scale, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeOut',
      ...config,
    }),
    createTimingAnimation(animatedValue, 1, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeIn',
      ...config,
    }),
  ]);
};

/**
 * Shake animation (horizontal movement)
 */
export const shake = (
  animatedValue: Animated.Value,
  intensity: number = 10,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return Animated.sequence([
    createTimingAnimation(animatedValue, intensity, {
      duration: ANIMATION_DURATIONS.fast / 4,
      easing: 'linear',
      ...config,
    }),
    createTimingAnimation(animatedValue, -intensity, {
      duration: ANIMATION_DURATIONS.fast / 2,
      easing: 'linear',
      ...config,
    }),
    createTimingAnimation(animatedValue, intensity, {
      duration: ANIMATION_DURATIONS.fast / 2,
      easing: 'linear',
      ...config,
    }),
    createTimingAnimation(animatedValue, 0, {
      duration: ANIMATION_DURATIONS.fast / 4,
      easing: 'linear',
      ...config,
    }),
  ]);
};

/**
 * Bounce animation
 */
export const bounce = (
  animatedValue: Animated.Value,
  height: number = 20,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return Animated.sequence([
    createTimingAnimation(animatedValue, -height, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeOut',
      ...config,
    }),
    createTimingAnimation(animatedValue, 0, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeOutBounce',
      ...config,
    }),
  ]);
};

/**
 * Rotate animation
 */
export const rotate = (
  animatedValue: Animated.Value,
  rotations: number = 1,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(animatedValue, rotations, {
    duration: ANIMATION_DURATIONS.slow,
    easing: 'linear',
    ...config,
  });
};

/**
 * Stagger animations for multiple elements
 */
export const staggerAnimations = (
  animations: Animated.CompositeAnimation[],
  staggerDelay: number = 100
): Animated.CompositeAnimation => {
  const staggeredAnimations = animations.map((animation, index) => 
    Animated.sequence([
      Animated.delay(index * staggerDelay),
      animation,
    ])
  );

  return Animated.parallel(staggeredAnimations);
};

/**
 * Create entrance animation sequence
 */
export const createEntranceAnimation = (
  opacity: Animated.Value,
  scale: Animated.Value,
  translateY: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return Animated.parallel([
    fadeIn(opacity, config),
    scaleIn(scale, config),
    slideIn(translateY, 'up', 30, config),
  ]);
};

/**
 * Create exit animation sequence
 */
export const createExitAnimation = (
  opacity: Animated.Value,
  scale: Animated.Value,
  translateY: Animated.Value,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return Animated.parallel([
    fadeOut(opacity, config),
    scaleOut(scale, config),
    slideOut(translateY, 'down', 30, config),
  ]);
};

/**
 * Create press animation (scale down and back up)
 */
export const createPressAnimation = (
  scale: Animated.Value,
  pressScale: number = 0.95
): {
  pressIn: () => void;
  pressOut: () => void;
} => {
  const pressIn = () => {
    createTimingAnimation(scale, pressScale, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeOut',
    }).start();
  };

  const pressOut = () => {
    createSpringAnimation(scale, 1, {
      tension: 150,
      friction: 8,
    }).start();
  };

  return { pressIn, pressOut };
};

/**
 * Create loading animation (continuous rotation)
 */
export const createLoadingAnimation = (
  rotation: Animated.Value
): Animated.CompositeAnimation => {
  return Animated.loop(
    createTimingAnimation(rotation, 1, {
      duration: 1000,
      easing: 'linear',
    })
  );
};

/**
 * Create progress animation
 */
export const createProgressAnimation = (
  progress: Animated.Value,
  targetProgress: number,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  return createTimingAnimation(progress, targetProgress, {
    duration: ANIMATION_DURATIONS.slow,
    easing: 'easeOut',
    ...config,
  });
};

/**
 * Check if animations should be reduced (accessibility)
 */
export const shouldReduceMotion = (): boolean => {
  // This would typically check system accessibility settings
  // For now, return false (animations enabled)
  return false;
};

/**
 * Create accessible animation (respects reduce motion preference)
 */
export const createAccessibleAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  config: AnimationConfig = {}
): Animated.CompositeAnimation => {
  if (shouldReduceMotion()) {
    // Instant animation for reduced motion
    return createTimingAnimation(animatedValue, toValue, {
      ...config,
      duration: ANIMATION_DURATIONS.instant,
    });
  }

  return createTimingAnimation(animatedValue, toValue, config);
};

/**
 * Animation presets for common UI elements
 */
export const ANIMATION_PRESETS = {
  // Button interactions
  buttonPress: (scale: Animated.Value) => createPressAnimation(scale, 0.95),
  buttonHover: (scale: Animated.Value) => createPressAnimation(scale, 1.05),
  
  // Modal animations
  modalEnter: (opacity: Animated.Value, scale: Animated.Value) => 
    Animated.parallel([
      fadeIn(opacity, { duration: ANIMATION_DURATIONS.normal }),
      scaleIn(scale, { tension: 120, friction: 8 }),
    ]),
  
  modalExit: (opacity: Animated.Value, scale: Animated.Value) =>
    Animated.parallel([
      fadeOut(opacity, { duration: ANIMATION_DURATIONS.fast }),
      scaleOut(scale, { duration: ANIMATION_DURATIONS.fast }),
    ]),
  
  // List item animations
  listItemEnter: (opacity: Animated.Value, translateX: Animated.Value) =>
    Animated.parallel([
      fadeIn(opacity, { duration: ANIMATION_DURATIONS.normal }),
      slideIn(translateX, 'right', 50, { duration: ANIMATION_DURATIONS.normal }),
    ]),
  
  // Loading states
  skeletonPulse: (opacity: Animated.Value) =>
    Animated.loop(
      Animated.sequence([
        fadeOut(opacity, { duration: ANIMATION_DURATIONS.slow }),
        fadeIn(opacity, { duration: ANIMATION_DURATIONS.slow }),
      ])
    ),
  
  // Success/Error feedback
  successPulse: (scale: Animated.Value) => pulse(scale, 1.2),
  errorShake: (translateX: Animated.Value) => shake(translateX, 8),
} as const;

export default {
  ANIMATION_DURATIONS,
  EASING_PRESETS,
  createTimingAnimation,
  createSpringAnimation,
  fadeIn,
  fadeOut,
  scaleIn,
  scaleOut,
  slideIn,
  slideOut,
  pulse,
  shake,
  bounce,
  rotate,
  staggerAnimations,
  createEntranceAnimation,
  createExitAnimation,
  createPressAnimation,
  createLoadingAnimation,
  createProgressAnimation,
  shouldReduceMotion,
  createAccessibleAnimation,
  ANIMATION_PRESETS,
};
