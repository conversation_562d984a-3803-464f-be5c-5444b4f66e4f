bd466bde80c46af188d824ba3b89479c
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function deepFreezeAndThrowOnMutationInDev(object) {
  if (__DEV__) {
    if (typeof object !== 'object' || object === null || Object.isFrozen(object) || Object.isSealed(object)) {
      return object;
    }
    var keys = Object.keys(object);
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      if (hasOwnProperty.call(object, key)) {
        Object.defineProperty(object, key, {
          get: identity.bind(null, object[key])
        });
        Object.defineProperty(object, key, {
          set: throwOnImmutableMutation.bind(null, key)
        });
      }
    }
    Object.freeze(object);
    Object.seal(object);
    for (var _i = 0; _i < keys.length; _i++) {
      var _key = keys[_i];
      if (hasOwnProperty.call(object, _key)) {
        deepFreezeAndThrowOnMutationInDev(object[_key]);
      }
    }
  }
  return object;
}
function throwOnImmutableMutation(key, value) {
  throw Error('You attempted to set the key `' + key + '` with the value `' + JSON.stringify(value) + '` on an object that is meant to be immutable ' + 'and has been frozen.');
}
function identity(value) {
  return value;
}
var _default = exports.default = deepFreezeAndThrowOnMutationInDev;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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