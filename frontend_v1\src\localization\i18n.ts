/**
 * Internationalization Configuration
 *
 * Comprehensive i18n setup for French-Canadian (Quebec French) localization
 * with proper Quebec French standards and cultural adaptations.
 *
 * Features:
 * - Quebec French translations
 * - Cultural adaptations
 * - Date/time formatting
 * - Number/currency formatting
 * - Pluralization rules
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, NativeModules } from 'react-native';

// Import translation files
import enTranslations from './translations/en.json';
import frCATranslations from './translations/fr-CA.json';

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = '@vierla_language';

// Supported languages
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇨🇦',
  },
  'fr-CA': {
    code: 'fr-CA',
    name: 'French (Canada)',
    nativeName: 'Français (Canada)',
    flag: '🇨🇦',
  },
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

// Get device language
const getDeviceLanguage = (): string => {
  let deviceLanguage = 'en';
  
  if (Platform.OS === 'ios') {
    deviceLanguage = NativeModules.SettingsManager?.settings?.AppleLocale ||
                    NativeModules.SettingsManager?.settings?.AppleLanguages?.[0] ||
                    'en';
  } else if (Platform.OS === 'android') {
    deviceLanguage = NativeModules.I18nManager?.localeIdentifier || 'en';
  }
  
  // Normalize language code
  if (deviceLanguage.startsWith('fr')) {
    // Check if it's specifically Canadian French
    if (deviceLanguage.includes('CA') || deviceLanguage.includes('Quebec')) {
      return 'fr-CA';
    }
    // Default French to Canadian French for Canadian market
    return 'fr-CA';
  }
  
  return 'en';
};

// Language detection plugin
const languageDetector = {
  type: 'languageDetector' as const,
  async: true,
  detect: async (callback: (lng: string) => void) => {
    try {
      // Try to get saved language preference
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (savedLanguage && Object.keys(SUPPORTED_LANGUAGES).includes(savedLanguage)) {
        callback(savedLanguage);
        return;
      }
      
      // Fall back to device language
      const deviceLanguage = getDeviceLanguage();
      callback(deviceLanguage);
    } catch (error) {
      console.warn('Language detection failed:', error);
      callback('en');
    }
  },
  init: () => {},
  cacheUserLanguage: async (lng: string) => {
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, lng);
    } catch (error) {
      console.warn('Failed to cache language:', error);
    }
  },
};

// Quebec French pluralization rules
const quebecFrenchPluralRule = (count: number): number => {
  // Quebec French follows standard French pluralization
  // 0 and 1 are singular, everything else is plural
  return count === 0 || count === 1 ? 0 : 1;
};

// Initialize i18n
i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3',
    fallbackLng: 'en',
    debug: __DEV__,
    
    // Resources
    resources: {
      en: {
        translation: enTranslations,
      },
      'fr-CA': {
        translation: frCATranslations,
      },
    },
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      format: (value, format, lng) => {
        if (format === 'currency') {
          return formatCurrency(value, lng);
        }
        if (format === 'date') {
          return formatDate(value, lng);
        }
        if (format === 'time') {
          return formatTime(value, lng);
        }
        if (format === 'number') {
          return formatNumber(value, lng);
        }
        return value;
      },
    },
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Quebec French specific configuration
    lng: getDeviceLanguage(),
    
    // Namespace configuration
    defaultNS: 'translation',
    ns: ['translation'],
    
    // React specific options
    react: {
      useSuspense: false,
    },
  });

// Custom formatting functions for Quebec standards
export const formatCurrency = (amount: number, language?: string): string => {
  const lng = language || i18n.language;
  
  if (lng === 'fr-CA') {
    // Quebec French currency formatting: 1 234,56 $
    return new Intl.NumberFormat('fr-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
    }).format(amount);
  }
  
  // English Canadian currency formatting: $1,234.56
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 2,
  }).format(amount);
};

export const formatDate = (date: Date | string, language?: string): string => {
  const lng = language || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (lng === 'fr-CA') {
    // Quebec French date format: 31 décembre 2023
    return new Intl.DateTimeFormat('fr-CA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(dateObj);
  }
  
  // English Canadian date format: December 31, 2023
  return new Intl.DateTimeFormat('en-CA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj);
};

export const formatTime = (time: Date | string, language?: string): string => {
  const lng = language || i18n.language;
  const timeObj = typeof time === 'string' ? new Date(time) : time;
  
  if (lng === 'fr-CA') {
    // Quebec French time format: 14 h 30
    return new Intl.DateTimeFormat('fr-CA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    }).format(timeObj);
  }
  
  // English Canadian time format: 2:30 PM
  return new Intl.DateTimeFormat('en-CA', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }).format(timeObj);
};

export const formatNumber = (number: number, language?: string): string => {
  const lng = language || i18n.language;
  
  if (lng === 'fr-CA') {
    // Quebec French number format: 1 234,56
    return new Intl.NumberFormat('fr-CA').format(number);
  }
  
  // English Canadian number format: 1,234.56
  return new Intl.NumberFormat('en-CA').format(number);
};

// Quebec French specific utilities
export const getQuebecFrenchGender = (word: string): 'masculine' | 'feminine' => {
  // Simple heuristics for Quebec French gender
  // This would be expanded with a proper dictionary in production
  const feminineEndings = ['e', 'ion', 'té', 'ée', 'ance', 'ence'];
  const lastTwoChars = word.slice(-2).toLowerCase();
  const lastChar = word.slice(-1).toLowerCase();
  
  if (feminineEndings.some(ending => word.toLowerCase().endsWith(ending))) {
    return 'feminine';
  }
  
  return 'masculine';
};

export const getQuebecFrenchArticle = (word: string, definite: boolean = true): string => {
  const gender = getQuebecFrenchGender(word);
  const startsWithVowel = /^[aeiouAEIOU]/.test(word);
  
  if (definite) {
    if (startsWithVowel) return "l'";
    return gender === 'masculine' ? 'le' : 'la';
  } else {
    if (startsWithVowel) return "un";
    return gender === 'masculine' ? 'un' : 'une';
  }
};

// Language switching utilities
export const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    await i18n.changeLanguage(language);
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    console.error('Failed to change language:', error);
  }
};

export const getCurrentLanguage = (): SupportedLanguage => {
  return i18n.language as SupportedLanguage;
};

export const isQuebecFrench = (): boolean => {
  return getCurrentLanguage() === 'fr-CA';
};

// Translation helpers
export const t = (key: string, options?: any): string => {
  return i18n.t(key, options);
};

export const tWithFallback = (key: string, fallback: string, options?: any): string => {
  const translation = i18n.t(key, { ...options, defaultValue: fallback });
  return translation === key ? fallback : translation;
};

// Quebec French specific translations
export const tQuebec = (key: string, options?: any): string => {
  if (isQuebecFrench()) {
    // Try Quebec-specific key first
    const quebecKey = `${key}_qc`;
    const quebecTranslation = i18n.t(quebecKey, { ...options, defaultValue: null });
    if (quebecTranslation) {
      return quebecTranslation;
    }
  }
  
  return i18n.t(key, options);
};

// Accessibility announcements in proper language
export const announceToScreenReader = (message: string): void => {
  // This would integrate with AccessibilityInfo.announceForAccessibility
  // with proper language context
  console.log(`Screen reader announcement (${getCurrentLanguage()}):`, message);
};

// Export i18n instance
export default i18n;
