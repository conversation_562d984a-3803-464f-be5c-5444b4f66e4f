/**
 * Typography System
 *
 * Comprehensive typography scale and hierarchy system following
 * modern design principles and accessibility guidelines.
 *
 * Features:
 * - Modular scale typography
 * - Responsive font sizes
 * - Accessibility compliance
 * - Platform optimization
 * - Visual hierarchy support
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform, PixelRatio } from 'react-native';

// Base typography configuration
const BASE_FONT_SIZE = 16;
const SCALE_RATIO = 1.25; // Major third scale
const LINE_HEIGHT_RATIO = 1.5;

// Font families
export const FONT_FAMILIES = {
  primary: Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    default: 'System',
  }),
  secondary: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    default: 'System',
  }),
  monospace: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    web: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
    default: 'monospace',
  }),
} as const;

// Font weights
export const FONT_WEIGHTS = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
} as const;

// Calculate responsive font size
const getResponsiveFontSize = (size: number): number => {
  const scale = PixelRatio.getFontScale();
  return Math.round(size * scale);
};

// Generate modular scale
const generateScale = (base: number, ratio: number, steps: number): number[] => {
  const scale: number[] = [];
  for (let i = -2; i <= steps; i++) {
    scale.push(Math.round(base * Math.pow(ratio, i)));
  }
  return scale;
};

const SCALE = generateScale(BASE_FONT_SIZE, SCALE_RATIO, 6);

// Typography scale
export const FONT_SIZES = {
  xs: getResponsiveFontSize(SCALE[0]), // 10px
  sm: getResponsiveFontSize(SCALE[1]), // 13px
  base: getResponsiveFontSize(SCALE[2]), // 16px
  lg: getResponsiveFontSize(SCALE[3]), // 20px
  xl: getResponsiveFontSize(SCALE[4]), // 25px
  '2xl': getResponsiveFontSize(SCALE[5]), // 31px
  '3xl': getResponsiveFontSize(SCALE[6]), // 39px
  '4xl': getResponsiveFontSize(SCALE[7]), // 49px
  '5xl': getResponsiveFontSize(SCALE[8]), // 61px
} as const;

// Line heights
export const LINE_HEIGHTS = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75,
  loose: 2,
} as const;

// Letter spacing
export const LETTER_SPACING = {
  tighter: -0.05,
  tight: -0.025,
  normal: 0,
  wide: 0.025,
  wider: 0.05,
  widest: 0.1,
} as const;

// Typography variants for semantic usage
export const TYPOGRAPHY_VARIANTS = {
  // Headings
  h1: {
    fontSize: FONT_SIZES['4xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['4xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight,
  },
  h2: {
    fontSize: FONT_SIZES['3xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['3xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight,
  },
  h3: {
    fontSize: FONT_SIZES['2xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES['2xl'] * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },
  h4: {
    fontSize: FONT_SIZES.xl,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.xl * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },
  h5: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },
  h6: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },

  // Body text
  body1: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },
  body2: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },

  // Specialized text
  subtitle1: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide,
  },
  subtitle2: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide,
  },
  caption: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide,
  },
  overline: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.widest,
    textTransform: 'uppercase' as const,
  },

  // Interactive elements
  button: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide,
  },
  buttonSmall: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide,
  },
  buttonLarge: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide,
  },

  // Form elements
  input: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide,
  },
  helper: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal,
  },

  // Navigation
  navItem: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.normal,
  },
  tabItem: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide,
  },

  // Code and monospace
  code: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.monospace,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.relaxed,
    letterSpacing: LETTER_SPACING.normal,
  },
} as const;

// Accessibility helpers
export const ACCESSIBILITY_FONT_SIZES = {
  minimum: 12, // Minimum readable size
  comfortable: 16, // Comfortable reading size
  large: 20, // Large text for accessibility
  extraLarge: 24, // Extra large for low vision
} as const;

// Reading width guidelines
export const READING_WIDTH = {
  optimal: 65, // 45-75 characters per line is optimal
  maximum: 75,
  minimum: 45,
} as const;

// Typography utilities
export const getOptimalLineHeight = (fontSize: number): number => {
  // Larger text needs tighter line height
  if (fontSize >= FONT_SIZES.xl) return fontSize * 1.2;
  if (fontSize >= FONT_SIZES.lg) return fontSize * 1.3;
  return fontSize * 1.5;
};

export const getAccessibleFontSize = (
  baseFontSize: number,
  userPreference: 'small' | 'normal' | 'large' | 'extraLarge' = 'normal'
): number => {
  const multipliers = {
    small: 0.875,
    normal: 1,
    large: 1.125,
    extraLarge: 1.25,
  };
  
  return Math.max(
    ACCESSIBILITY_FONT_SIZES.minimum,
    Math.round(baseFontSize * multipliers[userPreference])
  );
};

export const isTextSizeAccessible = (fontSize: number): boolean => {
  return fontSize >= ACCESSIBILITY_FONT_SIZES.minimum;
};

// Platform-specific adjustments
export const getPlatformAdjustedSize = (size: number): number => {
  if (Platform.OS === 'android') {
    // Android typically needs slightly larger sizes
    return Math.round(size * 1.05);
  }
  return size;
};

// Export default typography object
export default {
  FONT_FAMILIES,
  FONT_WEIGHTS,
  FONT_SIZES,
  LINE_HEIGHTS,
  LETTER_SPACING,
  TYPOGRAPHY_VARIANTS,
  ACCESSIBILITY_FONT_SIZES,
  READING_WIDTH,
  getOptimalLineHeight,
  getAccessibleFontSize,
  isTextSizeAccessible,
  getPlatformAdjustedSize,
};
