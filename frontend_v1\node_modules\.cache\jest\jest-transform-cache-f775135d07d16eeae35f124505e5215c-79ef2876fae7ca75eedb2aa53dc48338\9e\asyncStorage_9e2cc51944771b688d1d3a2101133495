993d8a441f9cac3dc63f0936d9de52ec
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var asyncStorageMock = {
  getItem: jest.fn(function () {
    return Promise.resolve(null);
  }),
  setItem: jest.fn(function () {
    return Promise.resolve();
  }),
  removeItem: jest.fn(function () {
    return Promise.resolve();
  }),
  clear: jest.fn(function () {
    return Promise.resolve();
  }),
  getAllKeys: jest.fn(function () {
    return Promise.resolve([]);
  }),
  multiGet: jest.fn(function () {
    return Promise.resolve([]);
  }),
  multiSet: jest.fn(function () {
    return Promise.resolve();
  }),
  multiRemove: jest.fn(function () {
    return Promise.resolve();
  })
};
var _default = exports.default = asyncStorageMock;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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