/**
 * Network & API Optimization Utilities
 *
 * Comprehensive utilities for network optimization, caching,
 * request batching, and offline capabilities.
 *
 * Features:
 * - Request caching
 * - Request batching
 * - Offline support
 * - Network monitoring
 * - Performance optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

// Network optimization configuration
export interface NetworkOptimizationConfig {
  enableCaching: boolean;
  enableBatching: boolean;
  enableOfflineSupport: boolean;
  enableRetry: boolean;
  cacheMaxAge: number; // milliseconds
  batchDelay: number; // milliseconds
  maxRetries: number;
  retryDelay: number; // milliseconds
  timeoutMs: number;
}

// Cache entry structure
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  etag?: string;
  maxAge: number;
  hitCount: number;
}

// Request queue item
interface QueuedRequest {
  id: string;
  url: string;
  options: RequestInit;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  timestamp: number;
  retryCount: number;
}

// Network performance metrics
export interface NetworkMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  bytesTransferred: number;
  offlineRequests: number;
}

// Network status
export interface NetworkStatus {
  isConnected: boolean;
  connectionType: string;
  isInternetReachable: boolean;
  strength: 'poor' | 'fair' | 'good' | 'excellent';
}

// Default configuration
const DEFAULT_CONFIG: NetworkOptimizationConfig = {
  enableCaching: true,
  enableBatching: true,
  enableOfflineSupport: true,
  enableRetry: true,
  cacheMaxAge: 5 * 60 * 1000, // 5 minutes
  batchDelay: 100, // 100ms
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  timeoutMs: 10000, // 10 seconds
};

// Global cache and metrics
const requestCache = new Map<string, CacheEntry<any>>();
const requestQueue: QueuedRequest[] = [];
const networkMetrics: NetworkMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  averageResponseTime: 0,
  bytesTransferred: 0,
  offlineRequests: 0,
};

let networkStatus: NetworkStatus = {
  isConnected: true,
  connectionType: 'unknown',
  isInternetReachable: true,
  strength: 'good',
};

let batchTimeout: NodeJS.Timeout | null = null;

/**
 * Initialize network monitoring
 */
export const initializeNetworkMonitoring = (): void => {
  NetInfo.addEventListener(state => {
    networkStatus = {
      isConnected: state.isConnected ?? false,
      connectionType: state.type,
      isInternetReachable: state.isInternetReachable ?? false,
      strength: getConnectionStrength(state),
    };
  });
};

/**
 * Get connection strength based on network info
 */
const getConnectionStrength = (state: any): NetworkStatus['strength'] => {
  if (!state.isConnected) return 'poor';
  
  switch (state.type) {
    case 'wifi':
      return 'excellent';
    case 'cellular':
      if (state.details?.cellularGeneration === '4g' || state.details?.cellularGeneration === '5g') {
        return 'good';
      }
      return 'fair';
    default:
      return 'fair';
  }
};

/**
 * Generate cache key for request
 */
const generateCacheKey = (url: string, options: RequestInit = {}): string => {
  const method = options.method || 'GET';
  const body = options.body ? JSON.stringify(options.body) : '';
  return btoa(`${method}:${url}:${body}`).replace(/[^a-zA-Z0-9]/g, '');
};

/**
 * Check if cache entry is valid
 */
const isCacheValid = (entry: CacheEntry<any>): boolean => {
  const now = Date.now();
  return (now - entry.timestamp) < entry.maxAge;
};

/**
 * Get cached response
 */
const getCachedResponse = <T>(cacheKey: string): T | null => {
  const entry = requestCache.get(cacheKey);
  if (entry && isCacheValid(entry)) {
    entry.hitCount++;
    networkMetrics.cacheHits++;
    return entry.data;
  }
  
  if (entry) {
    requestCache.delete(cacheKey);
  }
  
  networkMetrics.cacheMisses++;
  return null;
};

/**
 * Cache response
 */
const cacheResponse = <T>(
  cacheKey: string,
  data: T,
  maxAge: number = DEFAULT_CONFIG.cacheMaxAge,
  etag?: string
): void => {
  requestCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    etag,
    maxAge,
    hitCount: 1,
  });

  // Clean old cache entries
  if (requestCache.size > 100) {
    cleanCache();
  }
};

/**
 * Clean old cache entries
 */
const cleanCache = (): void => {
  const now = Date.now();
  const entries = Array.from(requestCache.entries());
  
  // Sort by hit count and age
  entries.sort((a, b) => {
    const aScore = a[1].hitCount - (now - a[1].timestamp) / 1000;
    const bScore = b[1].hitCount - (now - b[1].timestamp) / 1000;
    return aScore - bScore;
  });

  // Remove oldest 20%
  const toRemove = Math.ceil(entries.length * 0.2);
  for (let i = 0; i < toRemove; i++) {
    requestCache.delete(entries[i][0]);
  }
};

/**
 * Process request queue
 */
const processRequestQueue = async (): Promise<void> => {
  if (requestQueue.length === 0) return;

  const batch = requestQueue.splice(0, 10); // Process up to 10 requests at once
  
  await Promise.allSettled(
    batch.map(async (queuedRequest) => {
      try {
        const response = await executeRequest(
          queuedRequest.url,
          queuedRequest.options,
          false // Don't queue again
        );
        queuedRequest.resolve(response);
      } catch (error) {
        if (queuedRequest.retryCount < DEFAULT_CONFIG.maxRetries) {
          queuedRequest.retryCount++;
          queuedRequest.timestamp = Date.now() + DEFAULT_CONFIG.retryDelay;
          requestQueue.push(queuedRequest);
        } else {
          queuedRequest.reject(error);
        }
      }
    })
  );

  // Continue processing if there are more requests
  if (requestQueue.length > 0) {
    setTimeout(processRequestQueue, DEFAULT_CONFIG.batchDelay);
  }
};

/**
 * Execute HTTP request with optimizations
 */
const executeRequest = async <T>(
  url: string,
  options: RequestInit = {},
  allowQueue: boolean = true
): Promise<T> => {
  const startTime = Date.now();
  networkMetrics.totalRequests++;

  // Check cache first
  if (DEFAULT_CONFIG.enableCaching && (!options.method || options.method === 'GET')) {
    const cacheKey = generateCacheKey(url, options);
    const cached = getCachedResponse<T>(cacheKey);
    if (cached) {
      return cached;
    }
  }

  // Check network status
  if (!networkStatus.isConnected && DEFAULT_CONFIG.enableOfflineSupport) {
    if (allowQueue) {
      return new Promise((resolve, reject) => {
        const queuedRequest: QueuedRequest = {
          id: Math.random().toString(36),
          url,
          options,
          resolve,
          reject,
          timestamp: Date.now(),
          retryCount: 0,
        };
        
        requestQueue.push(queuedRequest);
        networkMetrics.offlineRequests++;
        
        // Start processing queue if not already running
        if (batchTimeout === null) {
          batchTimeout = setTimeout(() => {
            batchTimeout = null;
            processRequestQueue();
          }, DEFAULT_CONFIG.batchDelay);
        }
      });
    } else {
      throw new Error('Network unavailable');
    }
  }

  try {
    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DEFAULT_CONFIG.timeoutMs);

    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // Update metrics
    networkMetrics.successfulRequests++;
    networkMetrics.averageResponseTime = 
      (networkMetrics.averageResponseTime + responseTime) / 2;
    
    // Estimate bytes transferred
    const contentLength = response.headers.get('content-length');
    if (contentLength) {
      networkMetrics.bytesTransferred += parseInt(contentLength, 10);
    }

    // Cache successful GET requests
    if (DEFAULT_CONFIG.enableCaching && (!options.method || options.method === 'GET')) {
      const cacheKey = generateCacheKey(url, options);
      const etag = response.headers.get('etag') || undefined;
      cacheResponse(cacheKey, data, DEFAULT_CONFIG.cacheMaxAge, etag);
    }

    return data;
  } catch (error) {
    networkMetrics.failedRequests++;
    
    if (DEFAULT_CONFIG.enableRetry && allowQueue) {
      // Retry with exponential backoff
      const retryDelay = DEFAULT_CONFIG.retryDelay * Math.pow(2, 0); // Start with base delay
      
      return new Promise((resolve, reject) => {
        setTimeout(async () => {
          try {
            const result = await executeRequest<T>(url, options, false);
            resolve(result);
          } catch (retryError) {
            reject(retryError);
          }
        }, retryDelay);
      });
    }
    
    throw error;
  }
};

/**
 * Optimized fetch function
 */
export const optimizedFetch = async <T>(
  url: string,
  options: RequestInit = {}
): Promise<T> => {
  return executeRequest<T>(url, options);
};

/**
 * Batch multiple requests
 */
export const batchRequests = async <T>(
  requests: Array<{ url: string; options?: RequestInit }>
): Promise<T[]> => {
  const promises = requests.map(({ url, options }) => 
    optimizedFetch<T>(url, options)
  );
  
  return Promise.all(promises);
};

/**
 * Prefetch resources
 */
export const prefetchResources = async (urls: string[]): Promise<void> => {
  const prefetchPromises = urls.map(url => 
    optimizedFetch(url, { method: 'GET' }).catch(() => {
      // Ignore prefetch errors
    })
  );
  
  await Promise.allSettled(prefetchPromises);
};

/**
 * Clear request cache
 */
export const clearRequestCache = (): void => {
  requestCache.clear();
  networkMetrics.cacheHits = 0;
  networkMetrics.cacheMisses = 0;
};

/**
 * Get network metrics
 */
export const getNetworkMetrics = (): NetworkMetrics => {
  return { ...networkMetrics };
};

/**
 * Get network status
 */
export const getNetworkStatus = (): NetworkStatus => {
  return { ...networkStatus };
};

/**
 * Get cache statistics
 */
export const getCacheStatistics = () => {
  const totalEntries = requestCache.size;
  const totalHits = Array.from(requestCache.values()).reduce(
    (sum, entry) => sum + entry.hitCount, 0
  );
  const hitRate = networkMetrics.cacheHits + networkMetrics.cacheMisses > 0
    ? networkMetrics.cacheHits / (networkMetrics.cacheHits + networkMetrics.cacheMisses)
    : 0;

  return {
    totalEntries,
    totalHits,
    hitRate,
    cacheHits: networkMetrics.cacheHits,
    cacheMisses: networkMetrics.cacheMisses,
  };
};

/**
 * Network optimization recommendations
 */
export const getNetworkOptimizationRecommendations = (): string[] => {
  const recommendations: string[] = [];
  const metrics = getNetworkMetrics();
  const cacheStats = getCacheStatistics();
  
  // Check success rate
  const successRate = metrics.totalRequests > 0 
    ? metrics.successfulRequests / metrics.totalRequests 
    : 0;
  
  if (successRate < 0.9) {
    recommendations.push('High failure rate detected, check network stability');
  }
  
  // Check cache hit rate
  if (cacheStats.hitRate < 0.5) {
    recommendations.push('Low cache hit rate, consider adjusting cache strategy');
  }
  
  // Check response time
  if (metrics.averageResponseTime > 2000) {
    recommendations.push('Slow response times detected, consider request optimization');
  }
  
  // Check offline requests
  if (metrics.offlineRequests > 10) {
    recommendations.push('Many offline requests, improve offline handling');
  }
  
  return recommendations;
};

/**
 * Monitor network performance
 */
export const useNetworkPerformanceMonitor = () => {
  const [metrics, setMetrics] = React.useState<NetworkMetrics>(getNetworkMetrics());
  const [status, setStatus] = React.useState<NetworkStatus>(getNetworkStatus());

  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(getNetworkMetrics());
      setStatus(getNetworkStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    status,
    cacheStats: getCacheStatistics(),
    recommendations: getNetworkOptimizationRecommendations(),
  };
};

/**
 * Offline queue manager
 */
export const offlineQueueManager = {
  // Get queued requests count
  getQueuedCount: (): number => requestQueue.length,
  
  // Clear queue
  clearQueue: (): void => {
    requestQueue.length = 0;
  },
  
  // Process queue manually
  processQueue: (): Promise<void> => processRequestQueue(),
  
  // Get queue status
  getQueueStatus: () => ({
    count: requestQueue.length,
    oldestRequest: requestQueue.length > 0 
      ? Math.min(...requestQueue.map(r => r.timestamp))
      : null,
  }),
};

// Initialize network monitoring on module load
if (Platform.OS !== 'web') {
  initializeNetworkMonitoring();
}

export default {
  optimizedFetch,
  batchRequests,
  prefetchResources,
  clearRequestCache,
  getNetworkMetrics,
  getNetworkStatus,
  getCacheStatistics,
  getNetworkOptimizationRecommendations,
  useNetworkPerformanceMonitor,
  offlineQueueManager,
  initializeNetworkMonitoring,
  DEFAULT_CONFIG,
};
