{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "select": "Select", "clear": "Clear", "reset": "Reset", "submit": "Submit", "continue": "Continue", "finish": "Finish", "retry": "Try Again", "refresh": "Refresh", "update": "Update", "upload": "Upload", "download": "Download", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "help": "Help", "about": "About", "settings": "Settings", "profile": "Profile", "logout": "Sign Out", "login": "Sign In", "register": "Sign Up", "welcome": "Welcome", "goodbye": "Goodbye", "hello": "Hello", "yes": "Yes", "no": "No", "ok": "OK", "done": "Done", "skip": "<PERSON><PERSON>", "optional": "Optional", "required": "Required", "recommended": "Recommended", "new": "New", "popular": "Popular", "featured": "Featured", "trending": "Trending", "recent": "Recent", "all": "All", "none": "None", "other": "Other", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "view": "View", "preview": "Preview", "details": "Details", "summary": "Summary", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "free": "Free", "paid": "Paid", "price": "Price", "cost": "Cost", "amount": "Amount", "quantity": "Quantity", "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "public": "Public", "private": "Private", "draft": "Draft", "published": "Published", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "scheduled": "Scheduled", "in_progress": "In Progress", "on_hold": "On Hold", "urgent": "<PERSON><PERSON>", "high": "High", "medium": "Medium", "low": "Low", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "this_week": "This Week", "next_week": "Next Week", "last_week": "Last Week", "this_month": "This Month", "next_month": "Next Month", "last_month": "Last Month", "this_year": "This Year", "next_year": "Next Year", "last_year": "Last Year"}, "navigation": {"home": "Home", "services": "Services", "providers": "Providers", "bookings": "Bookings", "messages": "Messages", "notifications": "Notifications", "favorites": "Favorites", "history": "History", "account": "Account", "support": "Support", "contact": "Contact", "privacy": "Privacy", "terms": "Terms of Service", "faq": "FAQ", "feedback": "<PERSON><PERSON><PERSON>"}, "home": {"greeting": "Good {{timeOfDay}}", "welcome_back": "Welcome back", "browse_services": "Browse Services", "featured_providers": "Featured Providers", "favorite_providers": "Your Favorite Providers", "nearby_providers": "Nearby Providers", "recent_bookings": "Recent Bookings", "quick_booking": "Quick Booking", "search_placeholder": "Search for services...", "no_favorites": "You haven't added any favorites yet", "no_recent": "No recent bookings", "view_all": "View All", "book_now": "Book Now", "learn_more": "Learn More"}, "services": {"title": "Services", "categories": "Categories", "all_services": "All Services", "popular_services": "Popular Services", "new_services": "New Services", "service_details": "Service Details", "duration": "Duration", "price_range": "Price Range", "rating": "Rating", "reviews": "Reviews", "availability": "Availability", "description": "Description", "includes": "What's Included", "requirements": "Requirements", "cancellation": "Cancellation Policy", "book_service": "Book This Service", "add_to_favorites": "Add to Favorites", "remove_from_favorites": "Remove from Favorites", "share_service": "Share Service", "report_service": "Report Service"}, "providers": {"title": "Service Providers", "provider_profile": "Provider Profile", "about": "About", "services_offered": "Services Offered", "experience": "Experience", "certifications": "Certifications", "portfolio": "Portfolio", "reviews_and_ratings": "Reviews & Ratings", "contact_provider": "Contact Provider", "view_profile": "View Profile", "book_with_provider": "Book with {{name}}", "years_experience_one": "{{count}} year of experience", "years_experience_other": "{{count}} years of experience", "verified_provider": "Verified Provider", "top_rated": "Top Rated", "new_provider": "New Provider", "response_time": "Response Time", "completion_rate": "Completion Rate", "customer_satisfaction": "Customer Satisfaction"}, "booking": {"title": "Book Service", "select_service": "Select Service", "select_provider": "Select Provider", "select_date": "Select Date", "select_time": "Select Time", "service_location": "Service Location", "your_location": "Your Location", "provider_location": "Provider's Location", "custom_location": "Custom Location", "contact_information": "Contact Information", "special_requests": "Special Requests", "booking_summary": "Booking Summary", "payment_method": "Payment Method", "total_cost": "Total Cost", "confirm_booking": "Confirm Booking", "booking_confirmed": "Booking Confirmed", "booking_details": "Booking Details", "confirmation_number": "Confirmation Number", "estimated_duration": "Estimated Duration", "cancellation_policy": "Cancellation Policy", "reschedule": "Reschedule", "cancel_booking": "Cancel Booking", "modify_booking": "Modify Booking", "booking_status": "Booking Status", "upcoming": "Upcoming", "completed": "Completed", "cancelled": "Cancelled", "no_bookings": "No bookings found", "booking_history": "Booking History"}, "forms": {"first_name": "First Name", "last_name": "Last Name", "full_name": "Full Name", "email": "Email Address", "phone": "Phone Number", "address": "Address", "city": "City", "province": "Province", "postal_code": "Postal Code", "country": "Country", "date_of_birth": "Date of Birth", "gender": "Gender", "password": "Password", "confirm_password": "Confirm Password", "current_password": "Current Password", "new_password": "New Password", "username": "Username", "bio": "Bio", "website": "Website", "social_media": "Social Media", "emergency_contact": "Emergency Contact", "preferred_language": "Preferred Language", "timezone": "Time Zone", "notifications": "Notifications", "marketing_emails": "Marketing Emails", "sms_notifications": "SMS Notifications", "push_notifications": "Push Notifications"}, "validation": {"required": "This field is required", "email_invalid": "Please enter a valid email address", "phone_invalid": "Please enter a valid phone number", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords don't match", "postal_code_invalid": "Please enter a valid postal code", "date_invalid": "Please enter a valid date", "time_invalid": "Please enter a valid time", "number_invalid": "Please enter a valid number", "url_invalid": "Please enter a valid URL", "min_length": "Must be at least {{min}} characters", "max_length": "Must be no more than {{max}} characters", "min_value": "Must be at least {{min}}", "max_value": "Must be no more than {{max}}", "pattern_mismatch": "Please enter a valid format"}, "errors": {"network_error": "Network connection error. Please check your internet connection.", "server_error": "Server error. Please try again later.", "not_found": "The requested resource was not found.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "timeout": "Request timed out. Please try again.", "unknown_error": "An unexpected error occurred.", "validation_error": "Please check your input and try again.", "payment_error": "Payment processing error. Please try again.", "booking_error": "Booking error. Please try again.", "upload_error": "File upload error. Please try again.", "connection_lost": "Connection lost. Please check your internet connection.", "session_expired": "Your session has expired. Please sign in again.", "rate_limit": "Too many requests. Please wait and try again.", "maintenance": "Service temporarily unavailable due to maintenance."}, "success": {"profile_updated": "Profile updated successfully", "booking_confirmed": "Booking confirmed successfully", "payment_processed": "Payment processed successfully", "message_sent": "Message sent successfully", "review_submitted": "Review submitted successfully", "settings_saved": "Setting<PERSON> saved successfully", "password_changed": "Password changed successfully", "account_created": "Account created successfully", "verification_sent": "Verification email sent", "subscription_updated": "Subscription updated successfully", "file_uploaded": "File uploaded successfully", "data_exported": "Data exported successfully", "invitation_sent": "Invitation sent successfully"}, "accessibility": {"screen_reader_navigation": "Use arrow keys to navigate", "button_hint": "Double tap to activate", "link_hint": "Double tap to open", "image_description": "Image: {{description}}", "loading_announcement": "Loading content, please wait", "error_announcement": "Error occurred: {{message}}", "success_announcement": "Success: {{message}}", "page_loaded": "Page loaded successfully", "form_error": "Form has errors, please review", "required_field": "Required field", "optional_field": "Optional field", "character_count": "{{current}} of {{max}} characters", "search_results_one": "{{count}} search result found", "search_results_other": "{{count}} search results found", "no_results": "No results found", "menu_opened": "Menu opened", "menu_closed": "Menu closed", "tab_selected": "{{tab}} tab selected", "sort_by": "Sort by {{criteria}}", "filter_applied": "Filter applied: {{filter}}", "filter_removed": "Filter removed: {{filter}}"}}