3ab1e607efefc3793c316e120382ac7a
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var Platform = require("../Utilities/Platform").default;
var normalizeColor = require("./normalizeColor").default;
function processColor(color) {
  if (color === undefined || color === null) {
    return color;
  }
  var normalizedColor = normalizeColor(color);
  if (normalizedColor === null || normalizedColor === undefined) {
    return undefined;
  }
  if (typeof normalizedColor === 'object') {
    var processColorObject = require("./PlatformColorValueTypes").processColorObject;
    var processedColorObj = processColorObject(normalizedColor);
    if (processedColorObj != null) {
      return processedColorObj;
    }
  }
  if (typeof normalizedColor !== 'number') {
    return null;
  }
  normalizedColor = (normalizedColor << 24 | normalizedColor >>> 8) >>> 0;
  if (Platform.OS === 'android') {
    normalizedColor = normalizedColor | 0x0;
  }
  return normalizedColor;
}
var _default = exports.default = processColor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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