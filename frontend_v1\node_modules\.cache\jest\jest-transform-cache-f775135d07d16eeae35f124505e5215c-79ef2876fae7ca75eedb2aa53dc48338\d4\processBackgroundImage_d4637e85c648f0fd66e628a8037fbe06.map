{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "processBackgroundImage", "_slicedToArray2", "processColor", "DIRECTION_KEYWORD_REGEX", "ANGLE_UNIT_REGEX", "DEFAULT_DIRECTION", "type", "backgroundImage", "result", "parseCSSLinearGradient", "replace", "Array", "isArray", "bgImage", "processedColorStops", "index", "colorStops", "length", "colorStop", "positions", "color", "position", "endsWith", "push", "processedColor", "direction", "bgDirection", "toLowerCase", "test", "parsedAngle", "getAngleInDegrees", "parsedDirection", "getDirectionForKeyword", "concat", "cssString", "gradients", "match", "linearGradientRegex", "exec", "gradientContent", "parts", "split", "trimmedDirection", "trim", "shift", "colorStopsString", "join", "stops", "prevStop", "i", "stop", "trimmedStop", "colorStopParts", "position1", "getPositionFromCSSValue", "position2", "normalized", "angle", "_match", "unit", "numericValue", "parseFloat", "Math", "PI"], "sources": ["processBackgroundImage.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n'use strict';\n\nimport type {ProcessedColorValue} from './processColor';\nimport type {GradientValue} from './StyleSheetTypes';\n\nconst processColor = require('./processColor').default;\nconst DIRECTION_KEYWORD_REGEX =\n  /^to\\s+(?:top|bottom|left|right)(?:\\s+(?:top|bottom|left|right))?/i;\nconst ANGLE_UNIT_REGEX = /^([+-]?\\d*\\.?\\d+)(deg|grad|rad|turn)$/i;\n\ntype LinearGradientDirection =\n  | {type: 'angle', value: number}\n  | {type: 'keyword', value: string};\n\n// null color indicate that the transition hint syntax is used. e.g. red, 20%, blue\ntype ColorStopColor = ProcessedColorValue | null;\n// percentage or pixel value\ntype ColorStopPosition = number | string | null;\n\ntype ParsedGradientValue = {\n  type: 'linearGradient',\n  direction: LinearGradientDirection,\n  colorStops: $ReadOnlyArray<{\n    color: ColorStopColor,\n    position: ColorStopPosition,\n  }>,\n};\n\nconst DEFAULT_DIRECTION: LinearGradientDirection = {\n  type: 'angle',\n  value: 180,\n};\n\nexport default function processBackgroundImage(\n  backgroundImage: ?($ReadOnlyArray<GradientValue> | string),\n): $ReadOnlyArray<ParsedGradientValue> {\n  let result: $ReadOnlyArray<ParsedGradientValue> = [];\n  if (backgroundImage == null) {\n    return result;\n  }\n\n  if (typeof backgroundImage === 'string') {\n    result = parseCSSLinearGradient(backgroundImage.replace(/\\n/g, ' '));\n  } else if (Array.isArray(backgroundImage)) {\n    for (const bgImage of backgroundImage) {\n      const processedColorStops: Array<{\n        color: ColorStopColor,\n        position: ColorStopPosition,\n      }> = [];\n      for (let index = 0; index < bgImage.colorStops.length; index++) {\n        const colorStop = bgImage.colorStops[index];\n        const positions = colorStop.positions;\n        // Color transition hint syntax (red, 20%, blue)\n        if (\n          colorStop.color == null &&\n          Array.isArray(positions) &&\n          positions.length === 1\n        ) {\n          const position = positions[0];\n          if (\n            typeof position === 'number' ||\n            (typeof position === 'string' && position.endsWith('%'))\n          ) {\n            processedColorStops.push({\n              color: null,\n              position,\n            });\n          } else {\n            // If a position is invalid, return an empty array and do not apply gradient. Same as web.\n            return [];\n          }\n        } else {\n          const processedColor = processColor(colorStop.color);\n          if (processedColor == null) {\n            // If a color is invalid, return an empty array and do not apply gradient. Same as web.\n            return [];\n          }\n          if (positions != null && positions.length > 0) {\n            for (const position of positions) {\n              if (\n                typeof position === 'number' ||\n                (typeof position === 'string' && position.endsWith('%'))\n              ) {\n                processedColorStops.push({\n                  color: processedColor,\n                  position,\n                });\n              } else {\n                // If a position is invalid, return an empty array and do not apply gradient. Same as web.\n                return [];\n              }\n            }\n          } else {\n            processedColorStops.push({\n              color: processedColor,\n              position: null,\n            });\n          }\n        }\n      }\n\n      let direction: LinearGradientDirection = DEFAULT_DIRECTION;\n      const bgDirection =\n        bgImage.direction != null ? bgImage.direction.toLowerCase() : null;\n\n      if (bgDirection != null) {\n        if (ANGLE_UNIT_REGEX.test(bgDirection)) {\n          const parsedAngle = getAngleInDegrees(bgDirection);\n          if (parsedAngle != null) {\n            direction = {\n              type: 'angle',\n              value: parsedAngle,\n            };\n          } else {\n            // If an angle is invalid, return an empty array and do not apply any gradient. Same as web.\n            return [];\n          }\n        } else if (DIRECTION_KEYWORD_REGEX.test(bgDirection)) {\n          const parsedDirection = getDirectionForKeyword(bgDirection);\n          if (parsedDirection != null) {\n            direction = parsedDirection;\n          } else {\n            // If a direction is invalid, return an empty array and do not apply any gradient. Same as web.\n            return [];\n          }\n        } else {\n          // If a direction is invalid, return an empty array and do not apply any gradient. Same as web.\n          return [];\n        }\n      }\n\n      result = result.concat({\n        type: 'linearGradient',\n        direction,\n        colorStops: processedColorStops,\n      });\n    }\n  }\n\n  return result;\n}\n\nfunction parseCSSLinearGradient(\n  cssString: string,\n): $ReadOnlyArray<ParsedGradientValue> {\n  const gradients = [];\n  let match;\n\n  // matches one or more linear-gradient functions in CSS\n  const linearGradientRegex = /linear-gradient\\s*\\(((?:\\([^)]*\\)|[^())])*)\\)/gi;\n\n  while ((match = linearGradientRegex.exec(cssString))) {\n    const gradientContent = match[1];\n    const parts = gradientContent.split(',');\n    let direction: LinearGradientDirection = DEFAULT_DIRECTION;\n    const trimmedDirection = parts[0].trim().toLowerCase();\n\n    if (ANGLE_UNIT_REGEX.test(trimmedDirection)) {\n      const parsedAngle = getAngleInDegrees(trimmedDirection);\n      if (parsedAngle != null) {\n        direction = {\n          type: 'angle',\n          value: parsedAngle,\n        };\n        parts.shift();\n      } else {\n        // If an angle is invalid, return an empty array and do not apply any gradient. Same as web.\n        return [];\n      }\n    } else if (DIRECTION_KEYWORD_REGEX.test(trimmedDirection)) {\n      const parsedDirection = getDirectionForKeyword(trimmedDirection);\n      if (parsedDirection != null) {\n        direction = parsedDirection;\n        parts.shift();\n      } else {\n        // If a direction is invalid, return an empty array and do not apply any gradient. Same as web.\n        return [];\n      }\n    }\n\n    const colorStopsString = parts.join(',');\n    const colorStops = [];\n    // split by comma, but not if it's inside a parentheses. e.g. red, rgba(0, 0, 0, 0.5), green => [\"red\", \"rgba(0, 0, 0, 0.5)\", \"green\"]\n    const stops = colorStopsString.split(/,(?![^(]*\\))/);\n    let prevStop = null;\n    for (let i = 0; i < stops.length; i++) {\n      const stop = stops[i];\n      const trimmedStop = stop.trim().toLowerCase();\n      // Match function like pattern or single words\n      const colorStopParts = trimmedStop.match(/\\S+\\([^)]*\\)|\\S+/g);\n      if (colorStopParts == null) {\n        // If a color stop is invalid, return an empty array and do not apply any gradient. Same as web.\n        return [];\n      }\n      // Case 1: [color, position, position]\n      if (colorStopParts.length === 3) {\n        const color = colorStopParts[0];\n        const position1 = getPositionFromCSSValue(colorStopParts[1]);\n        const position2 = getPositionFromCSSValue(colorStopParts[2]);\n        const processedColor = processColor(color);\n        if (processedColor == null) {\n          // If a color is invalid, return an empty array and do not apply any gradient. Same as web.\n          return [];\n        }\n\n        if (position1 == null || position2 == null) {\n          // If a position is invalid, return an empty array and do not apply any gradient. Same as web.\n          return [];\n        }\n\n        colorStops.push({\n          color: processedColor,\n          position: position1,\n        });\n        colorStops.push({\n          color: processedColor,\n          position: position2,\n        });\n      }\n      // Case 2: [color, position]\n      else if (colorStopParts.length === 2) {\n        const color = colorStopParts[0];\n        const position = getPositionFromCSSValue(colorStopParts[1]);\n        const processedColor = processColor(color);\n        if (processedColor == null) {\n          // If a color is invalid, return an empty array and do not apply any gradient. Same as web.\n          return [];\n        }\n        if (position == null) {\n          // If a position is invalid, return an empty array and do not apply any gradient. Same as web.\n          return [];\n        }\n        colorStops.push({\n          color: processedColor,\n          position,\n        });\n      }\n      // Case 3: [color]\n      // Case 4: [position] => transition hint syntax\n      else if (colorStopParts.length === 1) {\n        const position = getPositionFromCSSValue(colorStopParts[0]);\n        if (position != null) {\n          // handle invalid transition hint syntax. transition hint syntax must have color before and after the position. e.g. red, 20%, blue\n          if (\n            (prevStop != null &&\n              prevStop.length === 1 &&\n              getPositionFromCSSValue(prevStop[0]) != null) ||\n            i === stops.length - 1 ||\n            i === 0\n          ) {\n            // If the last stop is a transition hint syntax, return an empty array and do not apply any gradient. Same as web.\n            return [];\n          }\n          colorStops.push({\n            color: null,\n            position,\n          });\n        } else {\n          const processedColor = processColor(colorStopParts[0]);\n          if (processedColor == null) {\n            // If a color is invalid, return an empty array and do not apply any gradient. Same as web.\n            return [];\n          }\n          colorStops.push({\n            color: processedColor,\n            position: null,\n          });\n        }\n      } else {\n        // If a color stop is invalid, return an empty array and do not apply any gradient. Same as web.\n        return [];\n      }\n      prevStop = colorStopParts;\n    }\n\n    gradients.push({\n      type: 'linearGradient',\n      direction,\n      colorStops,\n    });\n  }\n\n  return gradients;\n}\n\nfunction getDirectionForKeyword(direction?: string): ?LinearGradientDirection {\n  if (direction == null) {\n    return null;\n  }\n  // Remove extra whitespace\n  const normalized = direction.replace(/\\s+/g, ' ').toLowerCase();\n\n  switch (normalized) {\n    case 'to top':\n      return {type: 'angle', value: 0};\n    case 'to right':\n      return {type: 'angle', value: 90};\n    case 'to bottom':\n      return {type: 'angle', value: 180};\n    case 'to left':\n      return {type: 'angle', value: 270};\n    case 'to top right':\n    case 'to right top':\n      return {type: 'keyword', value: 'to top right'};\n    case 'to bottom right':\n    case 'to right bottom':\n      return {type: 'keyword', value: 'to bottom right'};\n    case 'to top left':\n    case 'to left top':\n      return {type: 'keyword', value: 'to top left'};\n    case 'to bottom left':\n    case 'to left bottom':\n      return {type: 'keyword', value: 'to bottom left'};\n    default:\n      return null;\n  }\n}\n\nfunction getAngleInDegrees(angle?: string): ?number {\n  if (angle == null) {\n    return null;\n  }\n  const match = angle.match(ANGLE_UNIT_REGEX);\n  if (!match) {\n    return null;\n  }\n\n  const [, value, unit] = match;\n\n  const numericValue = parseFloat(value);\n  switch (unit) {\n    case 'deg':\n      return numericValue;\n    case 'grad':\n      return numericValue * 0.9; // 1 grad = 0.9 degrees\n    case 'rad':\n      return (numericValue * 180) / Math.PI;\n    case 'turn':\n      return numericValue * 360; // 1 turn = 360 degrees\n    default:\n      return null;\n  }\n}\n\nfunction getPositionFromCSSValue(position: string) {\n  if (position.endsWith('px')) {\n    return parseFloat(position);\n  }\n\n  if (position.endsWith('%')) {\n    return position;\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAC,sBAAA;AAAA,IAAAC,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AAKb,IAAMQ,YAAY,GAAGR,OAAO,iBAAiB,CAAC,CAACK,OAAO;AACtD,IAAMI,uBAAuB,GAC3B,mEAAmE;AACrE,IAAMC,gBAAgB,GAAG,wCAAwC;AAoBjE,IAAMC,iBAA0C,GAAG;EACjDC,IAAI,EAAE,OAAO;EACbR,KAAK,EAAE;AACT,CAAC;AAEc,SAASE,sBAAsBA,CAC5CO,eAA0D,EACrB;EACrC,IAAIC,MAA2C,GAAG,EAAE;EACpD,IAAID,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAOC,MAAM;EACf;EAEA,IAAI,OAAOD,eAAe,KAAK,QAAQ,EAAE;IACvCC,MAAM,GAAGC,sBAAsB,CAACF,eAAe,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;EACtE,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACL,eAAe,CAAC,EAAE;IACzC,KAAK,IAAMM,OAAO,IAAIN,eAAe,EAAE;MACrC,IAAMO,mBAGJ,GAAG,EAAE;MACP,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,OAAO,CAACG,UAAU,CAACC,MAAM,EAAEF,KAAK,EAAE,EAAE;QAC9D,IAAMG,SAAS,GAAGL,OAAO,CAACG,UAAU,CAACD,KAAK,CAAC;QAC3C,IAAMI,SAAS,GAAGD,SAAS,CAACC,SAAS;QAErC,IACED,SAAS,CAACE,KAAK,IAAI,IAAI,IACvBT,KAAK,CAACC,OAAO,CAACO,SAAS,CAAC,IACxBA,SAAS,CAACF,MAAM,KAAK,CAAC,EACtB;UACA,IAAMI,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;UAC7B,IACE,OAAOE,QAAQ,KAAK,QAAQ,IAC3B,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAE,EACxD;YACAR,mBAAmB,CAACS,IAAI,CAAC;cACvBH,KAAK,EAAE,IAAI;cACXC,QAAQ,EAARA;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YAEL,OAAO,EAAE;UACX;QACF,CAAC,MAAM;UACL,IAAMG,cAAc,GAAGtB,YAAY,CAACgB,SAAS,CAACE,KAAK,CAAC;UACpD,IAAII,cAAc,IAAI,IAAI,EAAE;YAE1B,OAAO,EAAE;UACX;UACA,IAAIL,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACF,MAAM,GAAG,CAAC,EAAE;YAC7C,KAAK,IAAMI,SAAQ,IAAIF,SAAS,EAAE;cAChC,IACE,OAAOE,SAAQ,KAAK,QAAQ,IAC3B,OAAOA,SAAQ,KAAK,QAAQ,IAAIA,SAAQ,CAACC,QAAQ,CAAC,GAAG,CAAE,EACxD;gBACAR,mBAAmB,CAACS,IAAI,CAAC;kBACvBH,KAAK,EAAEI,cAAc;kBACrBH,QAAQ,EAARA;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM;gBAEL,OAAO,EAAE;cACX;YACF;UACF,CAAC,MAAM;YACLP,mBAAmB,CAACS,IAAI,CAAC;cACvBH,KAAK,EAAEI,cAAc;cACrBH,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAII,SAAkC,GAAGpB,iBAAiB;MAC1D,IAAMqB,WAAW,GACfb,OAAO,CAACY,SAAS,IAAI,IAAI,GAAGZ,OAAO,CAACY,SAAS,CAACE,WAAW,CAAC,CAAC,GAAG,IAAI;MAEpE,IAAID,WAAW,IAAI,IAAI,EAAE;QACvB,IAAItB,gBAAgB,CAACwB,IAAI,CAACF,WAAW,CAAC,EAAE;UACtC,IAAMG,WAAW,GAAGC,iBAAiB,CAACJ,WAAW,CAAC;UAClD,IAAIG,WAAW,IAAI,IAAI,EAAE;YACvBJ,SAAS,GAAG;cACVnB,IAAI,EAAE,OAAO;cACbR,KAAK,EAAE+B;YACT,CAAC;UACH,CAAC,MAAM;YAEL,OAAO,EAAE;UACX;QACF,CAAC,MAAM,IAAI1B,uBAAuB,CAACyB,IAAI,CAACF,WAAW,CAAC,EAAE;UACpD,IAAMK,eAAe,GAAGC,sBAAsB,CAACN,WAAW,CAAC;UAC3D,IAAIK,eAAe,IAAI,IAAI,EAAE;YAC3BN,SAAS,GAAGM,eAAe;UAC7B,CAAC,MAAM;YAEL,OAAO,EAAE;UACX;QACF,CAAC,MAAM;UAEL,OAAO,EAAE;QACX;MACF;MAEAvB,MAAM,GAAGA,MAAM,CAACyB,MAAM,CAAC;QACrB3B,IAAI,EAAE,gBAAgB;QACtBmB,SAAS,EAATA,SAAS;QACTT,UAAU,EAAEF;MACd,CAAC,CAAC;IACJ;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASC,sBAAsBA,CAC7ByB,SAAiB,EACoB;EACrC,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAIC,KAAK;EAGT,IAAMC,mBAAmB,GAAG,iDAAiD;EAE7E,OAAQD,KAAK,GAAGC,mBAAmB,CAACC,IAAI,CAACJ,SAAS,CAAC,EAAG;IACpD,IAAMK,eAAe,GAAGH,KAAK,CAAC,CAAC,CAAC;IAChC,IAAMI,KAAK,GAAGD,eAAe,CAACE,KAAK,CAAC,GAAG,CAAC;IACxC,IAAIhB,SAAkC,GAAGpB,iBAAiB;IAC1D,IAAMqC,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC;IAEtD,IAAIvB,gBAAgB,CAACwB,IAAI,CAACc,gBAAgB,CAAC,EAAE;MAC3C,IAAMb,WAAW,GAAGC,iBAAiB,CAACY,gBAAgB,CAAC;MACvD,IAAIb,WAAW,IAAI,IAAI,EAAE;QACvBJ,SAAS,GAAG;UACVnB,IAAI,EAAE,OAAO;UACbR,KAAK,EAAE+B;QACT,CAAC;QACDW,KAAK,CAACI,KAAK,CAAC,CAAC;MACf,CAAC,MAAM;QAEL,OAAO,EAAE;MACX;IACF,CAAC,MAAM,IAAIzC,uBAAuB,CAACyB,IAAI,CAACc,gBAAgB,CAAC,EAAE;MACzD,IAAMX,eAAe,GAAGC,sBAAsB,CAACU,gBAAgB,CAAC;MAChE,IAAIX,eAAe,IAAI,IAAI,EAAE;QAC3BN,SAAS,GAAGM,eAAe;QAC3BS,KAAK,CAACI,KAAK,CAAC,CAAC;MACf,CAAC,MAAM;QAEL,OAAO,EAAE;MACX;IACF;IAEA,IAAMC,gBAAgB,GAAGL,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;IACxC,IAAM9B,UAAU,GAAG,EAAE;IAErB,IAAM+B,KAAK,GAAGF,gBAAgB,CAACJ,KAAK,CAAC,cAAc,CAAC;IACpD,IAAIO,QAAQ,GAAG,IAAI;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAAC9B,MAAM,EAAEgC,CAAC,EAAE,EAAE;MACrC,IAAMC,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC;MACrB,IAAME,WAAW,GAAGD,IAAI,CAACP,IAAI,CAAC,CAAC,CAAChB,WAAW,CAAC,CAAC;MAE7C,IAAMyB,cAAc,GAAGD,WAAW,CAACf,KAAK,CAAC,mBAAmB,CAAC;MAC7D,IAAIgB,cAAc,IAAI,IAAI,EAAE;QAE1B,OAAO,EAAE;MACX;MAEA,IAAIA,cAAc,CAACnC,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAMG,KAAK,GAAGgC,cAAc,CAAC,CAAC,CAAC;QAC/B,IAAMC,SAAS,GAAGC,uBAAuB,CAACF,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAMG,SAAS,GAAGD,uBAAuB,CAACF,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAM5B,cAAc,GAAGtB,YAAY,CAACkB,KAAK,CAAC;QAC1C,IAAII,cAAc,IAAI,IAAI,EAAE;UAE1B,OAAO,EAAE;QACX;QAEA,IAAI6B,SAAS,IAAI,IAAI,IAAIE,SAAS,IAAI,IAAI,EAAE;UAE1C,OAAO,EAAE;QACX;QAEAvC,UAAU,CAACO,IAAI,CAAC;UACdH,KAAK,EAAEI,cAAc;UACrBH,QAAQ,EAAEgC;QACZ,CAAC,CAAC;QACFrC,UAAU,CAACO,IAAI,CAAC;UACdH,KAAK,EAAEI,cAAc;UACrBH,QAAQ,EAAEkC;QACZ,CAAC,CAAC;MACJ,CAAC,MAEI,IAAIH,cAAc,CAACnC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAMG,MAAK,GAAGgC,cAAc,CAAC,CAAC,CAAC;QAC/B,IAAM/B,QAAQ,GAAGiC,uBAAuB,CAACF,cAAc,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAM5B,eAAc,GAAGtB,YAAY,CAACkB,MAAK,CAAC;QAC1C,IAAII,eAAc,IAAI,IAAI,EAAE;UAE1B,OAAO,EAAE;QACX;QACA,IAAIH,QAAQ,IAAI,IAAI,EAAE;UAEpB,OAAO,EAAE;QACX;QACAL,UAAU,CAACO,IAAI,CAAC;UACdH,KAAK,EAAEI,eAAc;UACrBH,QAAQ,EAARA;QACF,CAAC,CAAC;MACJ,CAAC,MAGI,IAAI+B,cAAc,CAACnC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAMI,UAAQ,GAAGiC,uBAAuB,CAACF,cAAc,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI/B,UAAQ,IAAI,IAAI,EAAE;UAEpB,IACG2B,QAAQ,IAAI,IAAI,IACfA,QAAQ,CAAC/B,MAAM,KAAK,CAAC,IACrBqC,uBAAuB,CAACN,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAC9CC,CAAC,KAAKF,KAAK,CAAC9B,MAAM,GAAG,CAAC,IACtBgC,CAAC,KAAK,CAAC,EACP;YAEA,OAAO,EAAE;UACX;UACAjC,UAAU,CAACO,IAAI,CAAC;YACdH,KAAK,EAAE,IAAI;YACXC,QAAQ,EAARA;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAMG,gBAAc,GAAGtB,YAAY,CAACkD,cAAc,CAAC,CAAC,CAAC,CAAC;UACtD,IAAI5B,gBAAc,IAAI,IAAI,EAAE;YAE1B,OAAO,EAAE;UACX;UACAR,UAAU,CAACO,IAAI,CAAC;YACdH,KAAK,EAAEI,gBAAc;YACrBH,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QAEL,OAAO,EAAE;MACX;MACA2B,QAAQ,GAAGI,cAAc;IAC3B;IAEAjB,SAAS,CAACZ,IAAI,CAAC;MACbjB,IAAI,EAAE,gBAAgB;MACtBmB,SAAS,EAATA,SAAS;MACTT,UAAU,EAAVA;IACF,CAAC,CAAC;EACJ;EAEA,OAAOmB,SAAS;AAClB;AAEA,SAASH,sBAAsBA,CAACP,SAAkB,EAA4B;EAC5E,IAAIA,SAAS,IAAI,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,IAAM+B,UAAU,GAAG/B,SAAS,CAACf,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACiB,WAAW,CAAC,CAAC;EAE/D,QAAQ6B,UAAU;IAChB,KAAK,QAAQ;MACX,OAAO;QAAClD,IAAI,EAAE,OAAO;QAAER,KAAK,EAAE;MAAC,CAAC;IAClC,KAAK,UAAU;MACb,OAAO;QAACQ,IAAI,EAAE,OAAO;QAAER,KAAK,EAAE;MAAE,CAAC;IACnC,KAAK,WAAW;MACd,OAAO;QAACQ,IAAI,EAAE,OAAO;QAAER,KAAK,EAAE;MAAG,CAAC;IACpC,KAAK,SAAS;MACZ,OAAO;QAACQ,IAAI,EAAE,OAAO;QAAER,KAAK,EAAE;MAAG,CAAC;IACpC,KAAK,cAAc;IACnB,KAAK,cAAc;MACjB,OAAO;QAACQ,IAAI,EAAE,SAAS;QAAER,KAAK,EAAE;MAAc,CAAC;IACjD,KAAK,iBAAiB;IACtB,KAAK,iBAAiB;MACpB,OAAO;QAACQ,IAAI,EAAE,SAAS;QAAER,KAAK,EAAE;MAAiB,CAAC;IACpD,KAAK,aAAa;IAClB,KAAK,aAAa;MAChB,OAAO;QAACQ,IAAI,EAAE,SAAS;QAAER,KAAK,EAAE;MAAa,CAAC;IAChD,KAAK,gBAAgB;IACrB,KAAK,gBAAgB;MACnB,OAAO;QAACQ,IAAI,EAAE,SAAS;QAAER,KAAK,EAAE;MAAgB,CAAC;IACnD;MACE,OAAO,IAAI;EACf;AACF;AAEA,SAASgC,iBAAiBA,CAAC2B,KAAc,EAAW;EAClD,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,IAAMrB,KAAK,GAAGqB,KAAK,CAACrB,KAAK,CAAChC,gBAAgB,CAAC;EAC3C,IAAI,CAACgC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAAsB,MAAA,OAAAzD,eAAA,CAAAF,OAAA,EAAwBqC,KAAK;IAApBtC,KAAK,GAAA4D,MAAA;IAAEC,IAAI,GAAAD,MAAA;EAEpB,IAAME,YAAY,GAAGC,UAAU,CAAC/D,KAAK,CAAC;EACtC,QAAQ6D,IAAI;IACV,KAAK,KAAK;MACR,OAAOC,YAAY;IACrB,KAAK,MAAM;MACT,OAAOA,YAAY,GAAG,GAAG;IAC3B,KAAK,KAAK;MACR,OAAQA,YAAY,GAAG,GAAG,GAAIE,IAAI,CAACC,EAAE;IACvC,KAAK,MAAM;MACT,OAAOH,YAAY,GAAG,GAAG;IAC3B;MACE,OAAO,IAAI;EACf;AACF;AAEA,SAASN,uBAAuBA,CAACjC,QAAgB,EAAE;EACjD,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC3B,OAAOuC,UAAU,CAACxC,QAAQ,CAAC;EAC7B;EAEA,IAAIA,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAOD,QAAQ;EACjB;AACF", "ignoreList": []}