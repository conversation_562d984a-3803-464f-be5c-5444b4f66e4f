/**
 * Performance Context Provider
 *
 * Comprehensive performance monitoring and optimization context
 * for enhanced user experience and app performance tracking.
 *
 * Features:
 * - Performance monitoring
 * - Loading state management
 * - Caching strategies
 * - Memory optimization
 * - Network monitoring
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Platform, AppState, NetInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  PerformanceMetrics,
  LoadingState,
  PerformanceMonitor,
  LRUCache,
  LoadingStateManager,
  performanceMonitor,
  globalCache,
  loadingStateManager,
  PERFORMANCE_THRESHOLDS,
  getPerformanceRecommendations,
} from '../utils/performanceUtils';

// Performance context interface
interface PerformanceContextType {
  // Metrics
  metrics: PerformanceMetrics;
  
  // Loading states
  loadingStates: Map<string, LoadingState>;
  isLoading: (key?: string) => boolean;
  startLoading: (key: string, type?: LoadingState['type'], message?: string) => void;
  updateLoadingProgress: (key: string, progress: number, message?: string) => void;
  finishLoading: (key: string) => void;
  
  // Performance monitoring
  startTiming: (key: string) => void;
  endTiming: (key: string) => number;
  measureFunction: <T>(fn: () => T, key?: string) => { result: T; duration: number };
  measureAsyncFunction: <T>(fn: () => Promise<T>, key?: string) => Promise<{ result: T; duration: number }>;
  
  // Caching
  cacheGet: <T>(key: string) => T | null;
  cacheSet: <T>(key: string, value: T) => void;
  cacheClear: () => void;
  getCacheStats: () => { size: number; hitRate: number };
  
  // Network monitoring
  networkStatus: {
    isConnected: boolean;
    type: string | null;
    isInternetReachable: boolean | null;
  };
  
  // Memory monitoring
  memoryUsage: number;
  
  // Performance recommendations
  getRecommendations: () => string[];
  
  // Settings
  enablePerformanceMonitoring: boolean;
  setEnablePerformanceMonitoring: (enabled: boolean) => void;
  
  // Utilities
  preloadResources: (resources: string[]) => Promise<void>;
  optimizeForLowMemory: () => void;
}

// Create context
const PerformanceContext = createContext<PerformanceContextType | undefined>(undefined);

// Storage keys
const PERFORMANCE_SETTINGS_KEY = '@vierla_performance_settings';
const PERFORMANCE_METRICS_KEY = '@vierla_performance_metrics';

// Provider props
interface PerformanceProviderProps {
  children: React.ReactNode;
  enableMonitoring?: boolean;
}

export const PerformanceProvider: React.FC<PerformanceProviderProps> = ({
  children,
  enableMonitoring = true,
}) => {
  // State
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0,
    memoryUsage: 0,
    networkLatency: 0,
    cacheHitRate: 0,
  });

  const [loadingStates, setLoadingStates] = useState<Map<string, LoadingState>>(new Map());
  const [enablePerformanceMonitoring, setEnablePerformanceMonitoring] = useState(enableMonitoring);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [networkStatus, setNetworkStatus] = useState({
    isConnected: true,
    type: null as string | null,
    isInternetReachable: null as boolean | null,
  });

  // Refs
  const appStartTimeRef = useRef(Date.now());
  const metricsIntervalRef = useRef<NodeJS.Timeout>();
  const memoryMonitorRef = useRef<() => void>();

  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const saved = await AsyncStorage.getItem(PERFORMANCE_SETTINGS_KEY);
        if (saved) {
          const settings = JSON.parse(saved);
          setEnablePerformanceMonitoring(settings.enableMonitoring ?? enableMonitoring);
        }
      } catch (error) {
        console.warn('Failed to load performance settings:', error);
      }
    };

    loadSettings();
  }, [enableMonitoring]);

  // Save settings
  const saveSettings = useCallback(async () => {
    try {
      const settings = { enableMonitoring: enablePerformanceMonitoring };
      await AsyncStorage.setItem(PERFORMANCE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save performance settings:', error);
    }
  }, [enablePerformanceMonitoring]);

  // Network monitoring
  useEffect(() => {
    if (Platform.OS === 'web') return;

    const unsubscribe = NetInfo.addEventListener(state => {
      setNetworkStatus({
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
      });
    });

    return unsubscribe;
  }, []);

  // Memory monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring) return;

    const monitor = () => {
      const usage = performanceMonitor.getMemoryUsage();
      setMemoryUsage(usage);
    };

    // Monitor memory every 5 seconds
    const interval = setInterval(monitor, 5000);
    monitor(); // Initial measurement

    return () => clearInterval(interval);
  }, [enablePerformanceMonitoring]);

  // Loading states subscription
  useEffect(() => {
    const unsubscribe = loadingStateManager.subscribe(setLoadingStates);
    return unsubscribe;
  }, []);

  // Performance metrics collection
  useEffect(() => {
    if (!enablePerformanceMonitoring) return;

    const collectMetrics = () => {
      const newMetrics: PerformanceMetrics = {
        loadTime: Date.now() - appStartTimeRef.current,
        renderTime: 0, // Would be measured during renders
        interactionTime: 0, // Would be measured during interactions
        memoryUsage,
        networkLatency: 0, // Would be measured during network requests
        cacheHitRate: globalCache.getHitRate(),
      };

      setMetrics(newMetrics);
    };

    // Collect metrics every 10 seconds
    metricsIntervalRef.current = setInterval(collectMetrics, 10000);
    collectMetrics(); // Initial collection

    return () => {
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
    };
  }, [enablePerformanceMonitoring, memoryUsage]);

  // App state monitoring
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        // Save metrics when app goes to background
        saveMetrics();
      } else if (nextAppState === 'active') {
        // Reset timers when app becomes active
        appStartTimeRef.current = Date.now();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Save metrics
  const saveMetrics = useCallback(async () => {
    try {
      await AsyncStorage.setItem(PERFORMANCE_METRICS_KEY, JSON.stringify(metrics));
    } catch (error) {
      console.warn('Failed to save performance metrics:', error);
    }
  }, [metrics]);

  // Loading state management
  const isLoading = useCallback((key?: string) => {
    return loadingStateManager.isLoading(key);
  }, []);

  const startLoading = useCallback((
    key: string,
    type: LoadingState['type'] = 'action',
    message?: string
  ) => {
    loadingStateManager.startLoading(key, type, message);
  }, []);

  const updateLoadingProgress = useCallback((
    key: string,
    progress: number,
    message?: string
  ) => {
    loadingStateManager.updateProgress(key, progress, message);
  }, []);

  const finishLoading = useCallback((key: string) => {
    loadingStateManager.finishLoading(key);
  }, []);

  // Performance monitoring
  const startTiming = useCallback((key: string) => {
    if (enablePerformanceMonitoring) {
      performanceMonitor.startTiming(key);
    }
  }, [enablePerformanceMonitoring]);

  const endTiming = useCallback((key: string): number => {
    if (enablePerformanceMonitoring) {
      return performanceMonitor.endTiming(key);
    }
    return 0;
  }, [enablePerformanceMonitoring]);

  const measureFunction = useCallback(<T,>(
    fn: () => T,
    key?: string
  ): { result: T; duration: number } => {
    if (enablePerformanceMonitoring) {
      return performanceMonitor.measureFunction(fn, key);
    }
    return { result: fn(), duration: 0 };
  }, [enablePerformanceMonitoring]);

  const measureAsyncFunction = useCallback(async <T,>(
    fn: () => Promise<T>,
    key?: string
  ): Promise<{ result: T; duration: number }> => {
    if (enablePerformanceMonitoring) {
      return performanceMonitor.measureAsyncFunction(fn, key);
    }
    return { result: await fn(), duration: 0 };
  }, [enablePerformanceMonitoring]);

  // Caching
  const cacheGet = useCallback(<T,>(key: string): T | null => {
    return globalCache.get(key);
  }, []);

  const cacheSet = useCallback(<T,>(key: string, value: T) => {
    globalCache.set(key, value);
  }, []);

  const cacheClear = useCallback(() => {
    globalCache.clear();
  }, []);

  const getCacheStats = useCallback(() => {
    return {
      size: globalCache.size(),
      hitRate: globalCache.getHitRate(),
    };
  }, []);

  // Performance recommendations
  const getRecommendations = useCallback(() => {
    return getPerformanceRecommendations(metrics);
  }, [metrics]);

  // Resource preloading
  const preloadResources = useCallback(async (resources: string[]): Promise<void> => {
    startLoading('preload', 'initial', 'Preloading resources...');
    
    try {
      // Preload images, fonts, or other resources
      await Promise.all(
        resources.map(async (resource, index) => {
          // Simulate resource loading
          await new Promise(resolve => setTimeout(resolve, 100));
          updateLoadingProgress('preload', ((index + 1) / resources.length) * 100);
        })
      );
    } finally {
      finishLoading('preload');
    }
  }, [startLoading, updateLoadingProgress, finishLoading]);

  // Low memory optimization
  const optimizeForLowMemory = useCallback(() => {
    // Clear caches
    cacheClear();
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    console.log('Optimized for low memory');
  }, [cacheClear]);

  // Save settings when they change
  useEffect(() => {
    saveSettings();
  }, [saveSettings]);

  // Context value
  const contextValue: PerformanceContextType = {
    // Metrics
    metrics,
    
    // Loading states
    loadingStates,
    isLoading,
    startLoading,
    updateLoadingProgress,
    finishLoading,
    
    // Performance monitoring
    startTiming,
    endTiming,
    measureFunction,
    measureAsyncFunction,
    
    // Caching
    cacheGet,
    cacheSet,
    cacheClear,
    getCacheStats,
    
    // Network monitoring
    networkStatus,
    
    // Memory monitoring
    memoryUsage,
    
    // Performance recommendations
    getRecommendations,
    
    // Settings
    enablePerformanceMonitoring,
    setEnablePerformanceMonitoring,
    
    // Utilities
    preloadResources,
    optimizeForLowMemory,
  };

  return (
    <PerformanceContext.Provider value={contextValue}>
      {children}
    </PerformanceContext.Provider>
  );
};

// Hook to use performance context
export const usePerformance = (): PerformanceContextType => {
  const context = useContext(PerformanceContext);
  
  if (context === undefined) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  
  return context;
};

// Convenience hooks
export const useLoadingState = (key: string) => {
  const { loadingStates, startLoading, updateLoadingProgress, finishLoading } = usePerformance();
  
  return {
    isLoading: loadingStates.has(key),
    loadingState: loadingStates.get(key),
    startLoading: (type?: LoadingState['type'], message?: string) => startLoading(key, type, message),
    updateProgress: (progress: number, message?: string) => updateLoadingProgress(key, progress, message),
    finishLoading: () => finishLoading(key),
  };
};

export const usePerformanceMonitoring = () => {
  const { startTiming, endTiming, measureFunction, measureAsyncFunction } = usePerformance();
  
  return {
    startTiming,
    endTiming,
    measureFunction,
    measureAsyncFunction,
  };
};

export const useCache = <T>() => {
  const { cacheGet, cacheSet, getCacheStats } = usePerformance();
  
  return {
    get: (key: string) => cacheGet<T>(key),
    set: (key: string, value: T) => cacheSet(key, value),
    stats: getCacheStats(),
  };
};

export default PerformanceProvider;
