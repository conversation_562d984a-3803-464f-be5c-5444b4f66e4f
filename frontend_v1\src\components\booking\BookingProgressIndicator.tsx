/**
 * Booking Progress Indicator Component
 *
 * Visual progress indicator for booking flow with accessibility features
 * and conversion optimization elements.
 *
 * Features:
 * - Visual progress tracking
 * - Step navigation
 * - Accessibility compliance
 * - Time estimation
 * - Conversion optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';
import { useCognitiveAccessibility } from '../../contexts/CognitiveAccessibilityContext';
import { useBookingProgress, useBookingNavigation } from '../../contexts/BookingFlowContext';

// Component props
export interface BookingProgressIndicatorProps {
  // Display options
  showStepNames?: boolean;
  showTimeEstimate?: boolean;
  showPercentage?: boolean;
  allowStepNavigation?: boolean;
  
  // Styling
  style?: any;
  progressBarStyle?: any;
  stepStyle?: any;
  
  // Behavior
  onStepPress?: (stepId: string) => void;
  
  // Testing
  testID?: string;
}

export const BookingProgressIndicator: React.FC<BookingProgressIndicatorProps> = ({
  showStepNames = true,
  showTimeEstimate = true,
  showPercentage = true,
  allowStepNavigation = true,
  style,
  progressBarStyle,
  stepStyle,
  onStepPress,
  testID,
}) => {
  // Hooks
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();
  const triggerHapticFeedback = useHapticFeedback();
  const { settings } = useCognitiveAccessibility();
  
  const {
    progress,
    currentStep,
    totalSteps,
    completedSteps,
    estimatedTimeRemaining,
  } = useBookingProgress();
  
  const { goToStep, canProceed } = useBookingNavigation();

  // Animated progress value
  const progressAnimation = useMemo(() => new Animated.Value(progress), []);

  // Update animation when progress changes
  React.useEffect(() => {
    Animated.timing(progressAnimation, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress, progressAnimation]);

  // Handle step press
  const handleStepPress = (stepId: string) => {
    if (!allowStepNavigation) return;
    
    triggerHapticFeedback('light');
    
    if (onStepPress) {
      onStepPress(stepId);
    } else {
      goToStep(stepId);
    }
  };

  // Format time estimate
  const formatTimeEstimate = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.ceil(seconds / 60);
    return `${minutes}m`;
  };

  // Get step status
  const getStepStatus = (step: any): 'completed' | 'current' | 'upcoming' => {
    if (step.completed) return 'completed';
    if (step.id === currentStep?.id) return 'current';
    return 'upcoming';
  };

  // Get step icon
  const getStepIcon = (step: any, status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'current':
        return 'radio-button-on';
      default:
        return 'radio-button-off';
    }
  };

  // Get step color
  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors?.status?.success || '#4ECDC4';
      case 'current':
        return colors?.primary?.default || '#5A7A63';
      default:
        return colors?.text?.tertiary || '#CCC';
    }
  };

  return (
    <View style={[styles.container, style]} testID={testID}>
      {/* Progress Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={[styles.stepCounter, { color: colors?.text?.primary || '#333' }]}>
            Step {currentStep ? currentStep.title : '1'} of {totalSteps}
          </Text>
          {showPercentage && (
            <Text style={[styles.percentage, { color: colors?.text?.secondary || '#666' }]}>
              {Math.round(progress)}% complete
            </Text>
          )}
        </View>
        
        {showTimeEstimate && estimatedTimeRemaining > 0 && (
          <View style={styles.headerRight}>
            <Ionicons
              name="time-outline"
              size={16}
              color={colors?.text?.secondary || '#666'}
              style={styles.timeIcon}
            />
            <Text style={[styles.timeEstimate, { color: colors?.text?.secondary || '#666' }]}>
              {formatTimeEstimate(estimatedTimeRemaining)} left
            </Text>
          </View>
        )}
      </View>

      {/* Progress Bar */}
      <View style={[styles.progressBarContainer, progressBarStyle]}>
        <View
          style={[
            styles.progressBarBackground,
            { backgroundColor: colors?.background?.secondary || '#F0F0F0' },
          ]}
        >
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                backgroundColor: colors?.primary?.default || '#5A7A63',
                width: progressAnimation.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          />
        </View>
      </View>

      {/* Step Indicators */}
      {showStepNames && (
        <View style={styles.stepsContainer}>
          {/* Steps list for cognitive accessibility */}
          {settings.memoryAids && (
            <View style={styles.stepsOverview}>
              <Text style={[styles.overviewTitle, { color: colors?.text?.primary || '#333' }]}>
                Booking Steps:
              </Text>
              {currentStep && (
                <Text style={[styles.currentStepName, { color: colors?.primary?.default || '#5A7A63' }]}>
                  Current: {currentStep.title}
                </Text>
              )}
            </View>
          )}
          
          {/* Interactive step indicators */}
          <View style={styles.stepsList}>
            {/* Render first 3 steps for mobile optimization */}
            {[...Array(Math.min(3, totalSteps))].map((_, index) => {
              const stepNumber = index + 1;
              const isCompleted = completedSteps > index;
              const isCurrent = currentStep && currentStep.title === `Step ${stepNumber}`;
              const status = isCompleted ? 'completed' : isCurrent ? 'current' : 'upcoming';
              
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.stepIndicator,
                    stepStyle,
                    touchTargetStyles,
                    !allowStepNavigation && styles.stepIndicatorDisabled,
                  ]}
                  onPress={() => allowStepNavigation && handleStepPress(`step-${stepNumber}`)}
                  disabled={!allowStepNavigation}
                  accessibilityRole="button"
                  accessibilityLabel={`Step ${stepNumber}: ${status}`}
                  accessibilityHint={allowStepNavigation ? 'Tap to go to this step' : undefined}
                  accessibilityState={{
                    disabled: !allowStepNavigation,
                    selected: isCurrent,
                  }}
                >
                  <View style={styles.stepIconContainer}>
                    <Ionicons
                      name={getStepIcon({ completed: isCompleted }, status)}
                      size={20}
                      color={getStepColor(status)}
                    />
                  </View>
                  
                  <View style={styles.stepInfo}>
                    <Text
                      style={[
                        styles.stepNumber,
                        { color: getStepColor(status) },
                        isCurrent && styles.currentStepNumber,
                      ]}
                    >
                      {stepNumber}
                    </Text>
                    
                    {showStepNames && (
                      <Text
                        style={[
                          styles.stepName,
                          { color: colors?.text?.secondary || '#666' },
                          isCurrent && { color: colors?.primary?.default || '#5A7A63' },
                        ]}
                        numberOfLines={1}
                      >
                        Step {stepNumber}
                      </Text>
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
            
            {/* Show more indicator if there are more steps */}
            {totalSteps > 3 && (
              <View style={styles.moreStepsIndicator}>
                <Text style={[styles.moreStepsText, { color: colors?.text?.tertiary || '#999' }]}>
                  +{totalSteps - 3} more
                </Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Accessibility summary */}
      <View
        style={styles.accessibilitySummary}
        accessibilityRole="progressbar"
        accessibilityLabel={`Booking progress: ${Math.round(progress)}% complete. ${completedSteps} of ${totalSteps} steps finished.`}
        accessibilityValue={{
          min: 0,
          max: 100,
          now: Math.round(progress),
        }}
        importantForAccessibility="yes"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCounter: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  percentage: {
    fontSize: 12,
  },
  timeIcon: {
    marginRight: 4,
  },
  timeEstimate: {
    fontSize: 12,
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  stepsContainer: {
    marginTop: 8,
  },
  stepsOverview: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  overviewTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  currentStepName: {
    fontSize: 14,
  },
  stepsList: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stepIndicator: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
  stepIndicatorDisabled: {
    opacity: 0.7,
  },
  stepIconContainer: {
    marginRight: 8,
  },
  stepInfo: {
    flex: 1,
    alignItems: 'center',
  },
  stepNumber: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  currentStepNumber: {
    fontWeight: 'bold',
  },
  stepName: {
    fontSize: 10,
    textAlign: 'center',
  },
  moreStepsIndicator: {
    paddingHorizontal: 8,
  },
  moreStepsText: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  accessibilitySummary: {
    height: 0,
    overflow: 'hidden',
  },
});

export default BookingProgressIndicator;
