799c7668ab0d93679c0f707805372c1a
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var registerCallableModule = function () {
  if (global.RN$Bridgeless === true) {
    return function (name, moduleOrFactory) {
      if (typeof moduleOrFactory === 'function') {
        global.RN$registerCallableModule(name, moduleOrFactory);
        return;
      }
      global.RN$registerCallableModule(name, function () {
        return moduleOrFactory;
      });
    };
  }
  var BatchedBridge = require("../BatchedBridge/BatchedBridge").default;
  return function (name, moduleOrFactory) {
    if (typeof moduleOrFactory === 'function') {
      BatchedBridge.registerLazyCallableModule(name, moduleOrFactory);
      return;
    }
    BatchedBridge.registerCallableModule(name, moduleOrFactory);
  };
}();
var _default = exports.default = registerCallableModule;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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