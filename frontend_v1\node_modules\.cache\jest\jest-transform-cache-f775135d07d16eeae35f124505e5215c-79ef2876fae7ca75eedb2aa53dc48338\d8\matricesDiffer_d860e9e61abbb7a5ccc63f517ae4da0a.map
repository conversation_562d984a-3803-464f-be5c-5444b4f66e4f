{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "<PERSON><PERSON><PERSON><PERSON>", "one", "two", "_default"], "sources": ["matricesDiffer.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\n/**\n * Unrolls an array comparison specially for matrices. Prioritizes\n * checking of indices that are most likely to change so that the comparison\n * bails as early as possible.\n *\n * @param {MatrixMath.Matrix} one First matrix.\n * @param {MatrixMath.Matrix} two Second matrix.\n * @return {boolean} Whether or not the two matrices differ.\n */\nfunction matricesDiffer(one: ?Array<number>, two: ?Array<number>): boolean {\n  if (one === two) {\n    return false;\n  }\n  return (\n    !one ||\n    !two ||\n    one[12] !== two[12] ||\n    one[13] !== two[13] ||\n    one[14] !== two[14] ||\n    one[5] !== two[5] ||\n    one[10] !== two[10] ||\n    one[0] !== two[0] ||\n    one[1] !== two[1] ||\n    one[2] !== two[2] ||\n    one[3] !== two[3] ||\n    one[4] !== two[4] ||\n    one[6] !== two[6] ||\n    one[7] !== two[7] ||\n    one[8] !== two[8] ||\n    one[9] !== two[9] ||\n    one[11] !== two[11] ||\n    one[15] !== two[15]\n  );\n}\n\nexport default matricesDiffer;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAWb,SAASC,cAAcA,CAACC,GAAmB,EAAEC,GAAmB,EAAW;EACzE,IAAID,GAAG,KAAKC,GAAG,EAAE;IACf,OAAO,KAAK;EACd;EACA,OACE,CAACD,GAAG,IACJ,CAACC,GAAG,IACJD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC,IACnBD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC,IACnBD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC,IACnBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC,IACnBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,IACjBD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC,IACnBD,GAAG,CAAC,EAAE,CAAC,KAAKC,GAAG,CAAC,EAAE,CAAC;AAEvB;AAAC,IAAAC,QAAA,GAAAN,OAAA,CAAAE,OAAA,GAEcC,cAAc", "ignoreList": []}