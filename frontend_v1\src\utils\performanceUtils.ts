/**
 * Performance Utilities
 *
 * Comprehensive performance optimization utilities for enhanced user experience
 * with loading states, caching, and performance monitoring.
 *
 * Features:
 * - Performance monitoring
 * - Loading state management
 * - Caching strategies
 * - Memory optimization
 * - Network optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Performance metrics interface
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  networkLatency: number;
  cacheHitRate: number;
}

// Loading state interface
export interface LoadingState {
  isLoading: boolean;
  progress?: number;
  message?: string;
  type: 'initial' | 'refresh' | 'loadMore' | 'action';
  startTime: number;
  estimatedDuration?: number;
}

// Cache configuration
export interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
  strategy: 'lru' | 'fifo' | 'lfu';
  persistToDisk: boolean;
}

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  FAST_LOAD: 1000, // 1 second
  ACCEPTABLE_LOAD: 3000, // 3 seconds
  SLOW_LOAD: 5000, // 5 seconds
  INTERACTION_DELAY: 100, // 100ms
  ANIMATION_FRAME: 16.67, // 60fps
} as const;

/**
 * Performance monitor class
 */
export class PerformanceMonitor {
  private metrics: Map<string, number> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();

  /**
   * Start timing a performance metric
   */
  startTiming(key: string): void {
    this.metrics.set(key, performance.now());
  }

  /**
   * End timing and return duration
   */
  endTiming(key: string): number {
    const startTime = this.metrics.get(key);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.metrics.delete(key);
    return duration;
  }

  /**
   * Measure function execution time
   */
  measureFunction<T>(fn: () => T, key?: string): { result: T; duration: number } {
    const startTime = performance.now();
    const result = fn();
    const duration = performance.now() - startTime;

    if (key) {
      console.log(`Performance [${key}]: ${duration.toFixed(2)}ms`);
    }

    return { result, duration };
  }

  /**
   * Measure async function execution time
   */
  async measureAsyncFunction<T>(
    fn: () => Promise<T>,
    key?: string
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    const result = await fn();
    const duration = performance.now() - startTime;

    if (key) {
      console.log(`Performance [${key}]: ${duration.toFixed(2)}ms`);
    }

    return { result, duration };
  }

  /**
   * Get current memory usage (if available)
   */
  getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Monitor frame rate
   */
  monitorFrameRate(callback: (fps: number) => void): () => void {
    let frames = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frames++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime));
        callback(fps);
        frames = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }
}

/**
 * Simple LRU Cache implementation
 */
export class LRUCache<T> {
  private cache = new Map<string, { value: T; timestamp: number }>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize: number = 100, ttl: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // Check if item has expired
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, item);
    
    return item.value;
  }

  set(key: string, value: T): void {
    // Remove if already exists
    if (this.cache.has(key)) {
      this.cache.delete(key);
    }
    // Remove oldest if at capacity
    else if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, { value, timestamp: Date.now() });
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  getHitRate(): number {
    // This would need to be tracked separately in a real implementation
    return 0.8; // Placeholder
  }
}

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Lazy loading utility
 */
export const createLazyLoader = <T>(
  loader: () => Promise<T>,
  fallback?: T
): {
  load: () => Promise<T>;
  isLoaded: () => boolean;
  getValue: () => T | undefined;
} => {
  let loadPromise: Promise<T> | null = null;
  let loadedValue: T | undefined = undefined;
  let isLoaded = false;

  return {
    load: () => {
      if (!loadPromise) {
        loadPromise = loader().then(value => {
          loadedValue = value;
          isLoaded = true;
          return value;
        });
      }
      return loadPromise;
    },
    isLoaded: () => isLoaded,
    getValue: () => loadedValue || fallback,
  };
};

/**
 * Image preloader utility
 */
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(
    urls.map(url => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    })
  );
};

/**
 * Batch operations for performance
 */
export const batchOperations = <T, R>(
  items: T[],
  operation: (batch: T[]) => Promise<R[]>,
  batchSize: number = 10
): Promise<R[]> => {
  const batches: T[][] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }

  return Promise.all(batches.map(batch => operation(batch)))
    .then(results => results.flat());
};

/**
 * Memory usage monitor
 */
export const monitorMemoryUsage = (
  callback: (usage: number) => void,
  interval: number = 5000
): () => void => {
  const monitor = new PerformanceMonitor();
  
  const intervalId = setInterval(() => {
    const usage = monitor.getMemoryUsage();
    callback(usage);
  }, interval);

  return () => clearInterval(intervalId);
};

/**
 * Network latency measurement
 */
export const measureNetworkLatency = async (url: string): Promise<number> => {
  const startTime = performance.now();
  
  try {
    await fetch(url, { method: 'HEAD', mode: 'no-cors' });
    return performance.now() - startTime;
  } catch (error) {
    return -1; // Error occurred
  }
};

/**
 * Loading state manager
 */
export class LoadingStateManager {
  private loadingStates = new Map<string, LoadingState>();
  private listeners = new Set<(states: Map<string, LoadingState>) => void>();

  startLoading(
    key: string,
    type: LoadingState['type'] = 'action',
    message?: string,
    estimatedDuration?: number
  ): void {
    this.loadingStates.set(key, {
      isLoading: true,
      progress: 0,
      message,
      type,
      startTime: Date.now(),
      estimatedDuration,
    });
    
    this.notifyListeners();
  }

  updateProgress(key: string, progress: number, message?: string): void {
    const state = this.loadingStates.get(key);
    if (state) {
      state.progress = Math.max(0, Math.min(100, progress));
      if (message) state.message = message;
      this.notifyListeners();
    }
  }

  finishLoading(key: string): void {
    this.loadingStates.delete(key);
    this.notifyListeners();
  }

  isLoading(key?: string): boolean {
    if (key) {
      return this.loadingStates.has(key);
    }
    return this.loadingStates.size > 0;
  }

  getLoadingState(key: string): LoadingState | undefined {
    return this.loadingStates.get(key);
  }

  getAllLoadingStates(): Map<string, LoadingState> {
    return new Map(this.loadingStates);
  }

  subscribe(listener: (states: Map<string, LoadingState>) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.getAllLoadingStates()));
  }
}

/**
 * Performance optimization recommendations
 */
export const getPerformanceRecommendations = (metrics: PerformanceMetrics): string[] => {
  const recommendations: string[] = [];

  if (metrics.loadTime > PERFORMANCE_THRESHOLDS.SLOW_LOAD) {
    recommendations.push('Consider implementing lazy loading for heavy components');
    recommendations.push('Optimize image sizes and formats');
    recommendations.push('Implement code splitting');
  }

  if (metrics.renderTime > PERFORMANCE_THRESHOLDS.INTERACTION_DELAY) {
    recommendations.push('Optimize component re-renders with React.memo');
    recommendations.push('Use useMemo and useCallback for expensive calculations');
  }

  if (metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB
    recommendations.push('Check for memory leaks in event listeners');
    recommendations.push('Implement proper cleanup in useEffect');
  }

  if (metrics.cacheHitRate < 0.7) {
    recommendations.push('Improve caching strategy');
    recommendations.push('Implement better cache invalidation');
  }

  return recommendations;
};

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Global cache instance
export const globalCache = new LRUCache(200, 10 * 60 * 1000); // 200 items, 10 minutes TTL

// Global loading state manager
export const loadingStateManager = new LoadingStateManager();

export default {
  PerformanceMonitor,
  LRUCache,
  LoadingStateManager,
  debounce,
  throttle,
  createLazyLoader,
  preloadImages,
  batchOperations,
  monitorMemoryUsage,
  measureNetworkLatency,
  getPerformanceRecommendations,
  performanceMonitor,
  globalCache,
  loadingStateManager,
  PERFORMANCE_THRESHOLDS,
};
