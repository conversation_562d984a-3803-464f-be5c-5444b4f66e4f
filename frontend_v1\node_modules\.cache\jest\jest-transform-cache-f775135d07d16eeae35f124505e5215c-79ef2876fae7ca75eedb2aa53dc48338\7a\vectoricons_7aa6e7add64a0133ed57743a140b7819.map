{"version": 3, "names": ["React", "require", "_require", "Text", "createIconComponent", "name", "forwardRef", "props", "ref", "createElement", "Object", "assign", "testID", "accessibilityLabel", "Ionicons", "exports", "MaterialIcons", "FontAwesome", "<PERSON><PERSON><PERSON>", "AntDesign", "MaterialCommunityIcons", "<PERSON><PERSON>", "Foundation", "EvilIcons", "Octicons", "SimpleLineIcons", "Zocial", "_default", "default"], "sources": ["vector-icons.js"], "sourcesContent": ["/**\n * Mock for @expo/vector-icons\n */\n\nconst React = require('react');\nconst { Text } = require('react-native');\n\nconst createIconComponent = (name) => {\n  return React.forwardRef((props, ref) => {\n    return React.createElement(Text, {\n      ...props,\n      ref,\n      testID: props.testID || `icon-${name}`,\n      accessibilityLabel: props.accessibilityLabel || `${name} icon`,\n    }, props.name || name);\n  });\n};\n\nexport const Ionicons = createIconComponent('Ionicons');\nexport const MaterialIcons = createIconComponent('MaterialIcons');\nexport const FontAwesome = createIconComponent('FontAwesome');\nexport const Entypo = createIconComponent('Entypo');\nexport const AntDesign = createIconComponent('AntDesign');\nexport const MaterialCommunityIcons = createIconComponent('MaterialCommunityIcons');\nexport const Feather = createIconComponent('Feather');\nexport const Foundation = createIconComponent('Foundation');\nexport const EvilIcons = createIconComponent('EvilIcons');\nexport const Octicons = createIconComponent('Octicons');\nexport const SimpleLineIcons = createIconComponent('SimpleLineIcons');\nexport const Zocial = createIconComponent('Zocial');\n\nexport default {\n  Ionicons,\n  MaterialIcons,\n  FontAwesome,\n  Entypo,\n  AntDesign,\n  MaterialCommunityIcons,\n  Feather,\n  Foundation,\n  EvilIcons,\n  Octicons,\n  SimpleLineIcons,\n  Zocial,\n};\n"], "mappings": ";;;;AAIA,IAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,IAAAC,QAAA,GAAiBD,OAAO,CAAC,cAAc,CAAC;EAAhCE,IAAI,GAAAD,QAAA,CAAJC,IAAI;AAEZ,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,IAAI,EAAK;EACpC,OAAOL,KAAK,CAACM,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;IACtC,OAAOR,KAAK,CAACS,aAAa,CAACN,IAAI,EAAAO,MAAA,CAAAC,MAAA,KAC1BJ,KAAK;MACRC,GAAG,EAAHA,GAAG;MACHI,MAAM,EAAEL,KAAK,CAACK,MAAM,IAAI,QAAQP,IAAI,EAAE;MACtCQ,kBAAkB,EAAEN,KAAK,CAACM,kBAAkB,IAAI,GAAGR,IAAI;IAAO,IAC7DE,KAAK,CAACF,IAAI,IAAIA,IAAI,CAAC;EACxB,CAAC,CAAC;AACJ,CAAC;AAEM,IAAMS,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAGV,mBAAmB,CAAC,UAAU,CAAC;AAChD,IAAMY,aAAa,GAAAD,OAAA,CAAAC,aAAA,GAAGZ,mBAAmB,CAAC,eAAe,CAAC;AAC1D,IAAMa,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAGb,mBAAmB,CAAC,aAAa,CAAC;AACtD,IAAMc,MAAM,GAAAH,OAAA,CAAAG,MAAA,GAAGd,mBAAmB,CAAC,QAAQ,CAAC;AAC5C,IAAMe,SAAS,GAAAJ,OAAA,CAAAI,SAAA,GAAGf,mBAAmB,CAAC,WAAW,CAAC;AAClD,IAAMgB,sBAAsB,GAAAL,OAAA,CAAAK,sBAAA,GAAGhB,mBAAmB,CAAC,wBAAwB,CAAC;AAC5E,IAAMiB,OAAO,GAAAN,OAAA,CAAAM,OAAA,GAAGjB,mBAAmB,CAAC,SAAS,CAAC;AAC9C,IAAMkB,UAAU,GAAAP,OAAA,CAAAO,UAAA,GAAGlB,mBAAmB,CAAC,YAAY,CAAC;AACpD,IAAMmB,SAAS,GAAAR,OAAA,CAAAQ,SAAA,GAAGnB,mBAAmB,CAAC,WAAW,CAAC;AAClD,IAAMoB,QAAQ,GAAAT,OAAA,CAAAS,QAAA,GAAGpB,mBAAmB,CAAC,UAAU,CAAC;AAChD,IAAMqB,eAAe,GAAAV,OAAA,CAAAU,eAAA,GAAGrB,mBAAmB,CAAC,iBAAiB,CAAC;AAC9D,IAAMsB,MAAM,GAAAX,OAAA,CAAAW,MAAA,GAAGtB,mBAAmB,CAAC,QAAQ,CAAC;AAAC,IAAAuB,QAAA,GAAAZ,OAAA,CAAAa,OAAA,GAErC;EACbd,QAAQ,EAARA,QAAQ;EACRE,aAAa,EAAbA,aAAa;EACbC,WAAW,EAAXA,WAAW;EACXC,MAAM,EAANA,MAAM;EACNC,SAAS,EAATA,SAAS;EACTC,sBAAsB,EAAtBA,sBAAsB;EACtBC,OAAO,EAAPA,OAAO;EACPC,UAAU,EAAVA,UAAU;EACVC,SAAS,EAATA,SAAS;EACTC,QAAQ,EAARA,QAAQ;EACRC,eAAe,EAAfA,eAAe;EACfC,MAAM,EAANA;AACF,CAAC", "ignoreList": []}