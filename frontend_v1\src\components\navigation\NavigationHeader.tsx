/**
 * Navigation Header Component
 *
 * Provides consistent navigation header with breadcrumbs, back button,
 * and contextual actions across all screens.
 *
 * Features:
 * - Breadcrumb navigation
 * - Back button with proper accessibility
 * - Contextual action buttons
 * - Search integration
 * - Responsive design
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { BreadcrumbNavigation, BreadcrumbItem } from './BreadcrumbNavigation';
import { Text } from '../atoms/Text';
import { Box } from '../atoms/Box';
import { useNavigationHelper } from '../../hooks/useNavigationHelper';

// Action button interface
export interface HeaderAction {
  id: string;
  icon: string;
  label: string;
  onPress: () => void;
  badge?: number | string;
  disabled?: boolean;
  accessibilityLabel?: string;
}

// Component props
export interface NavigationHeaderProps {
  title?: string;
  subtitle?: string;
  showBreadcrumbs?: boolean;
  showBackButton?: boolean;
  actions?: HeaderAction[];
  onBackPress?: () => void;
  style?: any;
  testID?: string;
}

export const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  subtitle,
  showBreadcrumbs = true,
  showBackButton = true,
  actions = [],
  onBackPress,
  style,
  testID = 'navigation-header',
}) => {
  const insets = useSafeAreaInsets();
  const {
    breadcrumbs,
    canGoBack,
    goBackWithContext,
    handleBreadcrumbNavigation,
    getNavigationContext,
  } = useNavigationHelper();

  const context = getNavigationContext();

  // Handle back button press
  const handleBackPress = useCallback(() => {
    if (onBackPress) {
      onBackPress();
    } else {
      goBackWithContext();
    }
  }, [onBackPress, goBackWithContext]);

  // Determine if back button should be shown
  const shouldShowBackButton = showBackButton && canGoBack;

  // Get effective title
  const effectiveTitle = title || context.currentTitle;

  return (
    <Box
      style={[
        styles.container,
        { paddingTop: insets.top },
        style,
      ]}
      testID={testID}
    >
      {/* Status bar */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={false}
      />

      {/* Main header */}
      <View style={styles.mainHeader}>
        {/* Left section - Back button */}
        <View style={styles.leftSection}>
          {shouldShowBackButton && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackPress}
              accessibilityRole="button"
              accessibilityLabel="Go back"
              accessibilityHint="Navigate to the previous screen"
              testID={`${testID}-back-button`}
            >
              <Ionicons
                name="arrow-back"
                size={24}
                color="#333"
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Center section - Title */}
        <View style={styles.centerSection}>
          {effectiveTitle && (
            <Text
              variant="heading"
              size="lg"
              weight="semibold"
              color="#333"
              style={styles.title}
              numberOfLines={1}
              testID={`${testID}-title`}
            >
              {effectiveTitle}
            </Text>
          )}
          {subtitle && (
            <Text
              variant="body"
              size="sm"
              color="#666"
              style={styles.subtitle}
              numberOfLines={1}
              testID={`${testID}-subtitle`}
            >
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right section - Actions */}
        <View style={styles.rightSection}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.actionButton,
                action.disabled && styles.disabledActionButton,
              ]}
              onPress={action.onPress}
              disabled={action.disabled}
              accessibilityRole="button"
              accessibilityLabel={action.accessibilityLabel || action.label}
              accessibilityHint={`Tap to ${action.label.toLowerCase()}`}
              accessibilityState={{ disabled: action.disabled }}
              testID={`${testID}-action-${index}`}
            >
              <Ionicons
                name={action.icon as any}
                size={24}
                color={action.disabled ? '#CCC' : '#333'}
              />
              {action.badge && (
                <View style={styles.actionBadge}>
                  <Text
                    variant="body"
                    size="xs"
                    weight="bold"
                    color="#FFFFFF"
                    style={styles.badgeText}
                  >
                    {typeof action.badge === 'number' && action.badge > 99 
                      ? '99+' 
                      : action.badge.toString()
                    }
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Breadcrumbs */}
      {showBreadcrumbs && breadcrumbs.length > 0 && (
        <BreadcrumbNavigation
          items={breadcrumbs}
          onNavigate={handleBreadcrumbNavigation}
          style={styles.breadcrumbs}
          testID={`${testID}-breadcrumbs`}
        />
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  mainHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 56,
  },
  leftSection: {
    width: 48,
    alignItems: 'flex-start',
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 48,
    justifyContent: 'flex-end',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  title: {
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    marginTop: 2,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    position: 'relative',
  },
  disabledActionButton: {
    opacity: 0.5,
  },
  actionBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    lineHeight: 12,
  },
  breadcrumbs: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
});

export default NavigationHeader;
