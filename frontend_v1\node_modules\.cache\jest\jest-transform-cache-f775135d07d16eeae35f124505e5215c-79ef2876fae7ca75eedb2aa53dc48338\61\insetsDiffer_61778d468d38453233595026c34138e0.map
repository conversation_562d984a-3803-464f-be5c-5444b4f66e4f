{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "dummyInsets", "top", "undefined", "left", "right", "bottom", "ins<PERSON><PERSON><PERSON><PERSON>", "one", "two", "_default"], "sources": ["insetsDiffer.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\ntype Inset = {\n  top: ?number,\n  left: ?number,\n  right: ?number,\n  bottom: ?number,\n  ...\n};\n\nconst dummyInsets = {\n  top: undefined,\n  left: undefined,\n  right: undefined,\n  bottom: undefined,\n};\n\nfunction insetsDiffer(one: Inset, two: Inset): boolean {\n  one = one || dummyInsets;\n  two = two || dummyInsets;\n  return (\n    one !== two &&\n    (one.top !== two.top ||\n      one.left !== two.left ||\n      one.right !== two.right ||\n      one.bottom !== two.bottom)\n  );\n}\n\nexport default insetsDiffer;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAUb,IAAMC,WAAW,GAAG;EAClBC,GAAG,EAAEC,SAAS;EACdC,IAAI,EAAED,SAAS;EACfE,KAAK,EAAEF,SAAS;EAChBG,MAAM,EAAEH;AACV,CAAC;AAED,SAASI,YAAYA,CAACC,GAAU,EAAEC,GAAU,EAAW;EACrDD,GAAG,GAAGA,GAAG,IAAIP,WAAW;EACxBQ,GAAG,GAAGA,GAAG,IAAIR,WAAW;EACxB,OACEO,GAAG,KAAKC,GAAG,KACVD,GAAG,CAACN,GAAG,KAAKO,GAAG,CAACP,GAAG,IAClBM,GAAG,CAACJ,IAAI,KAAKK,GAAG,CAACL,IAAI,IACrBI,GAAG,CAACH,KAAK,KAAKI,GAAG,CAACJ,KAAK,IACvBG,GAAG,CAACF,MAAM,KAAKG,GAAG,CAACH,MAAM,CAAC;AAEhC;AAAC,IAAAI,QAAA,GAAAZ,OAAA,CAAAE,OAAA,GAEcO,YAAY", "ignoreList": []}