/**
 * Color Contrast Utilities
 *
 * Comprehensive utilities for color contrast validation and accessibility
 * compliance following WCAG 2.2 AA/AAA guidelines.
 *
 * Features:
 * - Contrast ratio calculation
 * - WCAG compliance validation
 * - Color accessibility testing
 * - High contrast mode support
 * - Color blindness simulation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// WCAG contrast ratio thresholds
export const WCAG_THRESHOLDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
} as const;

// Color interface
export interface Color {
  r: number;
  g: number;
  b: number;
  a?: number;
}

// Contrast result interface
export interface ContrastResult {
  ratio: number;
  wcagAA: boolean;
  wcagAAA: boolean;
  wcagAALarge: boolean;
  wcagAAALarge: boolean;
  level: 'fail' | 'aa' | 'aaa';
  recommendation?: string;
}

/**
 * Convert hex color to RGB
 */
export const hexToRgb = (hex: string): Color | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
  } : null;
};

/**
 * Convert RGB to hex
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
};

/**
 * Calculate relative luminance of a color
 * Based on WCAG 2.2 specification
 */
export const getRelativeLuminance = (color: Color): number => {
  const { r, g, b } = color;
  
  // Convert to sRGB
  const rsRGB = r / 255;
  const gsRGB = g / 255;
  const bsRGB = b / 255;
  
  // Apply gamma correction
  const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  
  // Calculate relative luminance
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
};

/**
 * Calculate contrast ratio between two colors
 * Based on WCAG 2.2 specification
 */
export const getContrastRatio = (color1: Color, color2: Color): number => {
  const lum1 = getRelativeLuminance(color1);
  const lum2 = getRelativeLuminance(color2);
  
  const lighter = Math.max(lum1, lum2);
  const darker = Math.min(lum1, lum2);
  
  return (lighter + 0.05) / (darker + 0.05);
};

/**
 * Check if contrast ratio meets WCAG requirements
 */
export const checkContrastCompliance = (
  foreground: string | Color,
  background: string | Color,
  isLargeText: boolean = false
): ContrastResult => {
  // Convert hex strings to Color objects if needed
  const fgColor = typeof foreground === 'string' ? hexToRgb(foreground) : foreground;
  const bgColor = typeof background === 'string' ? hexToRgb(background) : background;
  
  if (!fgColor || !bgColor) {
    throw new Error('Invalid color format');
  }
  
  const ratio = getContrastRatio(fgColor, bgColor);
  
  const wcagAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL);
  const wcagAAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AAA_LARGE : WCAG_THRESHOLDS.AAA_NORMAL);
  const wcagAALarge = ratio >= WCAG_THRESHOLDS.AA_LARGE;
  const wcagAAALarge = ratio >= WCAG_THRESHOLDS.AAA_LARGE;
  
  let level: 'fail' | 'aa' | 'aaa' = 'fail';
  if (wcagAAA) {
    level = 'aaa';
  } else if (wcagAA) {
    level = 'aa';
  }
  
  let recommendation: string | undefined;
  if (!wcagAA) {
    const targetRatio = isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL;
    recommendation = `Increase contrast ratio to at least ${targetRatio}:1 for WCAG AA compliance`;
  }
  
  return {
    ratio: Math.round(ratio * 100) / 100,
    wcagAA,
    wcagAAA,
    wcagAALarge,
    wcagAAALarge,
    level,
    recommendation,
  };
};

/**
 * Generate accessible color palette
 */
export const generateAccessibleColors = (baseColor: string): {
  primary: string;
  secondary: string;
  text: string;
  textSecondary: string;
  background: string;
  surface: string;
} => {
  const base = hexToRgb(baseColor);
  if (!base) throw new Error('Invalid base color');
  
  // Generate variations with proper contrast
  return {
    primary: baseColor,
    secondary: adjustColorForContrast(baseColor, '#FFFFFF', 3.0),
    text: '#000000',
    textSecondary: '#666666',
    background: '#FFFFFF',
    surface: '#F8F9FA',
  };
};

/**
 * Adjust color to meet minimum contrast ratio
 */
export const adjustColorForContrast = (
  color: string,
  background: string,
  minRatio: number
): string => {
  const colorRgb = hexToRgb(color);
  const bgRgb = hexToRgb(background);
  
  if (!colorRgb || !bgRgb) throw new Error('Invalid color format');
  
  let adjustedColor = { ...colorRgb };
  let currentRatio = getContrastRatio(adjustedColor, bgRgb);
  
  // If already meets requirement, return original
  if (currentRatio >= minRatio) {
    return color;
  }
  
  // Determine if we need to make it lighter or darker
  const bgLuminance = getRelativeLuminance(bgRgb);
  const shouldDarken = bgLuminance > 0.5;
  
  // Adjust color iteratively
  let step = shouldDarken ? -5 : 5;
  let iterations = 0;
  const maxIterations = 50;
  
  while (currentRatio < minRatio && iterations < maxIterations) {
    adjustedColor.r = Math.max(0, Math.min(255, adjustedColor.r + step));
    adjustedColor.g = Math.max(0, Math.min(255, adjustedColor.g + step));
    adjustedColor.b = Math.max(0, Math.min(255, adjustedColor.b + step));
    
    currentRatio = getContrastRatio(adjustedColor, bgRgb);
    iterations++;
  }
  
  return rgbToHex(adjustedColor.r, adjustedColor.g, adjustedColor.b);
};

/**
 * Get high contrast version of a color
 */
export const getHighContrastColor = (
  color: string,
  background: string = '#FFFFFF'
): string => {
  const colorRgb = hexToRgb(color);
  const bgRgb = hexToRgb(background);
  
  if (!colorRgb || !bgRgb) return color;
  
  const bgLuminance = getRelativeLuminance(bgRgb);
  
  // Return high contrast black or white based on background
  return bgLuminance > 0.5 ? '#000000' : '#FFFFFF';
};

/**
 * Simulate color blindness
 */
export const simulateColorBlindness = (
  color: string,
  type: 'protanopia' | 'deuteranopia' | 'tritanopia'
): string => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  let { r, g, b } = rgb;
  
  // Apply color blindness transformation matrices
  switch (type) {
    case 'protanopia': // Red-blind
      r = 0.567 * r + 0.433 * g;
      g = 0.558 * r + 0.442 * g;
      b = 0.242 * g + 0.758 * b;
      break;
    case 'deuteranopia': // Green-blind
      r = 0.625 * r + 0.375 * g;
      g = 0.7 * r + 0.3 * g;
      b = 0.3 * g + 0.7 * b;
      break;
    case 'tritanopia': // Blue-blind
      r = 0.95 * r + 0.05 * g;
      g = 0.433 * g + 0.567 * b;
      b = 0.475 * g + 0.525 * b;
      break;
  }
  
  return rgbToHex(
    Math.round(Math.max(0, Math.min(255, r))),
    Math.round(Math.max(0, Math.min(255, g))),
    Math.round(Math.max(0, Math.min(255, b)))
  );
};

/**
 * Validate color palette accessibility
 */
export const validateColorPalette = (palette: Record<string, string>): {
  valid: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Common color combinations to check
  const combinations = [
    { fg: 'text', bg: 'background', name: 'Primary text on background' },
    { fg: 'textSecondary', bg: 'background', name: 'Secondary text on background' },
    { fg: 'primary', bg: 'background', name: 'Primary color on background' },
    { fg: 'background', bg: 'primary', name: 'Background on primary' },
  ];
  
  for (const combo of combinations) {
    if (palette[combo.fg] && palette[combo.bg]) {
      try {
        const result = checkContrastCompliance(palette[combo.fg], palette[combo.bg]);
        
        if (!result.wcagAA) {
          issues.push(`${combo.name}: Contrast ratio ${result.ratio}:1 fails WCAG AA`);
          if (result.recommendation) {
            recommendations.push(`${combo.name}: ${result.recommendation}`);
          }
        }
      } catch (error) {
        issues.push(`${combo.name}: Invalid color format`);
      }
    }
  }
  
  return {
    valid: issues.length === 0,
    issues,
    recommendations,
  };
};

/**
 * Generate accessibility report for colors
 */
export const generateAccessibilityReport = (
  foreground: string,
  background: string,
  isLargeText: boolean = false
): string => {
  try {
    const result = checkContrastCompliance(foreground, background, isLargeText);
    
    let report = `Color Accessibility Report\n`;
    report += `========================\n`;
    report += `Foreground: ${foreground}\n`;
    report += `Background: ${background}\n`;
    report += `Text Size: ${isLargeText ? 'Large (18pt+)' : 'Normal'}\n`;
    report += `Contrast Ratio: ${result.ratio}:1\n\n`;
    
    report += `WCAG Compliance:\n`;
    report += `- AA Normal: ${result.wcagAA ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AA Large: ${result.wcagAALarge ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AAA Normal: ${result.wcagAAA ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AAA Large: ${result.wcagAAALarge ? '✓ Pass' : '✗ Fail'}\n\n`;
    
    if (result.recommendation) {
      report += `Recommendation: ${result.recommendation}\n`;
    }
    
    return report;
  } catch (error) {
    return `Error generating report: ${error}`;
  }
};

export default {
  hexToRgb,
  rgbToHex,
  getRelativeLuminance,
  getContrastRatio,
  checkContrastCompliance,
  generateAccessibleColors,
  adjustColorForContrast,
  getHighContrastColor,
  simulateColorBlindness,
  validateColorPalette,
  generateAccessibilityReport,
  WCAG_THRESHOLDS,
};
