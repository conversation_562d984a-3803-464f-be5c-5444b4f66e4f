/**
 * Visual Hierarchy Utilities
 *
 * Comprehensive utilities for creating effective visual hierarchy
 * and improving content readability and user comprehension.
 *
 * Features:
 * - Visual hierarchy analysis
 * - Content structure optimization
 * - Readability assessment
 * - Layout spacing calculations
 * - Accessibility compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { TYPOGRAPHY_VARIANTS, FONT_SIZES, getOptimalLineHeight } from '../constants/Typography';

// Visual hierarchy levels
export type HierarchyLevel = 'primary' | 'secondary' | 'tertiary' | 'quaternary';

// Content types for hierarchy
export type ContentType = 
  | 'headline' 
  | 'subheadline' 
  | 'body' 
  | 'caption' 
  | 'label' 
  | 'button' 
  | 'navigation';

// Visual weight factors
export interface VisualWeight {
  size: number;
  weight: number;
  color: number;
  spacing: number;
  position: number;
  contrast: number;
}

// Hierarchy configuration
export interface HierarchyConfig {
  level: HierarchyLevel;
  contentType: ContentType;
  importance: number; // 1-10 scale
  context: 'page' | 'section' | 'component';
}

// Spacing scale based on typography
export const SPACING_SCALE = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
} as const;

// Visual hierarchy rules
export const HIERARCHY_RULES = {
  // Size relationships
  sizeRatios: {
    primary: 1.0,
    secondary: 0.8,
    tertiary: 0.64,
    quaternary: 0.51,
  },
  
  // Weight relationships
  weightRatios: {
    primary: 1.0,
    secondary: 0.85,
    tertiary: 0.7,
    quaternary: 0.55,
  },
  
  // Spacing relationships
  spacingRatios: {
    primary: 1.0,
    secondary: 0.75,
    tertiary: 0.5,
    quaternary: 0.25,
  },
} as const;

/**
 * Calculate visual weight of an element
 */
export const calculateVisualWeight = (config: {
  fontSize: number;
  fontWeight: string;
  color: string;
  backgroundColor?: string;
  marginTop?: number;
  marginBottom?: number;
  position?: 'top' | 'middle' | 'bottom';
}): VisualWeight => {
  const {
    fontSize,
    fontWeight,
    color,
    backgroundColor = '#FFFFFF',
    marginTop = 0,
    marginBottom = 0,
    position = 'middle',
  } = config;

  // Size factor (normalized to base font size)
  const sizeFactor = fontSize / FONT_SIZES.base;

  // Weight factor
  const weightMap: Record<string, number> = {
    '300': 0.3,
    '400': 0.4,
    '500': 0.5,
    '600': 0.6,
    '700': 0.7,
    '800': 0.8,
  };
  const weightFactor = weightMap[fontWeight] || 0.4;

  // Color contrast factor (simplified)
  const colorFactor = getColorWeight(color, backgroundColor);

  // Spacing factor
  const spacingFactor = (marginTop + marginBottom) / SPACING_SCALE.md;

  // Position factor
  const positionMap = { top: 1.2, middle: 1.0, bottom: 0.8 };
  const positionFactor = positionMap[position];

  return {
    size: sizeFactor,
    weight: weightFactor,
    color: colorFactor,
    spacing: spacingFactor,
    position: positionFactor,
    contrast: colorFactor, // Simplified for now
  };
};

/**
 * Get color weight (simplified contrast calculation)
 */
const getColorWeight = (foreground: string, background: string): number => {
  // Simplified color weight calculation
  // In a real implementation, this would calculate actual contrast ratios
  const colorMap: Record<string, number> = {
    '#000000': 1.0,
    '#333333': 0.9,
    '#666666': 0.7,
    '#999999': 0.5,
    '#CCCCCC': 0.3,
    '#FFFFFF': 0.1,
  };
  
  return colorMap[foreground] || 0.7;
};

/**
 * Generate hierarchy styles based on level and content type
 */
export const generateHierarchyStyles = (
  level: HierarchyLevel,
  contentType: ContentType,
  baseSize: number = FONT_SIZES.base
) => {
  const sizeRatio = HIERARCHY_RULES.sizeRatios[level];
  const weightRatio = HIERARCHY_RULES.weightRatios[level];
  const spacingRatio = HIERARCHY_RULES.spacingRatios[level];

  // Get base typography variant
  const getBaseVariant = () => {
    switch (contentType) {
      case 'headline':
        return level === 'primary' ? TYPOGRAPHY_VARIANTS.h1 :
               level === 'secondary' ? TYPOGRAPHY_VARIANTS.h2 :
               level === 'tertiary' ? TYPOGRAPHY_VARIANTS.h3 :
               TYPOGRAPHY_VARIANTS.h4;
      case 'subheadline':
        return level === 'primary' ? TYPOGRAPHY_VARIANTS.h3 :
               level === 'secondary' ? TYPOGRAPHY_VARIANTS.h4 :
               level === 'tertiary' ? TYPOGRAPHY_VARIANTS.h5 :
               TYPOGRAPHY_VARIANTS.h6;
      case 'body':
        return TYPOGRAPHY_VARIANTS.body1;
      case 'caption':
        return TYPOGRAPHY_VARIANTS.caption;
      case 'label':
        return TYPOGRAPHY_VARIANTS.label;
      case 'button':
        return TYPOGRAPHY_VARIANTS.button;
      case 'navigation':
        return TYPOGRAPHY_VARIANTS.navItem;
      default:
        return TYPOGRAPHY_VARIANTS.body1;
    }
  };

  const baseVariant = getBaseVariant();
  const adjustedFontSize = Math.round(baseSize * sizeRatio);

  return {
    fontSize: adjustedFontSize,
    fontFamily: baseVariant.fontFamily,
    fontWeight: baseVariant.fontWeight,
    lineHeight: getOptimalLineHeight(adjustedFontSize),
    letterSpacing: baseVariant.letterSpacing,
    marginTop: SPACING_SCALE.md * spacingRatio,
    marginBottom: SPACING_SCALE.sm * spacingRatio,
  };
};

/**
 * Calculate optimal spacing between elements
 */
export const calculateOptimalSpacing = (
  element1: { fontSize: number; importance: number },
  element2: { fontSize: number; importance: number },
  relationship: 'sibling' | 'parent-child' | 'unrelated' = 'sibling'
): number => {
  const avgFontSize = (element1.fontSize + element2.fontSize) / 2;
  const importanceDiff = Math.abs(element1.importance - element2.importance);

  let baseSpacing = avgFontSize * 0.75; // Base spacing relative to font size

  // Adjust based on relationship
  switch (relationship) {
    case 'parent-child':
      baseSpacing *= 0.5; // Closer spacing for related content
      break;
    case 'unrelated':
      baseSpacing *= 1.5; // More spacing for unrelated content
      break;
    default:
      // sibling spacing remains as base
      break;
  }

  // Adjust based on importance difference
  baseSpacing += importanceDiff * 4;

  return Math.round(baseSpacing);
};

/**
 * Analyze content hierarchy
 */
export const analyzeContentHierarchy = (elements: Array<{
  id: string;
  type: ContentType;
  fontSize: number;
  fontWeight: string;
  color: string;
  position: number; // Y position on screen
  importance: number;
}>): {
  score: number;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  let score = 100;

  // Sort elements by position
  const sortedElements = [...elements].sort((a, b) => a.position - b.position);

  // Check if visual hierarchy matches logical hierarchy
  for (let i = 0; i < sortedElements.length - 1; i++) {
    const current = sortedElements[i];
    const next = sortedElements[i + 1];

    const currentWeight = calculateVisualWeight({
      fontSize: current.fontSize,
      fontWeight: current.fontWeight,
      color: current.color,
    });

    const nextWeight = calculateVisualWeight({
      fontSize: next.fontSize,
      fontWeight: next.fontWeight,
      color: next.color,
    });

    // Higher importance should have higher visual weight
    if (current.importance > next.importance) {
      const totalCurrentWeight = currentWeight.size + currentWeight.weight + currentWeight.color;
      const totalNextWeight = nextWeight.size + nextWeight.weight + nextWeight.color;

      if (totalCurrentWeight <= totalNextWeight) {
        issues.push(`Element "${current.id}" has higher importance but lower visual weight than "${next.id}"`);
        score -= 10;
      }
    }
  }

  // Check for sufficient contrast between hierarchy levels
  const uniqueImportanceLevels = [...new Set(elements.map(e => e.importance))].sort((a, b) => b - a);
  
  for (let i = 0; i < uniqueImportanceLevels.length - 1; i++) {
    const higherElements = elements.filter(e => e.importance === uniqueImportanceLevels[i]);
    const lowerElements = elements.filter(e => e.importance === uniqueImportanceLevels[i + 1]);

    const avgHigherSize = higherElements.reduce((sum, e) => sum + e.fontSize, 0) / higherElements.length;
    const avgLowerSize = lowerElements.reduce((sum, e) => sum + e.fontSize, 0) / lowerElements.length;

    const sizeRatio = avgHigherSize / avgLowerSize;
    
    if (sizeRatio < 1.2) {
      issues.push(`Insufficient size contrast between importance levels ${uniqueImportanceLevels[i]} and ${uniqueImportanceLevels[i + 1]}`);
      recommendations.push(`Increase size difference between hierarchy levels (current ratio: ${sizeRatio.toFixed(2)})`);
      score -= 15;
    }
  }

  // Check for too many hierarchy levels
  if (uniqueImportanceLevels.length > 4) {
    issues.push('Too many hierarchy levels may confuse users');
    recommendations.push('Consider consolidating to 3-4 hierarchy levels maximum');
    score -= 10;
  }

  // Generate recommendations based on issues
  if (issues.length === 0) {
    recommendations.push('Visual hierarchy is well-structured');
  } else {
    recommendations.push('Consider adjusting font sizes, weights, or colors to improve hierarchy');
    recommendations.push('Ensure more important content has greater visual weight');
  }

  return {
    score: Math.max(0, score),
    issues,
    recommendations,
  };
};

/**
 * Generate reading flow analysis
 */
export const analyzeReadingFlow = (elements: Array<{
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  importance: number;
  type: ContentType;
}>): {
  flow: string[];
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Sort by reading order (top to bottom, left to right for LTR)
  const readingOrder = [...elements].sort((a, b) => {
    if (Math.abs(a.y - b.y) < 20) { // Same row
      return a.x - b.x; // Left to right
    }
    return a.y - b.y; // Top to bottom
  });

  const flow = readingOrder.map(e => e.id);

  // Check if importance order matches reading order
  const importanceOrder = [...elements].sort((a, b) => b.importance - a.importance);
  
  for (let i = 0; i < Math.min(3, importanceOrder.length); i++) {
    const importantElement = importanceOrder[i];
    const readingPosition = readingOrder.findIndex(e => e.id === importantElement.id);
    
    if (readingPosition > 2) { // Important elements should appear early
      issues.push(`Important element "${importantElement.id}" appears late in reading flow`);
    }
  }

  // Check for proper spacing
  for (let i = 0; i < readingOrder.length - 1; i++) {
    const current = readingOrder[i];
    const next = readingOrder[i + 1];
    
    const verticalGap = next.y - (current.y + current.height);
    const horizontalGap = Math.abs(next.x - current.x);
    
    if (verticalGap < 8 && horizontalGap > current.width / 2) {
      issues.push(`Insufficient spacing between "${current.id}" and "${next.id}"`);
    }
  }

  if (issues.length === 0) {
    recommendations.push('Reading flow is well-optimized');
  } else {
    recommendations.push('Consider repositioning important elements earlier in the reading flow');
    recommendations.push('Ensure adequate spacing between content blocks');
  }

  return {
    flow,
    issues,
    recommendations,
  };
};

/**
 * Generate typography recommendations
 */
export const generateTypographyRecommendations = (
  content: string,
  currentStyles: any
): string[] => {
  const recommendations: string[] = [];
  
  // Check content length
  const wordCount = content.split(/\s+/).length;
  const lineLength = content.length;
  
  if (wordCount > 50 && currentStyles.fontSize < FONT_SIZES.lg) {
    recommendations.push('Consider larger font size for long-form content');
  }
  
  if (lineLength > 80 && !currentStyles.maxWidth) {
    recommendations.push('Add max-width to improve readability (45-75 characters per line)');
  }
  
  // Check line height
  const lineHeight = currentStyles.lineHeight || currentStyles.fontSize * 1.5;
  const optimalLineHeight = getOptimalLineHeight(currentStyles.fontSize);
  
  if (Math.abs(lineHeight - optimalLineHeight) > 2) {
    recommendations.push(`Adjust line height to ${optimalLineHeight}px for better readability`);
  }
  
  return recommendations;
};

export default {
  SPACING_SCALE,
  HIERARCHY_RULES,
  calculateVisualWeight,
  generateHierarchyStyles,
  calculateOptimalSpacing,
  analyzeContentHierarchy,
  analyzeReadingFlow,
  generateTypographyRecommendations,
};
