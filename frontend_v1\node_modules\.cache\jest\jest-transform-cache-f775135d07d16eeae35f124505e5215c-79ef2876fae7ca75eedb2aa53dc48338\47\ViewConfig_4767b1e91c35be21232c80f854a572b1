1a5743eec40485906fabdc071e67725a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createViewConfig = createViewConfig;
var _PlatformBaseViewConfig = _interopRequireDefault(require("./PlatformBaseViewConfig"));
function createViewConfig(partialViewConfig) {
  return {
    uiViewClassName: partialViewConfig.uiViewClassName,
    Commands: {},
    bubblingEventTypes: composeIndexers(_PlatformBaseViewConfig.default.bubblingEventTypes, partialViewConfig.bubblingEventTypes),
    directEventTypes: composeIndexers(_PlatformBaseViewConfig.default.directEventTypes, partialViewConfig.directEventTypes),
    validAttributes: composeIndexers(_PlatformBaseViewConfig.default.validAttributes, partialViewConfig.validAttributes)
  };
}
function composeIndexers(maybeA, maybeB) {
  var _ref;
  return maybeA == null || maybeB == null ? (_ref = maybeA != null ? maybeA : maybeB) != null ? _ref : {} : Object.assign({}, maybeA, maybeB);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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