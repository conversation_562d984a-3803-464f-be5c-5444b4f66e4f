/**
 * Image Optimization Utilities
 *
 * Comprehensive utilities for image optimization, lazy loading,
 * and asset management in React Native applications.
 *
 * Features:
 * - Image lazy loading
 * - Format optimization
 * - Size optimization
 * - Caching strategies
 * - Progressive loading
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Dimensions, Platform } from 'react-native';

// Image optimization configuration
export interface ImageOptimizationConfig {
  enableLazyLoading: boolean;
  enableProgressiveLoading: boolean;
  enableCaching: boolean;
  maxCacheSize: number; // MB
  compressionQuality: number; // 0-1
  enableWebP: boolean;
  enableAVIF: boolean;
  resizeThreshold: number; // pixels
}

// Image metadata
export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number; // bytes
  aspectRatio: number;
  isOptimized: boolean;
  cacheKey?: string;
}

// Optimized image result
export interface OptimizedImage {
  uri: string;
  metadata: ImageMetadata;
  placeholder?: string;
  blurHash?: string;
  loadTime?: number;
}

// Image loading state
export interface ImageLoadingState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  progress: number;
  error?: Error;
}

// Default configuration
const DEFAULT_CONFIG: ImageOptimizationConfig = {
  enableLazyLoading: true,
  enableProgressiveLoading: true,
  enableCaching: true,
  maxCacheSize: 100, // 100MB
  compressionQuality: 0.8,
  enableWebP: Platform.OS === 'web',
  enableAVIF: Platform.OS === 'web',
  resizeThreshold: 2048,
};

// Image cache
const imageCache = new Map<string, OptimizedImage>();
const cacheMetrics = {
  hits: 0,
  misses: 0,
  size: 0, // bytes
};

// Device screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');
const screenDensity = Platform.OS === 'ios' ? 2 : 3; // Approximate device density

/**
 * Generate optimized image URL with parameters
 */
export const generateOptimizedImageUrl = (
  originalUrl: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png';
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  } = {}
): string => {
  if (!originalUrl || originalUrl.startsWith('data:')) {
    return originalUrl;
  }

  // For local images, return as-is
  if (!originalUrl.startsWith('http')) {
    return originalUrl;
  }

  const {
    width,
    height,
    quality = DEFAULT_CONFIG.compressionQuality,
    format,
    fit = 'cover',
  } = options;

  // Build optimization parameters
  const params = new URLSearchParams();
  
  if (width) params.append('w', width.toString());
  if (height) params.append('h', height.toString());
  if (quality < 1) params.append('q', Math.round(quality * 100).toString());
  if (format) params.append('f', format);
  if (fit) params.append('fit', fit);

  // Add parameters to URL
  const separator = originalUrl.includes('?') ? '&' : '?';
  return `${originalUrl}${separator}${params.toString()}`;
};

/**
 * Calculate optimal image dimensions for screen
 */
export const calculateOptimalDimensions = (
  originalWidth: number,
  originalHeight: number,
  containerWidth?: number,
  containerHeight?: number
): { width: number; height: number } => {
  const maxWidth = containerWidth || screenWidth;
  const maxHeight = containerHeight || screenHeight;
  
  // Account for device density
  const targetWidth = maxWidth * screenDensity;
  const targetHeight = maxHeight * screenDensity;
  
  const aspectRatio = originalWidth / originalHeight;
  
  let optimizedWidth = targetWidth;
  let optimizedHeight = targetHeight;
  
  // Maintain aspect ratio
  if (targetWidth / aspectRatio <= targetHeight) {
    optimizedHeight = targetWidth / aspectRatio;
  } else {
    optimizedWidth = targetHeight * aspectRatio;
  }
  
  // Don't upscale beyond original dimensions
  optimizedWidth = Math.min(optimizedWidth, originalWidth);
  optimizedHeight = Math.min(optimizedHeight, originalHeight);
  
  return {
    width: Math.round(optimizedWidth),
    height: Math.round(optimizedHeight),
  };
};

/**
 * Get optimal image format for platform
 */
export const getOptimalImageFormat = (
  originalFormat: string,
  supportsWebP: boolean = DEFAULT_CONFIG.enableWebP,
  supportsAVIF: boolean = DEFAULT_CONFIG.enableAVIF
): string => {
  // For web platforms, prefer modern formats
  if (Platform.OS === 'web') {
    if (supportsAVIF && originalFormat !== 'gif') return 'avif';
    if (supportsWebP && originalFormat !== 'gif') return 'webp';
  }
  
  // For mobile, stick with widely supported formats
  if (originalFormat === 'png' || originalFormat === 'gif') {
    return originalFormat;
  }
  
  return 'jpeg';
};

/**
 * Generate cache key for image
 */
export const generateCacheKey = (
  url: string,
  width?: number,
  height?: number,
  quality?: number
): string => {
  const params = [url, width, height, quality].filter(Boolean).join('|');
  return btoa(params).replace(/[^a-zA-Z0-9]/g, '');
};

/**
 * Check if image is cached
 */
export const isImageCached = (cacheKey: string): boolean => {
  return imageCache.has(cacheKey);
};

/**
 * Get cached image
 */
export const getCachedImage = (cacheKey: string): OptimizedImage | null => {
  const cached = imageCache.get(cacheKey);
  if (cached) {
    cacheMetrics.hits++;
    return cached;
  }
  cacheMetrics.misses++;
  return null;
};

/**
 * Cache optimized image
 */
export const cacheOptimizedImage = (
  cacheKey: string,
  optimizedImage: OptimizedImage
): void => {
  if (!DEFAULT_CONFIG.enableCaching) return;
  
  // Check cache size limit
  const estimatedSize = optimizedImage.metadata.size || 1024; // Fallback to 1KB
  const maxSizeBytes = DEFAULT_CONFIG.maxCacheSize * 1024 * 1024;
  
  if (cacheMetrics.size + estimatedSize > maxSizeBytes) {
    // Clear oldest entries (simple LRU)
    const entries = Array.from(imageCache.entries());
    const toRemove = Math.ceil(entries.length * 0.2); // Remove 20%
    
    for (let i = 0; i < toRemove; i++) {
      const [key, image] = entries[i];
      cacheMetrics.size -= image.metadata.size || 1024;
      imageCache.delete(key);
    }
  }
  
  imageCache.set(cacheKey, optimizedImage);
  cacheMetrics.size += estimatedSize;
};

/**
 * Clear image cache
 */
export const clearImageCache = (): void => {
  imageCache.clear();
  cacheMetrics.hits = 0;
  cacheMetrics.misses = 0;
  cacheMetrics.size = 0;
};

/**
 * Get cache statistics
 */
export const getCacheStatistics = () => {
  const hitRate = cacheMetrics.hits + cacheMetrics.misses > 0 
    ? cacheMetrics.hits / (cacheMetrics.hits + cacheMetrics.misses)
    : 0;
    
  return {
    ...cacheMetrics,
    hitRate,
    entryCount: imageCache.size,
    sizeMB: cacheMetrics.size / (1024 * 1024),
  };
};

/**
 * Optimize image for display
 */
export const optimizeImageForDisplay = async (
  url: string,
  containerWidth?: number,
  containerHeight?: number,
  options: {
    quality?: number;
    format?: string;
    enableCaching?: boolean;
  } = {}
): Promise<OptimizedImage> => {
  const {
    quality = DEFAULT_CONFIG.compressionQuality,
    format,
    enableCaching = DEFAULT_CONFIG.enableCaching,
  } = options;

  // Generate cache key
  const cacheKey = generateCacheKey(url, containerWidth, containerHeight, quality);
  
  // Check cache first
  if (enableCaching) {
    const cached = getCachedImage(cacheKey);
    if (cached) {
      return cached;
    }
  }

  try {
    // For local images, return as-is with metadata
    if (!url.startsWith('http')) {
      const optimizedImage: OptimizedImage = {
        uri: url,
        metadata: {
          width: containerWidth || screenWidth,
          height: containerHeight || screenHeight,
          format: format || 'unknown',
          size: 0,
          aspectRatio: (containerWidth || screenWidth) / (containerHeight || screenHeight),
          isOptimized: false,
        },
      };
      
      if (enableCaching) {
        cacheOptimizedImage(cacheKey, optimizedImage);
      }
      
      return optimizedImage;
    }

    // For remote images, apply optimizations
    const optimalFormat = format || getOptimalImageFormat('jpeg');
    
    // Calculate optimal dimensions if container size is provided
    let optimizedUrl = url;
    let width = containerWidth;
    let height = containerHeight;
    
    if (containerWidth && containerHeight) {
      const optimal = calculateOptimalDimensions(
        containerWidth * 2, // Assume 2x for quality
        containerHeight * 2,
        containerWidth,
        containerHeight
      );
      width = optimal.width;
      height = optimal.height;
    }

    optimizedUrl = generateOptimizedImageUrl(url, {
      width,
      height,
      quality,
      format: optimalFormat as any,
    });

    const optimizedImage: OptimizedImage = {
      uri: optimizedUrl,
      metadata: {
        width: width || screenWidth,
        height: height || screenHeight,
        format: optimalFormat,
        size: 0, // Would be calculated in real implementation
        aspectRatio: (width || screenWidth) / (height || screenHeight),
        isOptimized: true,
        cacheKey,
      },
    };

    if (enableCaching) {
      cacheOptimizedImage(cacheKey, optimizedImage);
    }

    return optimizedImage;
  } catch (error) {
    console.error('Image optimization failed:', error);
    
    // Return original URL as fallback
    const fallbackImage: OptimizedImage = {
      uri: url,
      metadata: {
        width: containerWidth || screenWidth,
        height: containerHeight || screenHeight,
        format: 'unknown',
        size: 0,
        aspectRatio: (containerWidth || screenWidth) / (containerHeight || screenHeight),
        isOptimized: false,
      },
    };
    
    return fallbackImage;
  }
};

/**
 * Generate placeholder image
 */
export const generatePlaceholder = (
  width: number,
  height: number,
  backgroundColor: string = '#F0F0F0'
): string => {
  // Generate a simple colored placeholder
  // In a real implementation, this could generate blur hashes or use a placeholder service
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${backgroundColor}"/>
    </svg>
  `)}`;
};

/**
 * Preload images for better UX
 */
export const preloadImages = async (urls: string[]): Promise<void> => {
  const preloadPromises = urls.map(async (url) => {
    try {
      if (Platform.OS === 'web') {
        return new Promise<void>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = reject;
          img.src = url;
        });
      } else {
        // For React Native, we can use Image.prefetch
        // This would be implemented with actual Image.prefetch in real app
        return Promise.resolve();
      }
    } catch (error) {
      console.warn(`Failed to preload image: ${url}`, error);
    }
  });

  await Promise.allSettled(preloadPromises);
};

/**
 * Get image loading recommendations
 */
export const getImageLoadingRecommendations = (): string[] => {
  const recommendations: string[] = [];
  const stats = getCacheStatistics();
  
  if (stats.hitRate < 0.7) {
    recommendations.push('Consider implementing more aggressive image caching');
  }
  
  if (stats.sizeMB > DEFAULT_CONFIG.maxCacheSize * 0.8) {
    recommendations.push('Image cache is near capacity, consider clearing old entries');
  }
  
  if (stats.entryCount > 100) {
    recommendations.push('Large number of cached images, monitor memory usage');
  }
  
  return recommendations;
};

/**
 * Monitor image loading performance
 */
export const monitorImagePerformance = () => {
  const startTime = Date.now();
  let loadedImages = 0;
  let failedImages = 0;
  
  return {
    recordLoad: () => {
      loadedImages++;
    },
    recordError: () => {
      failedImages++;
    },
    getMetrics: () => {
      const totalTime = Date.now() - startTime;
      const totalImages = loadedImages + failedImages;
      
      return {
        totalImages,
        loadedImages,
        failedImages,
        successRate: totalImages > 0 ? loadedImages / totalImages : 0,
        averageLoadTime: totalImages > 0 ? totalTime / totalImages : 0,
        cacheStats: getCacheStatistics(),
      };
    },
  };
};

export default {
  generateOptimizedImageUrl,
  calculateOptimalDimensions,
  getOptimalImageFormat,
  generateCacheKey,
  isImageCached,
  getCachedImage,
  cacheOptimizedImage,
  clearImageCache,
  getCacheStatistics,
  optimizeImageForDisplay,
  generatePlaceholder,
  preloadImages,
  getImageLoadingRecommendations,
  monitorImagePerformance,
};
