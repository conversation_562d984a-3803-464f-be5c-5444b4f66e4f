4cf63d423775384ce09e0c516b6993a7
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = processFilter;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _processColor = _interopRequireDefault(require("./processColor"));
function processFilter(filter) {
  var result = [];
  if (filter == null) {
    return result;
  }
  if (typeof filter === 'string') {
    filter = filter.replace(/\n/g, ' ');
    var regex = /([\w-]+)\(([^()]*|\([^()]*\)|[^()]*\([^()]*\)[^()]*)\)/g;
    var matches;
    while (matches = regex.exec(filter)) {
      var filterName = matches[1].toLowerCase();
      if (filterName === 'drop-shadow') {
        var dropShadow = parseDropShadow(matches[2]);
        if (dropShadow != null) {
          result.push({
            dropShadow: dropShadow
          });
        } else {
          return [];
        }
      } else {
        var camelizedName = filterName === 'drop-shadow' ? 'dropShadow' : filterName === 'hue-rotate' ? 'hueRotate' : filterName;
        var amount = _getFilterAmount(camelizedName, matches[2]);
        if (amount != null) {
          var filterFunction = {};
          filterFunction[camelizedName] = amount;
          result.push(filterFunction);
        } else {
          return [];
        }
      }
    }
  } else if (Array.isArray(filter)) {
    for (var _filterFunction of filter) {
      var _Object$entries$ = (0, _slicedToArray2.default)(Object.entries(_filterFunction)[0], 2),
        _filterName = _Object$entries$[0],
        filterValue = _Object$entries$[1];
      if (_filterName === 'dropShadow') {
        var _dropShadow = parseDropShadow(filterValue);
        if (_dropShadow == null) {
          return [];
        }
        result.push({
          dropShadow: _dropShadow
        });
      } else {
        var _amount = _getFilterAmount(_filterName, filterValue);
        if (_amount != null) {
          var resultObject = {};
          resultObject[_filterName] = _amount;
          result.push(resultObject);
        } else {
          return [];
        }
      }
    }
  } else {
    throw new TypeError(`${typeof filter} filter is not a string or array`);
  }
  return result;
}
function _getFilterAmount(filterName, filterArgs) {
  var filterArgAsNumber;
  var unit;
  if (typeof filterArgs === 'string') {
    var argsWithUnitsRegex = new RegExp(/([+-]?\d*(\.\d+)?)([a-zA-Z%]+)?/g);
    var match = argsWithUnitsRegex.exec(filterArgs);
    if (!match || isNaN(Number(match[1]))) {
      return undefined;
    }
    filterArgAsNumber = Number(match[1]);
    unit = match[3];
  } else if (typeof filterArgs === 'number') {
    filterArgAsNumber = filterArgs;
  } else {
    return undefined;
  }
  switch (filterName) {
    case 'hueRotate':
      if (filterArgAsNumber === 0) {
        return 0;
      }
      if (unit !== 'deg' && unit !== 'rad') {
        return undefined;
      }
      return unit === 'rad' ? 180 * filterArgAsNumber / Math.PI : filterArgAsNumber;
    case 'blur':
      if (unit && unit !== 'px' || filterArgAsNumber < 0) {
        return undefined;
      }
      return filterArgAsNumber;
    case 'brightness':
    case 'contrast':
    case 'grayscale':
    case 'invert':
    case 'opacity':
    case 'saturate':
    case 'sepia':
      if (unit && unit !== '%' && unit !== 'px' || filterArgAsNumber < 0) {
        return undefined;
      }
      if (unit === '%') {
        filterArgAsNumber /= 100;
      }
      return filterArgAsNumber;
    default:
      return undefined;
  }
}
function parseDropShadow(rawDropShadow) {
  var dropShadow = typeof rawDropShadow === 'string' ? parseDropShadowString(rawDropShadow) : rawDropShadow;
  var parsedDropShadow = {
    offsetX: 0,
    offsetY: 0
  };
  var offsetX;
  var offsetY;
  for (var arg in dropShadow) {
    var value = void 0;
    switch (arg) {
      case 'offsetX':
        value = typeof dropShadow.offsetX === 'string' ? parseLength(dropShadow.offsetX) : dropShadow.offsetX;
        if (value == null) {
          return null;
        }
        offsetX = value;
        break;
      case 'offsetY':
        value = typeof dropShadow.offsetY === 'string' ? parseLength(dropShadow.offsetY) : dropShadow.offsetY;
        if (value == null) {
          return null;
        }
        offsetY = value;
        break;
      case 'standardDeviation':
        value = typeof dropShadow.standardDeviation === 'string' ? parseLength(dropShadow.standardDeviation) : dropShadow.standardDeviation;
        if (value == null || value < 0) {
          return null;
        }
        parsedDropShadow.standardDeviation = value;
        break;
      case 'color':
        var color = (0, _processColor.default)(dropShadow.color);
        if (color == null) {
          return null;
        }
        parsedDropShadow.color = color;
        break;
      default:
        return null;
    }
  }
  if (offsetX == null || offsetY == null) {
    return null;
  }
  parsedDropShadow.offsetX = offsetX;
  parsedDropShadow.offsetY = offsetY;
  return parsedDropShadow;
}
function parseDropShadowString(rawDropShadow) {
  var dropShadow = {
    offsetX: 0,
    offsetY: 0
  };
  var offsetX;
  var offsetY;
  var lengthCount = 0;
  var keywordDetectedAfterLength = false;
  for (var arg of rawDropShadow.split(/\s+(?![^(]*\))/)) {
    var processedColor = (0, _processColor.default)(arg);
    if (processedColor != null) {
      if (dropShadow.color != null) {
        return null;
      }
      if (offsetX != null) {
        keywordDetectedAfterLength = true;
      }
      dropShadow.color = arg;
      continue;
    }
    switch (lengthCount) {
      case 0:
        offsetX = arg;
        lengthCount++;
        break;
      case 1:
        if (keywordDetectedAfterLength) {
          return null;
        }
        offsetY = arg;
        lengthCount++;
        break;
      case 2:
        if (keywordDetectedAfterLength) {
          return null;
        }
        dropShadow.standardDeviation = arg;
        lengthCount++;
        break;
      default:
        return null;
    }
  }
  if (offsetX == null || offsetY == null) {
    return null;
  }
  dropShadow.offsetX = offsetX;
  dropShadow.offsetY = offsetY;
  return dropShadow;
}
function parseLength(length) {
  var argsWithUnitsRegex = /([+-]?\d*(\.\d+)?)([\w\W]+)?/g;
  var match = argsWithUnitsRegex.exec(length);
  if (!match || Number.isNaN(match[1])) {
    return null;
  }
  if (match[3] != null && match[3] !== 'px') {
    return null;
  }
  if (match[3] == null && match[1] !== '0') {
    return null;
  }
  return Number(match[1]);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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