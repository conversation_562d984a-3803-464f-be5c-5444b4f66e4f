/**
 * Cognitive Accessibility Context Provider
 *
 * Provides cognitive accessibility features and adaptations for users with
 * cognitive disabilities, following WCAG 2.2 AA guidelines.
 *
 * Features:
 * - Content simplification
 * - Reading assistance
 * - Memory aids
 * - Cognitive load reduction
 * - Clear communication patterns
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Platform, AccessibilityInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  CognitiveLoadLevel,
  MemoryAidType,
  simplifyText,
  analyzeContentComplexity,
  generateMemoryAids,
} from '../utils/cognitiveAccessibilityUtils';

// Cognitive accessibility settings
export interface CognitiveAccessibilitySettings {
  simplifiedLanguage: boolean;
  reducedAnimations: boolean;
  enhancedFocus: boolean;
  memoryAids: boolean;
  progressIndicators: boolean;
  confirmationDialogs: boolean;
  readingAssistance: boolean;
  cognitiveLoadLevel: CognitiveLoadLevel;
  preferredMemoryAidType: MemoryAidType;
  autoSave: boolean;
  extendedTimeouts: boolean;
  consistentNavigation: boolean;
}

// Context interface
interface CognitiveAccessibilityContextType {
  // Settings
  settings: CognitiveAccessibilitySettings;
  
  // Actions
  updateSettings: (newSettings: Partial<CognitiveAccessibilitySettings>) => void;
  toggleSimplifiedLanguage: () => void;
  toggleMemoryAids: () => void;
  setCognitiveLoadLevel: (level: CognitiveLoadLevel) => void;
  resetToDefaults: () => void;
  
  // Content processing
  processText: (text: string) => string;
  generateContentMemoryAids: (content: string, type?: MemoryAidType) => string[];
  analyzeContent: (content: string) => any;
  
  // UI helpers
  shouldShowProgressIndicator: () => boolean;
  shouldShowConfirmation: (action: string) => boolean;
  getTimeoutDuration: (baseTimeout: number) => number;
  
  // Memory aids
  addMemoryAid: (key: string, content: string) => void;
  getMemoryAid: (key: string) => string | null;
  clearMemoryAids: () => void;
}

// Default settings
const DEFAULT_SETTINGS: CognitiveAccessibilitySettings = {
  simplifiedLanguage: false,
  reducedAnimations: false,
  enhancedFocus: false,
  memoryAids: false,
  progressIndicators: true,
  confirmationDialogs: true,
  readingAssistance: false,
  cognitiveLoadLevel: 'medium',
  preferredMemoryAidType: 'textual',
  autoSave: true,
  extendedTimeouts: false,
  consistentNavigation: true,
};

// Create context
const CognitiveAccessibilityContext = createContext<CognitiveAccessibilityContextType | undefined>(undefined);

// Storage keys
const COGNITIVE_ACCESSIBILITY_STORAGE_KEY = '@vierla_cognitive_accessibility_settings';
const MEMORY_AIDS_STORAGE_KEY = '@vierla_memory_aids';

// Provider props
interface CognitiveAccessibilityProviderProps {
  children: React.ReactNode;
}

export const CognitiveAccessibilityProvider: React.FC<CognitiveAccessibilityProviderProps> = ({ children }) => {
  // State
  const [settings, setSettings] = useState<CognitiveAccessibilitySettings>(DEFAULT_SETTINGS);
  const [memoryAids, setMemoryAids] = useState<Record<string, string>>({});

  // Load saved settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const [savedSettings, savedMemoryAids] = await Promise.all([
          AsyncStorage.getItem(COGNITIVE_ACCESSIBILITY_STORAGE_KEY),
          AsyncStorage.getItem(MEMORY_AIDS_STORAGE_KEY),
        ]);

        if (savedSettings) {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...DEFAULT_SETTINGS, ...parsed });
        } else {
          // Check system accessibility preferences
          await detectSystemPreferences();
        }

        if (savedMemoryAids) {
          setMemoryAids(JSON.parse(savedMemoryAids));
        }
      } catch (error) {
        console.warn('Failed to load cognitive accessibility settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Detect system accessibility preferences
  const detectSystemPreferences = useCallback(async () => {
    try {
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        const isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled?.();
        const isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();

        if (isReduceMotionEnabled || isScreenReaderEnabled) {
          setSettings(prev => ({
            ...prev,
            reducedAnimations: isReduceMotionEnabled || false,
            readingAssistance: isScreenReaderEnabled,
            enhancedFocus: true,
            memoryAids: true,
            extendedTimeouts: true,
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to detect system accessibility preferences:', error);
    }
  }, []);

  // Save settings
  const saveSettings = useCallback(async (newSettings: CognitiveAccessibilitySettings) => {
    try {
      await AsyncStorage.setItem(COGNITIVE_ACCESSIBILITY_STORAGE_KEY, JSON.stringify(newSettings));
    } catch (error) {
      console.warn('Failed to save cognitive accessibility settings:', error);
    }
  }, []);

  // Save memory aids
  const saveMemoryAids = useCallback(async (aids: Record<string, string>) => {
    try {
      await AsyncStorage.setItem(MEMORY_AIDS_STORAGE_KEY, JSON.stringify(aids));
    } catch (error) {
      console.warn('Failed to save memory aids:', error);
    }
  }, []);

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<CognitiveAccessibilitySettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      saveSettings(updated);
      return updated;
    });
  }, [saveSettings]);

  // Toggle simplified language
  const toggleSimplifiedLanguage = useCallback(() => {
    updateSettings({ simplifiedLanguage: !settings.simplifiedLanguage });

    // Announce change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        settings.simplifiedLanguage ? 'Simplified language disabled' : 'Simplified language enabled'
      );
    }
  }, [settings.simplifiedLanguage, updateSettings]);

  // Toggle memory aids
  const toggleMemoryAids = useCallback(() => {
    updateSettings({ memoryAids: !settings.memoryAids });

    // Announce change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        settings.memoryAids ? 'Memory aids disabled' : 'Memory aids enabled'
      );
    }
  }, [settings.memoryAids, updateSettings]);

  // Set cognitive load level
  const setCognitiveLoadLevel = useCallback((level: CognitiveLoadLevel) => {
    updateSettings({ cognitiveLoadLevel: level });

    // Adjust other settings based on cognitive load level
    const adjustments: Partial<CognitiveAccessibilitySettings> = {};
    
    switch (level) {
      case 'low':
        adjustments.simplifiedLanguage = false;
        adjustments.memoryAids = false;
        adjustments.extendedTimeouts = false;
        break;
      case 'medium':
        adjustments.simplifiedLanguage = false;
        adjustments.memoryAids = true;
        adjustments.extendedTimeouts = false;
        break;
      case 'high':
        adjustments.simplifiedLanguage = true;
        adjustments.memoryAids = true;
        adjustments.extendedTimeouts = true;
        adjustments.enhancedFocus = true;
        break;
      case 'very-high':
        adjustments.simplifiedLanguage = true;
        adjustments.memoryAids = true;
        adjustments.extendedTimeouts = true;
        adjustments.enhancedFocus = true;
        adjustments.reducedAnimations = true;
        adjustments.progressIndicators = true;
        break;
    }

    updateSettings(adjustments);
  }, [updateSettings]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    saveSettings(DEFAULT_SETTINGS);
  }, [saveSettings]);

  // Process text based on settings
  const processText = useCallback((text: string): string => {
    if (!settings.simplifiedLanguage) {
      return text;
    }

    return simplifyText(text);
  }, [settings.simplifiedLanguage]);

  // Generate content memory aids
  const generateContentMemoryAids = useCallback((content: string, type?: MemoryAidType): string[] => {
    if (!settings.memoryAids) {
      return [];
    }

    return generateMemoryAids(content, type || settings.preferredMemoryAidType);
  }, [settings.memoryAids, settings.preferredMemoryAidType]);

  // Analyze content
  const analyzeContent = useCallback((content: string) => {
    return analyzeContentComplexity(content);
  }, []);

  // UI helpers
  const shouldShowProgressIndicator = useCallback(() => {
    return settings.progressIndicators;
  }, [settings.progressIndicators]);

  const shouldShowConfirmation = useCallback((action: string) => {
    if (!settings.confirmationDialogs) {
      return false;
    }

    // Always show confirmation for destructive actions
    const destructiveActions = ['delete', 'remove', 'cancel', 'logout', 'reset'];
    return destructiveActions.some(destructive => action.toLowerCase().includes(destructive));
  }, [settings.confirmationDialogs]);

  const getTimeoutDuration = useCallback((baseTimeout: number) => {
    if (!settings.extendedTimeouts) {
      return baseTimeout;
    }

    // Extend timeouts based on cognitive load level
    const multiplier = settings.cognitiveLoadLevel === 'very-high' ? 3 :
                     settings.cognitiveLoadLevel === 'high' ? 2.5 :
                     settings.cognitiveLoadLevel === 'medium' ? 2 : 1.5;

    return Math.round(baseTimeout * multiplier);
  }, [settings.extendedTimeouts, settings.cognitiveLoadLevel]);

  // Memory aid management
  const addMemoryAid = useCallback((key: string, content: string) => {
    setMemoryAids(prev => {
      const updated = { ...prev, [key]: content };
      saveMemoryAids(updated);
      return updated;
    });
  }, [saveMemoryAids]);

  const getMemoryAid = useCallback((key: string): string | null => {
    return memoryAids[key] || null;
  }, [memoryAids]);

  const clearMemoryAids = useCallback(() => {
    setMemoryAids({});
    saveMemoryAids({});
  }, [saveMemoryAids]);

  // Context value
  const contextValue: CognitiveAccessibilityContextType = {
    // Settings
    settings,
    
    // Actions
    updateSettings,
    toggleSimplifiedLanguage,
    toggleMemoryAids,
    setCognitiveLoadLevel,
    resetToDefaults,
    
    // Content processing
    processText,
    generateContentMemoryAids,
    analyzeContent,
    
    // UI helpers
    shouldShowProgressIndicator,
    shouldShowConfirmation,
    getTimeoutDuration,
    
    // Memory aids
    addMemoryAid,
    getMemoryAid,
    clearMemoryAids,
  };

  return (
    <CognitiveAccessibilityContext.Provider value={contextValue}>
      {children}
    </CognitiveAccessibilityContext.Provider>
  );
};

// Hook to use cognitive accessibility context
export const useCognitiveAccessibility = (): CognitiveAccessibilityContextType => {
  const context = useContext(CognitiveAccessibilityContext);
  
  if (context === undefined) {
    throw new Error('useCognitiveAccessibility must be used within a CognitiveAccessibilityProvider');
  }
  
  return context;
};

// Convenience hooks
export const useSimplifiedText = (text: string) => {
  const { processText } = useCognitiveAccessibility();
  return processText(text);
};

export const useMemoryAids = (content: string) => {
  const { generateContentMemoryAids, settings } = useCognitiveAccessibility();
  return settings.memoryAids ? generateContentMemoryAids(content) : [];
};

export const useCognitiveTimeout = (baseTimeout: number) => {
  const { getTimeoutDuration } = useCognitiveAccessibility();
  return getTimeoutDuration(baseTimeout);
};

export default CognitiveAccessibilityProvider;
