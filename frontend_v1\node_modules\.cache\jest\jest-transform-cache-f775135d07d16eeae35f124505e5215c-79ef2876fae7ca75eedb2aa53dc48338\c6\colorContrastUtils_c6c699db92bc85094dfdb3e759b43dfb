ab961f8d50cb93383e19790cbe05bb58
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateColorPalette = exports.simulateColorBlindness = exports.rgbToHex = exports.hexToRgb = exports.getRelativeLuminance = exports.getHighContrastColor = exports.getContrastRatio = exports.generateAccessibleColors = exports.generateAccessibilityReport = exports.default = exports.checkContrastCompliance = exports.adjustColorForContrast = exports.WCAG_THRESHOLDS = void 0;
var WCAG_THRESHOLDS = exports.WCAG_THRESHOLDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5
};
var hexToRgb = exports.hexToRgb = function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};
var rgbToHex = exports.rgbToHex = function rgbToHex(r, g, b) {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
};
var getRelativeLuminance = exports.getRelativeLuminance = function getRelativeLuminance(color) {
  var r = color.r,
    g = color.g,
    b = color.b;
  var rsRGB = r / 255;
  var gsRGB = g / 255;
  var bsRGB = b / 255;
  var rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  var gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  var bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
};
var getContrastRatio = exports.getContrastRatio = function getContrastRatio(color1, color2) {
  var lum1 = getRelativeLuminance(color1);
  var lum2 = getRelativeLuminance(color2);
  var lighter = Math.max(lum1, lum2);
  var darker = Math.min(lum1, lum2);
  return (lighter + 0.05) / (darker + 0.05);
};
var checkContrastCompliance = exports.checkContrastCompliance = function checkContrastCompliance(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var fgColor = typeof foreground === 'string' ? hexToRgb(foreground) : foreground;
  var bgColor = typeof background === 'string' ? hexToRgb(background) : background;
  if (!fgColor || !bgColor) {
    throw new Error('Invalid color format');
  }
  var ratio = getContrastRatio(fgColor, bgColor);
  var wcagAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL);
  var wcagAAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AAA_LARGE : WCAG_THRESHOLDS.AAA_NORMAL);
  var wcagAALarge = ratio >= WCAG_THRESHOLDS.AA_LARGE;
  var wcagAAALarge = ratio >= WCAG_THRESHOLDS.AAA_LARGE;
  var level = 'fail';
  if (wcagAAA) {
    level = 'aaa';
  } else if (wcagAA) {
    level = 'aa';
  }
  var recommendation;
  if (!wcagAA) {
    var targetRatio = isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL;
    recommendation = `Increase contrast ratio to at least ${targetRatio}:1 for WCAG AA compliance`;
  }
  return {
    ratio: Math.round(ratio * 100) / 100,
    wcagAA: wcagAA,
    wcagAAA: wcagAAA,
    wcagAALarge: wcagAALarge,
    wcagAAALarge: wcagAAALarge,
    level: level,
    recommendation: recommendation
  };
};
var generateAccessibleColors = exports.generateAccessibleColors = function generateAccessibleColors(baseColor) {
  var base = hexToRgb(baseColor);
  if (!base) throw new Error('Invalid base color');
  return {
    primary: baseColor,
    secondary: adjustColorForContrast(baseColor, '#FFFFFF', 3.0),
    text: '#000000',
    textSecondary: '#666666',
    background: '#FFFFFF',
    surface: '#F8F9FA'
  };
};
var adjustColorForContrast = exports.adjustColorForContrast = function adjustColorForContrast(color, background, minRatio) {
  var colorRgb = hexToRgb(color);
  var bgRgb = hexToRgb(background);
  if (!colorRgb || !bgRgb) throw new Error('Invalid color format');
  var adjustedColor = Object.assign({}, colorRgb);
  var currentRatio = getContrastRatio(adjustedColor, bgRgb);
  if (currentRatio >= minRatio) {
    return color;
  }
  var bgLuminance = getRelativeLuminance(bgRgb);
  var shouldDarken = bgLuminance > 0.5;
  var step = shouldDarken ? -5 : 5;
  var iterations = 0;
  var maxIterations = 50;
  while (currentRatio < minRatio && iterations < maxIterations) {
    adjustedColor.r = Math.max(0, Math.min(255, adjustedColor.r + step));
    adjustedColor.g = Math.max(0, Math.min(255, adjustedColor.g + step));
    adjustedColor.b = Math.max(0, Math.min(255, adjustedColor.b + step));
    currentRatio = getContrastRatio(adjustedColor, bgRgb);
    iterations++;
  }
  return rgbToHex(adjustedColor.r, adjustedColor.g, adjustedColor.b);
};
var getHighContrastColor = exports.getHighContrastColor = function getHighContrastColor(color) {
  var background = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '#FFFFFF';
  var colorRgb = hexToRgb(color);
  var bgRgb = hexToRgb(background);
  if (!colorRgb || !bgRgb) return color;
  var bgLuminance = getRelativeLuminance(bgRgb);
  return bgLuminance > 0.5 ? '#000000' : '#FFFFFF';
};
var simulateColorBlindness = exports.simulateColorBlindness = function simulateColorBlindness(color, type) {
  var rgb = hexToRgb(color);
  if (!rgb) return color;
  var r = rgb.r,
    g = rgb.g,
    b = rgb.b;
  switch (type) {
    case 'protanopia':
      r = 0.567 * r + 0.433 * g;
      g = 0.558 * r + 0.442 * g;
      b = 0.242 * g + 0.758 * b;
      break;
    case 'deuteranopia':
      r = 0.625 * r + 0.375 * g;
      g = 0.7 * r + 0.3 * g;
      b = 0.3 * g + 0.7 * b;
      break;
    case 'tritanopia':
      r = 0.95 * r + 0.05 * g;
      g = 0.433 * g + 0.567 * b;
      b = 0.475 * g + 0.525 * b;
      break;
  }
  return rgbToHex(Math.round(Math.max(0, Math.min(255, r))), Math.round(Math.max(0, Math.min(255, g))), Math.round(Math.max(0, Math.min(255, b))));
};
var validateColorPalette = exports.validateColorPalette = function validateColorPalette(palette) {
  var issues = [];
  var recommendations = [];
  var combinations = [{
    fg: 'text',
    bg: 'background',
    name: 'Primary text on background'
  }, {
    fg: 'textSecondary',
    bg: 'background',
    name: 'Secondary text on background'
  }, {
    fg: 'primary',
    bg: 'background',
    name: 'Primary color on background'
  }, {
    fg: 'background',
    bg: 'primary',
    name: 'Background on primary'
  }];
  for (var combo of combinations) {
    if (palette[combo.fg] && palette[combo.bg]) {
      try {
        var result = checkContrastCompliance(palette[combo.fg], palette[combo.bg]);
        if (!result.wcagAA) {
          issues.push(`${combo.name}: Contrast ratio ${result.ratio}:1 fails WCAG AA`);
          if (result.recommendation) {
            recommendations.push(`${combo.name}: ${result.recommendation}`);
          }
        }
      } catch (error) {
        issues.push(`${combo.name}: Invalid color format`);
      }
    }
  }
  return {
    valid: issues.length === 0,
    issues: issues,
    recommendations: recommendations
  };
};
var generateAccessibilityReport = exports.generateAccessibilityReport = function generateAccessibilityReport(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  try {
    var result = checkContrastCompliance(foreground, background, isLargeText);
    var report = `Color Accessibility Report\n`;
    report += `========================\n`;
    report += `Foreground: ${foreground}\n`;
    report += `Background: ${background}\n`;
    report += `Text Size: ${isLargeText ? 'Large (18pt+)' : 'Normal'}\n`;
    report += `Contrast Ratio: ${result.ratio}:1\n\n`;
    report += `WCAG Compliance:\n`;
    report += `- AA Normal: ${result.wcagAA ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AA Large: ${result.wcagAALarge ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AAA Normal: ${result.wcagAAA ? '✓ Pass' : '✗ Fail'}\n`;
    report += `- AAA Large: ${result.wcagAAALarge ? '✓ Pass' : '✗ Fail'}\n\n`;
    if (result.recommendation) {
      report += `Recommendation: ${result.recommendation}\n`;
    }
    return report;
  } catch (error) {
    return `Error generating report: ${error}`;
  }
};
var _default = exports.default = {
  hexToRgb: hexToRgb,
  rgbToHex: rgbToHex,
  getRelativeLuminance: getRelativeLuminance,
  getContrastRatio: getContrastRatio,
  checkContrastCompliance: checkContrastCompliance,
  generateAccessibleColors: generateAccessibleColors,
  adjustColorForContrast: adjustColorForContrast,
  getHighContrastColor: getHighContrastColor,
  simulateColorBlindness: simulateColorBlindness,
  validateColorPalette: validateColorPalette,
  generateAccessibilityReport: generateAccessibilityReport,
  WCAG_THRESHOLDS: WCAG_THRESHOLDS
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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