2a32dfcfa3467b27cf7551389c690198
_getJestObj().mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
_getJestObj().mock('react-native/Libraries/Animated/NativeAnimatedHelper');
_getJestObj().mock('react-native/Libraries/Utilities/Dimensions', function () {
  return {
    get: jest.fn(function () {
      return {
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1
      };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Platform', function () {
  return {
    OS: 'ios',
    Version: '14.0',
    select: jest.fn(function (obj) {
      return obj.ios;
    })
  };
});
_getJestObj().mock('expo-status-bar', function () {
  return {
    StatusBar: 'StatusBar',
    setStatusBarStyle: jest.fn(),
    setStatusBarBackgroundColor: jest.fn()
  };
});
_getJestObj().mock('expo-constants', function () {
  return {
    default: {
      statusBarHeight: 44,
      deviceName: 'iPhone',
      platform: {
        ios: {
          platform: 'ios'
        }
      },
      appOwnership: 'standalone',
      expoVersion: '45.0.0'
    }
  };
});
_getJestObj().mock('expo-font', function () {
  return {
    loadAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    isLoaded: jest.fn(function () {
      return true;
    }),
    isLoading: jest.fn(function () {
      return false;
    })
  };
});
_getJestObj().mock('expo-linear-gradient', function () {
  return {
    LinearGradient: 'LinearGradient'
  };
});
_getJestObj().mock('expo-haptics', function () {
  return {
    impactAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    notificationAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    selectionAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    ImpactFeedbackStyle: {
      Light: 'light',
      Medium: 'medium',
      Heavy: 'heavy'
    },
    NotificationFeedbackType: {
      Success: 'success',
      Warning: 'warning',
      Error: 'error'
    }
  };
});
_getJestObj().mock('expo-crypto', function () {
  return {
    randomUUID: jest.fn(function () {
      return 'mock-uuid-1234-5678-9012';
    }),
    digestStringAsync: jest.fn(function () {
      return Promise.resolve('mock-hash');
    })
  };
});
_getJestObj().mock('expo-location', function () {
  return {
    requestForegroundPermissionsAsync: jest.fn(function () {
      return Promise.resolve({
        status: 'granted'
      });
    }),
    getCurrentPositionAsync: jest.fn(function () {
      return Promise.resolve({
        coords: {
          latitude: 43.6532,
          longitude: -79.3832,
          accuracy: 10,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          speed: 0
        },
        timestamp: Date.now()
      });
    }),
    watchPositionAsync: jest.fn(function () {
      return Promise.resolve({
        remove: jest.fn()
      });
    })
  };
});
_getJestObj().mock('expo-local-authentication', function () {
  return {
    hasHardwareAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    supportedAuthenticationTypesAsync: jest.fn(function () {
      return Promise.resolve([1, 2]);
    }),
    isEnrolledAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    authenticateAsync: jest.fn(function () {
      return Promise.resolve({
        success: true,
        error: undefined
      });
    }),
    AuthenticationType: {
      FINGERPRINT: 1,
      FACIAL_RECOGNITION: 2
    }
  };
});
_getJestObj().mock('@react-navigation/native', function () {
  return {
    useNavigation: function useNavigation() {
      return {
        navigate: jest.fn(),
        goBack: jest.fn(),
        reset: jest.fn(),
        setParams: jest.fn(),
        dispatch: jest.fn(),
        isFocused: jest.fn(function () {
          return true;
        }),
        canGoBack: jest.fn(function () {
          return true;
        }),
        getId: jest.fn(function () {
          return 'mock-route-id';
        }),
        getParent: jest.fn(),
        getState: jest.fn(function () {
          return {
            index: 0,
            routes: [{
              name: 'Home',
              key: 'home-key'
            }]
          };
        })
      };
    },
    useRoute: function useRoute() {
      return {
        key: 'mock-route-key',
        name: 'MockScreen',
        params: {}
      };
    },
    useFocusEffect: jest.fn(),
    useIsFocused: jest.fn(function () {
      return true;
    }),
    NavigationContainer: function NavigationContainer(_ref) {
      var children = _ref.children;
      return children;
    },
    createNavigationContainerRef: jest.fn(function () {
      return {
        current: {
          navigate: jest.fn(),
          reset: jest.fn(),
          goBack: jest.fn()
        }
      };
    })
  };
});
_getJestObj().mock('@react-navigation/stack', function () {
  return {
    createStackNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref2) {
          var children = _ref2.children;
          return children;
        },
        Screen: function Screen(_ref3) {
          var children = _ref3.children;
          return children;
        }
      };
    }),
    CardStyleInterpolators: {
      forHorizontalIOS: {},
      forVerticalIOS: {},
      forModalPresentationIOS: {}
    },
    TransitionPresets: {
      SlideFromRightIOS: {},
      ModalSlideFromBottomIOS: {}
    }
  };
});
_getJestObj().mock('@react-navigation/bottom-tabs', function () {
  return {
    createBottomTabNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref4) {
          var children = _ref4.children;
          return children;
        },
        Screen: function Screen(_ref5) {
          var children = _ref5.children;
          return children;
        }
      };
    })
  };
});
Object.defineProperty(exports, "__esModule", {
  value: true
});
require("react-native-gesture-handler/jestSetup");
require("@testing-library/jest-native/extend-expect");
var _testingUtils = require("../utils/testingUtils");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
(0, _testingUtils.setupTestEnvironment)({
  enableAccessibilityTesting: true,
  enablePerformanceTesting: true,
  mockNetworkRequests: true,
  mockLocationServices: true,
  mockNotifications: true,
  logLevel: 'warn'
});
try {
  _getJestObj().mock('react-i18next', function () {
    return {
      useTranslation: function useTranslation() {
        return {
          t: function t(key, options) {
            if (options && typeof options === 'object') {
              var result = key;
              Object.keys(options).forEach(function (optionKey) {
                result = result.replace(`{{${optionKey}}}`, options[optionKey]);
              });
              return result;
            }
            return key;
          },
          i18n: {
            language: 'en',
            changeLanguage: jest.fn(function () {
              return Promise.resolve();
            })
          }
        };
      },
      initReactI18next: {
        type: '3rdParty',
        init: jest.fn()
      }
    };
  });
  require.resolve('react-i18next');
} catch (e) {}
try {
  _getJestObj().mock('zustand', function () {
    return {
      create: jest.fn(function (fn) {
        var store = fn(function () {
          return {};
        }, function () {
          return {};
        });
        return function () {
          return store;
        };
      })
    };
  });
  require.resolve('zustand');
} catch (e) {}
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
afterEach(function () {
  jest.clearAllMocks();
});
process.on('unhandledRejection', function (reason, promise) {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
jest.setTimeout(10000);
beforeEach(function () {
  jest.useFakeTimers();
});
afterEach(function () {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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