{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_objectWithoutProperties2", "_StyleSheet", "_Platform", "_View", "React", "_interopRequireWildcard", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "has", "get", "set", "_t", "hasOwnProperty", "call", "getOwnPropertyDescriptor", "PlatformActivityIndicator", "Platform", "OS", "GRAY", "ActivityIndicator", "_ref", "forwardedRef", "_ref$animating", "animating", "_ref$color", "color", "_ref$hidesWhenStopped", "hidesWhenStopped", "onLayout", "_ref$size", "size", "style", "restProps", "sizeStyle", "sizeProp", "styles", "sizeSmall", "sizeLarge", "height", "width", "nativeProps", "assign", "ref", "androidProps", "styleAttr", "indeterminate", "jsx", "StyleSheet", "compose", "container", "children", "ActivityIndicatorWithRef", "forwardRef", "displayName", "create", "alignItems", "justifyContent", "_default"], "sources": ["ActivityIndicator.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\nimport type {HostComponent} from '../../../src/private/types/HostComponent';\nimport type {ViewProps} from '../View/ViewPropTypes';\n\nimport StyleSheet, {type ColorValue} from '../../StyleSheet/StyleSheet';\nimport Platform from '../../Utilities/Platform';\nimport View from '../View/View';\nimport * as React from 'react';\n\nconst PlatformActivityIndicator =\n  Platform.OS === 'android'\n    ? require('../ProgressBarAndroid/ProgressBarAndroid').default\n    : require('./ActivityIndicatorViewNativeComponent').default;\n\nconst GRAY = '#999999';\n\ntype IndicatorSize = number | 'small' | 'large';\n\ntype ActivityIndicatorIOSProps = $ReadOnly<{\n  /**\n    Whether the indicator should hide when not animating.\n\n    @platform ios\n  */\n  hidesWhenStopped?: ?boolean,\n}>;\nexport type ActivityIndicatorProps = $ReadOnly<{\n  ...ViewProps,\n  ...ActivityIndicatorIOSProps,\n\n  /**\n   \tWhether to show the indicator (`true`) or hide it (`false`).\n   */\n  animating?: ?boolean,\n\n  /**\n    The foreground color of the spinner.\n\n    @default {@platform android} `null` (system accent default color)\n    @default {@platform ios} '#999999'\n  */\n  color?: ?ColorValue,\n\n  /**\n    Size of the indicator.\n\n    @type enum(`'small'`, `'large'`)\n    @type {@platform android} number\n  */\n  size?: ?IndicatorSize,\n}>;\n\nconst ActivityIndicator = (\n  {\n    animating = true,\n    color = Platform.OS === 'ios' ? GRAY : null,\n    hidesWhenStopped = true,\n    onLayout,\n    size = 'small',\n    style,\n    ...restProps\n  }: ActivityIndicatorProps,\n  forwardedRef?: any,\n) => {\n  let sizeStyle;\n  let sizeProp;\n\n  switch (size) {\n    case 'small':\n      sizeStyle = styles.sizeSmall;\n      sizeProp = 'small';\n      break;\n    case 'large':\n      sizeStyle = styles.sizeLarge;\n      sizeProp = 'large';\n      break;\n    default:\n      sizeStyle = {height: size, width: size};\n      break;\n  }\n\n  const nativeProps = {\n    animating,\n    color,\n    hidesWhenStopped,\n    ...restProps,\n    ref: forwardedRef,\n    style: sizeStyle,\n    size: sizeProp,\n  };\n\n  const androidProps = {\n    styleAttr: 'Normal',\n    indeterminate: true,\n  };\n\n  return (\n    <View\n      onLayout={onLayout}\n      style={StyleSheet.compose(styles.container, style)}>\n      {Platform.OS === 'android' ? (\n        // $FlowFixMe[prop-missing] Flow doesn't know when this is the android component\n        <PlatformActivityIndicator {...nativeProps} {...androidProps} />\n      ) : (\n        /* $FlowFixMe[incompatible-type] (>=0.106.0 site=react_native_android_fb) This comment\n         * suppresses an error found when Flow v0.106 was deployed. To see the\n         * error, delete this comment and run Flow. */\n        <PlatformActivityIndicator {...nativeProps} />\n      )}\n    </View>\n  );\n};\n\n/**\n  Displays a circular loading indicator.\n\n  ```SnackPlayer name=ActivityIndicator%20Example\n  import React from 'react';\n  import {ActivityIndicator, StyleSheet, View} from 'react-native';\n\n  const App = () => (\n    <View style={[styles.container, styles.horizontal]}>\n      <ActivityIndicator />\n      <ActivityIndicator size=\"large\" />\n      <ActivityIndicator size=\"small\" color=\"#0000ff\" />\n      <ActivityIndicator size=\"large\" color=\"#00ff00\" />\n    </View>\n  );\n\n  const styles = StyleSheet.create({\n    container: {\n      flex: 1,\n      justifyContent: 'center',\n    },\n    horizontal: {\n      flexDirection: 'row',\n      justifyContent: 'space-around',\n      padding: 10,\n    },\n  });\n\n  export default App;\n```\n*/\n\nconst ActivityIndicatorWithRef: component(\n  ref?: React.RefSetter<HostComponent<empty>>,\n  ...props: ActivityIndicatorProps\n) = React.forwardRef(ActivityIndicator);\nActivityIndicatorWithRef.displayName = 'ActivityIndicator';\n\nconst styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  sizeSmall: {\n    width: 20,\n    height: 20,\n  },\n  sizeLarge: {\n    width: 36,\n    height: 36,\n  },\n});\n\nexport default ActivityIndicatorWithRef;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,yBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAIb,IAAAO,WAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,SAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,KAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,KAAA,GAAAC,uBAAA,CAAAX,OAAA;AAA+B,IAAAY,WAAA,GAAAZ,OAAA;AAAA,IAAAa,SAAA;AAAA,SAAAF,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAlB,OAAA,EAAAS,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAI,GAAA,CAAAV,CAAA,UAAAM,CAAA,CAAAK,GAAA,CAAAX,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAAnB,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAA6B,wBAAA,CAAAhB,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE/B,IAAMgB,yBAAyB,GAC7BC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrBjC,OAAO,2CAA2C,CAAC,CAACK,OAAO,GAC3DL,OAAO,yCAAyC,CAAC,CAACK,OAAO;AAE/D,IAAM6B,IAAI,GAAG,SAAS;AAsCtB,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,IAAA,EAUrBC,YAAkB,EACf;EAAA,IAAAC,cAAA,GAAAF,IAAA,CATDG,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,IAAI,GAAAA,cAAA;IAAAE,UAAA,GAAAJ,IAAA,CAChBK,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAGR,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAGC,IAAI,GAAG,IAAI,GAAAM,UAAA;IAAAE,qBAAA,GAAAN,IAAA,CAC3CO,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACvBE,QAAQ,GAAAR,IAAA,CAARQ,QAAQ;IAAAC,SAAA,GAAAT,IAAA,CACRU,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,OAAO,GAAAA,SAAA;IACdE,KAAK,GAAAX,IAAA,CAALW,KAAK;IACFC,SAAS,OAAA1C,yBAAA,CAAAD,OAAA,EAAA+B,IAAA,EAAAvB,SAAA;EAId,IAAIoC,SAAS;EACb,IAAIC,QAAQ;EAEZ,QAAQJ,IAAI;IACV,KAAK,OAAO;MACVG,SAAS,GAAGE,MAAM,CAACC,SAAS;MAC5BF,QAAQ,GAAG,OAAO;MAClB;IACF,KAAK,OAAO;MACVD,SAAS,GAAGE,MAAM,CAACE,SAAS;MAC5BH,QAAQ,GAAG,OAAO;MAClB;IACF;MACED,SAAS,GAAG;QAACK,MAAM,EAAER,IAAI;QAAES,KAAK,EAAET;MAAI,CAAC;MACvC;EACJ;EAEA,IAAMU,WAAW,GAAAvD,MAAA,CAAAwD,MAAA;IACflB,SAAS,EAATA,SAAS;IACTE,KAAK,EAALA,KAAK;IACLE,gBAAgB,EAAhBA;EAAgB,GACbK,SAAS;IACZU,GAAG,EAAErB,YAAY;IACjBU,KAAK,EAAEE,SAAS;IAChBH,IAAI,EAAEI;EAAQ,EACf;EAED,IAAMS,YAAY,GAAG;IACnBC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC;EAED,OACE,IAAAjD,WAAA,CAAAkD,GAAA,EAACrD,KAAA,CAAAJ,OAAI;IACHuC,QAAQ,EAAEA,QAAS;IACnBG,KAAK,EAAEgB,mBAAU,CAACC,OAAO,CAACb,MAAM,CAACc,SAAS,EAAElB,KAAK,CAAE;IAAAmB,QAAA,EAClDlC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAExB,IAAArB,WAAA,CAAAkD,GAAA,EAAC/B,yBAAyB,EAAA9B,MAAA,CAAAwD,MAAA,KAAKD,WAAW,EAAMG,YAAY,CAAG,CAAC,GAKhE,IAAA/C,WAAA,CAAAkD,GAAA,EAAC/B,yBAAyB,EAAA9B,MAAA,CAAAwD,MAAA,KAAKD,WAAW,CAAG;EAC9C,CACG,CAAC;AAEX,CAAC;AAkCD,IAAMW,wBAGL,GAAGzD,KAAK,CAAC0D,UAAU,CAACjC,iBAAiB,CAAC;AACvCgC,wBAAwB,CAACE,WAAW,GAAG,mBAAmB;AAE1D,IAAMlB,MAAM,GAAGY,mBAAU,CAACO,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDpB,SAAS,EAAE;IACTG,KAAK,EAAE,EAAE;IACTD,MAAM,EAAE;EACV,CAAC;EACDD,SAAS,EAAE;IACTE,KAAK,EAAE,EAAE;IACTD,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAAC,IAAAmB,QAAA,GAAAtE,OAAA,CAAAE,OAAA,GAEY8D,wBAAwB", "ignoreList": []}