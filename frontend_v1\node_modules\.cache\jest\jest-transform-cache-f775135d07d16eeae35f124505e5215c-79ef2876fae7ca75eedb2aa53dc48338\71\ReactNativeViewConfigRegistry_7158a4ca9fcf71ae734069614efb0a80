61d98be933f95d48527184798f916561
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.customDirectEventTypes = exports.customBubblingEventTypes = void 0;
exports.get = get;
exports.register = register;
var _invariant = _interopRequireDefault(require("invariant"));
var customBubblingEventTypes = exports.customBubblingEventTypes = {};
var customDirectEventTypes = exports.customDirectEventTypes = {};
var viewConfigCallbacks = new Map();
var viewConfigs = new Map();
function processEventTypes(viewConfig) {
  var bubblingEventTypes = viewConfig.bubblingEventTypes,
    directEventTypes = viewConfig.directEventTypes;
  if (__DEV__) {
    if (bubblingEventTypes != null && directEventTypes != null) {
      for (var topLevelType in directEventTypes) {
        (0, _invariant.default)(bubblingEventTypes[topLevelType] == null, 'Event cannot be both direct and bubbling: %s', topLevelType);
      }
    }
  }
  if (bubblingEventTypes != null) {
    for (var _topLevelType in bubblingEventTypes) {
      if (customBubblingEventTypes[_topLevelType] == null) {
        customBubblingEventTypes[_topLevelType] = bubblingEventTypes[_topLevelType];
      }
    }
  }
  if (directEventTypes != null) {
    for (var _topLevelType2 in directEventTypes) {
      if (customDirectEventTypes[_topLevelType2] == null) {
        customDirectEventTypes[_topLevelType2] = directEventTypes[_topLevelType2];
      }
    }
  }
}
function register(name, callback) {
  (0, _invariant.default)(!viewConfigCallbacks.has(name), 'Tried to register two views with the same name %s', name);
  (0, _invariant.default)(typeof callback === 'function', 'View config getter callback for component `%s` must be a function (received `%s`)', name, callback === null ? 'null' : typeof callback);
  viewConfigCallbacks.set(name, callback);
  return name;
}
function get(name) {
  var viewConfig = viewConfigs.get(name);
  if (viewConfig == null) {
    var callback = viewConfigCallbacks.get(name);
    if (typeof callback !== 'function') {
      (0, _invariant.default)(false, 'View config getter callback for component `%s` must be a function (received `%s`).%s', name, callback === null ? 'null' : typeof callback, typeof name[0] === 'string' && /[a-z]/.test(name[0]) ? ' Make sure to start component names with a capital letter.' : '');
    }
    viewConfig = callback();
    (0, _invariant.default)(viewConfig, 'View config not found for component `%s`', name);
    processEventTypes(viewConfig);
    viewConfigs.set(name, viewConfig);
    viewConfigCallbacks.set(name, null);
  }
  return viewConfig;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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