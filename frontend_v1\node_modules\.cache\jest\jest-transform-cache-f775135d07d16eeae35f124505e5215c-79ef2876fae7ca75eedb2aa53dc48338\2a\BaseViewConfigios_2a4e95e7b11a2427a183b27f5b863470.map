{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "_NativeReactNativeFeatureFlags", "_interopRequireDefault", "_ReactNativeStyleAttributes", "_ViewConfigIgnore", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "bubblingEventTypes", "topPress", "phasedRegistrationNames", "bubbled", "captured", "topChange", "topFocus", "topBlur", "topSubmitEditing", "topEndEditing", "topKeyPress", "topTouchStart", "topTouchMove", "topTouchCancel", "topTouchEnd", "topClick", "topPointerCancel", "topPointerDown", "topPointerMove", "topPointerUp", "topPointerEnter", "skipBubbling", "topPointerLeave", "topPointerOver", "topPointerOut", "topGotPointerCapture", "topLostPointerCapture", "directEventTypes", "topAccessibilityAction", "registrationName", "topAccessibilityTap", "topMagicTap", "topAccessibilityEscape", "topLayout", "onGestureHandlerEvent", "DynamicallyInjectedByGestureHandler", "onGestureHandlerStateChange", "validAttributesForNonEventProps", "accessible", "accessibilityActions", "accessibilityLabel", "accessibilityHint", "accessibilityLanguage", "accessibilityValue", "accessibilityViewIsModal", "accessibilityElementsHidden", "accessibilityIgnoresInvertColors", "accessibilityShowsLargeContentViewer", "accessibilityLargeContentTitle", "testID", "backgroundColor", "process", "backfaceVisibility", "cursor", "opacity", "shadowColor", "shadowOffset", "diff", "shadowOpacity", "shadowRadius", "needsOffscreenAlphaCompositing", "overflow", "shouldRasterizeIOS", "transform", "transform<PERSON><PERSON>in", "accessibilityRole", "accessibilityState", "nativeID", "pointerEvents", "removeClippedSubviews", "role", "borderRadius", "borderColor", "borderBlockColor", "borderCurve", "borderWidth", "borderBlockWidth", "borderStyle", "hitSlop", "collapsable", "collaps<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "NativeReactNativeFeatureFlags", "enableNativeCSSParsing", "boxShadow", "mixBlendMode", "isolation", "borderTopWidth", "borderTopColor", "borderRightWidth", "borderRightColor", "borderBottomWidth", "borderBottomColor", "borderLeftWidth", "borderLeftColor", "borderStartWidth", "borderBlockStartWidth", "borderStartColor", "borderBlockStartColor", "borderEndWidth", "borderBlockEndWidth", "borderEndColor", "borderBlockEndColor", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "borderTopEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderBottomEndRadius", "borderEndEndRadius", "borderEndStartRadius", "borderStartEndRadius", "borderStartStartRadius", "display", "zIndex", "top", "right", "start", "end", "bottom", "left", "inset", "insetBlock", "insetBlockEnd", "insetBlockStart", "insetInline", "insetInlineEnd", "insetInlineStart", "width", "height", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "margin", "marginBlock", "marginBlockEnd", "marginBlockStart", "marginBottom", "marginEnd", "marginHorizontal", "marginInline", "marginInlineEnd", "marginInlineStart", "marginLeft", "marginRight", "marginStart", "marginTop", "marginVertical", "padding", "paddingBlock", "paddingBlockEnd", "paddingBlockStart", "paddingBottom", "paddingEnd", "paddingHorizontal", "paddingInline", "paddingInlineEnd", "paddingInlineStart", "paddingLeft", "paddingRight", "paddingStart", "paddingTop", "paddingVertical", "flex", "flexGrow", "rowGap", "columnGap", "gap", "flexShrink", "flexBasis", "flexDirection", "flexWrap", "justifyContent", "alignItems", "alignSelf", "align<PERSON><PERSON><PERSON>", "position", "aspectRatio", "boxSizing", "direction", "style", "ReactNativeStyleAttributes", "validAttributesForEventProps", "ConditionallyIgnoredEventHandlers", "onLayout", "onMagicTap", "onAccessibilityAction", "onAccessibilityEscape", "onAccessibilityTap", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "onResponderGrant", "onResponderReject", "onResponderStart", "onResponderEnd", "onResponderRelease", "onResponderMove", "onResponderTerminate", "onResponderTerminationRequest", "onShouldBlockNativeResponder", "onTouchStart", "onTouchMove", "onTouchEnd", "onTouchCancel", "onClick", "onClickCapture", "onPointerUp", "onPointerDown", "onPointerCancel", "onPointerEnter", "onPointerMove", "onPointerLeave", "onPointerOver", "onPointerOut", "onGotPointerCapture", "onLostPointerCapture", "PlatformBaseViewConfigIos", "validAttributes", "assign", "_default", "exports"], "sources": ["BaseViewConfig.ios.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {PartialViewConfigWithoutName} from './PlatformBaseViewConfig';\n\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\nimport NativeReactNativeFeatureFlags from '../../src/private/featureflags/specs/NativeReactNativeFeatureFlags';\nimport ReactNativeStyleAttributes from '../Components/View/ReactNativeStyleAttributes';\nimport {\n  ConditionallyIgnoredEventHandlers,\n  DynamicallyInjectedByGestureHandler,\n} from './ViewConfigIgnore';\n\nconst bubblingEventTypes = {\n  // Generic Events\n  topPress: {\n    phasedRegistrationNames: {\n      bubbled: 'onPress',\n      captured: 'onPressCapture',\n    },\n  },\n  topChange: {\n    phasedRegistrationNames: {\n      bubbled: 'onChange',\n      captured: 'onChangeCapture',\n    },\n  },\n  topFocus: {\n    phasedRegistrationNames: {\n      bubbled: 'onFocus',\n      captured: 'onFocusCapture',\n    },\n  },\n  topBlur: {\n    phasedRegistrationNames: {\n      bubbled: 'onBlur',\n      captured: 'onBlurCapture',\n    },\n  },\n  topSubmitEditing: {\n    phasedRegistrationNames: {\n      bubbled: 'onSubmitEditing',\n      captured: 'onSubmitEditingCapture',\n    },\n  },\n  topEndEditing: {\n    phasedRegistrationNames: {\n      bubbled: 'onEndEditing',\n      captured: 'onEndEditingCapture',\n    },\n  },\n  topKeyPress: {\n    phasedRegistrationNames: {\n      bubbled: 'onKeyPress',\n      captured: 'onKeyPressCapture',\n    },\n  },\n\n  // Touch Events\n  topTouchStart: {\n    phasedRegistrationNames: {\n      bubbled: 'onTouchStart',\n      captured: 'onTouchStartCapture',\n    },\n  },\n  topTouchMove: {\n    phasedRegistrationNames: {\n      bubbled: 'onTouchMove',\n      captured: 'onTouchMoveCapture',\n    },\n  },\n  topTouchCancel: {\n    phasedRegistrationNames: {\n      bubbled: 'onTouchCancel',\n      captured: 'onTouchCancelCapture',\n    },\n  },\n  topTouchEnd: {\n    phasedRegistrationNames: {\n      bubbled: 'onTouchEnd',\n      captured: 'onTouchEndCapture',\n    },\n  },\n\n  // Experimental/Work in Progress Pointer Events (not yet ready for use)\n  topClick: {\n    phasedRegistrationNames: {\n      captured: 'onClickCapture',\n      bubbled: 'onClick',\n    },\n  },\n  topPointerCancel: {\n    phasedRegistrationNames: {\n      captured: 'onPointerCancelCapture',\n      bubbled: 'onPointerCancel',\n    },\n  },\n  topPointerDown: {\n    phasedRegistrationNames: {\n      captured: 'onPointerDownCapture',\n      bubbled: 'onPointerDown',\n    },\n  },\n  topPointerMove: {\n    phasedRegistrationNames: {\n      captured: 'onPointerMoveCapture',\n      bubbled: 'onPointerMove',\n    },\n  },\n  topPointerUp: {\n    phasedRegistrationNames: {\n      captured: 'onPointerUpCapture',\n      bubbled: 'onPointerUp',\n    },\n  },\n  topPointerEnter: {\n    phasedRegistrationNames: {\n      captured: 'onPointerEnterCapture',\n      bubbled: 'onPointerEnter',\n      skipBubbling: true,\n    },\n  },\n  topPointerLeave: {\n    phasedRegistrationNames: {\n      captured: 'onPointerLeaveCapture',\n      bubbled: 'onPointerLeave',\n      skipBubbling: true,\n    },\n  },\n  topPointerOver: {\n    phasedRegistrationNames: {\n      captured: 'onPointerOverCapture',\n      bubbled: 'onPointerOver',\n    },\n  },\n  topPointerOut: {\n    phasedRegistrationNames: {\n      captured: 'onPointerOutCapture',\n      bubbled: 'onPointerOut',\n    },\n  },\n  topGotPointerCapture: {\n    phasedRegistrationNames: {\n      captured: 'onGotPointerCaptureCapture',\n      bubbled: 'onGotPointerCapture',\n    },\n  },\n  topLostPointerCapture: {\n    phasedRegistrationNames: {\n      captured: 'onLostPointerCaptureCapture',\n      bubbled: 'onLostPointerCapture',\n    },\n  },\n};\n\nconst directEventTypes = {\n  topAccessibilityAction: {\n    registrationName: 'onAccessibilityAction',\n  },\n  topAccessibilityTap: {\n    registrationName: 'onAccessibilityTap',\n  },\n  topMagicTap: {\n    registrationName: 'onMagicTap',\n  },\n  topAccessibilityEscape: {\n    registrationName: 'onAccessibilityEscape',\n  },\n  topLayout: {\n    registrationName: 'onLayout',\n  },\n  onGestureHandlerEvent: DynamicallyInjectedByGestureHandler({\n    registrationName: 'onGestureHandlerEvent',\n  }),\n  onGestureHandlerStateChange: DynamicallyInjectedByGestureHandler({\n    registrationName: 'onGestureHandlerStateChange',\n  }),\n};\n\nconst validAttributesForNonEventProps = {\n  // View Props\n  accessible: true,\n  accessibilityActions: true,\n  accessibilityLabel: true,\n  accessibilityHint: true,\n  accessibilityLanguage: true,\n  accessibilityValue: true,\n  accessibilityViewIsModal: true,\n  accessibilityElementsHidden: true,\n  accessibilityIgnoresInvertColors: true,\n  accessibilityShowsLargeContentViewer: true,\n  accessibilityLargeContentTitle: true,\n  testID: true,\n  backgroundColor: {process: require('../StyleSheet/processColor').default},\n  backfaceVisibility: true,\n  cursor: true,\n  opacity: true,\n  shadowColor: {process: require('../StyleSheet/processColor').default},\n  shadowOffset: {diff: require('../Utilities/differ/sizesDiffer').default},\n  shadowOpacity: true,\n  shadowRadius: true,\n  needsOffscreenAlphaCompositing: true,\n  overflow: true,\n  shouldRasterizeIOS: true,\n  transform: {diff: require('../Utilities/differ/matricesDiffer').default},\n  transformOrigin: true,\n  accessibilityRole: true,\n  accessibilityState: true,\n  nativeID: true,\n  pointerEvents: true,\n  removeClippedSubviews: true,\n  role: true,\n  borderRadius: true,\n  borderColor: {process: require('../StyleSheet/processColor').default},\n  borderBlockColor: {process: require('../StyleSheet/processColor').default},\n  borderCurve: true,\n  borderWidth: true,\n  borderBlockWidth: true,\n  borderStyle: true,\n  hitSlop: {diff: require('../Utilities/differ/insetsDiffer').default},\n  collapsable: true,\n  collapsableChildren: true,\n  filter:\n    NativeReactNativeFeatureFlags != null &&\n    ReactNativeFeatureFlags.enableNativeCSSParsing()\n      ? true\n      : {\n          process: require('../StyleSheet/processFilter').default,\n        },\n  boxShadow:\n    NativeReactNativeFeatureFlags != null &&\n    ReactNativeFeatureFlags.enableNativeCSSParsing()\n      ? true\n      : {\n          process: require('../StyleSheet/processBoxShadow').default,\n        },\n  mixBlendMode: true,\n  isolation: true,\n\n  borderTopWidth: true,\n  borderTopColor: {process: require('../StyleSheet/processColor').default},\n  borderRightWidth: true,\n  borderRightColor: {process: require('../StyleSheet/processColor').default},\n  borderBottomWidth: true,\n  borderBottomColor: {process: require('../StyleSheet/processColor').default},\n  borderLeftWidth: true,\n  borderLeftColor: {process: require('../StyleSheet/processColor').default},\n  borderStartWidth: true,\n  borderBlockStartWidth: true,\n  borderStartColor: {process: require('../StyleSheet/processColor').default},\n  borderBlockStartColor: {\n    process: require('../StyleSheet/processColor').default,\n  },\n  borderEndWidth: true,\n  borderBlockEndWidth: true,\n  borderEndColor: {process: require('../StyleSheet/processColor').default},\n  borderBlockEndColor: {process: require('../StyleSheet/processColor').default},\n\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  borderTopEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderBottomEndRadius: true,\n  borderEndEndRadius: true,\n  borderEndStartRadius: true,\n  borderStartEndRadius: true,\n  borderStartStartRadius: true,\n  display: true,\n  zIndex: true,\n\n  // ShadowView properties\n  top: true,\n  right: true,\n  start: true,\n  end: true,\n  bottom: true,\n  left: true,\n\n  inset: true,\n  insetBlock: true,\n  insetBlockEnd: true,\n  insetBlockStart: true,\n  insetInline: true,\n  insetInlineEnd: true,\n  insetInlineStart: true,\n\n  width: true,\n  height: true,\n\n  minWidth: true,\n  maxWidth: true,\n  minHeight: true,\n  maxHeight: true,\n\n  // Also declared as ViewProps\n  // borderTopWidth: true,\n  // borderRightWidth: true,\n  // borderBottomWidth: true,\n  // borderLeftWidth: true,\n  // borderStartWidth: true,\n  // borderEndWidth: true,\n  // borderWidth: true,\n\n  margin: true,\n  marginBlock: true,\n  marginBlockEnd: true,\n  marginBlockStart: true,\n  marginBottom: true,\n  marginEnd: true,\n  marginHorizontal: true,\n  marginInline: true,\n  marginInlineEnd: true,\n  marginInlineStart: true,\n  marginLeft: true,\n  marginRight: true,\n  marginStart: true,\n  marginTop: true,\n  marginVertical: true,\n\n  padding: true,\n  paddingBlock: true,\n  paddingBlockEnd: true,\n  paddingBlockStart: true,\n  paddingBottom: true,\n  paddingEnd: true,\n  paddingHorizontal: true,\n  paddingInline: true,\n  paddingInlineEnd: true,\n  paddingInlineStart: true,\n  paddingLeft: true,\n  paddingRight: true,\n  paddingStart: true,\n  paddingTop: true,\n  paddingVertical: true,\n\n  flex: true,\n  flexGrow: true,\n  rowGap: true,\n  columnGap: true,\n  gap: true,\n  flexShrink: true,\n  flexBasis: true,\n  flexDirection: true,\n  flexWrap: true,\n  justifyContent: true,\n  alignItems: true,\n  alignSelf: true,\n  alignContent: true,\n  position: true,\n  aspectRatio: true,\n  boxSizing: true,\n\n  // Also declared as ViewProps\n  // overflow: true,\n  // display: true,\n\n  direction: true,\n\n  style: ReactNativeStyleAttributes,\n};\n\n// Props for bubbling and direct events\nconst validAttributesForEventProps = ConditionallyIgnoredEventHandlers({\n  onLayout: true,\n  onMagicTap: true,\n\n  // Accessibility\n  onAccessibilityAction: true,\n  onAccessibilityEscape: true,\n  onAccessibilityTap: true,\n\n  // PanResponder handlers\n  onMoveShouldSetResponder: true,\n  onMoveShouldSetResponderCapture: true,\n  onStartShouldSetResponder: true,\n  onStartShouldSetResponderCapture: true,\n  onResponderGrant: true,\n  onResponderReject: true,\n  onResponderStart: true,\n  onResponderEnd: true,\n  onResponderRelease: true,\n  onResponderMove: true,\n  onResponderTerminate: true,\n  onResponderTerminationRequest: true,\n  onShouldBlockNativeResponder: true,\n\n  // Touch events\n  onTouchStart: true,\n  onTouchMove: true,\n  onTouchEnd: true,\n  onTouchCancel: true,\n\n  // Pointer events\n  onClick: true,\n  onClickCapture: true,\n  onPointerUp: true,\n  onPointerDown: true,\n  onPointerCancel: true,\n  onPointerEnter: true,\n  onPointerMove: true,\n  onPointerLeave: true,\n  onPointerOver: true,\n  onPointerOut: true,\n  onGotPointerCapture: true,\n  onLostPointerCapture: true,\n});\n\n/**\n * On iOS, view managers define all of a component's props.\n * All view managers extend RCTViewManager, and RCTViewManager declares these props.\n */\nconst PlatformBaseViewConfigIos: PartialViewConfigWithoutName = {\n  bubblingEventTypes,\n  directEventTypes,\n  validAttributes: {\n    ...validAttributesForNonEventProps,\n    ...validAttributesForEventProps,\n  },\n};\n\nexport default PlatformBaseViewConfigIos;\n"], "mappings": ";;;;;AAYA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,8BAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,2BAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;AAG4B,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,wBAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE5B,IAAMmB,kBAAkB,GAAG;EAEzBC,QAAQ,EAAE;IACRC,uBAAuB,EAAE;MACvBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,SAAS,EAAE;IACTH,uBAAuB,EAAE;MACvBC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDE,QAAQ,EAAE;IACRJ,uBAAuB,EAAE;MACvBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDG,OAAO,EAAE;IACPL,uBAAuB,EAAE;MACvBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDI,gBAAgB,EAAE;IAChBN,uBAAuB,EAAE;MACvBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDK,aAAa,EAAE;IACbP,uBAAuB,EAAE;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDM,WAAW,EAAE;IACXR,uBAAuB,EAAE;MACvBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE;IACZ;EACF,CAAC;EAGDO,aAAa,EAAE;IACbT,uBAAuB,EAAE;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDQ,YAAY,EAAE;IACZV,uBAAuB,EAAE;MACvBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDS,cAAc,EAAE;IACdX,uBAAuB,EAAE;MACvBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDU,WAAW,EAAE;IACXZ,uBAAuB,EAAE;MACvBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE;IACZ;EACF,CAAC;EAGDW,QAAQ,EAAE;IACRb,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,gBAAgB;MAC1BD,OAAO,EAAE;IACX;EACF,CAAC;EACDa,gBAAgB,EAAE;IAChBd,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,wBAAwB;MAClCD,OAAO,EAAE;IACX;EACF,CAAC;EACDc,cAAc,EAAE;IACdf,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,sBAAsB;MAChCD,OAAO,EAAE;IACX;EACF,CAAC;EACDe,cAAc,EAAE;IACdhB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,sBAAsB;MAChCD,OAAO,EAAE;IACX;EACF,CAAC;EACDgB,YAAY,EAAE;IACZjB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,oBAAoB;MAC9BD,OAAO,EAAE;IACX;EACF,CAAC;EACDiB,eAAe,EAAE;IACflB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,uBAAuB;MACjCD,OAAO,EAAE,gBAAgB;MACzBkB,YAAY,EAAE;IAChB;EACF,CAAC;EACDC,eAAe,EAAE;IACfpB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,uBAAuB;MACjCD,OAAO,EAAE,gBAAgB;MACzBkB,YAAY,EAAE;IAChB;EACF,CAAC;EACDE,cAAc,EAAE;IACdrB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,sBAAsB;MAChCD,OAAO,EAAE;IACX;EACF,CAAC;EACDqB,aAAa,EAAE;IACbtB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,qBAAqB;MAC/BD,OAAO,EAAE;IACX;EACF,CAAC;EACDsB,oBAAoB,EAAE;IACpBvB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,4BAA4B;MACtCD,OAAO,EAAE;IACX;EACF,CAAC;EACDuB,qBAAqB,EAAE;IACrBxB,uBAAuB,EAAE;MACvBE,QAAQ,EAAE,6BAA6B;MACvCD,OAAO,EAAE;IACX;EACF;AACF,CAAC;AAED,IAAMwB,gBAAgB,GAAG;EACvBC,sBAAsB,EAAE;IACtBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,mBAAmB,EAAE;IACnBD,gBAAgB,EAAE;EACpB,CAAC;EACDE,WAAW,EAAE;IACXF,gBAAgB,EAAE;EACpB,CAAC;EACDG,sBAAsB,EAAE;IACtBH,gBAAgB,EAAE;EACpB,CAAC;EACDI,SAAS,EAAE;IACTJ,gBAAgB,EAAE;EACpB,CAAC;EACDK,qBAAqB,EAAE,IAAAC,qDAAmC,EAAC;IACzDN,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACFO,2BAA2B,EAAE,IAAAD,qDAAmC,EAAC;IAC/DN,gBAAgB,EAAE;EACpB,CAAC;AACH,CAAC;AAED,IAAMQ,+BAA+B,GAAG;EAEtCC,UAAU,EAAE,IAAI;EAChBC,oBAAoB,EAAE,IAAI;EAC1BC,kBAAkB,EAAE,IAAI;EACxBC,iBAAiB,EAAE,IAAI;EACvBC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE,IAAI;EACxBC,wBAAwB,EAAE,IAAI;EAC9BC,2BAA2B,EAAE,IAAI;EACjCC,gCAAgC,EAAE,IAAI;EACtCC,oCAAoC,EAAE,IAAI;EAC1CC,8BAA8B,EAAE,IAAI;EACpCC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE;IAACC,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACzE8D,kBAAkB,EAAE,IAAI;EACxBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;IAACJ,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACrEkE,YAAY,EAAE;IAACC,IAAI,EAAElF,OAAO,kCAAkC,CAAC,CAACe;EAAO,CAAC;EACxEoE,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,8BAA8B,EAAE,IAAI;EACpCC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,SAAS,EAAE;IAACN,IAAI,EAAElF,OAAO,qCAAqC,CAAC,CAACe;EAAO,CAAC;EACxE0E,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,kBAAkB,EAAE,IAAI;EACxBC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE,IAAI;EACnBC,qBAAqB,EAAE,IAAI;EAC3BC,IAAI,EAAE,IAAI;EACVC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE;IAACrB,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACrEmF,gBAAgB,EAAE;IAACtB,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EAC1EoF,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,WAAW,EAAE,IAAI;EACjBC,OAAO,EAAE;IAACrB,IAAI,EAAElF,OAAO,mCAAmC,CAAC,CAACe;EAAO,CAAC;EACpEyF,WAAW,EAAE,IAAI;EACjBC,mBAAmB,EAAE,IAAI;EACzBC,MAAM,EACJC,sCAA6B,IAAI,IAAI,IACrC7G,uBAAuB,CAAC8G,sBAAsB,CAAC,CAAC,GAC5C,IAAI,GACJ;IACEhC,OAAO,EAAE5E,OAAO,8BAA8B,CAAC,CAACe;EAClD,CAAC;EACP8F,SAAS,EACPF,sCAA6B,IAAI,IAAI,IACrC7G,uBAAuB,CAAC8G,sBAAsB,CAAC,CAAC,GAC5C,IAAI,GACJ;IACEhC,OAAO,EAAE5E,OAAO,iCAAiC,CAAC,CAACe;EACrD,CAAC;EACP+F,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EAEfC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE;IAACrC,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACxEmG,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE;IAACvC,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EAC1EqG,iBAAiB,EAAE,IAAI;EACvBC,iBAAiB,EAAE;IAACzC,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EAC3EuG,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE;IAAC3C,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACzEyG,gBAAgB,EAAE,IAAI;EACtBC,qBAAqB,EAAE,IAAI;EAC3BC,gBAAgB,EAAE;IAAC9C,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EAC1E4G,qBAAqB,EAAE;IACrB/C,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EACjD,CAAC;EACD6G,cAAc,EAAE,IAAI;EACpBC,mBAAmB,EAAE,IAAI;EACzBC,cAAc,EAAE;IAAClD,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EACxEgH,mBAAmB,EAAE;IAACnD,OAAO,EAAE5E,OAAO,6BAA6B,CAAC,CAACe;EAAO,CAAC;EAE7EiH,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,kBAAkB,EAAE,IAAI;EACxBC,sBAAsB,EAAE,IAAI;EAC5BC,uBAAuB,EAAE,IAAI;EAC7BC,uBAAuB,EAAE,IAAI;EAC7BC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE,IAAI;EACxBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,sBAAsB,EAAE,IAAI;EAC5BC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EAGZC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,IAAI;EACTC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EAEVC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EAEtBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EAEZC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EAWfC,MAAM,EAAE,IAAI;EACZC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,IAAI;EAEpBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,IAAI;EACnBC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,IAAI;EACxBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,IAAI;EAErBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,GAAG,EAAE,IAAI;EACTC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EAMfC,SAAS,EAAE,IAAI;EAEfC,KAAK,EAAEC;AACT,CAAC;AAGD,IAAMC,4BAA4B,GAAG,IAAAC,mDAAiC,EAAC;EACrEC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAGhBC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE,IAAI;EAGxBC,wBAAwB,EAAE,IAAI;EAC9BC,+BAA+B,EAAE,IAAI;EACrCC,yBAAyB,EAAE,IAAI;EAC/BC,gCAAgC,EAAE,IAAI;EACtCC,gBAAgB,EAAE,IAAI;EACtBC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE,IAAI;EACrBC,oBAAoB,EAAE,IAAI;EAC1BC,6BAA6B,EAAE,IAAI;EACnCC,4BAA4B,EAAE,IAAI;EAGlCC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EAGnBC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE,IAAI;EACpBC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE;AACxB,CAAC,CAAC;AAMF,IAAMC,yBAAuD,GAAG;EAC9D7N,kBAAkB,EAAlBA,kBAAkB;EAClB2B,gBAAgB,EAAhBA,gBAAgB;EAChBmM,eAAe,EAAAjO,MAAA,CAAAkO,MAAA,KACV1L,+BAA+B,EAC/BoJ,4BAA4B;AAEnC,CAAC;AAAC,IAAAuC,QAAA,GAAAC,OAAA,CAAA3O,OAAA,GAEauO,yBAAyB", "ignoreList": []}