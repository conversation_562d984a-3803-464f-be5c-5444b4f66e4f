/**
 * Navigation Types - React Navigation Type Definitions
 *
 * Component Contract:
 * - Defines all navigation parameter lists for type safety
 * - Supports dual-role navigation (customer/provider)
 * - Provides compile-time navigation safety
 * - Follows React Navigation v6+ patterns
 * - Enables proper TypeScript intellisense
 * - Supports nested navigation structures
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import type { NavigatorScreenParams } from '@react-navigation/native';

/**
 * Root Stack Parameter List
 * Top-level navigation structure that switches between auth and main app
 */
export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  CustomerStack: NavigatorScreenParams<CustomerStackParamList>;
  ProviderStack: NavigatorScreenParams<ProviderStackParamList>;
};

/**
 * Authentication Stack Parameter List
 * Handles login and registration flows
 */
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
};

/**
 * Customer Stack Parameter List
 * Stack navigation for customer users (includes tabs and modal screens)
 */
export type CustomerStackParamList = {
  CustomerTabs: NavigatorScreenParams<CustomerTabParamList>;
  ProviderDetails: { providerId: string };
  ServiceDetails: { serviceId: string };
  BookingScreen: { providerId?: string; serviceId?: string; service?: any; provider?: any };
  BookingConfirmation: { bookingData?: any; booking?: any };
  BookingDetails: { bookingId: string };
  Checkout: { serviceId: string; providerId: string };
  Payment: { checkoutSessionId: string };
  Conversation: { conversationId: string; participantName: string };
  RescheduleBooking: { bookingId: string; providerName?: string; serviceName?: string };
  LeaveReview: { bookingId: string; providerName: string; serviceName: string };
  EditProfile: undefined;
  AccountSettings: undefined;
  Notifications: undefined;
};

/**
 * Customer Tab Parameter List
 * Bottom tab navigation for customer users
 */
export type CustomerTabParamList = {
  Home: undefined;
  Search: { category?: string; categoryName?: string } | undefined;
  Bookings: undefined;
  Messages: undefined;
  Profile: undefined;
};

/**
 * Provider Stack Parameter List
 * Stack navigation for provider users (includes tabs and modal screens)
 */
export type ProviderStackParamList = {
  ProviderTabs: NavigatorScreenParams<ProviderTabParamList>;
  PayoutOnboarding: undefined;
  ServiceEditor: { mode: 'create' | 'edit'; serviceId?: string };
  StoreCustomization: undefined;
  ProviderAnalytics: undefined;
  CustomerManagement: undefined;
  NotificationManagement: undefined;
};

/**
 * Provider Tab Parameter List
 * Bottom tab navigation for service provider users
 */
export type ProviderTabParamList = {
  Dashboard: undefined;
  Store: undefined;
  Bookings: undefined;
  Messages: undefined;
  Profile: undefined;
};

/**
 * Global navigation prop types
 * These can be used throughout the app for type-safe navigation
 */
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
