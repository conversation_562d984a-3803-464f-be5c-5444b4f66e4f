/**
 * Standard Button Component
 *
 * Implements consistent button styling and behavior across the application
 * using the design system tokens and accessibility best practices.
 *
 * Features:
 * - Design system integration
 * - Multiple variants and sizes
 * - Accessibility compliance
 * - Loading and disabled states
 * - Icon support
 * - Consistent touch targets
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useCallback } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  AccessibilityRole,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDesignSystem } from '../../contexts/DesignSystemContext';

// Button variant types
export type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'destructive';
export type ButtonSize = 'small' | 'medium' | 'large';

// Button props interface
export interface StandardButtonProps {
  // Content
  title: string;
  onPress: () => void;
  
  // Styling
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  
  // State
  disabled?: boolean;
  loading?: boolean;
  
  // Icon
  icon?: string;
  iconPosition?: 'left' | 'right';
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  
  // Testing
  testID?: string;
  
  // Custom styling
  style?: any;
  textStyle?: any;
}

export const StandardButton: React.FC<StandardButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  testID,
  style,
  textStyle,
}) => {
  const { theme, getAccessibleTouchTarget } = useDesignSystem();

  // Handle press with loading state
  const handlePress = useCallback(() => {
    if (!disabled && !loading) {
      onPress();
    }
  }, [disabled, loading, onPress]);

  // Get button styles based on variant and size
  const buttonStyles = React.useMemo(() => {
    const baseStyles = {
      borderRadius: theme.borderRadius.component.button,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      flexDirection: 'row' as const,
      minHeight: getAccessibleTouchTarget('minimum'),
    };

    // Size-specific styles
    const sizeStyles = theme.componentSizes.button[size];

    // Variant-specific styles
    let variantStyles = {};
    switch (variant) {
      case 'primary':
        variantStyles = {
          backgroundColor: disabled 
            ? theme.colors.interactive?.primary.disabled 
            : theme.colors.interactive?.primary.default,
          ...theme.elevation.sm,
        };
        break;
      case 'secondary':
        variantStyles = {
          backgroundColor: disabled 
            ? theme.colors.interactive?.secondary.disabled 
            : theme.colors.interactive?.secondary.default,
          borderWidth: 1,
          borderColor: disabled 
            ? theme.colors.interactive?.secondary.borderDisabled 
            : theme.colors.interactive?.secondary.border,
        };
        break;
      case 'ghost':
        variantStyles = {
          backgroundColor: disabled 
            ? theme.colors.interactive?.ghost.disabled 
            : theme.colors.interactive?.ghost.default,
        };
        break;
      case 'destructive':
        variantStyles = {
          backgroundColor: disabled 
            ? theme.colors.interactive?.destructive.disabled 
            : theme.colors.interactive?.destructive.default,
          ...theme.elevation.sm,
        };
        break;
    }

    return {
      ...baseStyles,
      ...sizeStyles,
      ...variantStyles,
      width: fullWidth ? '100%' : undefined,
      opacity: disabled ? theme.opacity.disabled : theme.opacity.visible,
    };
  }, [theme, variant, size, disabled, fullWidth, getAccessibleTouchTarget]);

  // Get text styles based on variant
  const textStyles = React.useMemo(() => {
    const baseTextStyles = {
      ...theme.typography.styles.button,
      fontSize: theme.componentSizes.button[size].fontSize,
    };

    let variantTextStyles = {};
    switch (variant) {
      case 'primary':
        variantTextStyles = {
          color: disabled 
            ? theme.colors.interactive?.primary.textDisabled 
            : theme.colors.interactive?.primary.text,
        };
        break;
      case 'secondary':
        variantTextStyles = {
          color: disabled 
            ? theme.colors.interactive?.secondary.textDisabled 
            : theme.colors.interactive?.secondary.text,
        };
        break;
      case 'ghost':
        variantTextStyles = {
          color: disabled 
            ? theme.colors.interactive?.ghost.textDisabled 
            : theme.colors.interactive?.ghost.text,
        };
        break;
      case 'destructive':
        variantTextStyles = {
          color: disabled 
            ? theme.colors.interactive?.destructive.textDisabled 
            : theme.colors.interactive?.destructive.text,
        };
        break;
    }

    return {
      ...baseTextStyles,
      ...variantTextStyles,
    };
  }, [theme, variant, size, disabled]);

  // Get icon color
  const iconColor = React.useMemo(() => {
    switch (variant) {
      case 'primary':
        return disabled 
          ? theme.colors.interactive?.primary.textDisabled 
          : theme.colors.interactive?.primary.text;
      case 'secondary':
        return disabled 
          ? theme.colors.interactive?.secondary.textDisabled 
          : theme.colors.interactive?.secondary.text;
      case 'ghost':
        return disabled 
          ? theme.colors.interactive?.ghost.textDisabled 
          : theme.colors.interactive?.ghost.text;
      case 'destructive':
        return disabled 
          ? theme.colors.interactive?.destructive.textDisabled 
          : theme.colors.interactive?.destructive.text;
      default:
        return theme.colors.text?.primary;
    }
  }, [theme, variant, disabled]);

  // Icon size based on button size
  const iconSize = React.useMemo(() => {
    switch (size) {
      case 'small': return 16;
      case 'medium': return 20;
      case 'large': return 24;
      default: return 20;
    }
  }, [size]);

  // Render icon
  const renderIcon = () => {
    if (!icon) return null;
    
    return (
      <Ionicons
        name={icon as any}
        size={iconSize}
        color={iconColor}
        style={[
          iconPosition === 'left' ? styles.iconLeft : styles.iconRight,
        ]}
      />
    );
  };

  // Render loading indicator
  const renderLoadingIndicator = () => {
    if (!loading) return null;
    
    return (
      <ActivityIndicator
        size="small"
        color={iconColor}
        style={styles.loadingIndicator}
      />
    );
  };

  return (
    <TouchableOpacity
      style={[buttonStyles, style]}
      onPress={handlePress}
      disabled={disabled || loading}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        disabled: disabled || loading,
        busy: loading,
      }}
      testID={testID}
    >
      {loading ? (
        renderLoadingIndicator()
      ) : (
        <View style={styles.content}>
          {icon && iconPosition === 'left' && renderIcon()}
          <Text style={[textStyles, textStyle]} numberOfLines={1}>
            {title}
          </Text>
          {icon && iconPosition === 'right' && renderIcon()}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
  loadingIndicator: {
    // Loading indicator styles handled by ActivityIndicator
  },
});

export default StandardButton;
