594db5c37243953fab1bde31c3238620
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../../src/private/featureflags/ReactNativeFeatureFlags"));
var _NativeReactNativeFeatureFlags = _interopRequireDefault(require("../../../src/private/featureflags/specs/NativeReactNativeFeatureFlags"));
var _processAspectRatio = _interopRequireDefault(require("../../StyleSheet/processAspectRatio"));
var _processBackgroundImage = _interopRequireDefault(require("../../StyleSheet/processBackgroundImage"));
var _processBoxShadow = _interopRequireDefault(require("../../StyleSheet/processBoxShadow"));
var _processColor = _interopRequireDefault(require("../../StyleSheet/processColor"));
var _processFilter = _interopRequireDefault(require("../../StyleSheet/processFilter"));
var _processFontVariant = _interopRequireDefault(require("../../StyleSheet/processFontVariant"));
var _processTransform = _interopRequireDefault(require("../../StyleSheet/processTransform"));
var _processTransformOrigin = _interopRequireDefault(require("../../StyleSheet/processTransformOrigin"));
var _sizesDiffer = _interopRequireDefault(require("../../Utilities/differ/sizesDiffer"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var colorAttributes = {
  process: _processColor.default
};
var ReactNativeStyleAttributes = {
  alignContent: true,
  alignItems: true,
  alignSelf: true,
  aspectRatio: {
    process: _processAspectRatio.default
  },
  borderBottomWidth: true,
  borderEndWidth: true,
  borderLeftWidth: true,
  borderRightWidth: true,
  borderStartWidth: true,
  borderTopWidth: true,
  boxSizing: true,
  columnGap: true,
  borderWidth: true,
  bottom: true,
  direction: true,
  display: true,
  end: true,
  flex: true,
  flexBasis: true,
  flexDirection: true,
  flexGrow: true,
  flexShrink: true,
  flexWrap: true,
  gap: true,
  height: true,
  inset: true,
  insetBlock: true,
  insetBlockEnd: true,
  insetBlockStart: true,
  insetInline: true,
  insetInlineEnd: true,
  insetInlineStart: true,
  justifyContent: true,
  left: true,
  margin: true,
  marginBlock: true,
  marginBlockEnd: true,
  marginBlockStart: true,
  marginBottom: true,
  marginEnd: true,
  marginHorizontal: true,
  marginInline: true,
  marginInlineEnd: true,
  marginInlineStart: true,
  marginLeft: true,
  marginRight: true,
  marginStart: true,
  marginTop: true,
  marginVertical: true,
  maxHeight: true,
  maxWidth: true,
  minHeight: true,
  minWidth: true,
  overflow: true,
  padding: true,
  paddingBlock: true,
  paddingBlockEnd: true,
  paddingBlockStart: true,
  paddingBottom: true,
  paddingEnd: true,
  paddingHorizontal: true,
  paddingInline: true,
  paddingInlineEnd: true,
  paddingInlineStart: true,
  paddingLeft: true,
  paddingRight: true,
  paddingStart: true,
  paddingTop: true,
  paddingVertical: true,
  position: true,
  right: true,
  rowGap: true,
  start: true,
  top: true,
  width: true,
  zIndex: true,
  elevation: true,
  shadowColor: colorAttributes,
  shadowOffset: {
    diff: _sizesDiffer.default
  },
  shadowOpacity: true,
  shadowRadius: true,
  transform: {
    process: _processTransform.default
  },
  transformOrigin: {
    process: _processTransformOrigin.default
  },
  filter: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {
    process: _processFilter.default
  },
  mixBlendMode: true,
  isolation: true,
  boxShadow: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {
    process: _processBoxShadow.default
  },
  experimental_backgroundImage: {
    process: _processBackgroundImage.default
  },
  backfaceVisibility: true,
  backgroundColor: colorAttributes,
  borderBlockColor: colorAttributes,
  borderBlockEndColor: colorAttributes,
  borderBlockStartColor: colorAttributes,
  borderBottomColor: colorAttributes,
  borderBottomEndRadius: true,
  borderBottomLeftRadius: true,
  borderBottomRightRadius: true,
  borderBottomStartRadius: true,
  borderColor: colorAttributes,
  borderCurve: true,
  borderEndColor: colorAttributes,
  borderEndEndRadius: true,
  borderEndStartRadius: true,
  borderLeftColor: colorAttributes,
  borderRadius: true,
  borderRightColor: colorAttributes,
  borderStartColor: colorAttributes,
  borderStartEndRadius: true,
  borderStartStartRadius: true,
  borderStyle: true,
  borderTopColor: colorAttributes,
  borderTopEndRadius: true,
  borderTopLeftRadius: true,
  borderTopRightRadius: true,
  borderTopStartRadius: true,
  cursor: true,
  opacity: true,
  outlineColor: colorAttributes,
  outlineOffset: true,
  outlineStyle: true,
  outlineWidth: true,
  pointerEvents: true,
  color: colorAttributes,
  fontFamily: true,
  fontSize: true,
  fontStyle: true,
  fontVariant: {
    process: _processFontVariant.default
  },
  fontWeight: true,
  includeFontPadding: true,
  letterSpacing: true,
  lineHeight: true,
  textAlign: true,
  textAlignVertical: true,
  textDecorationColor: colorAttributes,
  textDecorationLine: true,
  textDecorationStyle: true,
  textShadowColor: colorAttributes,
  textShadowOffset: true,
  textShadowRadius: true,
  textTransform: true,
  userSelect: true,
  verticalAlign: true,
  writingDirection: true,
  overlayColor: colorAttributes,
  resizeMode: true,
  tintColor: colorAttributes,
  objectFit: true
};
var _default = exports.default = ReactNativeStyleAttributes;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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