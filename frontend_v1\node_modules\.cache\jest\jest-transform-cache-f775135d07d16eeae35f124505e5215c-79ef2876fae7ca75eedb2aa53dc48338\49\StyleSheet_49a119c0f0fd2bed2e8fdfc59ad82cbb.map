{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_composeStyles", "_flattenStyle", "ReactNativeStyleAttributes", "PixelRatio", "hairlineWidth", "roundToNearestPixel", "get", "absoluteFill", "position", "left", "right", "top", "bottom", "__DEV__", "freeze", "_default", "absoluteFillObject", "compose", "composeStyles", "flatten", "setStyleAttributePreprocessor", "property", "process", "_ReactNativeStyleAttr", "_ReactNativeStyleAttr2", "assign", "console", "error", "warn", "create", "obj", "key"], "sources": ["StyleSheet.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {\n  ____ColorValue_Internal,\n  ____DangerouslyImpreciseStyle_Internal,\n  ____DangerouslyImpreciseStyleProp_Internal,\n  ____ImageStyle_Internal,\n  ____ImageStyleProp_Internal,\n  ____Styles_Internal,\n  ____TextStyle_Internal,\n  ____TextStyleProp_Internal,\n  ____ViewStyle_Internal,\n  ____ViewStyleProp_Internal,\n} from './StyleSheetTypes';\n\nimport composeStyles from '../../src/private/styles/composeStyles';\nimport flatten from './flattenStyle';\n\nconst ReactNativeStyleAttributes =\n  require('../Components/View/ReactNativeStyleAttributes').default;\nconst PixelRatio = require('../Utilities/PixelRatio').default;\n\nexport type {NativeColorValue} from './StyleSheetTypes';\n\n/**\n * This type should be used as the type for anything that is a color. It is\n * most useful when using DynamicColorIOS which can be a string or a dynamic\n * color object.\n *\n * type props = {backgroundColor: ColorValue};\n */\nexport type ColorValue = ____ColorValue_Internal;\n\n/**\n * This type should be used as the type for a prop that is passed through\n * to a <View>'s `style` prop. This ensures call sites of the component\n * can't pass styles that View doesn't support such as `fontSize`.`\n *\n * type Props = {style: ViewStyleProp}\n * const MyComponent = (props: Props) => <View style={props.style} />\n */\nexport type ViewStyleProp = ____ViewStyleProp_Internal;\n\n/**\n * This type should be used as the type for a prop that is passed through\n * to a <Text>'s `style` prop. This ensures call sites of the component\n * can't pass styles that Text doesn't support such as `resizeMode`.`\n *\n * type Props = {style: TextStyleProp}\n * const MyComponent = (props: Props) => <Text style={props.style} />\n */\nexport type TextStyleProp = ____TextStyleProp_Internal;\n\n/**\n * This type should be used as the type for a prop that is passed through\n * to an <Image>'s `style` prop. This ensures call sites of the component\n * can't pass styles that Image doesn't support such as `fontSize`.`\n *\n * type Props = {style: ImageStyleProp}\n * const MyComponent = (props: Props) => <Image style={props.style} />\n */\nexport type ImageStyleProp = ____ImageStyleProp_Internal;\n\n/**\n * WARNING: You probably shouldn't be using this type. This type\n * is similar to the ones above except it allows styles that are accepted\n * by all of View, Text, or Image. It is therefore very unsafe to pass this\n * through to an underlying component. Using this is almost always a mistake\n * and using one of the other more restrictive types is likely the right choice.\n */\nexport type DangerouslyImpreciseStyleProp =\n  ____DangerouslyImpreciseStyleProp_Internal;\n\n/**\n * Utility type for getting the values for specific style keys.\n *\n * The following is bad because position is more restrictive than 'string':\n * ```\n * type Props = {position: string};\n * ```\n *\n * You should use the following instead:\n *\n * ```\n * type Props = {position: TypeForStyleKey<'position'>};\n * ```\n *\n * This will correctly give you the type 'absolute' | 'relative'\n */\nexport type TypeForStyleKey<\n  +key: $Keys<____DangerouslyImpreciseStyle_Internal>,\n> = $ElementType<____DangerouslyImpreciseStyle_Internal, key>;\n\n/**\n * This type is an object of the different possible style\n * properties that can be specified for View.\n *\n * Note that this isn't a safe way to type a style prop for a component as\n * results from StyleSheet.create return an internal identifier, not\n * an object of styles.\n *\n * If you want to type the style prop of a function,\n * consider using ViewStyleProp.\n *\n * A reasonable usage of this type is for helper functions that return an\n * object of styles to pass to a View that can't be precomputed with\n * StyleSheet.create.\n */\nexport type ViewStyle = ____ViewStyle_Internal;\n\n/**\n * This type is an object of the different possible style\n * properties that can be specified for Text.\n *\n * Note that this isn't a safe way to type a style prop for a component as\n * results from StyleSheet.create return an internal identifier, not\n * an object of styles.\n *\n * If you want to type the style prop of a function,\n * consider using TextStyleProp.\n *\n * A reasonable usage of this type is for helper functions that return an\n * object of styles to pass to a Text that can't be precomputed with\n * StyleSheet.create.\n */\nexport type TextStyle = ____TextStyle_Internal;\n\n/**\n * This type is an object of the different possible style\n * properties that can be specified for Image.\n *\n * Note that this isn't a safe way to type a style prop for a component as\n * results from StyleSheet.create return an internal identifier, not\n * an object of styles.\n *\n * If you want to type the style prop of a function,\n * consider using ImageStyleProp.\n *\n * A reasonable usage of this type is for helper functions that return an\n * object of styles to pass to an Image that can't be precomputed with\n * StyleSheet.create.\n */\nexport type ImageStyle = ____ImageStyle_Internal;\n\n/**\n * WARNING: You probably shouldn't be using this type. This type is an object\n * with all possible style keys and their values. Note that this isn't\n * a safe way to type a style prop for a component as results from\n * StyleSheet.create return an internal identifier, not an object of styles.\n *\n * If you want to type the style prop of a function, consider using\n * ViewStyleProp, TextStyleProp, or ImageStyleProp.\n *\n * This should only be used by very core utilities that operate on an object\n * containing any possible style value.\n */\nexport type DangerouslyImpreciseStyle = ____DangerouslyImpreciseStyle_Internal;\n\nlet hairlineWidth: number = PixelRatio.roundToNearestPixel(0.4);\nif (hairlineWidth === 0) {\n  hairlineWidth = 1 / PixelRatio.get();\n}\n\nconst absoluteFill: {\n  +bottom: 0,\n  +left: 0,\n  +position: 'absolute',\n  +right: 0,\n  +top: 0,\n} = {\n  position: 'absolute',\n  left: 0,\n  right: 0,\n  top: 0,\n  bottom: 0,\n};\nif (__DEV__) {\n  Object.freeze(absoluteFill);\n}\n\n/**\n * A StyleSheet is an abstraction similar to CSS StyleSheets\n *\n * Create a new StyleSheet:\n *\n * ```\n * const styles = StyleSheet.create({\n *   container: {\n *     borderRadius: 4,\n *     borderWidth: 0.5,\n *     borderColor: '#d6d7da',\n *   },\n *   title: {\n *     fontSize: 19,\n *     fontWeight: 'bold',\n *   },\n *   activeTitle: {\n *     color: 'red',\n *   },\n * });\n * ```\n *\n * Use a StyleSheet:\n *\n * ```\n * <View style={styles.container}>\n *   <Text style={[styles.title, this.props.isActive && styles.activeTitle]} />\n * </View>\n * ```\n *\n * Code quality:\n *\n *  - By moving styles away from the render function, you're making the code\n *    easier to understand.\n *  - Naming the styles is a good way to add meaning to the low level components\n *  in the render function, and encourage reuse.\n *  - In most IDEs, using `StyleSheet.create()` will offer static type checking\n *  and suggestions to help you write valid styles.\n *\n */\nexport default {\n  /**\n   * This is defined as the width of a thin line on the platform. It can be\n   * used as the thickness of a border or division between two elements.\n   * Example:\n   * ```\n   *   {\n   *     borderBottomColor: '#bbb',\n   *     borderBottomWidth: StyleSheet.hairlineWidth\n   *   }\n   * ```\n   *\n   * This constant will always be a round number of pixels (so a line defined\n   * by it look crisp) and will try to match the standard width of a thin line\n   * on the underlying platform. However, you should not rely on it being a\n   * constant size, because on different platforms and screen densities its\n   * value may be calculated differently.\n   *\n   * A line with hairline width may not be visible if your simulator is downscaled.\n   */\n  hairlineWidth,\n\n  /**\n   * A very common pattern is to create overlays with position absolute and zero positioning,\n   * so `absoluteFill` can be used for convenience and to reduce duplication of these repeated\n   * styles.\n   */\n  absoluteFill: (absoluteFill: any), // TODO: This should be updated after we fix downstream Flow sites.\n\n  /**\n   * Sometimes you may want `absoluteFill` but with a couple tweaks - `absoluteFillObject` can be\n   * used to create a customized entry in a `StyleSheet`, e.g.:\n   *\n   *   const styles = StyleSheet.create({\n   *     wrapper: {\n   *       ...StyleSheet.absoluteFillObject,\n   *       top: 10,\n   *       backgroundColor: 'transparent',\n   *     },\n   *   });\n   */\n  absoluteFillObject: absoluteFill,\n\n  /**\n   * Combines two styles such that `style2` will override any styles in `style1`.\n   * If either style is falsy, the other one is returned without allocating an\n   * array, saving allocations and maintaining reference equality for\n   * PureComponent checks.\n   */\n  compose: composeStyles,\n\n  /**\n   * Flattens an array of style objects, into one aggregated style object.\n   *\n   * Example:\n   * ```\n   * const styles = StyleSheet.create({\n   *   listItem: {\n   *     flex: 1,\n   *     fontSize: 16,\n   *     color: 'white'\n   *   },\n   *   selectedListItem: {\n   *     color: 'green'\n   *   }\n   * });\n   *\n   * StyleSheet.flatten([styles.listItem, styles.selectedListItem])\n   * // returns { flex: 1, fontSize: 16, color: 'green' }\n   * ```\n   */\n  flatten,\n\n  /**\n   * WARNING: EXPERIMENTAL. Breaking changes will probably happen a lot and will\n   * not be reliably announced. The whole thing might be deleted, who knows? Use\n   * at your own risk.\n   *\n   * Sets a function to use to pre-process a style property value. This is used\n   * internally to process color and transform values. You should not use this\n   * unless you really know what you are doing and have exhausted other options.\n   */\n  setStyleAttributePreprocessor(\n    property: string,\n    process: (nextProp: mixed) => mixed,\n  ) {\n    let value;\n\n    if (ReactNativeStyleAttributes[property] === true) {\n      value = {process};\n    } else if (typeof ReactNativeStyleAttributes[property] === 'object') {\n      value = {...ReactNativeStyleAttributes[property], process};\n    } else {\n      console.error(`${property} is not a valid style attribute`);\n      return;\n    }\n\n    if (\n      __DEV__ &&\n      typeof value.process === 'function' &&\n      typeof ReactNativeStyleAttributes[property]?.process === 'function' &&\n      value.process !== ReactNativeStyleAttributes[property]?.process\n    ) {\n      console.warn(`Overwriting ${property} style attribute preprocessor`);\n    }\n\n    ReactNativeStyleAttributes[property] = value;\n  },\n\n  /**\n   * An identity function for creating style sheets.\n   */\n  // $FlowFixMe[unsupported-variance-annotation]\n  create<+S: ____Styles_Internal>(obj: S): $ReadOnly<S> {\n    // TODO: This should return S as the return type. But first,\n    // we need to codemod all the callsites that are typing this\n    // return value as a number (even though it was opaque).\n    if (__DEV__) {\n      for (const key in obj) {\n        if (obj[key]) {\n          Object.freeze(obj[key]);\n        }\n      }\n    }\n    return obj;\n  },\n};\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAeb,IAAAC,cAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAEA,IAAMQ,0BAA0B,GAC9BR,OAAO,gDAAgD,CAAC,CAACK,OAAO;AAClE,IAAMI,UAAU,GAAGT,OAAO,0BAA0B,CAAC,CAACK,OAAO;AA0I7D,IAAIK,aAAqB,GAAGD,UAAU,CAACE,mBAAmB,CAAC,GAAG,CAAC;AAC/D,IAAID,aAAa,KAAK,CAAC,EAAE;EACvBA,aAAa,GAAG,CAAC,GAAGD,UAAU,CAACG,GAAG,CAAC,CAAC;AACtC;AAEA,IAAMC,YAML,GAAG;EACFC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,OAAO,EAAE;EACXlB,MAAM,CAACmB,MAAM,CAACP,YAAY,CAAC;AAC7B;AAAC,IAAAQ,QAAA,GAAAlB,OAAA,CAAAE,OAAA,GA0Cc;EAoBbK,aAAa,EAAbA,aAAa;EAObG,YAAY,EAAGA,YAAkB;EAcjCS,kBAAkB,EAAET,YAAY;EAQhCU,OAAO,EAAEC,sBAAa;EAsBtBC,OAAO,EAAPA,qBAAO;EAWPC,6BAA6B,WAA7BA,6BAA6BA,CAC3BC,QAAgB,EAChBC,OAAmC,EACnC;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACA,IAAI1B,KAAK;IAET,IAAII,0BAA0B,CAACmB,QAAQ,CAAC,KAAK,IAAI,EAAE;MACjDvB,KAAK,GAAG;QAACwB,OAAO,EAAPA;MAAO,CAAC;IACnB,CAAC,MAAM,IAAI,OAAOpB,0BAA0B,CAACmB,QAAQ,CAAC,KAAK,QAAQ,EAAE;MACnEvB,KAAK,GAAAH,MAAA,CAAA8B,MAAA,KAAOvB,0BAA0B,CAACmB,QAAQ,CAAC;QAAEC,OAAO,EAAPA;MAAO,EAAC;IAC5D,CAAC,MAAM;MACLI,OAAO,CAACC,KAAK,CAAC,GAAGN,QAAQ,iCAAiC,CAAC;MAC3D;IACF;IAEA,IACER,OAAO,IACP,OAAOf,KAAK,CAACwB,OAAO,KAAK,UAAU,IACnC,SAAAC,qBAAA,GAAOrB,0BAA0B,CAACmB,QAAQ,CAAC,qBAApCE,qBAAA,CAAsCD,OAAO,MAAK,UAAU,IACnExB,KAAK,CAACwB,OAAO,OAAAE,sBAAA,GAAKtB,0BAA0B,CAACmB,QAAQ,CAAC,qBAApCG,sBAAA,CAAsCF,OAAO,GAC/D;MACAI,OAAO,CAACE,IAAI,CAAC,eAAeP,QAAQ,+BAA+B,CAAC;IACtE;IAEAnB,0BAA0B,CAACmB,QAAQ,CAAC,GAAGvB,KAAK;EAC9C,CAAC;EAMD+B,MAAM,WAANA,MAAMA,CAA0BC,GAAM,EAAgB;IAIpD,IAAIjB,OAAO,EAAE;MACX,KAAK,IAAMkB,IAAG,IAAID,GAAG,EAAE;QACrB,IAAIA,GAAG,CAACC,IAAG,CAAC,EAAE;UACZpC,MAAM,CAACmB,MAAM,CAACgB,GAAG,CAACC,IAAG,CAAC,CAAC;QACzB;MACF;IACF;IACA,OAAOD,GAAG;EACZ;AACF,CAAC", "ignoreList": []}