/**
 * Canadian Address Input Component
 *
 * Comprehensive address input component designed for Canadian addresses
 * with proper validation, formatting, and auto-completion features.
 *
 * Features:
 * - Canadian postal code validation
 * - Province auto-detection
 * - Address standardization
 * - Auto-completion suggestions
 * - Bilingual support
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Body } from '../typography/Typography';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles } from '../../contexts/MotorAccessibilityContext';
import {
  CanadianAddress,
  CANADIAN_PROVINCES,
  CanadianProvince,
  validateCanadianPostalCode,
  formatCanadianPostalCode,
  getProvinceFromPostalCode,
  standardizeCanadianAddress,
  validateCanadianAddressCompleteness,
  searchCanadianAddresses,
} from '../../utils/canadianLocationUtils';

// Component props
export interface CanadianAddressInputProps {
  // Value and change handling
  value: Partial<CanadianAddress>;
  onChange: (address: Partial<CanadianAddress>) => void;
  
  // Configuration
  required?: boolean;
  showUnitField?: boolean;
  enableAutoComplete?: boolean;
  defaultProvince?: CanadianProvince;
  
  // Validation
  onValidationChange?: (isValid: boolean, errors: string[]) => void;
  
  // Styling
  style?: any;
  
  // Accessibility
  accessibilityLabel?: string;
  
  // Testing
  testID?: string;
}

export const CanadianAddressInput: React.FC<CanadianAddressInputProps> = ({
  value,
  onChange,
  required = false,
  showUnitField = true,
  enableAutoComplete = true,
  defaultProvince = 'ON',
  onValidationChange,
  style,
  accessibilityLabel,
  testID,
}) => {
  // Hooks
  const { t } = useTranslation();
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();

  // State
  const [suggestions, setSuggestions] = useState<CanadianAddress[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showProvinceModal, setShowProvinceModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Update field value
  const updateField = useCallback((field: keyof CanadianAddress, fieldValue: string) => {
    const updatedAddress = { ...value, [field]: fieldValue };
    
    // Auto-detect province from postal code
    if (field === 'postalCode' && fieldValue.length >= 3) {
      const detectedProvince = getProvinceFromPostalCode(fieldValue);
      if (detectedProvince) {
        updatedAddress.province = detectedProvince;
      }
    }
    
    // Format postal code
    if (field === 'postalCode') {
      updatedAddress.postalCode = formatCanadianPostalCode(fieldValue);
    }
    
    onChange(updatedAddress);
  }, [value, onChange]);

  // Validate address
  useEffect(() => {
    const validation = validateCanadianAddressCompleteness(value);
    const errors = [...validation.missingFields, ...validation.warnings];
    
    setValidationErrors(errors);
    
    if (onValidationChange) {
      onValidationChange(validation.isComplete, errors);
    }
  }, [value, onValidationChange]);

  // Handle address search
  const handleAddressSearch = useCallback(async (query: string) => {
    if (!enableAutoComplete || query.length < 3) {
      setSuggestions([]);
      return;
    }

    try {
      const results = await searchCanadianAddresses(query, value.province);
      setSuggestions(results.slice(0, 5)); // Limit to 5 suggestions
      setShowSuggestions(results.length > 0);
    } catch (error) {
      console.warn('Address search failed:', error);
      setSuggestions([]);
    }
  }, [enableAutoComplete, value.province]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: CanadianAddress) => {
    onChange(suggestion);
    setShowSuggestions(false);
    setSuggestions([]);
  }, [onChange]);

  // Render input field
  const renderInput = (
    field: keyof CanadianAddress,
    label: string,
    options: {
      placeholder?: string;
      keyboardType?: any;
      maxLength?: number;
      autoCapitalize?: any;
      multiline?: boolean;
      onSearch?: (query: string) => void;
    } = {}
  ) => {
    const fieldValue = (value[field] as string) || '';
    const hasError = required && !fieldValue.trim();

    return (
      <View style={styles.inputContainer}>
        <Typography
          variant="label"
          color={colors?.text?.primary}
          style={styles.inputLabel}
        >
          {label}
          {required && <Typography variant="label" color={colors?.status?.error}> *</Typography>}
        </Typography>
        
        <TextInput
          style={[
            styles.input,
            {
              borderColor: hasError ? colors?.status?.error : colors?.border?.primary,
              backgroundColor: colors?.background?.secondary,
              color: colors?.text?.primary,
            },
            options.multiline && styles.multilineInput,
          ]}
          value={fieldValue}
          onChangeText={(text) => {
            updateField(field, text);
            if (options.onSearch) {
              options.onSearch(text);
            }
          }}
          placeholder={options.placeholder}
          keyboardType={options.keyboardType}
          maxLength={options.maxLength}
          autoCapitalize={options.autoCapitalize}
          multiline={options.multiline}
          placeholderTextColor={colors?.text?.tertiary}
          accessibilityLabel={label}
          accessibilityHint={hasError ? t('validation.required') : undefined}
        />
      </View>
    );
  };

  // Render province selector
  const renderProvinceSelector = () => {
    const selectedProvince = value.province || defaultProvince;
    const provinceName = CANADIAN_PROVINCES[selectedProvince]?.name || selectedProvince;

    return (
      <View style={styles.inputContainer}>
        <Typography
          variant="label"
          color={colors?.text?.primary}
          style={styles.inputLabel}
        >
          {t('forms.province')}
          {required && <Typography variant="label" color={colors?.status?.error}> *</Typography>}
        </Typography>
        
        <TouchableOpacity
          style={[
            styles.input,
            styles.selectorInput,
            {
              borderColor: colors?.border?.primary,
              backgroundColor: colors?.background?.secondary,
            },
            touchTargetStyles,
          ]}
          onPress={() => setShowProvinceModal(true)}
          accessibilityRole="button"
          accessibilityLabel={`${t('forms.province')}: ${provinceName}`}
          accessibilityHint={t('forms.tap_to_select_province')}
        >
          <Typography
            variant="body1"
            color={colors?.text?.primary}
            style={styles.selectorText}
          >
            {provinceName}
          </Typography>
          
          <Ionicons
            name="chevron-down"
            size={20}
            color={colors?.text?.secondary}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // Render province modal
  const renderProvinceModal = () => {
    const provinces = Object.entries(CANADIAN_PROVINCES) as [CanadianProvince, typeof CANADIAN_PROVINCES[CanadianProvince]][];

    return (
      <Modal
        visible={showProvinceModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowProvinceModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors?.background?.primary }]}>
            <View style={styles.modalHeader}>
              <Typography
                variant="h3"
                color={colors?.text?.primary}
                style={styles.modalTitle}
              >
                {t('forms.select_province')}
              </Typography>
              
              <TouchableOpacity
                style={[styles.closeButton, touchTargetStyles]}
                onPress={() => setShowProvinceModal(false)}
                accessibilityRole="button"
                accessibilityLabel={t('common.close')}
              >
                <Ionicons
                  name="close"
                  size={24}
                  color={colors?.text?.secondary}
                />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={provinces}
              keyExtractor={([code]) => code}
              renderItem={({ item: [code, info] }) => (
                <TouchableOpacity
                  style={[
                    styles.provinceOption,
                    {
                      backgroundColor: code === value.province 
                        ? colors?.primary?.light 
                        : colors?.background?.primary,
                      borderColor: colors?.border?.primary,
                    },
                    touchTargetStyles,
                  ]}
                  onPress={() => {
                    updateField('province', code);
                    setShowProvinceModal(false);
                  }}
                  accessibilityRole="button"
                  accessibilityLabel={info.name}
                  accessibilityState={{ selected: code === value.province }}
                >
                  <Typography
                    variant="body1"
                    color={code === value.province ? colors?.primary?.default : colors?.text?.primary}
                    style={styles.provinceName}
                  >
                    {info.name}
                  </Typography>
                  
                  <Typography
                    variant="caption"
                    color={colors?.text?.secondary}
                    style={styles.provinceCode}
                  >
                    {code}
                  </Typography>
                </TouchableOpacity>
              )}
              style={styles.provinceList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    );
  };

  // Render address suggestions
  const renderSuggestions = () => {
    if (!showSuggestions || suggestions.length === 0) return null;

    return (
      <View style={[styles.suggestionsContainer, { backgroundColor: colors?.background?.primary }]}>
        <FlatList
          data={suggestions}
          keyExtractor={(item, index) => `${item.postalCode}-${index}`}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.suggestionItem,
                { borderColor: colors?.border?.primary },
                touchTargetStyles,
              ]}
              onPress={() => handleSuggestionSelect(item)}
              accessibilityRole="button"
              accessibilityLabel={`Select address: ${item.streetNumber} ${item.streetName}, ${item.city}`}
            >
              <Typography
                variant="body1"
                color={colors?.text?.primary}
                style={styles.suggestionAddress}
              >
                {item.streetNumber} {item.streetName} {item.streetType}
              </Typography>
              
              <Body
                color={colors?.text?.secondary}
                style={styles.suggestionLocation}
              >
                {item.city}, {item.province} {item.postalCode}
              </Body>
            </TouchableOpacity>
          )}
          style={styles.suggestionsList}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        />
      </View>
    );
  };

  return (
    <View style={[styles.container, style]} testID={testID}>
      {/* Unit/Apartment (optional) */}
      {showUnitField && (
        <View style={styles.row}>
          {renderInput('unitType', t('forms.unit_type'), {
            placeholder: t('forms.apt_suite_unit'),
            autoCapitalize: 'words',
          })}
          
          {renderInput('unitNumber', t('forms.unit_number'), {
            placeholder: '123',
            keyboardType: 'numeric',
          })}
        </View>
      )}

      {/* Street Address */}
      <View style={styles.row}>
        {renderInput('streetNumber', t('forms.street_number'), {
          placeholder: '123',
          keyboardType: 'numeric',
        })}
        
        {renderInput('streetName', t('forms.street_name'), {
          placeholder: t('forms.main_street'),
          autoCapitalize: 'words',
          onSearch: handleAddressSearch,
        })}
      </View>

      {/* Address Suggestions */}
      {renderSuggestions()}

      {/* City and Postal Code */}
      <View style={styles.row}>
        {renderInput('city', t('forms.city'), {
          placeholder: t('forms.city_name'),
          autoCapitalize: 'words',
        })}
        
        {renderInput('postalCode', t('forms.postal_code'), {
          placeholder: 'A1A 1A1',
          maxLength: 7,
          autoCapitalize: 'characters',
        })}
      </View>

      {/* Province Selector */}
      {renderProvinceSelector()}

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <View style={styles.errorsContainer}>
          {validationErrors.map((error, index) => (
            <Body
              key={index}
              color={colors?.status?.error}
              style={styles.errorText}
            >
              • {error}
            </Body>
          ))}
        </View>
      )}

      {/* Province Modal */}
      {renderProvinceModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 16,
  },
  inputLabel: {
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 48,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  selectorInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectorText: {
    flex: 1,
  },
  
  // Suggestions
  suggestionsContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginBottom: 16,
    maxHeight: 200,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    padding: 12,
    borderBottomWidth: 1,
  },
  suggestionAddress: {
    fontWeight: '600',
  },
  suggestionLocation: {
    marginTop: 4,
  },
  
  // Province Modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  provinceList: {
    maxHeight: 400,
  },
  provinceOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  provinceName: {
    flex: 1,
    fontWeight: '600',
  },
  provinceCode: {
    fontWeight: '600',
  },
  
  // Validation
  errorsContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#FFF5F5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FEB2B2',
  },
  errorText: {
    marginBottom: 4,
    fontSize: 12,
  },
});

export default CanadianAddressInput;
