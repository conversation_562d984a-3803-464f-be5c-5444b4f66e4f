efd8386a9259272288e5c3c4dedfd94f
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateTouchTargetSpacing = exports.validateTouchTarget = exports.getRecommendedTouchTargetSize = exports.getRecommendedSpacing = exports.getPlatformMinimumTouchTarget = exports.getMotorAccessibilitySupport = exports.generateTouchTargetStyles = exports.generateGestureFriendlyArea = exports.default = exports.calculateTouchArea = exports.calculateAdaptiveTouchTargetSize = exports.TOUCH_TARGET_SPACING = exports.TOUCH_TARGET_SIZES = void 0;
var _reactNative = require("react-native");
var TOUCH_TARGET_SIZES = exports.TOUCH_TARGET_SIZES = {
  MINIMUM: 44,
  COMFORTABLE: 48,
  LARGE: 56,
  EXTRA_LARGE: 64,
  IOS_MINIMUM: 44,
  ANDROID_MINIMUM: 48,
  ICON_BUTTON: 44,
  TEXT_BUTTON: 44,
  FAB: 56,
  NAVIGATION_ITEM: 48,
  FORM_INPUT: 44
};
var TOUCH_TARGET_SPACING = exports.TOUCH_TARGET_SPACING = {
  MINIMUM: 8,
  COMFORTABLE: 12,
  LARGE: 16
};
var getPlatformMinimumTouchTarget = exports.getPlatformMinimumTouchTarget = function getPlatformMinimumTouchTarget() {
  return _reactNative.Platform.select({
    ios: TOUCH_TARGET_SIZES.IOS_MINIMUM,
    android: TOUCH_TARGET_SIZES.ANDROID_MINIMUM,
    default: TOUCH_TARGET_SIZES.MINIMUM
  });
};
var getRecommendedTouchTargetSize = exports.getRecommendedTouchTargetSize = function getRecommendedTouchTargetSize() {
  var motorLevel = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'none';
  switch (motorLevel) {
    case 'none':
      return TOUCH_TARGET_SIZES.COMFORTABLE;
    case 'mild':
      return TOUCH_TARGET_SIZES.LARGE;
    case 'moderate':
      return TOUCH_TARGET_SIZES.EXTRA_LARGE;
    case 'severe':
      return TOUCH_TARGET_SIZES.EXTRA_LARGE + 8;
    default:
      return TOUCH_TARGET_SIZES.COMFORTABLE;
  }
};
var getRecommendedSpacing = exports.getRecommendedSpacing = function getRecommendedSpacing() {
  var motorLevel = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'none';
  switch (motorLevel) {
    case 'none':
      return TOUCH_TARGET_SPACING.MINIMUM;
    case 'mild':
      return TOUCH_TARGET_SPACING.COMFORTABLE;
    case 'moderate':
    case 'severe':
      return TOUCH_TARGET_SPACING.LARGE;
    default:
      return TOUCH_TARGET_SPACING.MINIMUM;
  }
};
var validateTouchTarget = exports.validateTouchTarget = function validateTouchTarget(width, height) {
  var motorLevel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'none';
  var issues = [];
  var recommendations = [];
  var minSize = getPlatformMinimumTouchTarget();
  var recommendedSize = getRecommendedTouchTargetSize(motorLevel);
  if (width < minSize || height < minSize) {
    issues.push(`Touch target too small: ${width}x${height}px (minimum: ${minSize}x${minSize}px)`);
    recommendations.push(`Increase touch target size to at least ${minSize}x${minSize}px`);
  }
  if (motorLevel !== 'none' && (width < recommendedSize || height < recommendedSize)) {
    recommendations.push(`For ${motorLevel} motor difficulty, consider increasing to ${recommendedSize}x${recommendedSize}px`);
  }
  var aspectRatio = Math.max(width, height) / Math.min(width, height);
  if (aspectRatio > 3) {
    recommendations.push('Consider more balanced aspect ratio for better usability');
  }
  return {
    valid: issues.length === 0,
    issues: issues,
    recommendations: recommendations
  };
};
var calculateTouchArea = exports.calculateTouchArea = function calculateTouchArea(contentWidth, contentHeight) {
  var motorLevel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'none';
  var minTouchSize = getRecommendedTouchTargetSize(motorLevel);
  var paddingHorizontal = Math.max(0, (minTouchSize - contentWidth) / 2);
  var paddingVertical = Math.max(0, (minTouchSize - contentHeight) / 2);
  return {
    width: Math.max(contentWidth, minTouchSize),
    height: Math.max(contentHeight, minTouchSize),
    paddingHorizontal: paddingHorizontal,
    paddingVertical: paddingVertical,
    minTouchableArea: minTouchSize
  };
};
var generateTouchTargetStyles = exports.generateTouchTargetStyles = function generateTouchTargetStyles() {
  var contentSize = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var motorLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
  var minSize = getRecommendedTouchTargetSize(motorLevel);
  var spacing = getRecommendedSpacing(motorLevel);
  return {
    minWidth: contentSize.width ? Math.max(contentSize.width, minSize) : minSize,
    minHeight: contentSize.height ? Math.max(contentSize.height, minSize) : minSize,
    paddingHorizontal: contentSize.width ? Math.max(0, (minSize - contentSize.width) / 2) : 0,
    paddingVertical: contentSize.height ? Math.max(0, (minSize - contentSize.height) / 2) : 0,
    marginHorizontal: spacing / 2,
    marginVertical: spacing / 2
  };
};
var getMotorAccessibilitySupport = exports.getMotorAccessibilitySupport = function getMotorAccessibilitySupport() {
  return {
    supportsHapticFeedback: _reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android',
    supportsVoiceControl: _reactNative.Platform.OS === 'ios',
    supportsSwitchControl: _reactNative.Platform.OS === 'ios',
    supportsAssistiveTouch: _reactNative.Platform.OS === 'ios',
    screenSize: _reactNative.Dimensions.get('window'),
    isLargeScreen: _reactNative.Dimensions.get('window').width > 768,
    recommendLargerTargets: true,
    recommendHapticFeedback: true,
    recommendVoiceAlternatives: true
  };
};
var calculateAdaptiveTouchTargetSize = exports.calculateAdaptiveTouchTargetSize = function calculateAdaptiveTouchTargetSize(baseSize) {
  var userInteractionData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var adaptedSize = baseSize;
  if (userInteractionData.missRate && userInteractionData.missRate > 0.1) {
    adaptedSize += Math.min(16, userInteractionData.missRate * 80);
  }
  if (userInteractionData.averageAccuracy && userInteractionData.averageAccuracy < 0.8) {
    adaptedSize += Math.min(12, (1 - userInteractionData.averageAccuracy) * 60);
  }
  if (userInteractionData.preferredSize) {
    adaptedSize = Math.max(adaptedSize, userInteractionData.preferredSize);
  }
  adaptedSize = Math.max(adaptedSize, getPlatformMinimumTouchTarget());
  return Math.round(adaptedSize);
};
var generateGestureFriendlyArea = exports.generateGestureFriendlyArea = function generateGestureFriendlyArea(gestureType) {
  var motorLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
  var baseSize = getRecommendedTouchTargetSize(motorLevel);
  switch (gestureType) {
    case 'tap':
      return {
        width: baseSize,
        height: baseSize,
        hitSlop: {
          top: 8,
          bottom: 8,
          left: 8,
          right: 8
        }
      };
    case 'swipe':
      return {
        width: baseSize * 1.5,
        height: baseSize,
        hitSlop: {
          top: 12,
          bottom: 12,
          left: 16,
          right: 16
        }
      };
    case 'pinch':
      return {
        width: baseSize * 2,
        height: baseSize * 2,
        hitSlop: {
          top: 16,
          bottom: 16,
          left: 16,
          right: 16
        }
      };
    case 'long-press':
      return {
        width: baseSize * 1.2,
        height: baseSize * 1.2,
        hitSlop: {
          top: 10,
          bottom: 10,
          left: 10,
          right: 10
        }
      };
    case 'drag':
      return {
        width: baseSize * 1.3,
        height: baseSize * 1.3,
        hitSlop: {
          top: 12,
          bottom: 12,
          left: 12,
          right: 12
        }
      };
    default:
      return {
        width: baseSize,
        height: baseSize,
        hitSlop: {
          top: 8,
          bottom: 8,
          left: 8,
          right: 8
        }
      };
  }
};
var validateTouchTargetSpacing = exports.validateTouchTargetSpacing = function validateTouchTargetSpacing(targets) {
  var motorLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
  var minSpacing = getRecommendedSpacing(motorLevel);
  var conflicts = [];
  var recommendations = [];
  for (var i = 0; i < targets.length; i++) {
    for (var j = i + 1; j < targets.length; j++) {
      var target1 = targets[i];
      var target2 = targets[j];
      var horizontalDistance = Math.max(0, Math.max(target1.x, target2.x) - Math.min(target1.x + target1.width, target2.x + target2.width));
      var verticalDistance = Math.max(0, Math.max(target1.y, target2.y) - Math.min(target1.y + target1.height, target2.y + target2.height));
      var distance = Math.sqrt(horizontalDistance ** 2 + verticalDistance ** 2);
      if (distance < minSpacing) {
        conflicts.push({
          target1: i,
          target2: j,
          distance: distance
        });
      }
    }
  }
  if (conflicts.length > 0) {
    recommendations.push(`Increase spacing between touch targets to at least ${minSpacing}px`);
    if (motorLevel !== 'none') {
      recommendations.push(`Consider additional spacing for ${motorLevel} motor accessibility`);
    }
  }
  return {
    valid: conflicts.length === 0,
    conflicts: conflicts,
    recommendations: recommendations
  };
};
var _default = exports.default = {
  TOUCH_TARGET_SIZES: TOUCH_TARGET_SIZES,
  TOUCH_TARGET_SPACING: TOUCH_TARGET_SPACING,
  getPlatformMinimumTouchTarget: getPlatformMinimumTouchTarget,
  getRecommendedTouchTargetSize: getRecommendedTouchTargetSize,
  getRecommendedSpacing: getRecommendedSpacing,
  validateTouchTarget: validateTouchTarget,
  calculateTouchArea: calculateTouchArea,
  generateTouchTargetStyles: generateTouchTargetStyles,
  getMotorAccessibilitySupport: getMotorAccessibilitySupport,
  calculateAdaptiveTouchTargetSize: calculateAdaptiveTouchTargetSize,
  generateGestureFriendlyArea: generateGestureFriendlyArea,
  validateTouchTargetSpacing: validateTouchTargetSpacing
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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