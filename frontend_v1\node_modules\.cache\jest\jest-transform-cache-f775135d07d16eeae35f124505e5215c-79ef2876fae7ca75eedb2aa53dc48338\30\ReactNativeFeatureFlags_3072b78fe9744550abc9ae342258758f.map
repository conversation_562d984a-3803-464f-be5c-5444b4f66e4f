{"version": 3, "names": ["_ReactNativeFeatureFlagsBase", "require", "jsOnlyTestFlag", "exports", "createJavaScriptFlagGetter", "animatedShouldDebounceQueueFlush", "animatedShouldUseSingleOp", "avoidStateUpdateInAnimatedPropsMemo", "disableInteractionManager", "enableAccessToHostTreeInFabric", "enableAnimatedClearImmediateFix", "enableDOMDocumentAPI", "fixVirtualizeListCollapseWindowSize", "isLayoutAnimationEnabled", "scheduleAnimatedCleanupInMicrotask", "shouldUseAnimatedObjectForTransform", "shouldUseRemoveClippedSubviewsAsDefaultOnIOS", "shouldUseSetNativePropsInFabric", "useRefsForTextInputState", "commonTestFlag", "createNativeFlagGetter", "commonTestFlagWithoutNativeImplementation", "disableMountItemReorderingAndroid", "enableAccumulatedUpdatesInRawPropsAndroid", "enableBridgelessArchitecture", "enableCppPropsIteratorSetter", "enableEagerRootViewAttachment", "enableFabricLogs", "enableFab<PERSON><PERSON><PERSON><PERSON>", "enableIOSViewClipToPaddingBox", "enableImagePrefetchingAndroid", "enableJSRuntimeGCOnMemoryPressureOnIOS", "enableLayoutAnimationsOnAndroid", "enableLayoutAnimationsOnIOS", "enableLongTaskAPI", "enableNativeCSSParsing", "enableNewBackgroundAndBorderDrawables", "enablePreciseSchedulingForPremountItemsOnAndroid", "enablePropsUpdateReconciliationAndroid", "enableReportEventPaintTime", "enableSynchronousStateUpdates", "enableUIConsistency", "enableViewCulling", "enableViewRecycling", "enableViewRecyclingForText", "enableViewRecyclingForView", "excludeYogaFromRawProps", "fixDifferentiatorEmittingUpdatesWithWrongParentTag", "fixMappingOfEventPrioritiesBetweenFabricAndReact", "fixMountingCoordinatorReportedPendingTransactionsOnAndroid", "fuseboxEnabledRelease", "fuseboxNetworkInspectionEnabled", "lazyAnimationCallbacks", "removeTurboModuleManagerDelegateMutex", "throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS", "traceTurboModulePromiseRejectionsOnAndroid", "updateRuntimeShadowNodeReferencesOnCommit", "useAlwaysAvailableJSErrorHandling", "useFabricInterop", "useNativeViewConfigsInBridgelessMode", "useOptimizedEventBatchingOnAndroid", "useRawPropsJsiValue", "useShadowNodeStateOnClone", "useTurboModuleInterop", "useTurboModules", "override", "setOverrides"], "sources": ["ReactNativeFeatureFlags.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @generated SignedSource<<a28a3b729eaeee7ba3b1c2105d277286>>\n * @flow strict\n */\n\n/**\n * IMPORTANT: Do NOT modify this file directly.\n *\n * To change the definition of the flags, edit\n *   packages/react-native/scripts/featureflags/ReactNativeFeatureFlags.config.js.\n *\n * To regenerate this code, run the following script from the repo root:\n *   yarn featureflags --update\n */\n\nimport {\n  type Getter,\n  type OverridesFor,\n  createJavaScriptFlagGetter,\n  createNativeFlagGetter,\n  setOverrides,\n} from './ReactNativeFeatureFlagsBase';\n\nexport type ReactNativeFeatureFlagsJsOnly = $ReadOnly<{\n  jsOnlyTestFlag: Getter<boolean>,\n  animatedShouldDebounceQueueFlush: Getter<boolean>,\n  animatedShouldUseSingleOp: Getter<boolean>,\n  avoidStateUpdateInAnimatedPropsMemo: Getter<boolean>,\n  disableInteractionManager: Getter<boolean>,\n  enableAccessToHostTreeInFabric: Getter<boolean>,\n  enableAnimatedClearImmediateFix: Getter<boolean>,\n  enableDOMDocumentAPI: Getter<boolean>,\n  fixVirtualizeListCollapseWindowSize: Getter<boolean>,\n  isLayoutAnimationEnabled: Getter<boolean>,\n  scheduleAnimatedCleanupInMicrotask: Getter<boolean>,\n  shouldUseAnimatedObjectForTransform: Getter<boolean>,\n  shouldUseRemoveClippedSubviewsAsDefaultOnIOS: Getter<boolean>,\n  shouldUseSetNativePropsInFabric: Getter<boolean>,\n  useRefsForTextInputState: Getter<boolean>,\n}>;\n\nexport type ReactNativeFeatureFlagsJsOnlyOverrides = OverridesFor<ReactNativeFeatureFlagsJsOnly>;\n\nexport type ReactNativeFeatureFlags = $ReadOnly<{\n  ...ReactNativeFeatureFlagsJsOnly,\n  commonTestFlag: Getter<boolean>,\n  commonTestFlagWithoutNativeImplementation: Getter<boolean>,\n  disableMountItemReorderingAndroid: Getter<boolean>,\n  enableAccumulatedUpdatesInRawPropsAndroid: Getter<boolean>,\n  enableBridgelessArchitecture: Getter<boolean>,\n  enableCppPropsIteratorSetter: Getter<boolean>,\n  enableEagerRootViewAttachment: Getter<boolean>,\n  enableFabricLogs: Getter<boolean>,\n  enableFabricRenderer: Getter<boolean>,\n  enableIOSViewClipToPaddingBox: Getter<boolean>,\n  enableImagePrefetchingAndroid: Getter<boolean>,\n  enableJSRuntimeGCOnMemoryPressureOnIOS: Getter<boolean>,\n  enableLayoutAnimationsOnAndroid: Getter<boolean>,\n  enableLayoutAnimationsOnIOS: Getter<boolean>,\n  enableLongTaskAPI: Getter<boolean>,\n  enableNativeCSSParsing: Getter<boolean>,\n  enableNewBackgroundAndBorderDrawables: Getter<boolean>,\n  enablePreciseSchedulingForPremountItemsOnAndroid: Getter<boolean>,\n  enablePropsUpdateReconciliationAndroid: Getter<boolean>,\n  enableReportEventPaintTime: Getter<boolean>,\n  enableSynchronousStateUpdates: Getter<boolean>,\n  enableUIConsistency: Getter<boolean>,\n  enableViewCulling: Getter<boolean>,\n  enableViewRecycling: Getter<boolean>,\n  enableViewRecyclingForText: Getter<boolean>,\n  enableViewRecyclingForView: Getter<boolean>,\n  excludeYogaFromRawProps: Getter<boolean>,\n  fixDifferentiatorEmittingUpdatesWithWrongParentTag: Getter<boolean>,\n  fixMappingOfEventPrioritiesBetweenFabricAndReact: Getter<boolean>,\n  fixMountingCoordinatorReportedPendingTransactionsOnAndroid: Getter<boolean>,\n  fuseboxEnabledRelease: Getter<boolean>,\n  fuseboxNetworkInspectionEnabled: Getter<boolean>,\n  lazyAnimationCallbacks: Getter<boolean>,\n  removeTurboModuleManagerDelegateMutex: Getter<boolean>,\n  throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS: Getter<boolean>,\n  traceTurboModulePromiseRejectionsOnAndroid: Getter<boolean>,\n  updateRuntimeShadowNodeReferencesOnCommit: Getter<boolean>,\n  useAlwaysAvailableJSErrorHandling: Getter<boolean>,\n  useFabricInterop: Getter<boolean>,\n  useNativeViewConfigsInBridgelessMode: Getter<boolean>,\n  useOptimizedEventBatchingOnAndroid: Getter<boolean>,\n  useRawPropsJsiValue: Getter<boolean>,\n  useShadowNodeStateOnClone: Getter<boolean>,\n  useTurboModuleInterop: Getter<boolean>,\n  useTurboModules: Getter<boolean>,\n}>;\n\n/**\n * JS-only flag for testing. Do NOT modify.\n */\nexport const jsOnlyTestFlag: Getter<boolean> = createJavaScriptFlagGetter('jsOnlyTestFlag', false);\n\n/**\n * Enables an experimental flush-queue debouncing in Animated.js.\n */\nexport const animatedShouldDebounceQueueFlush: Getter<boolean> = createJavaScriptFlagGetter('animatedShouldDebounceQueueFlush', false);\n\n/**\n * Enables an experimental mega-operation for Animated.js that replaces many calls to native with a single call into native, to reduce JSI/JNI traffic.\n */\nexport const animatedShouldUseSingleOp: Getter<boolean> = createJavaScriptFlagGetter('animatedShouldUseSingleOp', false);\n\n/**\n * Changes `useAnimatedPropsMemo` to avoid state updates to invalidate the cached `AnimatedProps`.\n */\nexport const avoidStateUpdateInAnimatedPropsMemo: Getter<boolean> = createJavaScriptFlagGetter('avoidStateUpdateInAnimatedPropsMemo', false);\n\n/**\n * Disables InteractionManager and replaces its scheduler with `setImmediate`.\n */\nexport const disableInteractionManager: Getter<boolean> = createJavaScriptFlagGetter('disableInteractionManager', false);\n\n/**\n * Enables access to the host tree in Fabric using DOM-compatible APIs.\n */\nexport const enableAccessToHostTreeInFabric: Getter<boolean> = createJavaScriptFlagGetter('enableAccessToHostTreeInFabric', false);\n\n/**\n * Enables an experimental to use the proper clearIntermediate instead of calling the wrong clearTimeout and canceling another timer.\n */\nexport const enableAnimatedClearImmediateFix: Getter<boolean> = createJavaScriptFlagGetter('enableAnimatedClearImmediateFix', true);\n\n/**\n * Enables the DOM Document API, exposing instaces of document through `getRootNode` and `ownerDocument`, and providing access to the `documentElement` representing the root node. This flag will be short-lived, only to test the Document API specifically, and then it will be collapsed into the enableAccessToHostTreeInFabric flag.\n */\nexport const enableDOMDocumentAPI: Getter<boolean> = createJavaScriptFlagGetter('enableDOMDocumentAPI', false);\n\n/**\n * Fixing an edge case where the current window size is not properly calculated with fast scrolling. Window size collapsed to 1 element even if windowSize more than the current amount of elements\n */\nexport const fixVirtualizeListCollapseWindowSize: Getter<boolean> = createJavaScriptFlagGetter('fixVirtualizeListCollapseWindowSize', false);\n\n/**\n * Function used to enable / disabled Layout Animations in React Native.\n */\nexport const isLayoutAnimationEnabled: Getter<boolean> = createJavaScriptFlagGetter('isLayoutAnimationEnabled', true);\n\n/**\n * Changes the cleanup of`AnimatedProps` to occur in a microtask instead of synchronously during effect cleanup (for unmount) or subsequent mounts (for updates).\n */\nexport const scheduleAnimatedCleanupInMicrotask: Getter<boolean> = createJavaScriptFlagGetter('scheduleAnimatedCleanupInMicrotask', false);\n\n/**\n * Enables use of AnimatedObject for animating transform values.\n */\nexport const shouldUseAnimatedObjectForTransform: Getter<boolean> = createJavaScriptFlagGetter('shouldUseAnimatedObjectForTransform', false);\n\n/**\n * removeClippedSubviews prop will be used as the default in FlatList on iOS to match Android\n */\nexport const shouldUseRemoveClippedSubviewsAsDefaultOnIOS: Getter<boolean> = createJavaScriptFlagGetter('shouldUseRemoveClippedSubviewsAsDefaultOnIOS', false);\n\n/**\n * Enables use of setNativeProps in JS driven animations.\n */\nexport const shouldUseSetNativePropsInFabric: Getter<boolean> = createJavaScriptFlagGetter('shouldUseSetNativePropsInFabric', true);\n\n/**\n * Enable a variant of TextInput that moves some state to refs to avoid unnecessary re-renders\n */\nexport const useRefsForTextInputState: Getter<boolean> = createJavaScriptFlagGetter('useRefsForTextInputState', false);\n\n/**\n * Common flag for testing. Do NOT modify.\n */\nexport const commonTestFlag: Getter<boolean> = createNativeFlagGetter('commonTestFlag', false);\n/**\n * Common flag for testing (without native implementation). Do NOT modify.\n */\nexport const commonTestFlagWithoutNativeImplementation: Getter<boolean> = createNativeFlagGetter('commonTestFlagWithoutNativeImplementation', false);\n/**\n * Prevent FabricMountingManager from reordering mountitems, which may lead to invalid state on the UI thread\n */\nexport const disableMountItemReorderingAndroid: Getter<boolean> = createNativeFlagGetter('disableMountItemReorderingAndroid', false);\n/**\n * When enabled, Andoid will accumulate updates in rawProps to reduce the number of mounting instructions for cascading rerenders.\n */\nexport const enableAccumulatedUpdatesInRawPropsAndroid: Getter<boolean> = createNativeFlagGetter('enableAccumulatedUpdatesInRawPropsAndroid', false);\n/**\n * Feature flag to enable the new bridgeless architecture. Note: Enabling this will force enable the following flags: `useTurboModules` & `enableFabricRenderer.\n */\nexport const enableBridgelessArchitecture: Getter<boolean> = createNativeFlagGetter('enableBridgelessArchitecture', false);\n/**\n * Enable prop iterator setter-style construction of Props in C++ (this flag is not used in Java).\n */\nexport const enableCppPropsIteratorSetter: Getter<boolean> = createNativeFlagGetter('enableCppPropsIteratorSetter', false);\n/**\n * Feature flag to configure eager attachment of the root view/initialisation of the JS code.\n */\nexport const enableEagerRootViewAttachment: Getter<boolean> = createNativeFlagGetter('enableEagerRootViewAttachment', false);\n/**\n * This feature flag enables logs for Fabric.\n */\nexport const enableFabricLogs: Getter<boolean> = createNativeFlagGetter('enableFabricLogs', false);\n/**\n * Enables the use of the Fabric renderer in the whole app.\n */\nexport const enableFabricRenderer: Getter<boolean> = createNativeFlagGetter('enableFabricRenderer', false);\n/**\n * iOS Views will clip to their padding box vs border box\n */\nexport const enableIOSViewClipToPaddingBox: Getter<boolean> = createNativeFlagGetter('enableIOSViewClipToPaddingBox', false);\n/**\n * When enabled, Andoid will build and initiate image prefetch requests on ImageShadowNode::layout\n */\nexport const enableImagePrefetchingAndroid: Getter<boolean> = createNativeFlagGetter('enableImagePrefetchingAndroid', false);\n/**\n * Trigger JS runtime GC on memory pressure event on iOS\n */\nexport const enableJSRuntimeGCOnMemoryPressureOnIOS: Getter<boolean> = createNativeFlagGetter('enableJSRuntimeGCOnMemoryPressureOnIOS', false);\n/**\n * When enabled, LayoutAnimations API will animate state changes on Android.\n */\nexport const enableLayoutAnimationsOnAndroid: Getter<boolean> = createNativeFlagGetter('enableLayoutAnimationsOnAndroid', false);\n/**\n * When enabled, LayoutAnimations API will animate state changes on iOS.\n */\nexport const enableLayoutAnimationsOnIOS: Getter<boolean> = createNativeFlagGetter('enableLayoutAnimationsOnIOS', true);\n/**\n * Enables the reporting of long tasks through `PerformanceObserver`. Only works if the event loop is enabled.\n */\nexport const enableLongTaskAPI: Getter<boolean> = createNativeFlagGetter('enableLongTaskAPI', false);\n/**\n * Parse CSS strings using the Fabric CSS parser instead of ViewConfig processing\n */\nexport const enableNativeCSSParsing: Getter<boolean> = createNativeFlagGetter('enableNativeCSSParsing', false);\n/**\n * Use BackgroundDrawable and BorderDrawable instead of CSSBackgroundDrawable\n */\nexport const enableNewBackgroundAndBorderDrawables: Getter<boolean> = createNativeFlagGetter('enableNewBackgroundAndBorderDrawables', false);\n/**\n * Moves execution of pre-mount items to outside the choregrapher in the main thread, so we can estimate idle time more precisely (Android only).\n */\nexport const enablePreciseSchedulingForPremountItemsOnAndroid: Getter<boolean> = createNativeFlagGetter('enablePreciseSchedulingForPremountItemsOnAndroid', false);\n/**\n * When enabled, Android will receive prop updates based on the differences between the last rendered shadow node and the last committed shadow node.\n */\nexport const enablePropsUpdateReconciliationAndroid: Getter<boolean> = createNativeFlagGetter('enablePropsUpdateReconciliationAndroid', false);\n/**\n * Report paint time inside the Event Timing API implementation (PerformanceObserver).\n */\nexport const enableReportEventPaintTime: Getter<boolean> = createNativeFlagGetter('enableReportEventPaintTime', false);\n/**\n * Dispatches state updates synchronously in Fabric (e.g.: updates the scroll position in the shadow tree synchronously from the main thread).\n */\nexport const enableSynchronousStateUpdates: Getter<boolean> = createNativeFlagGetter('enableSynchronousStateUpdates', false);\n/**\n * Ensures that JavaScript always has a consistent view of the state of the UI (e.g.: commits done in other threads are not immediately propagated to JS during its execution).\n */\nexport const enableUIConsistency: Getter<boolean> = createNativeFlagGetter('enableUIConsistency', false);\n/**\n * Enables View Culling: as soon as a view goes off screen, it can be reused anywhere in the UI and pieced together with other items to create new UI elements.\n */\nexport const enableViewCulling: Getter<boolean> = createNativeFlagGetter('enableViewCulling', false);\n/**\n * Enables View Recycling. When enabled, individual ViewManagers must still opt-in.\n */\nexport const enableViewRecycling: Getter<boolean> = createNativeFlagGetter('enableViewRecycling', false);\n/**\n * Enables View Recycling for <Text> via ReactTextView/ReactTextViewManager.\n */\nexport const enableViewRecyclingForText: Getter<boolean> = createNativeFlagGetter('enableViewRecyclingForText', true);\n/**\n * Enables View Recycling for <View> via ReactViewGroup/ReactViewManager.\n */\nexport const enableViewRecyclingForView: Getter<boolean> = createNativeFlagGetter('enableViewRecyclingForView', true);\n/**\n * When enabled, rawProps in Props will not include Yoga specific props.\n */\nexport const excludeYogaFromRawProps: Getter<boolean> = createNativeFlagGetter('excludeYogaFromRawProps', false);\n/**\n * Fixes a bug in Differentiator where parent views may be referenced before they're created\n */\nexport const fixDifferentiatorEmittingUpdatesWithWrongParentTag: Getter<boolean> = createNativeFlagGetter('fixDifferentiatorEmittingUpdatesWithWrongParentTag', true);\n/**\n * Uses the default event priority instead of the discreet event priority by default when dispatching events from Fabric to React.\n */\nexport const fixMappingOfEventPrioritiesBetweenFabricAndReact: Getter<boolean> = createNativeFlagGetter('fixMappingOfEventPrioritiesBetweenFabricAndReact', false);\n/**\n * Fixes a limitation on Android where the mounting coordinator would report there are no pending transactions but some of them were actually not processed due to the use of the push model.\n */\nexport const fixMountingCoordinatorReportedPendingTransactionsOnAndroid: Getter<boolean> = createNativeFlagGetter('fixMountingCoordinatorReportedPendingTransactionsOnAndroid', false);\n/**\n * Flag determining if the React Native DevTools (Fusebox) CDP backend should be enabled in release builds. This flag is global and should not be changed across React Host lifetimes.\n */\nexport const fuseboxEnabledRelease: Getter<boolean> = createNativeFlagGetter('fuseboxEnabledRelease', false);\n/**\n * Enable network inspection support in the React Native DevTools CDP backend. This flag is global and should not be changed across React Host lifetimes.\n */\nexport const fuseboxNetworkInspectionEnabled: Getter<boolean> = createNativeFlagGetter('fuseboxNetworkInspectionEnabled', false);\n/**\n * Only enqueue Choreographer calls if there is an ongoing animation, instead of enqueueing every frame.\n */\nexport const lazyAnimationCallbacks: Getter<boolean> = createNativeFlagGetter('lazyAnimationCallbacks', false);\n/**\n * When enabled, mutex _turboModuleManagerDelegateMutex in RCTTurboModuleManager will not be used\n */\nexport const removeTurboModuleManagerDelegateMutex: Getter<boolean> = createNativeFlagGetter('removeTurboModuleManagerDelegateMutex', false);\n/**\n * Throw an exception instead of deadlocking when a TurboModule that requires main queue setup is initialized during a synchronous render on iOS.\n */\nexport const throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS: Getter<boolean> = createNativeFlagGetter('throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS', false);\n/**\n * Enables storing js caller stack when creating promise in native module. This is useful in case of Promise rejection and tracing the cause.\n */\nexport const traceTurboModulePromiseRejectionsOnAndroid: Getter<boolean> = createNativeFlagGetter('traceTurboModulePromiseRejectionsOnAndroid', false);\n/**\n * When enabled, runtime shadow node references will be updated during the commit. This allows running RSNRU from any thread without corrupting the renderer state.\n */\nexport const updateRuntimeShadowNodeReferencesOnCommit: Getter<boolean> = createNativeFlagGetter('updateRuntimeShadowNodeReferencesOnCommit', false);\n/**\n * In Bridgeless mode, use the always available javascript error reporting pipeline.\n */\nexport const useAlwaysAvailableJSErrorHandling: Getter<boolean> = createNativeFlagGetter('useAlwaysAvailableJSErrorHandling', false);\n/**\n * Should this application enable the Fabric Interop Layer for Android? If yes, the application will behave so that it can accept non-Fabric components and render them on Fabric. This toggle is controlling extra logic such as custom event dispatching that are needed for the Fabric Interop Layer to work correctly.\n */\nexport const useFabricInterop: Getter<boolean> = createNativeFlagGetter('useFabricInterop', false);\n/**\n * When enabled, the native view configs are used in bridgeless mode.\n */\nexport const useNativeViewConfigsInBridgelessMode: Getter<boolean> = createNativeFlagGetter('useNativeViewConfigsInBridgelessMode', false);\n/**\n * Uses an optimized mechanism for event batching on Android that does not need to wait for a Choreographer frame callback.\n */\nexport const useOptimizedEventBatchingOnAndroid: Getter<boolean> = createNativeFlagGetter('useOptimizedEventBatchingOnAndroid', false);\n/**\n * Instead of using folly::dynamic as internal representation in RawProps and RawValue, use jsi::Value\n */\nexport const useRawPropsJsiValue: Getter<boolean> = createNativeFlagGetter('useRawPropsJsiValue', false);\n/**\n * Use the state stored on the source shadow node when cloning it instead of reading in the most recent state on the shadow node family.\n */\nexport const useShadowNodeStateOnClone: Getter<boolean> = createNativeFlagGetter('useShadowNodeStateOnClone', false);\n/**\n * In Bridgeless mode, should legacy NativeModules use the TurboModule system?\n */\nexport const useTurboModuleInterop: Getter<boolean> = createNativeFlagGetter('useTurboModuleInterop', false);\n/**\n * When enabled, NativeModules will be executed by using the TurboModule system\n */\nexport const useTurboModules: Getter<boolean> = createNativeFlagGetter('useTurboModules', false);\n\n/**\n * Overrides the feature flags with the provided methods.\n * NOTE: Only JS-only flags can be overridden from JavaScript using this API.\n */\nexport const override = setOverrides;\n"], "mappings": ";;;;AAoBA,IAAAA,4BAAA,GAAAC,OAAA;AAgFO,IAAMC,cAA+B,GAAAC,OAAA,CAAAD,cAAA,GAAG,IAAAE,uDAA0B,EAAC,gBAAgB,EAAE,KAAK,CAAC;AAK3F,IAAMC,gCAAiD,GAAAF,OAAA,CAAAE,gCAAA,GAAG,IAAAD,uDAA0B,EAAC,kCAAkC,EAAE,KAAK,CAAC;AAK/H,IAAME,yBAA0C,GAAAH,OAAA,CAAAG,yBAAA,GAAG,IAAAF,uDAA0B,EAAC,2BAA2B,EAAE,KAAK,CAAC;AAKjH,IAAMG,mCAAoD,GAAAJ,OAAA,CAAAI,mCAAA,GAAG,IAAAH,uDAA0B,EAAC,qCAAqC,EAAE,KAAK,CAAC;AAKrI,IAAMI,yBAA0C,GAAAL,OAAA,CAAAK,yBAAA,GAAG,IAAAJ,uDAA0B,EAAC,2BAA2B,EAAE,KAAK,CAAC;AAKjH,IAAMK,8BAA+C,GAAAN,OAAA,CAAAM,8BAAA,GAAG,IAAAL,uDAA0B,EAAC,gCAAgC,EAAE,KAAK,CAAC;AAK3H,IAAMM,+BAAgD,GAAAP,OAAA,CAAAO,+BAAA,GAAG,IAAAN,uDAA0B,EAAC,iCAAiC,EAAE,IAAI,CAAC;AAK5H,IAAMO,oBAAqC,GAAAR,OAAA,CAAAQ,oBAAA,GAAG,IAAAP,uDAA0B,EAAC,sBAAsB,EAAE,KAAK,CAAC;AAKvG,IAAMQ,mCAAoD,GAAAT,OAAA,CAAAS,mCAAA,GAAG,IAAAR,uDAA0B,EAAC,qCAAqC,EAAE,KAAK,CAAC;AAKrI,IAAMS,wBAAyC,GAAAV,OAAA,CAAAU,wBAAA,GAAG,IAAAT,uDAA0B,EAAC,0BAA0B,EAAE,IAAI,CAAC;AAK9G,IAAMU,kCAAmD,GAAAX,OAAA,CAAAW,kCAAA,GAAG,IAAAV,uDAA0B,EAAC,oCAAoC,EAAE,KAAK,CAAC;AAKnI,IAAMW,mCAAoD,GAAAZ,OAAA,CAAAY,mCAAA,GAAG,IAAAX,uDAA0B,EAAC,qCAAqC,EAAE,KAAK,CAAC;AAKrI,IAAMY,4CAA6D,GAAAb,OAAA,CAAAa,4CAAA,GAAG,IAAAZ,uDAA0B,EAAC,8CAA8C,EAAE,KAAK,CAAC;AAKvJ,IAAMa,+BAAgD,GAAAd,OAAA,CAAAc,+BAAA,GAAG,IAAAb,uDAA0B,EAAC,iCAAiC,EAAE,IAAI,CAAC;AAK5H,IAAMc,wBAAyC,GAAAf,OAAA,CAAAe,wBAAA,GAAG,IAAAd,uDAA0B,EAAC,0BAA0B,EAAE,KAAK,CAAC;AAK/G,IAAMe,cAA+B,GAAAhB,OAAA,CAAAgB,cAAA,GAAG,IAAAC,mDAAsB,EAAC,gBAAgB,EAAE,KAAK,CAAC;AAIvF,IAAMC,yCAA0D,GAAAlB,OAAA,CAAAkB,yCAAA,GAAG,IAAAD,mDAAsB,EAAC,2CAA2C,EAAE,KAAK,CAAC;AAI7I,IAAME,iCAAkD,GAAAnB,OAAA,CAAAmB,iCAAA,GAAG,IAAAF,mDAAsB,EAAC,mCAAmC,EAAE,KAAK,CAAC;AAI7H,IAAMG,yCAA0D,GAAApB,OAAA,CAAAoB,yCAAA,GAAG,IAAAH,mDAAsB,EAAC,2CAA2C,EAAE,KAAK,CAAC;AAI7I,IAAMI,4BAA6C,GAAArB,OAAA,CAAAqB,4BAAA,GAAG,IAAAJ,mDAAsB,EAAC,8BAA8B,EAAE,KAAK,CAAC;AAInH,IAAMK,4BAA6C,GAAAtB,OAAA,CAAAsB,4BAAA,GAAG,IAAAL,mDAAsB,EAAC,8BAA8B,EAAE,KAAK,CAAC;AAInH,IAAMM,6BAA8C,GAAAvB,OAAA,CAAAuB,6BAAA,GAAG,IAAAN,mDAAsB,EAAC,+BAA+B,EAAE,KAAK,CAAC;AAIrH,IAAMO,gBAAiC,GAAAxB,OAAA,CAAAwB,gBAAA,GAAG,IAAAP,mDAAsB,EAAC,kBAAkB,EAAE,KAAK,CAAC;AAI3F,IAAMQ,oBAAqC,GAAAzB,OAAA,CAAAyB,oBAAA,GAAG,IAAAR,mDAAsB,EAAC,sBAAsB,EAAE,KAAK,CAAC;AAInG,IAAMS,6BAA8C,GAAA1B,OAAA,CAAA0B,6BAAA,GAAG,IAAAT,mDAAsB,EAAC,+BAA+B,EAAE,KAAK,CAAC;AAIrH,IAAMU,6BAA8C,GAAA3B,OAAA,CAAA2B,6BAAA,GAAG,IAAAV,mDAAsB,EAAC,+BAA+B,EAAE,KAAK,CAAC;AAIrH,IAAMW,sCAAuD,GAAA5B,OAAA,CAAA4B,sCAAA,GAAG,IAAAX,mDAAsB,EAAC,wCAAwC,EAAE,KAAK,CAAC;AAIvI,IAAMY,+BAAgD,GAAA7B,OAAA,CAAA6B,+BAAA,GAAG,IAAAZ,mDAAsB,EAAC,iCAAiC,EAAE,KAAK,CAAC;AAIzH,IAAMa,2BAA4C,GAAA9B,OAAA,CAAA8B,2BAAA,GAAG,IAAAb,mDAAsB,EAAC,6BAA6B,EAAE,IAAI,CAAC;AAIhH,IAAMc,iBAAkC,GAAA/B,OAAA,CAAA+B,iBAAA,GAAG,IAAAd,mDAAsB,EAAC,mBAAmB,EAAE,KAAK,CAAC;AAI7F,IAAMe,sBAAuC,GAAAhC,OAAA,CAAAgC,sBAAA,GAAG,IAAAf,mDAAsB,EAAC,wBAAwB,EAAE,KAAK,CAAC;AAIvG,IAAMgB,qCAAsD,GAAAjC,OAAA,CAAAiC,qCAAA,GAAG,IAAAhB,mDAAsB,EAAC,uCAAuC,EAAE,KAAK,CAAC;AAIrI,IAAMiB,gDAAiE,GAAAlC,OAAA,CAAAkC,gDAAA,GAAG,IAAAjB,mDAAsB,EAAC,kDAAkD,EAAE,KAAK,CAAC;AAI3J,IAAMkB,sCAAuD,GAAAnC,OAAA,CAAAmC,sCAAA,GAAG,IAAAlB,mDAAsB,EAAC,wCAAwC,EAAE,KAAK,CAAC;AAIvI,IAAMmB,0BAA2C,GAAApC,OAAA,CAAAoC,0BAAA,GAAG,IAAAnB,mDAAsB,EAAC,4BAA4B,EAAE,KAAK,CAAC;AAI/G,IAAMoB,6BAA8C,GAAArC,OAAA,CAAAqC,6BAAA,GAAG,IAAApB,mDAAsB,EAAC,+BAA+B,EAAE,KAAK,CAAC;AAIrH,IAAMqB,mBAAoC,GAAAtC,OAAA,CAAAsC,mBAAA,GAAG,IAAArB,mDAAsB,EAAC,qBAAqB,EAAE,KAAK,CAAC;AAIjG,IAAMsB,iBAAkC,GAAAvC,OAAA,CAAAuC,iBAAA,GAAG,IAAAtB,mDAAsB,EAAC,mBAAmB,EAAE,KAAK,CAAC;AAI7F,IAAMuB,mBAAoC,GAAAxC,OAAA,CAAAwC,mBAAA,GAAG,IAAAvB,mDAAsB,EAAC,qBAAqB,EAAE,KAAK,CAAC;AAIjG,IAAMwB,0BAA2C,GAAAzC,OAAA,CAAAyC,0BAAA,GAAG,IAAAxB,mDAAsB,EAAC,4BAA4B,EAAE,IAAI,CAAC;AAI9G,IAAMyB,0BAA2C,GAAA1C,OAAA,CAAA0C,0BAAA,GAAG,IAAAzB,mDAAsB,EAAC,4BAA4B,EAAE,IAAI,CAAC;AAI9G,IAAM0B,uBAAwC,GAAA3C,OAAA,CAAA2C,uBAAA,GAAG,IAAA1B,mDAAsB,EAAC,yBAAyB,EAAE,KAAK,CAAC;AAIzG,IAAM2B,kDAAmE,GAAA5C,OAAA,CAAA4C,kDAAA,GAAG,IAAA3B,mDAAsB,EAAC,oDAAoD,EAAE,IAAI,CAAC;AAI9J,IAAM4B,gDAAiE,GAAA7C,OAAA,CAAA6C,gDAAA,GAAG,IAAA5B,mDAAsB,EAAC,kDAAkD,EAAE,KAAK,CAAC;AAI3J,IAAM6B,0DAA2E,GAAA9C,OAAA,CAAA8C,0DAAA,GAAG,IAAA7B,mDAAsB,EAAC,4DAA4D,EAAE,KAAK,CAAC;AAI/K,IAAM8B,qBAAsC,GAAA/C,OAAA,CAAA+C,qBAAA,GAAG,IAAA9B,mDAAsB,EAAC,uBAAuB,EAAE,KAAK,CAAC;AAIrG,IAAM+B,+BAAgD,GAAAhD,OAAA,CAAAgD,+BAAA,GAAG,IAAA/B,mDAAsB,EAAC,iCAAiC,EAAE,KAAK,CAAC;AAIzH,IAAMgC,sBAAuC,GAAAjD,OAAA,CAAAiD,sBAAA,GAAG,IAAAhC,mDAAsB,EAAC,wBAAwB,EAAE,KAAK,CAAC;AAIvG,IAAMiC,qCAAsD,GAAAlD,OAAA,CAAAkD,qCAAA,GAAG,IAAAjC,mDAAsB,EAAC,uCAAuC,EAAE,KAAK,CAAC;AAIrI,IAAMkC,oEAAqF,GAAAnD,OAAA,CAAAmD,oEAAA,GAAG,IAAAlC,mDAAsB,EAAC,sEAAsE,EAAE,KAAK,CAAC;AAInM,IAAMmC,0CAA2D,GAAApD,OAAA,CAAAoD,0CAAA,GAAG,IAAAnC,mDAAsB,EAAC,4CAA4C,EAAE,KAAK,CAAC;AAI/I,IAAMoC,yCAA0D,GAAArD,OAAA,CAAAqD,yCAAA,GAAG,IAAApC,mDAAsB,EAAC,2CAA2C,EAAE,KAAK,CAAC;AAI7I,IAAMqC,iCAAkD,GAAAtD,OAAA,CAAAsD,iCAAA,GAAG,IAAArC,mDAAsB,EAAC,mCAAmC,EAAE,KAAK,CAAC;AAI7H,IAAMsC,gBAAiC,GAAAvD,OAAA,CAAAuD,gBAAA,GAAG,IAAAtC,mDAAsB,EAAC,kBAAkB,EAAE,KAAK,CAAC;AAI3F,IAAMuC,oCAAqD,GAAAxD,OAAA,CAAAwD,oCAAA,GAAG,IAAAvC,mDAAsB,EAAC,sCAAsC,EAAE,KAAK,CAAC;AAInI,IAAMwC,kCAAmD,GAAAzD,OAAA,CAAAyD,kCAAA,GAAG,IAAAxC,mDAAsB,EAAC,oCAAoC,EAAE,KAAK,CAAC;AAI/H,IAAMyC,mBAAoC,GAAA1D,OAAA,CAAA0D,mBAAA,GAAG,IAAAzC,mDAAsB,EAAC,qBAAqB,EAAE,KAAK,CAAC;AAIjG,IAAM0C,yBAA0C,GAAA3D,OAAA,CAAA2D,yBAAA,GAAG,IAAA1C,mDAAsB,EAAC,2BAA2B,EAAE,KAAK,CAAC;AAI7G,IAAM2C,qBAAsC,GAAA5D,OAAA,CAAA4D,qBAAA,GAAG,IAAA3C,mDAAsB,EAAC,uBAAuB,EAAE,KAAK,CAAC;AAIrG,IAAM4C,eAAgC,GAAA7D,OAAA,CAAA6D,eAAA,GAAG,IAAA5C,mDAAsB,EAAC,iBAAiB,EAAE,KAAK,CAAC;AAMzF,IAAM6C,QAAQ,GAAA9D,OAAA,CAAA8D,QAAA,GAAGC,yCAAY", "ignoreList": []}