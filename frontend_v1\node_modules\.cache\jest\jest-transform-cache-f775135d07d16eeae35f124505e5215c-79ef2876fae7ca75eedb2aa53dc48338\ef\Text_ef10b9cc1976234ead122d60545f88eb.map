{"version": 3, "names": ["PressabilityDebug", "_interopRequireWildcard", "require", "_usePressability", "_interopRequireDefault", "_flattenStyle", "_processColor", "_Platform", "_TextAncestor", "_TextNativeComponent", "_react", "React", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Text", "forwardRef", "_ref", "forwardedRef", "_accessibilityState2", "accessible", "accessibilityLabel", "accessibilityState", "allowFontScaling", "ariaBusy", "ariaChe<PERSON>", "ariaDisabled", "ariaExpanded", "aria<PERSON><PERSON><PERSON>", "ariaSelected", "children", "ellipsizeMode", "disabled", "id", "nativeID", "numberOfLines", "onLongPress", "onPress", "onPressIn", "onPressOut", "onResponderGrant", "onResponderMove", "onResponderRelease", "onResponderTerminate", "onResponderTerminationRequest", "onStartShouldSetResponder", "pressRetentionOffset", "selectable", "selectionColor", "suppressHighlighting", "style", "restProps", "_objectWithoutProperties2", "_accessibilityLabel", "_accessibilityState", "busy", "checked", "expanded", "selected", "_accessibilityStateDisabled", "_disabled", "isPressable", "_selectionColor", "processColor", "undefined", "_style", "__DEV__", "isEnabled", "color", "_numberOfLines", "console", "error", "_selectable", "processedStyle", "flattenStyle", "overrides", "fontWeight", "toString", "userSelect", "userSelectToSelectableMap", "verticalAlign", "textAlignVertical", "verticalAlignToTextAlignVerticalMap", "_nativeID", "hasTextAncestor", "useContext", "TextAncestor", "jsx", "NativePressableVirtualText", "ref", "textProps", "assign", "textPressabilityProps", "NativeVirtualText", "_accessible", "Platform", "select", "ios", "android", "nativeText", "NativePressableText", "NativeText", "Array", "isArray", "length", "hasNonTextChild", "child", "Provider", "value", "displayName", "useTextPressability", "_ref2", "_useState", "useState", "_useState2", "_slicedToArray2", "isHighlighted", "setHighlighted", "config", "useMemo", "_onPressIn", "_onPressOut", "OS", "event", "pressRectOffset", "eventHandlers", "usePressability", "eventHandlersForText", "onClick", "_ref3", "_useTextPressability", "_useTextPressability2", "_ref4", "_useTextPressability3", "_useTextPressability4", "auto", "text", "none", "contain", "all", "top", "bottom", "middle", "_default", "exports"], "sources": ["Text.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {TextStyleProp} from '../StyleSheet/StyleSheet';\nimport type {____TextStyle_Internal as TextStyleInternal} from '../StyleSheet/StyleSheetTypes';\nimport type {GestureResponderEvent} from '../Types/CoreEventTypes';\nimport type {NativeTextProps} from './TextNativeComponent';\nimport type {PressRetentionOffset, TextProps} from './TextProps';\n\nimport * as PressabilityDebug from '../Pressability/PressabilityDebug';\nimport usePressability from '../Pressability/usePressability';\nimport flattenStyle from '../StyleSheet/flattenStyle';\nimport processColor from '../StyleSheet/processColor';\nimport Platform from '../Utilities/Platform';\nimport TextAncestor from './TextAncestor';\nimport {NativeText, NativeVirtualText} from './TextNativeComponent';\nimport * as React from 'react';\nimport {useContext, useMemo, useState} from 'react';\n\nexport type {TextProps} from './TextProps';\n\ntype TextForwardRef = React.ElementRef<\n  typeof NativeText | typeof NativeVirtualText,\n>;\n\n/**\n * Text is the fundamental component for displaying text.\n *\n * @see https://reactnative.dev/docs/text\n */\nconst Text: component(\n  ref?: React.RefSetter<TextForwardRef>,\n  ...props: TextProps\n) = React.forwardRef(\n  (\n    {\n      accessible,\n      accessibilityLabel,\n      accessibilityState,\n      allowFontScaling,\n      'aria-busy': ariaBusy,\n      'aria-checked': ariaChecked,\n      'aria-disabled': ariaDisabled,\n      'aria-expanded': ariaExpanded,\n      'aria-label': ariaLabel,\n      'aria-selected': ariaSelected,\n      children,\n      ellipsizeMode,\n      disabled,\n      id,\n      nativeID,\n      numberOfLines,\n      onLongPress,\n      onPress,\n      onPressIn,\n      onPressOut,\n      onResponderGrant,\n      onResponderMove,\n      onResponderRelease,\n      onResponderTerminate,\n      onResponderTerminationRequest,\n      onStartShouldSetResponder,\n      pressRetentionOffset,\n      selectable,\n      selectionColor,\n      suppressHighlighting,\n      style,\n      ...restProps\n    }: TextProps,\n    forwardedRef,\n  ) => {\n    const _accessibilityLabel = ariaLabel ?? accessibilityLabel;\n\n    let _accessibilityState: ?TextProps['accessibilityState'] =\n      accessibilityState;\n    if (\n      ariaBusy != null ||\n      ariaChecked != null ||\n      ariaDisabled != null ||\n      ariaExpanded != null ||\n      ariaSelected != null\n    ) {\n      if (_accessibilityState != null) {\n        _accessibilityState = {\n          busy: ariaBusy ?? _accessibilityState.busy,\n          checked: ariaChecked ?? _accessibilityState.checked,\n          disabled: ariaDisabled ?? _accessibilityState.disabled,\n          expanded: ariaExpanded ?? _accessibilityState.expanded,\n          selected: ariaSelected ?? _accessibilityState.selected,\n        };\n      } else {\n        _accessibilityState = {\n          busy: ariaBusy,\n          checked: ariaChecked,\n          disabled: ariaDisabled,\n          expanded: ariaExpanded,\n          selected: ariaSelected,\n        };\n      }\n    }\n\n    const _accessibilityStateDisabled = _accessibilityState?.disabled;\n    const _disabled = disabled ?? _accessibilityStateDisabled;\n\n    const isPressable =\n      (onPress != null ||\n        onLongPress != null ||\n        onStartShouldSetResponder != null) &&\n      _disabled !== true;\n\n    // TODO: Move this processing to the view configuration.\n    const _selectionColor =\n      selectionColor != null ? processColor(selectionColor) : undefined;\n\n    let _style = style;\n    if (__DEV__) {\n      if (PressabilityDebug.isEnabled() && onPress != null) {\n        _style = [style, {color: 'magenta'}];\n      }\n    }\n\n    let _numberOfLines = numberOfLines;\n    if (_numberOfLines != null && !(_numberOfLines >= 0)) {\n      if (__DEV__) {\n        console.error(\n          `'numberOfLines' in <Text> must be a non-negative number, received: ${_numberOfLines}. The value will be set to 0.`,\n        );\n      }\n      _numberOfLines = 0;\n    }\n\n    let _selectable = selectable;\n\n    let processedStyle = flattenStyle<TextStyleProp>(_style);\n    if (processedStyle != null) {\n      let overrides: ?{...TextStyleInternal} = null;\n      if (typeof processedStyle.fontWeight === 'number') {\n        overrides = overrides || ({}: {...TextStyleInternal});\n        overrides.fontWeight =\n          // $FlowFixMe[incompatible-cast]\n          (processedStyle.fontWeight.toString(): TextStyleInternal['fontWeight']);\n      }\n\n      if (processedStyle.userSelect != null) {\n        _selectable = userSelectToSelectableMap[processedStyle.userSelect];\n        overrides = overrides || ({}: {...TextStyleInternal});\n        overrides.userSelect = undefined;\n      }\n\n      if (processedStyle.verticalAlign != null) {\n        overrides = overrides || ({}: {...TextStyleInternal});\n        overrides.textAlignVertical =\n          verticalAlignToTextAlignVerticalMap[processedStyle.verticalAlign];\n        overrides.verticalAlign = undefined;\n      }\n\n      if (overrides != null) {\n        // $FlowFixMe[incompatible-type]\n        _style = [_style, overrides];\n      }\n    }\n\n    const _nativeID = id ?? nativeID;\n\n    const hasTextAncestor = useContext(TextAncestor);\n    if (hasTextAncestor) {\n      if (isPressable) {\n        return (\n          <NativePressableVirtualText\n            ref={forwardedRef}\n            textProps={{\n              ...restProps,\n              accessibilityLabel: _accessibilityLabel,\n              accessibilityState: _accessibilityState,\n              nativeID: _nativeID,\n              numberOfLines: _numberOfLines,\n              selectable: _selectable,\n              selectionColor: _selectionColor,\n              style: _style,\n              disabled: disabled,\n              children,\n            }}\n            textPressabilityProps={{\n              onLongPress,\n              onPress,\n              onPressIn,\n              onPressOut,\n              onResponderGrant,\n              onResponderMove,\n              onResponderRelease,\n              onResponderTerminate,\n              onResponderTerminationRequest,\n              onStartShouldSetResponder,\n              pressRetentionOffset,\n              suppressHighlighting,\n            }}\n          />\n        );\n      }\n\n      return (\n        <NativeVirtualText\n          {...restProps}\n          accessibilityLabel={_accessibilityLabel}\n          accessibilityState={_accessibilityState}\n          nativeID={_nativeID}\n          numberOfLines={_numberOfLines}\n          ref={forwardedRef}\n          selectable={_selectable}\n          selectionColor={_selectionColor}\n          style={_style}\n          disabled={disabled}>\n          {children}\n        </NativeVirtualText>\n      );\n    }\n\n    // If the disabled prop and accessibilityState.disabled are out of sync but not both in\n    // falsy states we need to update the accessibilityState object to use the disabled prop.\n    if (\n      _disabled !== _accessibilityStateDisabled &&\n      ((_disabled != null && _disabled !== false) ||\n        (_accessibilityStateDisabled != null &&\n          _accessibilityStateDisabled !== false))\n    ) {\n      _accessibilityState = {..._accessibilityState, disabled: _disabled};\n    }\n\n    const _accessible = Platform.select({\n      ios: accessible !== false,\n      android:\n        accessible == null\n          ? onPress != null || onLongPress != null\n          : accessible,\n      default: accessible,\n    });\n\n    let nativeText = null;\n    if (isPressable) {\n      nativeText = (\n        <NativePressableText\n          ref={forwardedRef}\n          textProps={{\n            ...restProps,\n            accessibilityLabel: _accessibilityLabel,\n            accessibilityState: _accessibilityState,\n            accessible: _accessible,\n            allowFontScaling: allowFontScaling !== false,\n            disabled: _disabled,\n            ellipsizeMode: ellipsizeMode ?? 'tail',\n            nativeID: _nativeID,\n            numberOfLines: _numberOfLines,\n            selectable: _selectable,\n            selectionColor: _selectionColor,\n            style: _style,\n            children,\n          }}\n          textPressabilityProps={{\n            onLongPress,\n            onPress,\n            onPressIn,\n            onPressOut,\n            onResponderGrant,\n            onResponderMove,\n            onResponderRelease,\n            onResponderTerminate,\n            onResponderTerminationRequest,\n            onStartShouldSetResponder,\n            pressRetentionOffset,\n            suppressHighlighting,\n          }}\n        />\n      );\n    } else {\n      nativeText = (\n        <NativeText\n          {...restProps}\n          accessibilityLabel={_accessibilityLabel}\n          accessibilityState={_accessibilityState}\n          accessible={_accessible}\n          allowFontScaling={allowFontScaling !== false}\n          disabled={_disabled}\n          ellipsizeMode={ellipsizeMode ?? 'tail'}\n          nativeID={_nativeID}\n          numberOfLines={_numberOfLines}\n          ref={forwardedRef}\n          selectable={_selectable}\n          selectionColor={_selectionColor}\n          style={_style}>\n          {children}\n        </NativeText>\n      );\n    }\n\n    if (children == null) {\n      return nativeText;\n    }\n\n    // If the children do not contain a JSX element it would not be possible to have a\n    // nested `Text` component so we can skip adding the `TextAncestor` context wrapper\n    // which has a performance overhead. Since we do this for performance reasons we need\n    // to keep the check simple to avoid regressing overall perf. For this reason the\n    // `children.length` constant is set to `3`, this should be a reasonable tradeoff\n    // to capture the majority of `Text` uses but also not make this check too expensive.\n    if (Array.isArray(children) && children.length <= 3) {\n      let hasNonTextChild = false;\n      for (let child of children) {\n        if (child != null && typeof child === 'object') {\n          hasNonTextChild = true;\n          break;\n        }\n      }\n      if (!hasNonTextChild) {\n        return nativeText;\n      }\n    } else if (typeof children !== 'object') {\n      return nativeText;\n    }\n\n    return (\n      <TextAncestor.Provider value={true}>{nativeText}</TextAncestor.Provider>\n    );\n  },\n);\n\nText.displayName = 'Text';\n\ntype TextPressabilityProps = $ReadOnly<{\n  onLongPress?: ?(event: GestureResponderEvent) => mixed,\n  onPress?: ?(event: GestureResponderEvent) => mixed,\n  onPressIn?: ?(event: GestureResponderEvent) => mixed,\n  onPressOut?: ?(event: GestureResponderEvent) => mixed,\n  onResponderGrant?: ?(event: GestureResponderEvent) => void,\n  onResponderMove?: ?(event: GestureResponderEvent) => void,\n  onResponderRelease?: ?(event: GestureResponderEvent) => void,\n  onResponderTerminate?: ?(event: GestureResponderEvent) => void,\n  onResponderTerminationRequest?: ?() => boolean,\n  onStartShouldSetResponder?: ?() => boolean,\n  pressRetentionOffset?: ?PressRetentionOffset,\n  suppressHighlighting?: ?boolean,\n}>;\n\n/**\n * Hook that handles setting up Pressability of Text components.\n *\n * NOTE: This hook is relatively expensive so it should only be used absolutely necessary.\n */\nfunction useTextPressability({\n  onLongPress,\n  onPress,\n  onPressIn,\n  onPressOut,\n  onResponderGrant,\n  onResponderMove,\n  onResponderRelease,\n  onResponderTerminate,\n  onResponderTerminationRequest,\n  onStartShouldSetResponder,\n  pressRetentionOffset,\n  suppressHighlighting,\n}: TextPressabilityProps) {\n  const [isHighlighted, setHighlighted] = useState(false);\n\n  // Setup pressability config and wrap callbacks needs to track the highlight state.\n  const config = useMemo(() => {\n    let _onPressIn = onPressIn;\n    let _onPressOut = onPressOut;\n\n    // Updating isHighlighted causes unnecessary re-renders for platforms that don't use it\n    // in the best case, and cause issues with text selection in the worst case. Forcing\n    // the isHighlighted prop to false on all platforms except iOS.\n    if (Platform.OS === 'ios') {\n      _onPressIn = (event: GestureResponderEvent) => {\n        setHighlighted(suppressHighlighting == null || !suppressHighlighting);\n        onPressIn?.(event);\n      };\n\n      _onPressOut = (event: GestureResponderEvent) => {\n        setHighlighted(false);\n        onPressOut?.(event);\n      };\n    }\n\n    return {\n      disabled: false,\n      pressRectOffset: pressRetentionOffset,\n      onLongPress,\n      onPress,\n      onPressIn: _onPressIn,\n      onPressOut: _onPressOut,\n    };\n  }, [\n    pressRetentionOffset,\n    onLongPress,\n    onPress,\n    onPressIn,\n    onPressOut,\n    suppressHighlighting,\n  ]);\n\n  // Init the pressability class\n  const eventHandlers = usePressability(config);\n\n  // Create NativeText event handlers which proxy events to pressability\n  const eventHandlersForText = useMemo(\n    () =>\n      eventHandlers == null\n        ? null\n        : {\n            onResponderGrant(event: GestureResponderEvent) {\n              eventHandlers.onResponderGrant(event);\n              if (onResponderGrant != null) {\n                onResponderGrant(event);\n              }\n            },\n            onResponderMove(event: GestureResponderEvent) {\n              eventHandlers.onResponderMove(event);\n              if (onResponderMove != null) {\n                onResponderMove(event);\n              }\n            },\n            onResponderRelease(event: GestureResponderEvent) {\n              eventHandlers.onResponderRelease(event);\n              if (onResponderRelease != null) {\n                onResponderRelease(event);\n              }\n            },\n            onResponderTerminate(event: GestureResponderEvent) {\n              eventHandlers.onResponderTerminate(event);\n              if (onResponderTerminate != null) {\n                onResponderTerminate(event);\n              }\n            },\n            onClick: eventHandlers.onClick,\n            onResponderTerminationRequest:\n              onResponderTerminationRequest != null\n                ? onResponderTerminationRequest\n                : eventHandlers.onResponderTerminationRequest,\n            onStartShouldSetResponder:\n              onStartShouldSetResponder != null\n                ? onStartShouldSetResponder\n                : eventHandlers.onStartShouldSetResponder,\n          },\n    [\n      eventHandlers,\n      onResponderGrant,\n      onResponderMove,\n      onResponderRelease,\n      onResponderTerminate,\n      onResponderTerminationRequest,\n      onStartShouldSetResponder,\n    ],\n  );\n\n  // Return the highlight state and NativeText event handlers\n  return useMemo(\n    () => [isHighlighted, eventHandlersForText],\n    [isHighlighted, eventHandlersForText],\n  );\n}\n\ntype NativePressableTextProps = $ReadOnly<{\n  textProps: NativeTextProps,\n  textPressabilityProps: TextPressabilityProps,\n}>;\n\n/**\n * Wrap the NativeVirtualText component and initialize pressability.\n *\n * This logic is split out from the main Text component to enable the more\n * expensive pressability logic to be only initialized when needed.\n */\nconst NativePressableVirtualText: component(\n  ref: React.RefSetter<TextForwardRef>,\n  ...props: NativePressableTextProps\n) = React.forwardRef(({textProps, textPressabilityProps}, forwardedRef) => {\n  const [isHighlighted, eventHandlersForText] = useTextPressability(\n    textPressabilityProps,\n  );\n\n  return (\n    <NativeVirtualText\n      {...textProps}\n      {...eventHandlersForText}\n      isHighlighted={isHighlighted}\n      isPressable={true}\n      ref={forwardedRef}\n    />\n  );\n});\n\n/**\n * Wrap the NativeText component and initialize pressability.\n *\n * This logic is split out from the main Text component to enable the more\n * expensive pressability logic to be only initialized when needed.\n */\nconst NativePressableText: component(\n  ref: React.RefSetter<TextForwardRef>,\n  ...props: NativePressableTextProps\n) = React.forwardRef(({textProps, textPressabilityProps}, forwardedRef) => {\n  const [isHighlighted, eventHandlersForText] = useTextPressability(\n    textPressabilityProps,\n  );\n\n  return (\n    <NativeText\n      {...textProps}\n      {...eventHandlersForText}\n      isHighlighted={isHighlighted}\n      isPressable={true}\n      ref={forwardedRef}\n    />\n  );\n});\n\nconst userSelectToSelectableMap = {\n  auto: true,\n  text: true,\n  none: false,\n  contain: true,\n  all: true,\n};\n\nconst verticalAlignToTextAlignVerticalMap = {\n  auto: 'auto',\n  top: 'top',\n  bottom: 'bottom',\n  middle: 'center',\n};\n\nexport default Text;\n"], "mappings": ";;;;;;;AAgBA,IAAAA,iBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,aAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,SAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,aAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,oBAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAT,uBAAA,CAAAC,OAAA;AAA+B,IAAAS,KAAA,GAAAD,MAAA;AAAA,IAAAE,WAAA,GAAAV,OAAA;AAAA,IAAAW,SAAA;AAAA,SAAAZ,wBAAAa,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAf,uBAAA,YAAAA,wBAAAa,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAc/B,IAAMmB,IAGL,GAAGvB,KAAK,CAACwB,UAAU,CAClB,UAAAC,IAAA,EAmCEC,YAAY,EACT;EAAA,IAAAC,oBAAA;EAAA,IAlCDC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IACVC,kBAAkB,GAAAJ,IAAA,CAAlBI,kBAAkB;IAClBC,kBAAkB,GAAAL,IAAA,CAAlBK,kBAAkB;IAClBC,gBAAgB,GAAAN,IAAA,CAAhBM,gBAAgB;IACHC,QAAQ,GAAAP,IAAA,CAArB,WAAW;IACKQ,WAAW,GAAAR,IAAA,CAA3B,cAAc;IACGS,YAAY,GAAAT,IAAA,CAA7B,eAAe;IACEU,YAAY,GAAAV,IAAA,CAA7B,eAAe;IACDW,SAAS,GAAAX,IAAA,CAAvB,YAAY;IACKY,YAAY,GAAAZ,IAAA,CAA7B,eAAe;IACfa,QAAQ,GAAAb,IAAA,CAARa,QAAQ;IACRC,aAAa,GAAAd,IAAA,CAAbc,aAAa;IACbC,QAAQ,GAAAf,IAAA,CAARe,QAAQ;IACRC,EAAE,GAAAhB,IAAA,CAAFgB,EAAE;IACFC,QAAQ,GAAAjB,IAAA,CAARiB,QAAQ;IACRC,aAAa,GAAAlB,IAAA,CAAbkB,aAAa;IACbC,WAAW,GAAAnB,IAAA,CAAXmB,WAAW;IACXC,OAAO,GAAApB,IAAA,CAAPoB,OAAO;IACPC,SAAS,GAAArB,IAAA,CAATqB,SAAS;IACTC,UAAU,GAAAtB,IAAA,CAAVsB,UAAU;IACVC,gBAAgB,GAAAvB,IAAA,CAAhBuB,gBAAgB;IAChBC,eAAe,GAAAxB,IAAA,CAAfwB,eAAe;IACfC,kBAAkB,GAAAzB,IAAA,CAAlByB,kBAAkB;IAClBC,oBAAoB,GAAA1B,IAAA,CAApB0B,oBAAoB;IACpBC,6BAA6B,GAAA3B,IAAA,CAA7B2B,6BAA6B;IAC7BC,yBAAyB,GAAA5B,IAAA,CAAzB4B,yBAAyB;IACzBC,oBAAoB,GAAA7B,IAAA,CAApB6B,oBAAoB;IACpBC,UAAU,GAAA9B,IAAA,CAAV8B,UAAU;IACVC,cAAc,GAAA/B,IAAA,CAAd+B,cAAc;IACdC,oBAAoB,GAAAhC,IAAA,CAApBgC,oBAAoB;IACpBC,KAAK,GAAAjC,IAAA,CAALiC,KAAK;IACFC,SAAS,OAAAC,yBAAA,CAAA/C,OAAA,EAAAY,IAAA,EAAAvB,SAAA;EAId,IAAM2D,mBAAmB,GAAGzB,SAAS,WAATA,SAAS,GAAIP,kBAAkB;EAE3D,IAAIiC,mBAAqD,GACvDhC,kBAAkB;EACpB,IACEE,QAAQ,IAAI,IAAI,IAChBC,WAAW,IAAI,IAAI,IACnBC,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,IACpBE,YAAY,IAAI,IAAI,EACpB;IACA,IAAIyB,mBAAmB,IAAI,IAAI,EAAE;MAC/BA,mBAAmB,GAAG;QACpBC,IAAI,EAAE/B,QAAQ,WAARA,QAAQ,GAAI8B,mBAAmB,CAACC,IAAI;QAC1CC,OAAO,EAAE/B,WAAW,WAAXA,WAAW,GAAI6B,mBAAmB,CAACE,OAAO;QACnDxB,QAAQ,EAAEN,YAAY,WAAZA,YAAY,GAAI4B,mBAAmB,CAACtB,QAAQ;QACtDyB,QAAQ,EAAE9B,YAAY,WAAZA,YAAY,GAAI2B,mBAAmB,CAACG,QAAQ;QACtDC,QAAQ,EAAE7B,YAAY,WAAZA,YAAY,GAAIyB,mBAAmB,CAACI;MAChD,CAAC;IACH,CAAC,MAAM;MACLJ,mBAAmB,GAAG;QACpBC,IAAI,EAAE/B,QAAQ;QACdgC,OAAO,EAAE/B,WAAW;QACpBO,QAAQ,EAAEN,YAAY;QACtB+B,QAAQ,EAAE9B,YAAY;QACtB+B,QAAQ,EAAE7B;MACZ,CAAC;IACH;EACF;EAEA,IAAM8B,2BAA2B,IAAAxC,oBAAA,GAAGmC,mBAAmB,qBAAnBnC,oBAAA,CAAqBa,QAAQ;EACjE,IAAM4B,SAAS,GAAG5B,QAAQ,WAARA,QAAQ,GAAI2B,2BAA2B;EAEzD,IAAME,WAAW,GACf,CAACxB,OAAO,IAAI,IAAI,IACdD,WAAW,IAAI,IAAI,IACnBS,yBAAyB,IAAI,IAAI,KACnCe,SAAS,KAAK,IAAI;EAGpB,IAAME,eAAe,GACnBd,cAAc,IAAI,IAAI,GAAG,IAAAe,qBAAY,EAACf,cAAc,CAAC,GAAGgB,SAAS;EAEnE,IAAIC,MAAM,GAAGf,KAAK;EAClB,IAAIgB,OAAO,EAAE;IACX,IAAIrF,iBAAiB,CAACsF,SAAS,CAAC,CAAC,IAAI9B,OAAO,IAAI,IAAI,EAAE;MACpD4B,MAAM,GAAG,CAACf,KAAK,EAAE;QAACkB,KAAK,EAAE;MAAS,CAAC,CAAC;IACtC;EACF;EAEA,IAAIC,cAAc,GAAGlC,aAAa;EAClC,IAAIkC,cAAc,IAAI,IAAI,IAAI,EAAEA,cAAc,IAAI,CAAC,CAAC,EAAE;IACpD,IAAIH,OAAO,EAAE;MACXI,OAAO,CAACC,KAAK,CACX,sEAAsEF,cAAc,+BACtF,CAAC;IACH;IACAA,cAAc,GAAG,CAAC;EACpB;EAEA,IAAIG,WAAW,GAAGzB,UAAU;EAE5B,IAAI0B,cAAc,GAAG,IAAAC,qBAAY,EAAgBT,MAAM,CAAC;EACxD,IAAIQ,cAAc,IAAI,IAAI,EAAE;IAC1B,IAAIE,SAAkC,GAAG,IAAI;IAC7C,IAAI,OAAOF,cAAc,CAACG,UAAU,KAAK,QAAQ,EAAE;MACjDD,SAAS,GAAGA,SAAS,IAAK,CAAC,CAA0B;MACrDA,SAAS,CAACC,UAAU,GAEjBH,cAAc,CAACG,UAAU,CAACC,QAAQ,CAAC,CAAmC;IAC3E;IAEA,IAAIJ,cAAc,CAACK,UAAU,IAAI,IAAI,EAAE;MACrCN,WAAW,GAAGO,yBAAyB,CAACN,cAAc,CAACK,UAAU,CAAC;MAClEH,SAAS,GAAGA,SAAS,IAAK,CAAC,CAA0B;MACrDA,SAAS,CAACG,UAAU,GAAGd,SAAS;IAClC;IAEA,IAAIS,cAAc,CAACO,aAAa,IAAI,IAAI,EAAE;MACxCL,SAAS,GAAGA,SAAS,IAAK,CAAC,CAA0B;MACrDA,SAAS,CAACM,iBAAiB,GACzBC,mCAAmC,CAACT,cAAc,CAACO,aAAa,CAAC;MACnEL,SAAS,CAACK,aAAa,GAAGhB,SAAS;IACrC;IAEA,IAAIW,SAAS,IAAI,IAAI,EAAE;MAErBV,MAAM,GAAG,CAACA,MAAM,EAAEU,SAAS,CAAC;IAC9B;EACF;EAEA,IAAMQ,SAAS,GAAGlD,EAAE,WAAFA,EAAE,GAAIC,QAAQ;EAEhC,IAAMkD,eAAe,GAAG,IAAAC,iBAAU,EAACC,qBAAY,CAAC;EAChD,IAAIF,eAAe,EAAE;IACnB,IAAIvB,WAAW,EAAE;MACf,OACE,IAAApE,WAAA,CAAA8F,GAAA,EAACC,0BAA0B;QACzBC,GAAG,EAAEvE,YAAa;QAClBwE,SAAS,EAAA9E,MAAA,CAAA+E,MAAA,KACJxC,SAAS;UACZ9B,kBAAkB,EAAEgC,mBAAmB;UACvC/B,kBAAkB,EAAEgC,mBAAmB;UACvCpB,QAAQ,EAAEiD,SAAS;UACnBhD,aAAa,EAAEkC,cAAc;UAC7BtB,UAAU,EAAEyB,WAAW;UACvBxB,cAAc,EAAEc,eAAe;UAC/BZ,KAAK,EAAEe,MAAM;UACbjC,QAAQ,EAAEA,QAAQ;UAClBF,QAAQ,EAARA;QAAQ,EACR;QACF8D,qBAAqB,EAAE;UACrBxD,WAAW,EAAXA,WAAW;UACXC,OAAO,EAAPA,OAAO;UACPC,SAAS,EAATA,SAAS;UACTC,UAAU,EAAVA,UAAU;UACVC,gBAAgB,EAAhBA,gBAAgB;UAChBC,eAAe,EAAfA,eAAe;UACfC,kBAAkB,EAAlBA,kBAAkB;UAClBC,oBAAoB,EAApBA,oBAAoB;UACpBC,6BAA6B,EAA7BA,6BAA6B;UAC7BC,yBAAyB,EAAzBA,yBAAyB;UACzBC,oBAAoB,EAApBA,oBAAoB;UACpBG,oBAAoB,EAApBA;QACF;MAAE,CACH,CAAC;IAEN;IAEA,OACE,IAAAxD,WAAA,CAAA8F,GAAA,EAACjG,oBAAA,CAAAuG,iBAAiB,EAAAjF,MAAA,CAAA+E,MAAA,KACZxC,SAAS;MACb9B,kBAAkB,EAAEgC,mBAAoB;MACxC/B,kBAAkB,EAAEgC,mBAAoB;MACxCpB,QAAQ,EAAEiD,SAAU;MACpBhD,aAAa,EAAEkC,cAAe;MAC9BoB,GAAG,EAAEvE,YAAa;MAClB6B,UAAU,EAAEyB,WAAY;MACxBxB,cAAc,EAAEc,eAAgB;MAChCZ,KAAK,EAAEe,MAAO;MACdjC,QAAQ,EAAEA,QAAS;MAAAF,QAAA,EAClBA;IAAQ,EACQ,CAAC;EAExB;EAIA,IACE8B,SAAS,KAAKD,2BAA2B,KACvCC,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,KAAK,IACvCD,2BAA2B,IAAI,IAAI,IAClCA,2BAA2B,KAAK,KAAM,CAAC,EAC3C;IACAL,mBAAmB,GAAA1C,MAAA,CAAA+E,MAAA,KAAOrC,mBAAmB;MAAEtB,QAAQ,EAAE4B;IAAS,EAAC;EACrE;EAEA,IAAMkC,WAAW,GAAGC,iBAAQ,CAACC,MAAM,CAAC;IAClCC,GAAG,EAAE7E,UAAU,KAAK,KAAK;IACzB8E,OAAO,EACL9E,UAAU,IAAI,IAAI,GACdiB,OAAO,IAAI,IAAI,IAAID,WAAW,IAAI,IAAI,GACtChB,UAAU;IAChBf,OAAO,EAAEe;EACX,CAAC,CAAC;EAEF,IAAI+E,UAAU,GAAG,IAAI;EACrB,IAAItC,WAAW,EAAE;IACfsC,UAAU,GACR,IAAA1G,WAAA,CAAA8F,GAAA,EAACa,mBAAmB;MAClBX,GAAG,EAAEvE,YAAa;MAClBwE,SAAS,EAAA9E,MAAA,CAAA+E,MAAA,KACJxC,SAAS;QACZ9B,kBAAkB,EAAEgC,mBAAmB;QACvC/B,kBAAkB,EAAEgC,mBAAmB;QACvClC,UAAU,EAAE0E,WAAW;QACvBvE,gBAAgB,EAAEA,gBAAgB,KAAK,KAAK;QAC5CS,QAAQ,EAAE4B,SAAS;QACnB7B,aAAa,EAAEA,aAAa,WAAbA,aAAa,GAAI,MAAM;QACtCG,QAAQ,EAAEiD,SAAS;QACnBhD,aAAa,EAAEkC,cAAc;QAC7BtB,UAAU,EAAEyB,WAAW;QACvBxB,cAAc,EAAEc,eAAe;QAC/BZ,KAAK,EAAEe,MAAM;QACbnC,QAAQ,EAARA;MAAQ,EACR;MACF8D,qBAAqB,EAAE;QACrBxD,WAAW,EAAXA,WAAW;QACXC,OAAO,EAAPA,OAAO;QACPC,SAAS,EAATA,SAAS;QACTC,UAAU,EAAVA,UAAU;QACVC,gBAAgB,EAAhBA,gBAAgB;QAChBC,eAAe,EAAfA,eAAe;QACfC,kBAAkB,EAAlBA,kBAAkB;QAClBC,oBAAoB,EAApBA,oBAAoB;QACpBC,6BAA6B,EAA7BA,6BAA6B;QAC7BC,yBAAyB,EAAzBA,yBAAyB;QACzBC,oBAAoB,EAApBA,oBAAoB;QACpBG,oBAAoB,EAApBA;MACF;IAAE,CACH,CACF;EACH,CAAC,MAAM;IACLkD,UAAU,GACR,IAAA1G,WAAA,CAAA8F,GAAA,EAACjG,oBAAA,CAAA+G,UAAU,EAAAzF,MAAA,CAAA+E,MAAA,KACLxC,SAAS;MACb9B,kBAAkB,EAAEgC,mBAAoB;MACxC/B,kBAAkB,EAAEgC,mBAAoB;MACxClC,UAAU,EAAE0E,WAAY;MACxBvE,gBAAgB,EAAEA,gBAAgB,KAAK,KAAM;MAC7CS,QAAQ,EAAE4B,SAAU;MACpB7B,aAAa,EAAEA,aAAa,WAAbA,aAAa,GAAI,MAAO;MACvCG,QAAQ,EAAEiD,SAAU;MACpBhD,aAAa,EAAEkC,cAAe;MAC9BoB,GAAG,EAAEvE,YAAa;MAClB6B,UAAU,EAAEyB,WAAY;MACxBxB,cAAc,EAAEc,eAAgB;MAChCZ,KAAK,EAAEe,MAAO;MAAAnC,QAAA,EACbA;IAAQ,EACC,CACb;EACH;EAEA,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAOqE,UAAU;EACnB;EAQA,IAAIG,KAAK,CAACC,OAAO,CAACzE,QAAQ,CAAC,IAAIA,QAAQ,CAAC0E,MAAM,IAAI,CAAC,EAAE;IACnD,IAAIC,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIC,KAAK,IAAI5E,QAAQ,EAAE;MAC1B,IAAI4E,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC9CD,eAAe,GAAG,IAAI;QACtB;MACF;IACF;IACA,IAAI,CAACA,eAAe,EAAE;MACpB,OAAON,UAAU;IACnB;EACF,CAAC,MAAM,IAAI,OAAOrE,QAAQ,KAAK,QAAQ,EAAE;IACvC,OAAOqE,UAAU;EACnB;EAEA,OACE,IAAA1G,WAAA,CAAA8F,GAAA,EAAClG,aAAA,CAAAgB,OAAY,CAACsG,QAAQ;IAACC,KAAK,EAAE,IAAK;IAAA9E,QAAA,EAAEqE;EAAU,CAAwB,CAAC;AAE5E,CACF,CAAC;AAEDpF,IAAI,CAAC8F,WAAW,GAAG,MAAM;AAsBzB,SAASC,mBAAmBA,CAAAC,KAAA,EAaF;EAAA,IAZxB3E,WAAW,GAAA2E,KAAA,CAAX3E,WAAW;IACXC,OAAO,GAAA0E,KAAA,CAAP1E,OAAO;IACPC,SAAS,GAAAyE,KAAA,CAATzE,SAAS;IACTC,UAAU,GAAAwE,KAAA,CAAVxE,UAAU;IACVC,iBAAgB,GAAAuE,KAAA,CAAhBvE,gBAAgB;IAChBC,gBAAe,GAAAsE,KAAA,CAAftE,eAAe;IACfC,mBAAkB,GAAAqE,KAAA,CAAlBrE,kBAAkB;IAClBC,qBAAoB,GAAAoE,KAAA,CAApBpE,oBAAoB;IACpBC,6BAA6B,GAAAmE,KAAA,CAA7BnE,6BAA6B;IAC7BC,yBAAyB,GAAAkE,KAAA,CAAzBlE,yBAAyB;IACzBC,oBAAoB,GAAAiE,KAAA,CAApBjE,oBAAoB;IACpBG,oBAAoB,GAAA8D,KAAA,CAApB9D,oBAAoB;EAEpB,IAAA+D,SAAA,GAAwC,IAAAC,eAAQ,EAAC,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAA9G,OAAA,EAAA2G,SAAA;IAAhDI,aAAa,GAAAF,UAAA;IAAEG,cAAc,GAAAH,UAAA;EAGpC,IAAMI,MAAM,GAAG,IAAAC,cAAO,EAAC,YAAM;IAC3B,IAAIC,UAAU,GAAGlF,SAAS;IAC1B,IAAImF,WAAW,GAAGlF,UAAU;IAK5B,IAAIwD,iBAAQ,CAAC2B,EAAE,KAAK,KAAK,EAAE;MACzBF,UAAU,GAAG,SAAbA,UAAUA,CAAIG,KAA4B,EAAK;QAC7CN,cAAc,CAACpE,oBAAoB,IAAI,IAAI,IAAI,CAACA,oBAAoB,CAAC;QACrEX,SAAS,YAATA,SAAS,CAAGqF,KAAK,CAAC;MACpB,CAAC;MAEDF,WAAW,GAAG,SAAdA,WAAWA,CAAIE,KAA4B,EAAK;QAC9CN,cAAc,CAAC,KAAK,CAAC;QACrB9E,UAAU,YAAVA,UAAU,CAAGoF,KAAK,CAAC;MACrB,CAAC;IACH;IAEA,OAAO;MACL3F,QAAQ,EAAE,KAAK;MACf4F,eAAe,EAAE9E,oBAAoB;MACrCV,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACPC,SAAS,EAAEkF,UAAU;MACrBjF,UAAU,EAAEkF;IACd,CAAC;EACH,CAAC,EAAE,CACD3E,oBAAoB,EACpBV,WAAW,EACXC,OAAO,EACPC,SAAS,EACTC,UAAU,EACVU,oBAAoB,CACrB,CAAC;EAGF,IAAM4E,aAAa,GAAG,IAAAC,wBAAe,EAACR,MAAM,CAAC;EAG7C,IAAMS,oBAAoB,GAAG,IAAAR,cAAO,EAClC;IAAA,OACEM,aAAa,IAAI,IAAI,GACjB,IAAI,GACJ;MACErF,gBAAgB,WAAhBA,gBAAgBA,CAACmF,KAA4B,EAAE;QAC7CE,aAAa,CAACrF,gBAAgB,CAACmF,KAAK,CAAC;QACrC,IAAInF,iBAAgB,IAAI,IAAI,EAAE;UAC5BA,iBAAgB,CAACmF,KAAK,CAAC;QACzB;MACF,CAAC;MACDlF,eAAe,WAAfA,eAAeA,CAACkF,KAA4B,EAAE;QAC5CE,aAAa,CAACpF,eAAe,CAACkF,KAAK,CAAC;QACpC,IAAIlF,gBAAe,IAAI,IAAI,EAAE;UAC3BA,gBAAe,CAACkF,KAAK,CAAC;QACxB;MACF,CAAC;MACDjF,kBAAkB,WAAlBA,kBAAkBA,CAACiF,KAA4B,EAAE;QAC/CE,aAAa,CAACnF,kBAAkB,CAACiF,KAAK,CAAC;QACvC,IAAIjF,mBAAkB,IAAI,IAAI,EAAE;UAC9BA,mBAAkB,CAACiF,KAAK,CAAC;QAC3B;MACF,CAAC;MACDhF,oBAAoB,WAApBA,oBAAoBA,CAACgF,KAA4B,EAAE;QACjDE,aAAa,CAAClF,oBAAoB,CAACgF,KAAK,CAAC;QACzC,IAAIhF,qBAAoB,IAAI,IAAI,EAAE;UAChCA,qBAAoB,CAACgF,KAAK,CAAC;QAC7B;MACF,CAAC;MACDK,OAAO,EAAEH,aAAa,CAACG,OAAO;MAC9BpF,6BAA6B,EAC3BA,6BAA6B,IAAI,IAAI,GACjCA,6BAA6B,GAC7BiF,aAAa,CAACjF,6BAA6B;MACjDC,yBAAyB,EACvBA,yBAAyB,IAAI,IAAI,GAC7BA,yBAAyB,GACzBgF,aAAa,CAAChF;IACtB,CAAC;EAAA,GACP,CACEgF,aAAa,EACbrF,iBAAgB,EAChBC,gBAAe,EACfC,mBAAkB,EAClBC,qBAAoB,EACpBC,6BAA6B,EAC7BC,yBAAyB,CAE7B,CAAC;EAGD,OAAO,IAAA0E,cAAO,EACZ;IAAA,OAAM,CAACH,aAAa,EAAEW,oBAAoB,CAAC;EAAA,GAC3C,CAACX,aAAa,EAAEW,oBAAoB,CACtC,CAAC;AACH;AAaA,IAAMvC,0BAGL,GAAGhG,KAAK,CAACwB,UAAU,CAAC,UAAAiH,KAAA,EAAqC/G,YAAY,EAAK;EAAA,IAApDwE,SAAS,GAAAuC,KAAA,CAATvC,SAAS;IAAEE,qBAAqB,GAAAqC,KAAA,CAArBrC,qBAAqB;EACrD,IAAAsC,oBAAA,GAA8CpB,mBAAmB,CAC/DlB,qBACF,CAAC;IAAAuC,qBAAA,OAAAhB,eAAA,CAAA9G,OAAA,EAAA6H,oBAAA;IAFMd,aAAa,GAAAe,qBAAA;IAAEJ,oBAAoB,GAAAI,qBAAA;EAI1C,OACE,IAAA1I,WAAA,CAAA8F,GAAA,EAACjG,oBAAA,CAAAuG,iBAAiB,EAAAjF,MAAA,CAAA+E,MAAA,KACZD,SAAS,EACTqC,oBAAoB;IACxBX,aAAa,EAAEA,aAAc;IAC7BvD,WAAW,EAAE,IAAK;IAClB4B,GAAG,EAAEvE;EAAa,EACnB,CAAC;AAEN,CAAC,CAAC;AAQF,IAAMkF,mBAGL,GAAG5G,KAAK,CAACwB,UAAU,CAAC,UAAAoH,KAAA,EAAqClH,YAAY,EAAK;EAAA,IAApDwE,SAAS,GAAA0C,KAAA,CAAT1C,SAAS;IAAEE,qBAAqB,GAAAwC,KAAA,CAArBxC,qBAAqB;EACrD,IAAAyC,qBAAA,GAA8CvB,mBAAmB,CAC/DlB,qBACF,CAAC;IAAA0C,qBAAA,OAAAnB,eAAA,CAAA9G,OAAA,EAAAgI,qBAAA;IAFMjB,aAAa,GAAAkB,qBAAA;IAAEP,oBAAoB,GAAAO,qBAAA;EAI1C,OACE,IAAA7I,WAAA,CAAA8F,GAAA,EAACjG,oBAAA,CAAA+G,UAAU,EAAAzF,MAAA,CAAA+E,MAAA,KACLD,SAAS,EACTqC,oBAAoB;IACxBX,aAAa,EAAEA,aAAc;IAC7BvD,WAAW,EAAE,IAAK;IAClB4B,GAAG,EAAEvE;EAAa,EACnB,CAAC;AAEN,CAAC,CAAC;AAEF,IAAM6D,yBAAyB,GAAG;EAChCwD,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,KAAK;EACXC,OAAO,EAAE,IAAI;EACbC,GAAG,EAAE;AACP,CAAC;AAED,IAAMzD,mCAAmC,GAAG;EAC1CqD,IAAI,EAAE,MAAM;EACZK,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE;AACV,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA3I,OAAA,GAEaU,IAAI", "ignoreList": []}