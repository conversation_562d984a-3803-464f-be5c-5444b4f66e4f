/**
 * Integration Dashboard Component
 * 
 * Comprehensive dashboard for monitoring and managing all system integrations,
 * providing real-time status, metrics, and control capabilities.
 * 
 * Features:
 * - Real-time integration status monitoring
 * - Service health visualization
 * - Performance metrics and analytics
 * - Circuit breaker status and controls
 * - Integration event timeline
 * - Service configuration management
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  RefreshControl,
} from 'react-native';
import { 
  advancedIntegrationOrchestrator,
  IntegrationState,
  IntegrationMetrics,
  IntegrationEvent,
} from '../../integration/advancedIntegrationOrchestrator';
import { Colors } from '../../constants/Colors';

// Dashboard props
interface IntegrationDashboardProps {
  visible?: boolean;
  onClose?: () => void;
  refreshInterval?: number;
}

// Dashboard state
interface DashboardState {
  serviceStates: Map<string, IntegrationState>;
  serviceMetrics: Map<string, IntegrationMetrics>;
  events: IntegrationEvent[];
  isRefreshing: boolean;
  selectedService: string | null;
}

/**
 * Integration Dashboard Component
 */
export const IntegrationDashboard: React.FC<IntegrationDashboardProps> = ({
  visible = false,
  onClose,
  refreshInterval = 5000,
}) => {
  // State management
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    serviceStates: new Map(),
    serviceMetrics: new Map(),
    events: [],
    isRefreshing: false,
    selectedService: null,
  });

  const [autoRefresh, setAutoRefresh] = useState(true);

  /**
   * Refresh dashboard data
   */
  const refreshData = useCallback(async () => {
    setDashboardState(prev => ({ ...prev, isRefreshing: true }));

    try {
      const serviceStates = advancedIntegrationOrchestrator.getAllServiceStates();
      const serviceMetrics = advancedIntegrationOrchestrator.getAllServiceMetrics();
      const events = advancedIntegrationOrchestrator.getEventHistory().slice(-50); // Last 50 events

      setDashboardState(prev => ({
        ...prev,
        serviceStates,
        serviceMetrics,
        events,
        isRefreshing: false,
      }));
    } catch (error) {
      console.error('Failed to refresh dashboard data:', error);
      setDashboardState(prev => ({ ...prev, isRefreshing: false }));
    }
  }, []);

  /**
   * Setup auto-refresh
   */
  useEffect(() => {
    if (!visible || !autoRefresh) return;

    const interval = setInterval(refreshData, refreshInterval);
    
    // Initial refresh
    refreshData();

    return () => clearInterval(interval);
  }, [visible, autoRefresh, refreshInterval, refreshData]);

  /**
   * Get status color based on service state
   */
  const getStatusColor = (status: IntegrationState['status']): string => {
    switch (status) {
      case 'healthy': return Colors.success;
      case 'degraded': return Colors.warning;
      case 'unhealthy': return Colors.error;
      case 'offline': return Colors.gray500;
      default: return Colors.gray500;
    }
  };

  /**
   * Get circuit breaker color
   */
  const getCircuitBreakerColor = (state: IntegrationState['circuitBreakerState']): string => {
    switch (state) {
      case 'closed': return Colors.success;
      case 'half-open': return Colors.warning;
      case 'open': return Colors.error;
      default: return Colors.gray500;
    }
  };

  /**
   * Calculate overall system health
   */
  const calculateOverallHealth = (): { status: string; percentage: number } => {
    const states = Array.from(dashboardState.serviceStates.values());
    if (states.length === 0) return { status: 'Unknown', percentage: 0 };

    const healthyCount = states.filter(s => s.status === 'healthy').length;
    const percentage = (healthyCount / states.length) * 100;

    let status = 'Healthy';
    if (percentage < 50) status = 'Critical';
    else if (percentage < 80) status = 'Degraded';

    return { status, percentage };
  };

  /**
   * Render overall system status
   */
  const renderOverallStatus = () => {
    const { status, percentage } = calculateOverallHealth();
    const statusColor = percentage >= 80 ? Colors.success : percentage >= 50 ? Colors.warning : Colors.error;

    return (
      <View style={styles.overallStatusCard}>
        <Text style={styles.overallStatusTitle}>System Health</Text>
        <View style={styles.overallStatusContent}>
          <View style={[styles.healthIndicator, { backgroundColor: statusColor }]} />
          <Text style={[styles.overallStatusText, { color: statusColor }]}>{status}</Text>
          <Text style={styles.overallStatusPercentage}>{percentage.toFixed(1)}%</Text>
        </View>
        <Text style={styles.overallStatusSubtext}>
          {dashboardState.serviceStates.size} services monitored
        </Text>
      </View>
    );
  };

  /**
   * Render service list
   */
  const renderServiceList = () => (
    <View style={styles.serviceListContainer}>
      <Text style={styles.sectionTitle}>Services</Text>
      {Array.from(dashboardState.serviceStates.entries()).map(([serviceName, state]) => {
        const metrics = dashboardState.serviceMetrics.get(serviceName);
        
        return (
          <TouchableOpacity
            key={serviceName}
            style={[
              styles.serviceCard,
              dashboardState.selectedService === serviceName && styles.serviceCardSelected,
            ]}
            onPress={() => setDashboardState(prev => ({
              ...prev,
              selectedService: prev.selectedService === serviceName ? null : serviceName,
            }))}
          >
            <View style={styles.serviceCardHeader}>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>{serviceName}</Text>
                <View style={styles.serviceStatusRow}>
                  <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(state.status) }]} />
                  <Text style={[styles.serviceStatus, { color: getStatusColor(state.status) }]}>
                    {state.status.toUpperCase()}
                  </Text>
                </View>
              </View>
              <View style={styles.serviceMetrics}>
                <Text style={styles.metricText}>
                  {state.responseTime.toFixed(0)}ms
                </Text>
                <Text style={styles.metricLabel}>Response</Text>
              </View>
            </View>

            {dashboardState.selectedService === serviceName && (
              <View style={styles.serviceDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Circuit Breaker:</Text>
                  <View style={styles.circuitBreakerStatus}>
                    <View style={[
                      styles.circuitBreakerIndicator,
                      { backgroundColor: getCircuitBreakerColor(state.circuitBreakerState) }
                    ]} />
                    <Text style={styles.detailValue}>{state.circuitBreakerState.toUpperCase()}</Text>
                  </View>
                </View>
                
                {metrics && (
                  <>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Success Rate:</Text>
                      <Text style={styles.detailValue}>
                        {((metrics.successfulRequests / Math.max(metrics.totalRequests, 1)) * 100).toFixed(1)}%
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Total Requests:</Text>
                      <Text style={styles.detailValue}>{metrics.totalRequests}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Cache Hit Rate:</Text>
                      <Text style={styles.detailValue}>{metrics.cacheHitRate.toFixed(1)}%</Text>
                    </View>
                  </>
                )}
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Last Check:</Text>
                  <Text style={styles.detailValue}>
                    {new Date(state.lastHealthCheck).toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  /**
   * Render event timeline
   */
  const renderEventTimeline = () => (
    <View style={styles.eventTimelineContainer}>
      <Text style={styles.sectionTitle}>Recent Events</Text>
      <ScrollView style={styles.eventList} nestedScrollEnabled>
        {dashboardState.events.slice(-20).reverse().map((event, index) => (
          <View key={index} style={styles.eventItem}>
            <View style={styles.eventHeader}>
              <Text style={styles.eventService}>{event.service}</Text>
              <Text style={styles.eventTime}>
                {new Date(event.timestamp).toLocaleTimeString()}
              </Text>
            </View>
            <View style={styles.eventContent}>
              <View style={[
                styles.eventTypeIndicator,
                { backgroundColor: event.type === 'success' ? Colors.success : Colors.error }
              ]} />
              <Text style={styles.eventType}>{event.type.toUpperCase()}</Text>
              {event.responseTime && (
                <Text style={styles.eventResponseTime}>{event.responseTime.toFixed(0)}ms</Text>
              )}
            </View>
            {event.details && typeof event.details === 'object' && (
              <Text style={styles.eventDetails}>
                {JSON.stringify(event.details, null, 2)}
              </Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );

  /**
   * Render dashboard controls
   */
  const renderControls = () => (
    <View style={styles.controlsContainer}>
      <View style={styles.controlRow}>
        <Text style={styles.controlLabel}>Auto Refresh</Text>
        <Switch
          value={autoRefresh}
          onValueChange={setAutoRefresh}
          trackColor={{ false: Colors.gray300, true: Colors.sage600 }}
          thumbColor={autoRefresh ? Colors.sage800 : Colors.gray500}
        />
      </View>
      
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => {
          Alert.alert(
            'Restart Integration System',
            'Are you sure you want to restart the integration system?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Restart',
                style: 'destructive',
                onPress: async () => {
                  try {
                    await advancedIntegrationOrchestrator.stop();
                    await advancedIntegrationOrchestrator.start();
                    Alert.alert('Success', 'Integration system restarted successfully');
                  } catch (error) {
                    Alert.alert('Error', 'Failed to restart integration system');
                  }
                },
              },
            ]
          );
        }}
      >
        <Text style={styles.actionButtonText}>Restart System</Text>
      </TouchableOpacity>
    </View>
  );

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Integration Dashboard</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>×</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={dashboardState.isRefreshing}
            onRefresh={refreshData}
            tintColor={Colors.sage600}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Overall Status */}
        {renderOverallStatus()}

        {/* Service List */}
        {renderServiceList()}

        {/* Event Timeline */}
        {renderEventTimeline()}

        {/* Controls */}
        {renderControls()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.text.primary,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  overallStatusCard: {
    backgroundColor: Colors.background.secondary,
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
  },
  overallStatusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  overallStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  healthIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  overallStatusText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  overallStatusPercentage: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
  overallStatusSubtext: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  serviceListContainer: {
    marginBottom: 24,
  },
  serviceCard: {
    backgroundColor: Colors.background.secondary,
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  serviceCardSelected: {
    borderColor: Colors.sage600,
  },
  serviceCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  serviceStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  serviceStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  serviceMetrics: {
    alignItems: 'flex-end',
  },
  metricText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  serviceDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  circuitBreakerStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  circuitBreakerIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  eventTimelineContainer: {
    marginBottom: 24,
  },
  eventList: {
    maxHeight: 300,
  },
  eventItem: {
    backgroundColor: Colors.background.secondary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  eventService: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  eventTime: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  eventContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  eventTypeIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  eventType: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  eventResponseTime: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginLeft: 'auto',
  },
  eventDetails: {
    fontSize: 10,
    color: Colors.text.secondary,
    marginTop: 4,
    fontFamily: 'monospace',
  },
  controlsContainer: {
    backgroundColor: Colors.background.secondary,
    padding: 16,
    borderRadius: 8,
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  controlLabel: {
    fontSize: 16,
    color: Colors.text.primary,
  },
  actionButton: {
    backgroundColor: Colors.sage600,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default IntegrationDashboard;
