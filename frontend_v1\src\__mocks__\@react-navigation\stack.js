/**
 * Mock for @react-navigation/stack
 */

const React = require('react');

export const createStackNavigator = jest.fn(() => ({
  Navigator: ({ children }) => children,
  Screen: ({ children }) => children,
}));

export const CardStyleInterpolators = {
  forHorizontalIOS: {},
  forVerticalIOS: {},
  forModalPresentationIOS: {},
};

export const TransitionPresets = {
  SlideFromRightIOS: {},
  ModalSlideFromBottomIOS: {},
};

export default {
  createStackNavigator,
  CardStyleInterpolators,
  TransitionPresets,
};
