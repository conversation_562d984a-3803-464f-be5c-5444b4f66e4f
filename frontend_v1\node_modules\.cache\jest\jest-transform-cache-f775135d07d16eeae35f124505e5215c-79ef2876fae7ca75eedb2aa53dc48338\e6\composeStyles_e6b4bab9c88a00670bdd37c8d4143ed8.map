{"version": 3, "names": ["composeStyles", "style1", "style2"], "sources": ["composeStyles.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n/**\n * Combines two styles such that `style2` will override any styles in `style1`.\n * If either style is null or undefined, the other one is returned without\n * allocating an array, saving allocations and enabling memoization.\n */\nexport default function composeStyles<T1, T2>(\n  style1: ?T1,\n  style2: ?T2,\n): ?(T1 | T2 | $ReadOnlyArray<T1 | T2>) {\n  if (style1 == null) {\n    return style2;\n  }\n  if (style2 == null) {\n    return style1;\n  }\n  return [style1, style2];\n}\n"], "mappings": ";;;;AAee,SAASA,aAAaA,CACnCC,MAAW,EACXC,MAAW,EAC2B;EACtC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOC,MAAM;EACf;EACA,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOD,MAAM;EACf;EACA,OAAO,CAACA,MAAM,EAAEC,MAAM,CAAC;AACzB", "ignoreList": []}