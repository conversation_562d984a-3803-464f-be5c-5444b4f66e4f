8fe5b52aecd0844f90f541f5ea427ab3
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.testHelpers = exports.testDataUtils = exports.testAssertions = exports.setupTestEnvironment = exports.performanceTestUtils = exports.mockDataGenerators = exports.default = exports.accessibilityTestUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var mockDataGenerators = exports.mockDataGenerators = {
  user: function user() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `user_${Math.random().toString(36).substr(2, 9)}`,
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://example.com/avatar.jpg',
      createdAt: new Date().toISOString()
    }, overrides);
  },
  service: function service() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `service_${Math.random().toString(36).substr(2, 9)}`,
      title: 'House Cleaning',
      description: 'Professional house cleaning service',
      category: 'cleaning',
      price: 75.00,
      duration: 120,
      rating: 4.5,
      reviewCount: 128,
      images: ['https://example.com/service1.jpg']
    }, overrides);
  },
  provider: function provider() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `provider_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Jane Smith',
      company: 'Smith Cleaning Services',
      rating: 4.8,
      reviewCount: 256,
      verified: true,
      experience: 5,
      location: 'Toronto, ON',
      avatar: 'https://example.com/provider.jpg',
      services: ['cleaning', 'maintenance']
    }, overrides);
  },
  booking: function booking() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `booking_${Math.random().toString(36).substr(2, 9)}`,
      serviceId: 'service_123',
      providerId: 'provider_456',
      customerId: 'user_789',
      date: new Date().toISOString(),
      status: 'confirmed',
      price: 75.00,
      address: '123 Main St, Toronto, ON M5V 3A8',
      notes: 'Please call when arriving'
    }, overrides);
  },
  address: function address() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `address_${Math.random().toString(36).substr(2, 9)}`,
      streetNumber: '123',
      streetName: 'Main Street',
      city: 'Toronto',
      province: 'ON',
      postalCode: 'M5V 3A8',
      country: 'Canada',
      coordinates: {
        latitude: 43.6532,
        longitude: -79.3832
      }
    }, overrides);
  },
  review: function review() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `review_${Math.random().toString(36).substr(2, 9)}`,
      rating: 5,
      comment: 'Excellent service! Highly recommended.',
      authorName: 'Happy Customer',
      date: new Date().toISOString(),
      verified: true,
      helpful: 12
    }, overrides);
  }
};
var setupTestEnvironment = exports.setupTestEnvironment = function setupTestEnvironment() {
  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var defaultConfig = {
    enableAccessibilityTesting: true,
    enablePerformanceTesting: true,
    mockNetworkRequests: true,
    mockLocationServices: true,
    mockNotifications: true,
    logLevel: 'warn'
  };
  var finalConfig = Object.assign({}, defaultConfig, config);
  if (finalConfig.logLevel === 'silent') {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
  if (finalConfig.mockNetworkRequests) {
    global.fetch = jest.fn(function () {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: function json() {
          return Promise.resolve({});
        },
        text: function text() {
          return Promise.resolve('');
        }
      });
    });
  }
  if (finalConfig.mockLocationServices) {
    var mockGeolocation = {
      getCurrentPosition: jest.fn(function (success) {
        return success({
          coords: {
            latitude: 43.6532,
            longitude: -79.3832,
            accuracy: 10
          }
        });
      }),
      watchPosition: jest.fn(),
      clearWatch: jest.fn()
    };
    if (typeof global.navigator === 'undefined') {
      global.navigator = {};
    }
    Object.defineProperty(global.navigator, 'geolocation', {
      value: mockGeolocation,
      writable: true
    });
  }
  if (finalConfig.mockNotifications) {
    var mockNotifications = {
      requestPermissionsAsync: jest.fn(function () {
        return Promise.resolve({
          status: 'granted'
        });
      }),
      scheduleNotificationAsync: jest.fn(function () {
        return Promise.resolve('notification-id');
      }),
      cancelNotificationAsync: jest.fn(function () {
        return Promise.resolve();
      })
    };
    jest.doMock('expo-notifications', function () {
      return mockNotifications;
    });
  }
  return finalConfig;
};
var accessibilityTestUtils = exports.accessibilityTestUtils = {
  hasAccessibilityLabel: function hasAccessibilityLabel(element) {
    return !!(element.props.accessibilityLabel || element.props['aria-label']);
  },
  hasAccessibilityRole: function hasAccessibilityRole(element) {
    return !!(element.props.accessibilityRole || element.props.role);
  },
  hasAccessibilityHint: function hasAccessibilityHint(element) {
    return !!element.props.accessibilityHint;
  },
  isFocusable: function isFocusable(element) {
    return element.props.accessible !== false && (element.props.accessibilityRole === 'button' || element.props.accessibilityRole === 'link' || element.props.onPress || element.props.onFocus);
  },
  hasSufficientTouchTarget: function hasSufficientTouchTarget(element) {
    var style = element.props.style;
    if (!style) return false;
    var minSize = 44;
    var width = style.width || style.minWidth;
    var height = style.height || style.minHeight;
    return width >= minSize && height >= minSize;
  },
  auditAccessibility: function auditAccessibility(component) {
    var violations = [];
    var warnings = [];
    var suggestions = [];
    var interactiveElements = component.findAll(function (node) {
      return node.props.onPress || node.props.accessibilityRole === 'button' || node.props.accessibilityRole === 'link';
    });
    interactiveElements.forEach(function (element, index) {
      var elementId = `element-${index}`;
      if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
        violations.push({
          rule: 'accessibility-label-required',
          severity: 'error',
          message: 'Interactive element must have accessibility label',
          element: elementId
        });
      }
      if (!accessibilityTestUtils.hasAccessibilityRole(element)) {
        warnings.push(`Element ${elementId} should have accessibility role`);
      }
      if (!accessibilityTestUtils.hasSufficientTouchTarget(element)) {
        violations.push({
          rule: 'touch-target-size',
          severity: 'warning',
          message: 'Touch target should be at least 44x44 points',
          element: elementId
        });
      }
    });
    var images = component.findAll(function (node) {
      return node.type === 'Image';
    });
    images.forEach(function (image, index) {
      if (!image.props.accessibilityLabel && !image.props.alt) {
        suggestions.push(`Image ${index} should have descriptive accessibility label`);
      }
    });
    return {
      passed: violations.filter(function (v) {
        return v.severity === 'error';
      }).length === 0,
      violations: violations,
      warnings: warnings,
      suggestions: suggestions
    };
  }
};
var performanceTestUtils = exports.performanceTestUtils = {
  measureRenderTime: function () {
    var _measureRenderTime = (0, _asyncToGenerator2.default)(function* (renderFunction) {
      var startTime = performance.now();
      yield renderFunction();
      var endTime = performance.now();
      return endTime - startTime;
    });
    function measureRenderTime(_x) {
      return _measureRenderTime.apply(this, arguments);
    }
    return measureRenderTime;
  }(),
  measureMemoryUsage: function measureMemoryUsage() {
    return Math.random() * 100;
  },
  countComponents: function countComponents(component) {
    var count = 1;
    if (component.children) {
      component.children.forEach(function (child) {
        if (typeof child === 'object' && 'type' in child) {
          count += performanceTestUtils.countComponents(child);
        }
      });
    }
    return count;
  },
  auditPerformance: function () {
    var _auditPerformance = (0, _asyncToGenerator2.default)(function* (component) {
      var thresholds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
        maxRenderTime: 100,
        maxMemoryUsage: 50,
        maxComponentCount: 100
      };
      var renderTime = yield performanceTestUtils.measureRenderTime((0, _asyncToGenerator2.default)(function* () {
        yield new Promise(function (resolve) {
          return setTimeout(resolve, 10);
        });
      }));
      var memoryUsage = performanceTestUtils.measureMemoryUsage();
      var componentCount = performanceTestUtils.countComponents(component);
      var passed = renderTime <= thresholds.maxRenderTime && memoryUsage <= thresholds.maxMemoryUsage && componentCount <= thresholds.maxComponentCount;
      return {
        renderTime: renderTime,
        memoryUsage: memoryUsage,
        componentCount: componentCount,
        passed: passed,
        thresholds: thresholds
      };
    });
    function auditPerformance(_x2) {
      return _auditPerformance.apply(this, arguments);
    }
    return auditPerformance;
  }()
};
var testDataUtils = exports.testDataUtils = {
  generateArray: function generateArray(generator, count) {
    return Array.from({
      length: count
    }, generator);
  },
  generateApiResponse: function generateApiResponse(data) {
    var success = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    return {
      success: success,
      data: success ? data : null,
      error: success ? null : 'Mock error message',
      timestamp: new Date().toISOString()
    };
  },
  generatePaginatedResponse: function generatePaginatedResponse(items) {
    var page = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;
    var startIndex = (page - 1) * limit;
    var endIndex = startIndex + limit;
    var paginatedItems = items.slice(startIndex, endIndex);
    return {
      items: paginatedItems,
      pagination: {
        page: page,
        limit: limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }
};
var testAssertions = exports.testAssertions = {
  assertAccessible: function assertAccessible(component) {
    var result = accessibilityTestUtils.auditAccessibility(component);
    if (!result.passed) {
      var errorMessages = result.violations.filter(function (v) {
        return v.severity === 'error';
      }).map(function (v) {
        return `${v.rule}: ${v.message}`;
      }).join('\n');
      throw new Error(`Accessibility violations found:\n${errorMessages}`);
    }
  },
  assertPerformant: function () {
    var _assertPerformant = (0, _asyncToGenerator2.default)(function* (component, thresholds) {
      var result = yield performanceTestUtils.auditPerformance(component, thresholds);
      if (!result.passed) {
        var issues = [];
        if (result.renderTime > result.thresholds.maxRenderTime) {
          issues.push(`Render time ${result.renderTime}ms exceeds threshold ${result.thresholds.maxRenderTime}ms`);
        }
        if (result.memoryUsage > result.thresholds.maxMemoryUsage) {
          issues.push(`Memory usage ${result.memoryUsage}MB exceeds threshold ${result.thresholds.maxMemoryUsage}MB`);
        }
        if (result.componentCount > result.thresholds.maxComponentCount) {
          issues.push(`Component count ${result.componentCount} exceeds threshold ${result.thresholds.maxComponentCount}`);
        }
        throw new Error(`Performance issues found:\n${issues.join('\n')}`);
      }
    });
    function assertPerformant(_x3, _x4) {
      return _assertPerformant.apply(this, arguments);
    }
    return assertPerformant;
  }(),
  assertElementAccessible: function assertElementAccessible(component, testID) {
    var element = component.findByProps({
      testID: testID
    });
    if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
      throw new Error(`Element with testID "${testID}" must have accessibility label`);
    }
    if (accessibilityTestUtils.isFocusable(element) && !accessibilityTestUtils.hasSufficientTouchTarget(element)) {
      throw new Error(`Focusable element with testID "${testID}" must have sufficient touch target size`);
    }
  }
};
var testHelpers = exports.testHelpers = {
  waitForElement: function () {
    var _waitForElement = (0, _asyncToGenerator2.default)(function* (component, testID) {
      var timeout = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5000;
      var startTime = Date.now();
      while (Date.now() - startTime < timeout) {
        try {
          return component.findByProps({
            testID: testID
          });
        } catch (_unused) {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 100);
          });
        }
      }
      throw new Error(`Element with testID "${testID}" not found within ${timeout}ms`);
    });
    function waitForElement(_x5, _x6) {
      return _waitForElement.apply(this, arguments);
    }
    return waitForElement;
  }(),
  simulatePress: function () {
    var _simulatePress = (0, _asyncToGenerator2.default)(function* (element) {
      if (element.props.onPress) {
        yield element.props.onPress();
      }
    });
    function simulatePress(_x7) {
      return _simulatePress.apply(this, arguments);
    }
    return simulatePress;
  }(),
  simulateTextInput: function () {
    var _simulateTextInput = (0, _asyncToGenerator2.default)(function* (element, text) {
      if (element.props.onChangeText) {
        yield element.props.onChangeText(text);
      }
    });
    function simulateTextInput(_x8, _x9) {
      return _simulateTextInput.apply(this, arguments);
    }
    return simulateTextInput;
  }(),
  createTestWrapper: function createTestWrapper(children) {
    return children;
  }
};
var _default = exports.default = {
  mockDataGenerators: mockDataGenerators,
  setupTestEnvironment: setupTestEnvironment,
  accessibilityTestUtils: accessibilityTestUtils,
  performanceTestUtils: performanceTestUtils,
  testDataUtils: testDataUtils,
  testAssertions: testAssertions,
  testHelpers: testHelpers
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************