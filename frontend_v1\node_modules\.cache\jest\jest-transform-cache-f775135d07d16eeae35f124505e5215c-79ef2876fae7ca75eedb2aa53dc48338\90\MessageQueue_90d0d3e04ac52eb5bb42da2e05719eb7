558f429e97556c3f58dc5a7ce9641563
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var Systrace = require("../Performance/Systrace");
var deepFreezeAndThrowOnMutationInDev = require("../Utilities/deepFreezeAndThrowOnMutationInDev").default;
var stringifySafe = require("../Utilities/stringifySafe").default;
var warnOnce = require("../Utilities/warnOnce").default;
var ErrorUtils = require("../vendor/core/ErrorUtils").default;
var invariant = require('invariant');
var TO_JS = 0;
var TO_NATIVE = 1;
var MODULE_IDS = 0;
var METHOD_IDS = 1;
var PARAMS = 2;
var MIN_TIME_BETWEEN_FLUSHES_MS = 5;
var TRACE_TAG_REACT_APPS = 1 << 17;
var DEBUG_INFO_LIMIT = 32;
var MessageQueue = function () {
  function MessageQueue() {
    (0, _classCallCheck2.default)(this, MessageQueue);
    this._lazyCallableModules = {};
    this._queue = [[], [], [], 0];
    this._successCallbacks = new Map();
    this._failureCallbacks = new Map();
    this._callID = 0;
    this._lastFlush = 0;
    this._eventLoopStartTime = Date.now();
    this._reactNativeMicrotasksCallback = null;
    if (__DEV__) {
      this._debugInfo = {};
      this._remoteModuleTable = {};
      this._remoteMethodTable = {};
    }
    this.callFunctionReturnFlushedQueue = this.callFunctionReturnFlushedQueue.bind(this);
    this.flushedQueue = this.flushedQueue.bind(this);
    this.invokeCallbackAndReturnFlushedQueue = this.invokeCallbackAndReturnFlushedQueue.bind(this);
  }
  return (0, _createClass2.default)(MessageQueue, [{
    key: "callFunctionReturnFlushedQueue",
    value: function callFunctionReturnFlushedQueue(module, method, args) {
      var _this = this;
      this.__guard(function () {
        _this.__callFunction(module, method, args);
      });
      return this.flushedQueue();
    }
  }, {
    key: "invokeCallbackAndReturnFlushedQueue",
    value: function invokeCallbackAndReturnFlushedQueue(cbID, args) {
      var _this2 = this;
      this.__guard(function () {
        _this2.__invokeCallback(cbID, args);
      });
      return this.flushedQueue();
    }
  }, {
    key: "flushedQueue",
    value: function flushedQueue() {
      var _this3 = this;
      this.__guard(function () {
        _this3.__callReactNativeMicrotasks();
      });
      var queue = this._queue;
      this._queue = [[], [], [], this._callID];
      return queue[0].length ? queue : null;
    }
  }, {
    key: "getEventLoopRunningTime",
    value: function getEventLoopRunningTime() {
      return Date.now() - this._eventLoopStartTime;
    }
  }, {
    key: "registerCallableModule",
    value: function registerCallableModule(name, module) {
      this._lazyCallableModules[name] = function () {
        return module;
      };
    }
  }, {
    key: "registerLazyCallableModule",
    value: function registerLazyCallableModule(name, factory) {
      var module;
      var getValue = factory;
      this._lazyCallableModules[name] = function () {
        if (getValue) {
          module = getValue();
          getValue = null;
        }
        return module;
      };
    }
  }, {
    key: "getCallableModule",
    value: function getCallableModule(name) {
      var getValue = this._lazyCallableModules[name];
      return getValue ? getValue() : null;
    }
  }, {
    key: "callNativeSyncHook",
    value: function callNativeSyncHook(moduleID, methodID, params, onFail, onSucc) {
      if (__DEV__) {
        invariant(global.nativeCallSyncHook, 'Calling synchronous methods on native ' + 'modules is not supported in Chrome.\n\n Consider providing alternative ' + 'methods to expose this method in debug mode, e.g. by exposing constants ' + 'ahead-of-time.');
      }
      this.processCallbacks(moduleID, methodID, params, onFail, onSucc);
      return global.nativeCallSyncHook(moduleID, methodID, params);
    }
  }, {
    key: "processCallbacks",
    value: function processCallbacks(moduleID, methodID, params, onFail, onSucc) {
      var _this4 = this;
      if (onFail || onSucc) {
        if (__DEV__) {
          this._debugInfo[this._callID] = [moduleID, methodID];
          if (this._callID > DEBUG_INFO_LIMIT) {
            delete this._debugInfo[this._callID - DEBUG_INFO_LIMIT];
          }
          if (this._successCallbacks.size > 500) {
            var info = {};
            this._successCallbacks.forEach(function (_, callID) {
              var debug = _this4._debugInfo[callID];
              var module = debug && _this4._remoteModuleTable[debug[0]];
              var method = debug && _this4._remoteMethodTable[debug[0]][debug[1]];
              info[callID] = {
                module: module,
                method: method
              };
            });
            warnOnce('excessive-number-of-pending-callbacks', `Excessive number of pending callbacks: ${this._successCallbacks.size}. Some pending callbacks that might have leaked by never being called from native code: ${stringifySafe(info)}`);
          }
        }
        onFail && params.push(this._callID << 1);
        onSucc && params.push(this._callID << 1 | 1);
        this._successCallbacks.set(this._callID, onSucc);
        this._failureCallbacks.set(this._callID, onFail);
      }
      if (__DEV__) {
        global.nativeTraceBeginAsyncFlow && global.nativeTraceBeginAsyncFlow(TRACE_TAG_REACT_APPS, 'native', this._callID);
      }
      this._callID++;
    }
  }, {
    key: "enqueueNativeCall",
    value: function enqueueNativeCall(moduleID, methodID, params, onFail, onSucc) {
      this.processCallbacks(moduleID, methodID, params, onFail, onSucc);
      this._queue[MODULE_IDS].push(moduleID);
      this._queue[METHOD_IDS].push(methodID);
      if (__DEV__) {
        var _isValidArgument = function isValidArgument(val) {
          switch (typeof val) {
            case 'undefined':
            case 'boolean':
            case 'string':
              return true;
            case 'number':
              return isFinite(val);
            case 'object':
              if (val == null) {
                return true;
              }
              if (Array.isArray(val)) {
                return val.every(_isValidArgument);
              }
              for (var k in val) {
                if (typeof val[k] !== 'function' && !_isValidArgument(val[k])) {
                  return false;
                }
              }
              return true;
            case 'function':
              return false;
            default:
              return false;
          }
        };
        var replacer = function replacer(key, val) {
          var t = typeof val;
          if (t === 'function') {
            return '<<Function ' + val.name + '>>';
          } else if (t === 'number' && !isFinite(val)) {
            return '<<' + val.toString() + '>>';
          } else {
            return val;
          }
        };
        invariant(_isValidArgument(params), '%s is not usable as a native method argument', JSON.stringify(params, replacer));
        deepFreezeAndThrowOnMutationInDev(params);
      }
      this._queue[PARAMS].push(params);
      var now = Date.now();
      if (global.nativeFlushQueueImmediate && now - this._lastFlush >= MIN_TIME_BETWEEN_FLUSHES_MS) {
        var queue = this._queue;
        this._queue = [[], [], [], this._callID];
        this._lastFlush = now;
        global.nativeFlushQueueImmediate(queue);
      }
      Systrace.counterEvent('pending_js_to_native_queue', this._queue[0].length);
      if (__DEV__ && this.__spy && isFinite(moduleID)) {
        this.__spy({
          type: TO_NATIVE,
          module: this._remoteModuleTable[moduleID],
          method: this._remoteMethodTable[moduleID][methodID],
          args: params
        });
      } else if (this.__spy) {
        this.__spy({
          type: TO_NATIVE,
          module: moduleID + '',
          method: methodID,
          args: params
        });
      }
    }
  }, {
    key: "createDebugLookup",
    value: function createDebugLookup(moduleID, name, methods) {
      if (__DEV__) {
        this._remoteModuleTable[moduleID] = name;
        this._remoteMethodTable[moduleID] = methods || [];
      }
    }
  }, {
    key: "setReactNativeMicrotasksCallback",
    value: function setReactNativeMicrotasksCallback(fn) {
      this._reactNativeMicrotasksCallback = fn;
    }
  }, {
    key: "__guard",
    value: function __guard(fn) {
      if (this.__shouldPauseOnThrow()) {
        fn();
      } else {
        try {
          fn();
        } catch (error) {
          ErrorUtils.reportFatalError(error);
        }
      }
    }
  }, {
    key: "__shouldPauseOnThrow",
    value: function __shouldPauseOnThrow() {
      return typeof DebuggerInternal !== 'undefined' && DebuggerInternal.shouldPauseOnThrow === true;
    }
  }, {
    key: "__callReactNativeMicrotasks",
    value: function __callReactNativeMicrotasks() {
      Systrace.beginEvent('JSTimers.callReactNativeMicrotasks()');
      try {
        if (this._reactNativeMicrotasksCallback != null) {
          this._reactNativeMicrotasksCallback();
        }
      } finally {
        Systrace.endEvent();
      }
    }
  }, {
    key: "__callFunction",
    value: function __callFunction(module, method, args) {
      this._lastFlush = Date.now();
      this._eventLoopStartTime = this._lastFlush;
      if (__DEV__ || this.__spy) {
        Systrace.beginEvent(`${module}.${method}(${stringifySafe(args)})`);
      } else {
        Systrace.beginEvent(`${module}.${method}(...)`);
      }
      try {
        if (this.__spy) {
          this.__spy({
            type: TO_JS,
            module: module,
            method: method,
            args: args
          });
        }
        var moduleMethods = this.getCallableModule(module);
        if (!moduleMethods) {
          var callableModuleNames = Object.keys(this._lazyCallableModules);
          var n = callableModuleNames.length;
          var callableModuleNameList = callableModuleNames.join(', ');
          var isBridgelessMode = global.RN$Bridgeless === true ? 'true' : 'false';
          invariant(false, `Failed to call into JavaScript module method ${module}.${method}(). Module has not been registered as callable. Bridgeless Mode: ${isBridgelessMode}. Registered callable JavaScript modules (n = ${n}): ${callableModuleNameList}.
          A frequent cause of the error is that the application entry file path is incorrect. This can also happen when the JS bundle is corrupt or there is an early initialization error when loading React Native.`);
        }
        if (!moduleMethods[method]) {
          invariant(false, `Failed to call into JavaScript module method ${module}.${method}(). Module exists, but the method is undefined.`);
        }
        moduleMethods[method].apply(moduleMethods, args);
      } finally {
        Systrace.endEvent();
      }
    }
  }, {
    key: "__invokeCallback",
    value: function __invokeCallback(cbID, args) {
      this._lastFlush = Date.now();
      this._eventLoopStartTime = this._lastFlush;
      var callID = cbID >>> 1;
      var isSuccess = cbID & 1;
      var callback = isSuccess ? this._successCallbacks.get(callID) : this._failureCallbacks.get(callID);
      if (__DEV__) {
        var debug = this._debugInfo[callID];
        var module = debug && this._remoteModuleTable[debug[0]];
        var method = debug && this._remoteMethodTable[debug[0]][debug[1]];
        invariant(callback, `No callback found with cbID ${cbID} and callID ${callID} for ` + (method ? ` ${module}.${method} - most likely the callback was already invoked` : `module ${module || '<unknown>'}`) + `. Args: '${stringifySafe(args)}'`);
        var profileName = debug ? '<callback for ' + module + '.' + method + '>' : cbID;
        if (callback && this.__spy) {
          this.__spy({
            type: TO_JS,
            module: null,
            method: profileName,
            args: args
          });
        }
        Systrace.beginEvent(`MessageQueue.invokeCallback(${profileName}, ${stringifySafe(args)})`);
      }
      try {
        if (!callback) {
          return;
        }
        this._successCallbacks.delete(callID);
        this._failureCallbacks.delete(callID);
        callback.apply(void 0, (0, _toConsumableArray2.default)(args));
      } finally {
        if (__DEV__) {
          Systrace.endEvent();
        }
      }
    }
  }], [{
    key: "spy",
    value: function spy(spyOrToggle) {
      if (spyOrToggle === true) {
        MessageQueue.prototype.__spy = function (info) {
          console.log(`${info.type === TO_JS ? 'N->JS' : 'JS->N'} : ` + `${info.module != null ? info.module + '.' : ''}${info.method}` + `(${JSON.stringify(info.args)})`);
        };
      } else if (spyOrToggle === false) {
        MessageQueue.prototype.__spy = null;
      } else {
        MessageQueue.prototype.__spy = spyOrToggle;
      }
    }
  }]);
}();
var _default = exports.default = MessageQueue;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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