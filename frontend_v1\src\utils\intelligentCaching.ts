/**
 * Intelligent Caching System
 * 
 * Advanced caching system that builds upon the existing caching infrastructure
 * to provide intelligent cache management, predictive caching, and adaptive
 * cache strategies based on usage patterns.
 * 
 * Features:
 * - Intelligent cache eviction policies
 * - Predictive cache warming
 * - Usage pattern analysis
 * - Adaptive cache sizing
 * - Performance-based cache optimization
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { globalCache, CacheOptions } from './caching';
import { performanceMonitor } from './performance';

// Cache usage statistics
interface CacheUsageStats {
  key: string;
  accessCount: number;
  lastAccessed: number;
  createdAt: number;
  size: number;
  hitRate: number;
  avgAccessTime: number;
}

// Cache prediction model
interface CachePrediction {
  key: string;
  probability: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedAccessTime: number;
}

// Intelligent cache configuration
interface IntelligentCacheConfig {
  maxSize: number;
  evictionPolicy: 'lru' | 'lfu' | 'adaptive' | 'ml-based';
  enablePredictiveCaching: boolean;
  enableUsageAnalytics: boolean;
  performanceThreshold: number;
  adaptiveSizing: boolean;
}

// Default configuration
const DEFAULT_CONFIG: IntelligentCacheConfig = {
  maxSize: 100 * 1024 * 1024, // 100MB
  evictionPolicy: 'adaptive',
  enablePredictiveCaching: true,
  enableUsageAnalytics: true,
  performanceThreshold: 100, // ms
  adaptiveSizing: true,
};

/**
 * Intelligent Cache Manager
 */
export class IntelligentCacheManager {
  private config: IntelligentCacheConfig;
  private usageStats: Map<string, CacheUsageStats> = new Map();
  private accessPatterns: Map<string, number[]> = new Map();
  private predictions: Map<string, CachePrediction> = new Map();
  private currentSize: number = 0;
  private performanceHistory: number[] = [];

  constructor(config: Partial<IntelligentCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeAnalytics();
  }

  /**
   * Initialize analytics and monitoring
   */
  private initializeAnalytics(): void {
    if (this.config.enableUsageAnalytics) {
      // Start periodic analytics collection
      setInterval(() => {
        this.analyzeUsagePatterns();
        this.updatePredictions();
        this.optimizeCache();
      }, 30000); // Every 30 seconds
    }
  }

  /**
   * Intelligent cache get with analytics
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();
    
    try {
      const result = await globalCache.get<T>(key);
      const endTime = performance.now();
      const accessTime = endTime - startTime;

      // Update usage statistics
      this.updateUsageStats(key, accessTime, result !== null);
      
      // Record performance
      this.performanceHistory.push(accessTime);
      if (this.performanceHistory.length > 100) {
        this.performanceHistory.shift();
      }

      // Track access pattern
      this.trackAccessPattern(key);

      return result;
    } catch (error) {
      console.error('[IntelligentCache] Get error:', error);
      return null;
    }
  }

  /**
   * Intelligent cache set with optimization
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Estimate data size
      const estimatedSize = this.estimateDataSize(data);
      
      // Check if cache needs optimization before setting
      if (this.currentSize + estimatedSize > this.config.maxSize) {
        await this.evictItems(estimatedSize);
      }

      // Set data with enhanced options
      const enhancedOptions = this.enhanceOptions(key, options);
      await globalCache.set(key, data, enhancedOptions);
      
      // Update size tracking
      this.currentSize += estimatedSize;
      
      // Initialize usage stats for new items
      if (!this.usageStats.has(key)) {
        this.usageStats.set(key, {
          key,
          accessCount: 0,
          lastAccessed: Date.now(),
          createdAt: Date.now(),
          size: estimatedSize,
          hitRate: 0,
          avgAccessTime: 0,
        });
      }

      const endTime = performance.now();
      performanceMonitor.recordMetric({
        name: 'intelligent-cache-set',
        value: endTime - startTime,
        timestamp: Date.now(),
        type: 'timing',
        tags: { key, size: estimatedSize.toString() },
      });

    } catch (error) {
      console.error('[IntelligentCache] Set error:', error);
    }
  }

  /**
   * Predictive cache warming
   */
  async warmCache(predictions: CachePrediction[]): Promise<void> {
    if (!this.config.enablePredictiveCaching) return;

    // Sort predictions by priority and probability
    const sortedPredictions = predictions
      .sort((a, b) => {
        const priorityWeight = { critical: 4, high: 3, medium: 2, low: 1 };
        const aScore = a.probability * priorityWeight[a.priority];
        const bScore = b.probability * priorityWeight[b.priority];
        return bScore - aScore;
      })
      .slice(0, 10); // Limit to top 10 predictions

    for (const prediction of sortedPredictions) {
      try {
        // Check if already cached
        const existing = await this.get(prediction.key);
        if (existing) continue;

        // Simulate data loading (replace with actual data fetching logic)
        const data = await this.fetchDataForKey(prediction.key);
        if (data) {
          await this.set(prediction.key, data, {
            ttl: 10 * 60 * 1000, // 10 minutes for predicted data
            tags: ['predicted'],
          });
        }
      } catch (error) {
        console.warn(`[IntelligentCache] Failed to warm cache for ${prediction.key}:`, error);
      }
    }
  }

  /**
   * Analyze usage patterns for intelligent caching
   */
  private analyzeUsagePatterns(): void {
    const now = Date.now();
    const patterns: Map<string, number> = new Map();

    // Analyze access frequency and recency
    this.usageStats.forEach((stats, key) => {
      const recency = now - stats.lastAccessed;
      const frequency = stats.accessCount;
      const age = now - stats.createdAt;
      
      // Calculate pattern score (higher = more valuable)
      const recencyScore = Math.max(0, 1 - (recency / (24 * 60 * 60 * 1000))); // Decay over 24 hours
      const frequencyScore = Math.min(1, frequency / 10); // Normalize to max 10 accesses
      const ageScore = Math.min(1, age / (7 * 24 * 60 * 60 * 1000)); // Mature after 1 week
      
      const patternScore = (recencyScore * 0.4) + (frequencyScore * 0.4) + (ageScore * 0.2);
      patterns.set(key, patternScore);
    });

    // Update predictions based on patterns
    this.updatePredictionsFromPatterns(patterns);
  }

  /**
   * Update cache predictions
   */
  private updatePredictions(): void {
    const predictions: Map<string, CachePrediction> = new Map();

    // Analyze access patterns to predict future accesses
    this.accessPatterns.forEach((timestamps, key) => {
      if (timestamps.length < 2) return;

      // Calculate access intervals
      const intervals = [];
      for (let i = 1; i < timestamps.length; i++) {
        intervals.push(timestamps[i] - timestamps[i - 1]);
      }

      // Predict next access time
      const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
      const lastAccess = timestamps[timestamps.length - 1];
      const predictedNextAccess = lastAccess + avgInterval;
      const timeToPredictedAccess = predictedNextAccess - Date.now();

      // Calculate probability based on pattern consistency
      const intervalVariance = this.calculateVariance(intervals);
      const consistency = Math.max(0, 1 - (intervalVariance / avgInterval));
      const probability = Math.min(0.95, consistency * 0.8 + 0.1);

      // Determine priority based on usage stats
      const stats = this.usageStats.get(key);
      let priority: CachePrediction['priority'] = 'medium';
      
      if (stats) {
        if (stats.accessCount > 20 && stats.hitRate > 0.8) {
          priority = 'critical';
        } else if (stats.accessCount > 10 && stats.hitRate > 0.6) {
          priority = 'high';
        } else if (stats.accessCount > 5) {
          priority = 'medium';
        } else {
          priority = 'low';
        }
      }

      predictions.set(key, {
        key,
        probability,
        priority,
        estimatedAccessTime: timeToPredictedAccess,
      });
    });

    this.predictions = predictions;
  }

  /**
   * Optimize cache based on performance and usage
   */
  private async optimizeCache(): Promise<void> {
    // Adaptive sizing based on performance
    if (this.config.adaptiveSizing) {
      const avgPerformance = this.performanceHistory.reduce((sum, time) => sum + time, 0) / 
                            Math.max(this.performanceHistory.length, 1);
      
      if (avgPerformance > this.config.performanceThreshold) {
        // Performance is poor, reduce cache size
        this.config.maxSize *= 0.9;
        await this.evictItems(this.currentSize - this.config.maxSize);
      } else if (avgPerformance < this.config.performanceThreshold * 0.5) {
        // Performance is good, can increase cache size
        this.config.maxSize *= 1.1;
      }
    }

    // Optimize eviction policy based on hit rates
    const avgHitRate = Array.from(this.usageStats.values())
      .reduce((sum, stats) => sum + stats.hitRate, 0) / 
      Math.max(this.usageStats.size, 1);

    if (avgHitRate < 0.5 && this.config.evictionPolicy !== 'lfu') {
      this.config.evictionPolicy = 'lfu'; // Switch to frequency-based eviction
    } else if (avgHitRate > 0.8 && this.config.evictionPolicy !== 'lru') {
      this.config.evictionPolicy = 'lru'; // Switch to recency-based eviction
    }
  }

  /**
   * Intelligent cache eviction
   */
  private async evictItems(targetSize: number): Promise<void> {
    const itemsToEvict: string[] = [];
    let evictedSize = 0;

    // Get eviction candidates based on policy
    const candidates = this.getEvictionCandidates();

    for (const key of candidates) {
      const stats = this.usageStats.get(key);
      if (stats) {
        itemsToEvict.push(key);
        evictedSize += stats.size;
        
        if (evictedSize >= targetSize) break;
      }
    }

    // Evict items
    for (const key of itemsToEvict) {
      await globalCache.delete(key);
      this.usageStats.delete(key);
      this.accessPatterns.delete(key);
      this.predictions.delete(key);
    }

    this.currentSize -= evictedSize;
  }

  /**
   * Get eviction candidates based on current policy
   */
  private getEvictionCandidates(): string[] {
    const items = Array.from(this.usageStats.entries());

    switch (this.config.evictionPolicy) {
      case 'lru':
        return items
          .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
          .map(([key]) => key);

      case 'lfu':
        return items
          .sort(([, a], [, b]) => a.accessCount - b.accessCount)
          .map(([key]) => key);

      case 'adaptive':
        return items
          .sort(([, a], [, b]) => {
            const aScore = (a.hitRate * 0.4) + (a.accessCount / 100 * 0.3) + 
                          ((Date.now() - a.lastAccessed) / (24 * 60 * 60 * 1000) * 0.3);
            const bScore = (b.hitRate * 0.4) + (b.accessCount / 100 * 0.3) + 
                          ((Date.now() - b.lastAccessed) / (24 * 60 * 60 * 1000) * 0.3);
            return aScore - bScore;
          })
          .map(([key]) => key);

      default:
        return items.map(([key]) => key);
    }
  }

  /**
   * Update usage statistics
   */
  private updateUsageStats(key: string, accessTime: number, hit: boolean): void {
    const stats = this.usageStats.get(key);
    if (stats) {
      stats.accessCount++;
      stats.lastAccessed = Date.now();
      stats.avgAccessTime = (stats.avgAccessTime + accessTime) / 2;
      stats.hitRate = hit ? Math.min(1, stats.hitRate + 0.1) : Math.max(0, stats.hitRate - 0.1);
    }
  }

  /**
   * Track access patterns for prediction
   */
  private trackAccessPattern(key: string): void {
    const pattern = this.accessPatterns.get(key) || [];
    pattern.push(Date.now());
    
    // Keep only recent accesses (last 50)
    if (pattern.length > 50) {
      pattern.shift();
    }
    
    this.accessPatterns.set(key, pattern);
  }

  /**
   * Enhance cache options based on intelligence
   */
  private enhanceOptions(key: string, options: CacheOptions): CacheOptions {
    const prediction = this.predictions.get(key);
    const stats = this.usageStats.get(key);

    let enhancedTtl = options.ttl || 5 * 60 * 1000; // Default 5 minutes

    // Adjust TTL based on usage patterns
    if (stats) {
      if (stats.accessCount > 10 && stats.hitRate > 0.8) {
        enhancedTtl *= 2; // Extend TTL for frequently accessed items
      } else if (stats.accessCount < 3) {
        enhancedTtl *= 0.5; // Reduce TTL for rarely accessed items
      }
    }

    // Adjust based on predictions
    if (prediction && prediction.probability > 0.7) {
      enhancedTtl *= 1.5; // Extend TTL for predicted items
    }

    return {
      ...options,
      ttl: enhancedTtl,
      tags: [...(options.tags || []), 'intelligent'],
    };
  }

  /**
   * Estimate data size (simplified)
   */
  private estimateDataSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate
    } catch {
      return 1024; // Default 1KB
    }
  }

  /**
   * Calculate variance for pattern analysis
   */
  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  }

  /**
   * Update predictions from usage patterns
   */
  private updatePredictionsFromPatterns(patterns: Map<string, number>): void {
    // Implementation would analyze patterns and update predictions
    // This is a simplified version
    patterns.forEach((score, key) => {
      if (score > 0.7) {
        this.predictions.set(key, {
          key,
          probability: score,
          priority: score > 0.9 ? 'critical' : score > 0.8 ? 'high' : 'medium',
          estimatedAccessTime: Date.now() + (60 * 1000), // Predict access in 1 minute
        });
      }
    });
  }

  /**
   * Fetch data for key (placeholder for actual implementation)
   */
  private async fetchDataForKey(key: string): Promise<any> {
    // This would be replaced with actual data fetching logic
    // based on the key pattern and application requirements
    return null;
  }

  /**
   * Clear cache with intelligent cleanup
   */
  async clearCache(tags?: string[]): Promise<void> {
    if (tags) {
      // Clear only items with specific tags
      await globalCache.clear(tags);

      // Update local tracking
      this.usageStats.forEach((stats, key) => {
        // Remove stats for cleared items (simplified)
        this.usageStats.delete(key);
        this.accessPatterns.delete(key);
        this.predictions.delete(key);
      });
    } else {
      // Clear all cache
      await globalCache.clear();
      this.usageStats.clear();
      this.accessPatterns.clear();
      this.predictions.clear();
      this.currentSize = 0;
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.currentSize,
      maxSize: this.config.maxSize,
      itemCount: this.usageStats.size,
      hitRate: Array.from(this.usageStats.values())
        .reduce((sum, stats) => sum + stats.hitRate, 0) / Math.max(this.usageStats.size, 1),
      avgPerformance: this.performanceHistory.reduce((sum, time) => sum + time, 0) /
                     Math.max(this.performanceHistory.length, 1),
      predictions: this.predictions.size,
      evictionPolicy: this.config.evictionPolicy,
      utilizationRate: this.currentSize / this.config.maxSize,
    };
  }

  /**
   * Get detailed usage analytics
   */
  getUsageAnalytics() {
    const topItems = Array.from(this.usageStats.entries())
      .sort(([, a], [, b]) => b.accessCount - a.accessCount)
      .slice(0, 10)
      .map(([key, stats]) => ({ key, ...stats }));

    const recentPredictions = Array.from(this.predictions.values())
      .sort((a, b) => b.probability - a.probability)
      .slice(0, 10);

    return {
      topAccessedItems: topItems,
      recentPredictions,
      totalAccesses: Array.from(this.usageStats.values())
        .reduce((sum, stats) => sum + stats.accessCount, 0),
      avgHitRate: Array.from(this.usageStats.values())
        .reduce((sum, stats) => sum + stats.hitRate, 0) / Math.max(this.usageStats.size, 1),
      cacheEfficiency: this.calculateCacheEfficiency(),
    };
  }

  /**
   * Calculate cache efficiency score
   */
  private calculateCacheEfficiency(): number {
    const stats = this.getCacheStats();
    const analytics = this.getUsageAnalytics();

    // Efficiency based on hit rate, utilization, and performance
    const hitRateScore = stats.hitRate * 0.4;
    const utilizationScore = Math.min(1, stats.utilizationRate * 1.2) * 0.3;
    const performanceScore = Math.max(0, 1 - (stats.avgPerformance / 100)) * 0.3;

    return Math.min(1, hitRateScore + utilizationScore + performanceScore);
  }
}

// Global intelligent cache instance
export const intelligentCache = new IntelligentCacheManager();

export default intelligentCache;
