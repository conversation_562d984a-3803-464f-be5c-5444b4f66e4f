/**
 * Performance Dashboard Component
 * 
 * Comprehensive performance monitoring dashboard that displays real-time
 * performance metrics, cache statistics, and optimization recommendations.
 * 
 * Features:
 * - Real-time performance metrics
 * - Cache analytics and statistics
 * - Performance trends and charts
 * - Optimization recommendations
 * - Interactive performance controls
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { performanceMonitor } from '../../utils/performance';
import { intelligentCache } from '../../utils/intelligentCaching';
import { useAdvancedPerformanceOptimization } from '../../utils/advancedPerformanceOptimization';
import { Colors } from '../../constants/Colors';

// Performance dashboard props
interface PerformanceDashboardProps {
  visible?: boolean;
  onClose?: () => void;
  enableRealTimeUpdates?: boolean;
}

// Performance metrics state
interface PerformanceMetricsState {
  renderTime: number;
  memoryUsage: number;
  cacheStats: any;
  networkLatency: number;
  frameRate: number;
  bundleSize: number;
}

// Optimization recommendations
interface OptimizationRecommendation {
  type: 'warning' | 'error' | 'info';
  title: string;
  description: string;
  action?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Performance Dashboard Component
 */
export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  visible = false,
  onClose,
  enableRealTimeUpdates = true,
}) => {
  // State management
  const [metrics, setMetrics] = useState<PerformanceMetricsState>({
    renderTime: 0,
    memoryUsage: 0,
    cacheStats: {},
    networkLatency: 0,
    frameRate: 60,
    bundleSize: 0,
  });

  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>([]);
  const [isOptimizationEnabled, setIsOptimizationEnabled] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(1000);

  // Performance optimization hook
  const {
    metrics: perfMetrics,
    performanceLevel,
    adaptivePerformanceTuning,
    memoryOptimization,
  } = useAdvancedPerformanceOptimization('PerformanceDashboard', {
    enableIntelligentRendering: isOptimizationEnabled,
    enableAdaptivePerformance: isOptimizationEnabled,
    componentPriority: 'low', // Dashboard is not critical for app functionality
  });

  /**
   * Collect performance metrics
   */
  const collectMetrics = useCallback(async () => {
    try {
      // Get cache statistics
      const cacheStats = intelligentCache.getCacheStats();
      const usageAnalytics = intelligentCache.getUsageAnalytics();

      // Get performance metrics from monitor
      const performanceData = performanceMonitor.getMetrics();

      // Update metrics state
      setMetrics({
        renderTime: perfMetrics.renderTime,
        memoryUsage: perfMetrics.memoryUsage,
        cacheStats: { ...cacheStats, ...usageAnalytics },
        networkLatency: perfMetrics.networkLatency,
        frameRate: 60 - perfMetrics.frameDrops, // Simplified frame rate calculation
        bundleSize: 0, // Would need to be measured separately
      });

      // Generate recommendations
      generateRecommendations(cacheStats, perfMetrics);

    } catch (error) {
      console.error('[PerformanceDashboard] Error collecting metrics:', error);
    }
  }, [perfMetrics]);

  /**
   * Generate optimization recommendations
   */
  const generateRecommendations = useCallback((
    cacheStats: any,
    performanceMetrics: any
  ) => {
    const newRecommendations: OptimizationRecommendation[] = [];

    // Render performance recommendations
    if (performanceMetrics.renderTime > 16) {
      newRecommendations.push({
        type: 'warning',
        title: 'Slow Rendering Detected',
        description: `Render time (${performanceMetrics.renderTime.toFixed(2)}ms) exceeds 16ms target`,
        action: 'Enable intelligent rendering optimization',
        priority: 'high',
      });
    }

    // Memory usage recommendations
    if (performanceMetrics.memoryUsage > 100) {
      newRecommendations.push({
        type: 'error',
        title: 'High Memory Usage',
        description: `Memory usage (${performanceMetrics.memoryUsage.toFixed(2)}MB) is high`,
        action: 'Run memory optimization',
        priority: 'critical',
      });
    }

    // Cache efficiency recommendations
    if (cacheStats.hitRate < 0.5) {
      newRecommendations.push({
        type: 'warning',
        title: 'Low Cache Hit Rate',
        description: `Cache hit rate (${(cacheStats.hitRate * 100).toFixed(1)}%) is below optimal`,
        action: 'Review cache strategy',
        priority: 'medium',
      });
    }

    // Cache utilization recommendations
    if (cacheStats.utilizationRate > 0.9) {
      newRecommendations.push({
        type: 'info',
        title: 'High Cache Utilization',
        description: 'Cache is nearly full, consider increasing size or improving eviction',
        action: 'Optimize cache size',
        priority: 'medium',
      });
    }

    setRecommendations(newRecommendations);
  }, []);

  /**
   * Handle optimization actions
   */
  const handleOptimizationAction = useCallback((recommendation: OptimizationRecommendation) => {
    switch (recommendation.action) {
      case 'Run memory optimization':
        memoryOptimization();
        Alert.alert('Memory Optimization', 'Memory optimization completed');
        break;
      
      case 'Enable intelligent rendering optimization':
        setIsOptimizationEnabled(true);
        Alert.alert('Optimization Enabled', 'Intelligent rendering optimization enabled');
        break;
      
      case 'Optimize cache size':
        adaptivePerformanceTuning();
        Alert.alert('Cache Optimization', 'Cache optimization completed');
        break;
      
      default:
        Alert.alert('Action', `Action: ${recommendation.action}`);
    }
  }, [memoryOptimization, adaptivePerformanceTuning]);

  /**
   * Clear cache with confirmation
   */
  const handleClearCache = useCallback(() => {
    Alert.alert(
      'Clear Cache',
      'Are you sure you want to clear the cache? This may temporarily impact performance.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            await intelligentCache.clearCache();
            Alert.alert('Cache Cleared', 'Cache has been cleared successfully');
          },
        },
      ]
    );
  }, []);

  // Setup real-time updates
  useEffect(() => {
    if (!visible || !enableRealTimeUpdates) return;

    const interval = setInterval(collectMetrics, refreshInterval);
    
    // Initial collection
    collectMetrics();

    return () => clearInterval(interval);
  }, [visible, enableRealTimeUpdates, refreshInterval, collectMetrics]);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Performance Dashboard</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        {/* Performance Level Indicator */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performance Level</Text>
          <View style={[styles.performanceIndicator, styles[`performance${performanceLevel}`]]}>
            <Text style={styles.performanceText}>{performanceLevel.toUpperCase()}</Text>
          </View>
        </View>

        {/* Real-time Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Real-time Metrics</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricCard}>
              <Text style={styles.metricLabel}>Render Time</Text>
              <Text style={styles.metricValue}>{metrics.renderTime.toFixed(2)}ms</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricLabel}>Memory Usage</Text>
              <Text style={styles.metricValue}>{metrics.memoryUsage.toFixed(1)}MB</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricLabel}>Frame Rate</Text>
              <Text style={styles.metricValue}>{metrics.frameRate.toFixed(0)}fps</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricLabel}>Cache Hit Rate</Text>
              <Text style={styles.metricValue}>
                {((metrics.cacheStats.hitRate || 0) * 100).toFixed(1)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Cache Statistics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cache Statistics</Text>
          <View style={styles.cacheStats}>
            <Text style={styles.statText}>
              Size: {(metrics.cacheStats.size / 1024 / 1024).toFixed(2)}MB / 
              {(metrics.cacheStats.maxSize / 1024 / 1024).toFixed(2)}MB
            </Text>
            <Text style={styles.statText}>Items: {metrics.cacheStats.itemCount}</Text>
            <Text style={styles.statText}>
              Efficiency: {((metrics.cacheStats.cacheEfficiency || 0) * 100).toFixed(1)}%
            </Text>
            <Text style={styles.statText}>Policy: {metrics.cacheStats.evictionPolicy}</Text>
          </View>
        </View>

        {/* Optimization Recommendations */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recommendations</Text>
          {recommendations.length === 0 ? (
            <Text style={styles.noRecommendations}>No recommendations at this time</Text>
          ) : (
            recommendations.map((rec, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.recommendationCard, styles[`recommendation${rec.type}`]]}
                onPress={() => handleOptimizationAction(rec)}
              >
                <View style={styles.recommendationHeader}>
                  <Text style={styles.recommendationTitle}>{rec.title}</Text>
                  <Text style={[styles.recommendationPriority, styles[`priority${rec.priority}`]]}>
                    {rec.priority.toUpperCase()}
                  </Text>
                </View>
                <Text style={styles.recommendationDescription}>{rec.description}</Text>
                {rec.action && (
                  <Text style={styles.recommendationAction}>Action: {rec.action}</Text>
                )}
              </TouchableOpacity>
            ))
          )}
        </View>

        {/* Controls */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Controls</Text>
          <View style={styles.controlsContainer}>
            <View style={styles.control}>
              <Text style={styles.controlLabel}>Enable Optimization</Text>
              <Switch
                value={isOptimizationEnabled}
                onValueChange={setIsOptimizationEnabled}
                trackColor={{ false: Colors.gray300, true: Colors.sage600 }}
                thumbColor={isOptimizationEnabled ? Colors.sage800 : Colors.gray500}
              />
            </View>
            
            <TouchableOpacity style={styles.actionButton} onPress={handleClearCache}>
              <Text style={styles.actionButtonText}>Clear Cache</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={() => memoryOptimization()}
            >
              <Text style={styles.actionButtonText}>Optimize Memory</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.text.primary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  performanceIndicator: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  performancelow: {
    backgroundColor: Colors.error + '20',
    borderColor: Colors.error,
    borderWidth: 1,
  },
  performancemedium: {
    backgroundColor: Colors.warning + '20',
    borderColor: Colors.warning,
    borderWidth: 1,
  },
  performancehigh: {
    backgroundColor: Colors.success + '20',
    borderColor: Colors.success,
    borderWidth: 1,
  },
  performanceText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    backgroundColor: Colors.background.secondary,
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  cacheStats: {
    backgroundColor: Colors.background.secondary,
    padding: 16,
    borderRadius: 8,
  },
  statText: {
    fontSize: 14,
    color: Colors.text.primary,
    marginBottom: 4,
  },
  noRecommendations: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  recommendationCard: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  recommendationwarning: {
    backgroundColor: Colors.warning + '10',
    borderColor: Colors.warning,
  },
  recommendationerror: {
    backgroundColor: Colors.error + '10',
    borderColor: Colors.error,
  },
  recommendationinfo: {
    backgroundColor: Colors.info + '10',
    borderColor: Colors.info,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  recommendationPriority: {
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  prioritylow: {
    backgroundColor: Colors.gray300,
    color: Colors.text.secondary,
  },
  prioritymedium: {
    backgroundColor: Colors.warning,
    color: Colors.white,
  },
  priorityhigh: {
    backgroundColor: Colors.error,
    color: Colors.white,
  },
  prioritycritical: {
    backgroundColor: Colors.error,
    color: Colors.white,
  },
  recommendationDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  recommendationAction: {
    fontSize: 12,
    color: Colors.sage600,
    fontWeight: '500',
  },
  controlsContainer: {
    backgroundColor: Colors.background.secondary,
    padding: 16,
    borderRadius: 8,
  },
  control: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  controlLabel: {
    fontSize: 16,
    color: Colors.text.primary,
  },
  actionButton: {
    backgroundColor: Colors.sage600,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PerformanceDashboard;
