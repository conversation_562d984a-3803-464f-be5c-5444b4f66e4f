/**
 * Keyboard Navigation Hook
 *
 * Provides comprehensive keyboard navigation support for accessibility
 * and power users, following WCAG 2.2 AA guidelines.
 *
 * Features:
 * - Focus management
 * - Keyboard shortcuts
 * - Tab navigation
 * - Arrow key navigation
 * - Escape key handling
 * - Enter/Space activation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useEffect, useCallback, useRef, useState } from 'react';
import { Platform, Keyboard, BackHandler } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

// Keyboard event types
export interface KeyboardEvent {
  key: string;
  code: string;
  ctrlKey: boolean;
  shiftKey: boolean;
  altKey: boolean;
  metaKey: boolean;
  preventDefault: () => void;
  stopPropagation: () => void;
}

// Focus direction
export type FocusDirection = 'up' | 'down' | 'left' | 'right' | 'next' | 'previous';

// Keyboard shortcut configuration
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  enabled?: boolean;
}

// Focusable element interface
export interface FocusableElement {
  id: string;
  ref: React.RefObject<any>;
  priority?: number;
  group?: string;
  disabled?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onActivate?: () => void;
}

// Hook configuration
export interface KeyboardNavigationConfig {
  enabled?: boolean;
  trapFocus?: boolean;
  autoFocus?: boolean;
  shortcuts?: KeyboardShortcut[];
  onEscape?: () => void;
  onTab?: (direction: 'forward' | 'backward') => boolean;
}

export const useKeyboardNavigation = (config: KeyboardNavigationConfig = {}) => {
  const {
    enabled = true,
    trapFocus = false,
    autoFocus = false,
    shortcuts = [],
    onEscape,
    onTab,
  } = config;

  // State
  const [focusableElements, setFocusableElements] = useState<FocusableElement[]>([]);
  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  
  // Refs
  const containerRef = useRef<any>(null);
  const shortcutsRef = useRef(shortcuts);

  // Update shortcuts ref when shortcuts change
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  // Keyboard visibility listeners
  useEffect(() => {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      const showListener = Keyboard.addListener('keyboardDidShow', () => {
        setIsKeyboardVisible(true);
      });
      
      const hideListener = Keyboard.addListener('keyboardDidHide', () => {
        setIsKeyboardVisible(false);
      });

      return () => {
        showListener.remove();
        hideListener.remove();
      };
    }
  }, []);

  // Register focusable element
  const registerFocusableElement = useCallback((element: FocusableElement) => {
    setFocusableElements(prev => {
      const filtered = prev.filter(el => el.id !== element.id);
      const newElements = [...filtered, element];
      return newElements.sort((a, b) => (a.priority || 0) - (b.priority || 0));
    });
  }, []);

  // Unregister focusable element
  const unregisterFocusableElement = useCallback((id: string) => {
    setFocusableElements(prev => prev.filter(el => el.id !== id));
  }, []);

  // Get next focusable element
  const getNextFocusableElement = useCallback((
    direction: FocusDirection,
    currentIndex: number = currentFocusIndex
  ): number => {
    const enabledElements = focusableElements.filter(el => !el.disabled);
    
    if (enabledElements.length === 0) return -1;

    switch (direction) {
      case 'next':
      case 'down':
      case 'right':
        return currentIndex >= enabledElements.length - 1 ? 0 : currentIndex + 1;
      
      case 'previous':
      case 'up':
      case 'left':
        return currentIndex <= 0 ? enabledElements.length - 1 : currentIndex - 1;
      
      default:
        return currentIndex;
    }
  }, [focusableElements, currentFocusIndex]);

  // Set focus to element
  const setFocusToElement = useCallback((index: number) => {
    const enabledElements = focusableElements.filter(el => !el.disabled);
    
    if (index >= 0 && index < enabledElements.length) {
      const element = enabledElements[index];
      
      if (element.ref.current) {
        // Call onBlur for previous element
        if (currentFocusIndex >= 0 && currentFocusIndex < enabledElements.length) {
          const prevElement = enabledElements[currentFocusIndex];
          if (prevElement.onBlur) {
            prevElement.onBlur();
          }
        }

        // Focus new element
        if (element.ref.current.focus) {
          element.ref.current.focus();
        }
        
        // Call onFocus for new element
        if (element.onFocus) {
          element.onFocus();
        }
        
        setCurrentFocusIndex(index);
        return true;
      }
    }
    
    return false;
  }, [focusableElements, currentFocusIndex]);

  // Move focus in direction
  const moveFocus = useCallback((direction: FocusDirection) => {
    if (!enabled) return false;
    
    const nextIndex = getNextFocusableElement(direction);
    return setFocusToElement(nextIndex);
  }, [enabled, getNextFocusableElement, setFocusToElement]);

  // Activate current element
  const activateCurrentElement = useCallback(() => {
    const enabledElements = focusableElements.filter(el => !el.disabled);
    
    if (currentFocusIndex >= 0 && currentFocusIndex < enabledElements.length) {
      const element = enabledElements[currentFocusIndex];
      
      if (element.onActivate) {
        element.onActivate();
        return true;
      }
      
      // Try to trigger press/click
      if (element.ref.current) {
        if (element.ref.current.props?.onPress) {
          element.ref.current.props.onPress();
          return true;
        }
      }
    }
    
    return false;
  }, [focusableElements, currentFocusIndex]);

  // Handle keyboard shortcuts
  const handleKeyboardShortcut = useCallback((event: KeyboardEvent) => {
    const activeShortcuts = shortcutsRef.current.filter(shortcut => 
      shortcut.enabled !== false
    );

    for (const shortcut of activeShortcuts) {
      const keyMatches = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatches = (shortcut.ctrlKey || false) === event.ctrlKey;
      const shiftMatches = (shortcut.shiftKey || false) === event.shiftKey;
      const altMatches = (shortcut.altKey || false) === event.altKey;
      const metaMatches = (shortcut.metaKey || false) === event.metaKey;

      if (keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches) {
        event.preventDefault();
        event.stopPropagation();
        shortcut.action();
        return true;
      }
    }

    return false;
  }, []);

  // Handle key press
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!enabled) return false;

    // Handle shortcuts first
    if (handleKeyboardShortcut(event)) {
      return true;
    }

    switch (event.key) {
      case 'Tab':
        event.preventDefault();
        const direction = event.shiftKey ? 'previous' : 'next';
        
        // Call custom tab handler if provided
        if (onTab && onTab(direction === 'next' ? 'forward' : 'backward')) {
          return true;
        }
        
        return moveFocus(direction);

      case 'ArrowUp':
        event.preventDefault();
        return moveFocus('up');

      case 'ArrowDown':
        event.preventDefault();
        return moveFocus('down');

      case 'ArrowLeft':
        event.preventDefault();
        return moveFocus('left');

      case 'ArrowRight':
        event.preventDefault();
        return moveFocus('right');

      case 'Enter':
      case ' ':
        event.preventDefault();
        return activateCurrentElement();

      case 'Escape':
        if (onEscape) {
          event.preventDefault();
          onEscape();
          return true;
        }
        break;

      case 'Home':
        event.preventDefault();
        return setFocusToElement(0);

      case 'End':
        event.preventDefault();
        const enabledElements = focusableElements.filter(el => !el.disabled);
        return setFocusToElement(enabledElements.length - 1);

      default:
        break;
    }

    return false;
  }, [
    enabled,
    handleKeyboardShortcut,
    onTab,
    moveFocus,
    activateCurrentElement,
    onEscape,
    setFocusToElement,
    focusableElements,
  ]);

  // Auto focus first element
  useEffect(() => {
    if (autoFocus && focusableElements.length > 0 && currentFocusIndex === -1) {
      setFocusToElement(0);
    }
  }, [autoFocus, focusableElements, currentFocusIndex, setFocusToElement]);

  // Set up keyboard event listeners (web only)
  useEffect(() => {
    if (Platform.OS === 'web' && enabled) {
      const handleKeyDown = (event: any) => {
        handleKeyPress(event);
      };

      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [enabled, handleKeyPress]);

  // Handle Android back button
  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android' && enabled) {
        const onBackPress = () => {
          if (onEscape) {
            onEscape();
            return true;
          }
          return false;
        };

        const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
        return () => subscription.remove();
      }
    }, [enabled, onEscape])
  );

  // Focus trap effect
  useEffect(() => {
    if (trapFocus && containerRef.current && Platform.OS === 'web') {
      const container = containerRef.current;
      
      const handleFocusIn = (event: FocusEvent) => {
        const target = event.target as Element;
        
        if (!container.contains(target)) {
          event.preventDefault();
          setFocusToElement(0);
        }
      };

      document.addEventListener('focusin', handleFocusIn);
      
      return () => {
        document.removeEventListener('focusin', handleFocusIn);
      };
    }
  }, [trapFocus, setFocusToElement]);

  return {
    // State
    focusableElements,
    currentFocusIndex,
    isKeyboardVisible,
    
    // Actions
    registerFocusableElement,
    unregisterFocusableElement,
    moveFocus,
    setFocusToElement,
    activateCurrentElement,
    
    // Refs
    containerRef,
    
    // Utilities
    getNextFocusableElement,
    handleKeyPress,
  };
};

export default useKeyboardNavigation;
