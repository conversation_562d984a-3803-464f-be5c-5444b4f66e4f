6f1cf9805f588b6257b8cc2ca3fb441a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.testHelpers = exports.testDataUtils = exports.testAssertions = exports.setupTestEnvironment = exports.performanceTestUtils = exports.mockDataGenerators = exports.default = exports.accessibilityTestUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var mockDataGenerators = exports.mockDataGenerators = {
  user: function user() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `user_${Math.random().toString(36).substr(2, 9)}`,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: 'https://example.com/avatar.jpg',
      createdAt: new Date().toISOString()
    }, overrides);
  },
  service: function service() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `service_${Math.random().toString(36).substr(2, 9)}`,
      title: 'House Cleaning',
      description: 'Professional house cleaning service',
      category: 'cleaning',
      price: 75.00,
      duration: 120,
      rating: 4.5,
      reviewCount: 128,
      images: ['https://example.com/service1.jpg']
    }, overrides);
  },
  provider: function provider() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `provider_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Jane Smith',
      company: 'Smith Cleaning Services',
      rating: 4.8,
      reviewCount: 256,
      verified: true,
      experience: 5,
      location: 'Toronto, ON',
      avatar: 'https://example.com/provider.jpg',
      services: ['cleaning', 'maintenance']
    }, overrides);
  },
  booking: function booking() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `booking_${Math.random().toString(36).substr(2, 9)}`,
      serviceId: 'service_123',
      providerId: 'provider_456',
      customerId: 'user_789',
      date: new Date().toISOString(),
      status: 'confirmed',
      price: 75.00,
      address: '123 Main St, Toronto, ON M5V 3A8',
      notes: 'Please call when arriving'
    }, overrides);
  },
  address: function address() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `address_${Math.random().toString(36).substr(2, 9)}`,
      streetNumber: '123',
      streetName: 'Main Street',
      city: 'Toronto',
      province: 'ON',
      postalCode: 'M5V 3A8',
      country: 'Canada',
      coordinates: {
        latitude: 43.6532,
        longitude: -79.3832
      }
    }, overrides);
  },
  review: function review() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: `review_${Math.random().toString(36).substr(2, 9)}`,
      rating: 5,
      comment: 'Excellent service! Highly recommended.',
      authorName: 'Happy Customer',
      date: new Date().toISOString(),
      verified: true,
      helpful: 12
    }, overrides);
  }
};
var setupTestEnvironment = exports.setupTestEnvironment = function setupTestEnvironment() {
  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var defaultConfig = {
    enableAccessibilityTesting: true,
    enablePerformanceTesting: true,
    mockNetworkRequests: true,
    mockLocationServices: true,
    mockNotifications: true,
    logLevel: 'warn'
  };
  var finalConfig = Object.assign({}, defaultConfig, config);
  if (finalConfig.logLevel === 'silent') {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
  if (finalConfig.mockNetworkRequests) {
    global.fetch = jest.fn(function () {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: function json() {
          return Promise.resolve({});
        },
        text: function text() {
          return Promise.resolve('');
        }
      });
    });
  }
  if (finalConfig.mockLocationServices) {
    var mockGeolocation = {
      getCurrentPosition: jest.fn(function (success) {
        return success({
          coords: {
            latitude: 43.6532,
            longitude: -79.3832,
            accuracy: 10
          }
        });
      }),
      watchPosition: jest.fn(),
      clearWatch: jest.fn()
    };
    Object.defineProperty(global.navigator, 'geolocation', {
      value: mockGeolocation,
      writable: true
    });
  }
  if (finalConfig.mockNotifications) {
    var mockNotifications = {
      requestPermissionsAsync: jest.fn(function () {
        return Promise.resolve({
          status: 'granted'
        });
      }),
      scheduleNotificationAsync: jest.fn(function () {
        return Promise.resolve('notification-id');
      }),
      cancelNotificationAsync: jest.fn(function () {
        return Promise.resolve();
      })
    };
    jest.doMock('expo-notifications', function () {
      return mockNotifications;
    });
  }
  return finalConfig;
};
var accessibilityTestUtils = exports.accessibilityTestUtils = {
  hasAccessibilityLabel: function hasAccessibilityLabel(element) {
    return !!(element.props.accessibilityLabel || element.props['aria-label']);
  },
  hasAccessibilityRole: function hasAccessibilityRole(element) {
    return !!(element.props.accessibilityRole || element.props.role);
  },
  hasAccessibilityHint: function hasAccessibilityHint(element) {
    return !!element.props.accessibilityHint;
  },
  isFocusable: function isFocusable(element) {
    return element.props.accessible !== false && (element.props.accessibilityRole === 'button' || element.props.accessibilityRole === 'link' || element.props.onPress || element.props.onFocus);
  },
  hasSufficientTouchTarget: function hasSufficientTouchTarget(element) {
    var style = element.props.style;
    if (!style) return false;
    var minSize = 44;
    var width = style.width || style.minWidth;
    var height = style.height || style.minHeight;
    return width >= minSize && height >= minSize;
  },
  auditAccessibility: function auditAccessibility(component) {
    var violations = [];
    var warnings = [];
    var suggestions = [];
    var interactiveElements = component.findAll(function (node) {
      return node.props.onPress || node.props.accessibilityRole === 'button' || node.props.accessibilityRole === 'link';
    });
    interactiveElements.forEach(function (element, index) {
      var elementId = `element-${index}`;
      if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
        violations.push({
          rule: 'accessibility-label-required',
          severity: 'error',
          message: 'Interactive element must have accessibility label',
          element: elementId
        });
      }
      if (!accessibilityTestUtils.hasAccessibilityRole(element)) {
        warnings.push(`Element ${elementId} should have accessibility role`);
      }
      if (!accessibilityTestUtils.hasSufficientTouchTarget(element)) {
        violations.push({
          rule: 'touch-target-size',
          severity: 'warning',
          message: 'Touch target should be at least 44x44 points',
          element: elementId
        });
      }
    });
    var images = component.findAll(function (node) {
      return node.type === 'Image';
    });
    images.forEach(function (image, index) {
      if (!image.props.accessibilityLabel && !image.props.alt) {
        suggestions.push(`Image ${index} should have descriptive accessibility label`);
      }
    });
    return {
      passed: violations.filter(function (v) {
        return v.severity === 'error';
      }).length === 0,
      violations: violations,
      warnings: warnings,
      suggestions: suggestions
    };
  }
};
var performanceTestUtils = exports.performanceTestUtils = {
  measureRenderTime: function () {
    var _measureRenderTime = (0, _asyncToGenerator2.default)(function* (renderFunction) {
      var startTime = performance.now();
      yield renderFunction();
      var endTime = performance.now();
      return endTime - startTime;
    });
    function measureRenderTime(_x) {
      return _measureRenderTime.apply(this, arguments);
    }
    return measureRenderTime;
  }(),
  measureMemoryUsage: function measureMemoryUsage() {
    return Math.random() * 100;
  },
  countComponents: function countComponents(component) {
    var count = 1;
    if (component.children) {
      component.children.forEach(function (child) {
        if (typeof child === 'object' && 'type' in child) {
          count += performanceTestUtils.countComponents(child);
        }
      });
    }
    return count;
  },
  auditPerformance: function () {
    var _auditPerformance = (0, _asyncToGenerator2.default)(function* (component) {
      var thresholds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
        maxRenderTime: 100,
        maxMemoryUsage: 50,
        maxComponentCount: 100
      };
      var renderTime = yield performanceTestUtils.measureRenderTime((0, _asyncToGenerator2.default)(function* () {
        yield new Promise(function (resolve) {
          return setTimeout(resolve, 10);
        });
      }));
      var memoryUsage = performanceTestUtils.measureMemoryUsage();
      var componentCount = performanceTestUtils.countComponents(component);
      var passed = renderTime <= thresholds.maxRenderTime && memoryUsage <= thresholds.maxMemoryUsage && componentCount <= thresholds.maxComponentCount;
      return {
        renderTime: renderTime,
        memoryUsage: memoryUsage,
        componentCount: componentCount,
        passed: passed,
        thresholds: thresholds
      };
    });
    function auditPerformance(_x2) {
      return _auditPerformance.apply(this, arguments);
    }
    return auditPerformance;
  }()
};
var testDataUtils = exports.testDataUtils = {
  generateArray: function generateArray(generator, count) {
    return Array.from({
      length: count
    }, generator);
  },
  generateApiResponse: function generateApiResponse(data) {
    var success = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    return {
      success: success,
      data: success ? data : null,
      error: success ? null : 'Mock error message',
      timestamp: new Date().toISOString()
    };
  },
  generatePaginatedResponse: function generatePaginatedResponse(items) {
    var page = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;
    var startIndex = (page - 1) * limit;
    var endIndex = startIndex + limit;
    var paginatedItems = items.slice(startIndex, endIndex);
    return {
      items: paginatedItems,
      pagination: {
        page: page,
        limit: limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }
};
var testAssertions = exports.testAssertions = {
  assertAccessible: function assertAccessible(component) {
    var result = accessibilityTestUtils.auditAccessibility(component);
    if (!result.passed) {
      var errorMessages = result.violations.filter(function (v) {
        return v.severity === 'error';
      }).map(function (v) {
        return `${v.rule}: ${v.message}`;
      }).join('\n');
      throw new Error(`Accessibility violations found:\n${errorMessages}`);
    }
  },
  assertPerformant: function () {
    var _assertPerformant = (0, _asyncToGenerator2.default)(function* (component, thresholds) {
      var result = yield performanceTestUtils.auditPerformance(component, thresholds);
      if (!result.passed) {
        var issues = [];
        if (result.renderTime > result.thresholds.maxRenderTime) {
          issues.push(`Render time ${result.renderTime}ms exceeds threshold ${result.thresholds.maxRenderTime}ms`);
        }
        if (result.memoryUsage > result.thresholds.maxMemoryUsage) {
          issues.push(`Memory usage ${result.memoryUsage}MB exceeds threshold ${result.thresholds.maxMemoryUsage}MB`);
        }
        if (result.componentCount > result.thresholds.maxComponentCount) {
          issues.push(`Component count ${result.componentCount} exceeds threshold ${result.thresholds.maxComponentCount}`);
        }
        throw new Error(`Performance issues found:\n${issues.join('\n')}`);
      }
    });
    function assertPerformant(_x3, _x4) {
      return _assertPerformant.apply(this, arguments);
    }
    return assertPerformant;
  }(),
  assertElementAccessible: function assertElementAccessible(component, testID) {
    var element = component.findByProps({
      testID: testID
    });
    if (!accessibilityTestUtils.hasAccessibilityLabel(element)) {
      throw new Error(`Element with testID "${testID}" must have accessibility label`);
    }
    if (accessibilityTestUtils.isFocusable(element) && !accessibilityTestUtils.hasSufficientTouchTarget(element)) {
      throw new Error(`Focusable element with testID "${testID}" must have sufficient touch target size`);
    }
  }
};
var testHelpers = exports.testHelpers = {
  waitForElement: function () {
    var _waitForElement = (0, _asyncToGenerator2.default)(function* (component, testID) {
      var timeout = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5000;
      var startTime = Date.now();
      while (Date.now() - startTime < timeout) {
        try {
          return component.findByProps({
            testID: testID
          });
        } catch (_unused) {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 100);
          });
        }
      }
      throw new Error(`Element with testID "${testID}" not found within ${timeout}ms`);
    });
    function waitForElement(_x5, _x6) {
      return _waitForElement.apply(this, arguments);
    }
    return waitForElement;
  }(),
  simulatePress: function () {
    var _simulatePress = (0, _asyncToGenerator2.default)(function* (element) {
      if (element.props.onPress) {
        yield element.props.onPress();
      }
    });
    function simulatePress(_x7) {
      return _simulatePress.apply(this, arguments);
    }
    return simulatePress;
  }(),
  simulateTextInput: function () {
    var _simulateTextInput = (0, _asyncToGenerator2.default)(function* (element, text) {
      if (element.props.onChangeText) {
        yield element.props.onChangeText(text);
      }
    });
    function simulateTextInput(_x8, _x9) {
      return _simulateTextInput.apply(this, arguments);
    }
    return simulateTextInput;
  }(),
  createTestWrapper: function createTestWrapper(children) {
    return children;
  }
};
var _default = exports.default = {
  mockDataGenerators: mockDataGenerators,
  setupTestEnvironment: setupTestEnvironment,
  accessibilityTestUtils: accessibilityTestUtils,
  performanceTestUtils: performanceTestUtils,
  testDataUtils: testDataUtils,
  testAssertions: testAssertions,
  testHelpers: testHelpers
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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