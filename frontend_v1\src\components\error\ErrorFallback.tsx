/**
 * Error Fallback Component
 *
 * Comprehensive error fallback component for handling various error states
 * with user-friendly displays and recovery options.
 *
 * Features:
 * - Multiple error state handling
 * - User-friendly error messages
 * - Recovery actions
 * - Accessibility compliance
 * - Customizable layouts
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { View, StyleSheet, Image, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Heading, Body } from '../typography/Typography';
import { AnimatedButton } from '../animation/AnimatedButton';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { AppError } from '../../utils/errorHandlingUtils';

// Error types for different fallback displays
export type ErrorFallbackType = 
  | 'network'
  | 'not_found'
  | 'unauthorized'
  | 'server'
  | 'maintenance'
  | 'offline'
  | 'generic';

// Component props
export interface ErrorFallbackProps {
  // Error information
  error?: AppError | Error | null;
  type?: ErrorFallbackType;
  title?: string;
  message?: string;
  
  // Actions
  onRetry?: () => void;
  onGoBack?: () => void;
  onContactSupport?: () => void;
  
  // Configuration
  showRetry?: boolean;
  showGoBack?: boolean;
  showContactSupport?: boolean;
  showErrorDetails?: boolean;
  
  // Styling
  variant?: 'full-screen' | 'inline' | 'compact';
  style?: any;
  
  // Testing
  testID?: string;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  type = 'generic',
  title,
  message,
  onRetry,
  onGoBack,
  onContactSupport,
  showRetry = true,
  showGoBack = true,
  showContactSupport = false,
  showErrorDetails = __DEV__,
  variant = 'full-screen',
  style,
  testID,
}) => {
  // Hooks
  const { t } = useTranslation();
  const { colors } = useHighContrastColors();

  // Get error type from error object if not provided
  const getErrorType = (): ErrorFallbackType => {
    if (type !== 'generic') return type;
    
    if (error && 'type' in error) {
      const errorType = (error as AppError).type;
      switch (errorType) {
        case 'network':
        case 'timeout':
          return 'network';
        case 'not_found':
          return 'not_found';
        case 'authentication':
        case 'authorization':
          return 'unauthorized';
        case 'server':
          return 'server';
        case 'offline':
          return 'offline';
        default:
          return 'generic';
      }
    }
    
    return 'generic';
  };

  const errorType = getErrorType();

  // Get error content based on type
  const getErrorContent = () => {
    const content = {
      network: {
        icon: 'wifi-off',
        title: t('errors.network_error_title', 'Connection Problem'),
        message: t('errors.network_error_message', 'Unable to connect to the internet. Please check your connection and try again.'),
        illustration: '🌐',
      },
      not_found: {
        icon: 'search',
        title: t('errors.not_found_title', 'Page Not Found'),
        message: t('errors.not_found_message', 'The page you\'re looking for doesn\'t exist or has been moved.'),
        illustration: '🔍',
      },
      unauthorized: {
        icon: 'lock-closed',
        title: t('errors.unauthorized_title', 'Access Denied'),
        message: t('errors.unauthorized_message', 'You don\'t have permission to access this content.'),
        illustration: '🔒',
      },
      server: {
        icon: 'server',
        title: t('errors.server_error_title', 'Server Error'),
        message: t('errors.server_error_message', 'We\'re experiencing technical difficulties. Please try again later.'),
        illustration: '⚙️',
      },
      maintenance: {
        icon: 'construct',
        title: t('errors.maintenance_title', 'Under Maintenance'),
        message: t('errors.maintenance_message', 'We\'re currently performing maintenance. Please check back soon.'),
        illustration: '🔧',
      },
      offline: {
        icon: 'cloud-offline',
        title: t('errors.offline_title', 'You\'re Offline'),
        message: t('errors.offline_message', 'Please check your internet connection and try again.'),
        illustration: '📡',
      },
      generic: {
        icon: 'alert-circle',
        title: t('errors.generic_title', 'Something Went Wrong'),
        message: t('errors.generic_message', 'An unexpected error occurred. Please try again.'),
        illustration: '⚠️',
      },
    };

    return content[errorType];
  };

  const errorContent = getErrorContent();

  // Render error icon
  const renderErrorIcon = () => {
    if (variant === 'compact') {
      return (
        <Typography variant="h2" style={styles.compactIcon}>
          {errorContent.illustration}
        </Typography>
      );
    }

    return (
      <View style={[styles.iconContainer, { backgroundColor: colors?.status?.error || '#FF6B6B' }]}>
        <Ionicons
          name={errorContent.icon as any}
          size={variant === 'inline' ? 32 : 48}
          color={colors?.text?.inverse || '#FFFFFF'}
        />
      </View>
    );
  };

  // Render error illustration
  const renderErrorIllustration = () => {
    if (variant !== 'full-screen') return null;

    return (
      <View style={styles.illustrationContainer}>
        <Typography variant="h1" style={styles.illustration}>
          {errorContent.illustration}
        </Typography>
      </View>
    );
  };

  // Render action buttons
  const renderActions = () => {
    const actions = [];

    if (showRetry && onRetry) {
      actions.push(
        <AnimatedButton
          key="retry"
          title={t('common.retry')}
          onPress={onRetry}
          variant="primary"
          icon="refresh"
          style={styles.actionButton}
          accessibilityLabel={t('common.retry')}
          accessibilityHint={t('errors.retry_hint', 'Try the action again')}
        />
      );
    }

    if (showGoBack && onGoBack) {
      actions.push(
        <AnimatedButton
          key="go-back"
          title={t('common.back')}
          onPress={onGoBack}
          variant="outline"
          icon="arrow-back"
          style={styles.actionButton}
          accessibilityLabel={t('common.back')}
          accessibilityHint={t('errors.go_back_hint', 'Go back to the previous page')}
        />
      );
    }

    if (showContactSupport && onContactSupport) {
      actions.push(
        <AnimatedButton
          key="contact-support"
          title={t('navigation.support')}
          onPress={onContactSupport}
          variant="ghost"
          icon="help-circle"
          style={styles.actionButton}
          accessibilityLabel={t('navigation.support')}
          accessibilityHint={t('errors.contact_support_hint', 'Contact customer support')}
        />
      );
    }

    if (actions.length === 0) return null;

    return (
      <View style={styles.actionsContainer}>
        {actions}
      </View>
    );
  };

  // Render error details (development only)
  const renderErrorDetails = () => {
    if (!showErrorDetails || !error) return null;

    const errorMessage = error.message || 'Unknown error';
    const errorStack = 'stack' in error ? error.stack : undefined;

    return (
      <View style={[styles.detailsContainer, { backgroundColor: colors?.background?.secondary }]}>
        <Typography
          variant="label"
          color={colors?.text?.primary}
          style={styles.detailsTitle}
        >
          {t('errors.error_details', 'Error Details')} (Development)
        </Typography>
        
        <Body
          color={colors?.text?.secondary}
          style={styles.detailsText}
        >
          {errorMessage}
        </Body>
        
        {errorStack && (
          <Body
            color={colors?.text?.tertiary}
            style={styles.stackTrace}
          >
            {errorStack}
          </Body>
        )}
      </View>
    );
  };

  // Get container styles based on variant
  const getContainerStyles = () => {
    const baseStyles = [styles.container];
    
    switch (variant) {
      case 'full-screen':
        baseStyles.push(styles.fullScreenContainer);
        break;
      case 'inline':
        baseStyles.push(styles.inlineContainer);
        break;
      case 'compact':
        baseStyles.push(styles.compactContainer);
        break;
    }
    
    baseStyles.push({ backgroundColor: colors?.background?.primary || '#FFFFFF' });
    baseStyles.push(style);
    
    return baseStyles;
  };

  // Render content based on variant
  const renderContent = () => {
    const content = (
      <>
        {renderErrorIllustration()}
        {renderErrorIcon()}
        
        <Heading
          level={variant === 'compact' ? 4 : variant === 'inline' ? 3 : 2}
          color={colors?.text?.primary}
          align="center"
          style={styles.title}
        >
          {title || errorContent.title}
        </Heading>
        
        <Body
          color={colors?.text?.secondary}
          align="center"
          style={styles.message}
        >
          {message || errorContent.message}
        </Body>
        
        {renderActions()}
        {renderErrorDetails()}
      </>
    );

    if (variant === 'full-screen') {
      return (
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {content}
        </ScrollView>
      );
    }

    return content;
  };

  return (
    <View style={getContainerStyles()} testID={testID}>
      {renderContent()}
    </View>
  );
};

// Specialized error fallback components
export const NetworkErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="network" />
);

export const NotFoundErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="not_found" />
);

export const UnauthorizedErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="unauthorized" />
);

export const ServerErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="server" />
);

export const MaintenanceErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="maintenance" />
);

export const OfflineErrorFallback: React.FC<Omit<ErrorFallbackProps, 'type'>> = (props) => (
  <ErrorFallback {...props} type="offline" />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreenContainer: {
    padding: 20,
    minHeight: '100%',
  },
  inlineContainer: {
    padding: 16,
    minHeight: 200,
  },
  compactContainer: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 60,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  illustrationContainer: {
    marginBottom: 24,
  },
  illustration: {
    fontSize: 80,
    textAlign: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  compactIcon: {
    marginRight: 12,
    fontSize: 24,
  },
  title: {
    marginBottom: 12,
    maxWidth: 300,
  },
  message: {
    marginBottom: 32,
    maxWidth: 400,
    lineHeight: 24,
  },
  actionsContainer: {
    width: '100%',
    maxWidth: 300,
    gap: 12,
  },
  actionButton: {
    width: '100%',
  },
  detailsContainer: {
    marginTop: 32,
    padding: 16,
    borderRadius: 8,
    width: '100%',
    maxWidth: 500,
  },
  detailsTitle: {
    marginBottom: 8,
    fontWeight: '600',
  },
  detailsText: {
    marginBottom: 12,
    fontFamily: 'monospace',
    fontSize: 12,
  },
  stackTrace: {
    fontFamily: 'monospace',
    fontSize: 10,
    lineHeight: 14,
  },
});

export default ErrorFallback;
