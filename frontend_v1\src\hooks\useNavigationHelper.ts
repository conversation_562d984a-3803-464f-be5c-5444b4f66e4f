/**
 * Navigation Helper Hook
 *
 * Provides navigation utilities, breadcrumb generation, and wayfinding features
 * to improve user navigation experience throughout the app.
 *
 * Features:
 * - Automatic breadcrumb generation
 * - Navigation history tracking
 * - Back button handling
 * - Deep link support
 * - Navigation analytics
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useEffect, useState } from 'react';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { BackHandler, Platform } from 'react-native';
import { BreadcrumbItem } from '../components/navigation/BreadcrumbNavigation';

// Navigation history item
interface NavigationHistoryItem {
  routeName: string;
  params?: any;
  timestamp: number;
  title?: string;
}

// Route configuration for breadcrumbs
interface RouteConfig {
  title: string;
  icon?: string;
  parent?: string;
  showInBreadcrumb?: boolean;
}

// Route configurations
const ROUTE_CONFIGS: Record<string, RouteConfig> = {
  'CustomerTabs': {
    title: 'Home',
    icon: 'home',
    showInBreadcrumb: false,
  },
  'CustomerHome': {
    title: 'Home',
    icon: 'home',
    showInBreadcrumb: true,
  },
  'ProviderDetails': {
    title: 'Provider Details',
    icon: 'business',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
  'ServiceDetails': {
    title: 'Service Details',
    icon: 'cut',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
  'BookingScreen': {
    title: 'Book Service',
    icon: 'calendar',
    parent: 'ServiceDetails',
    showInBreadcrumb: true,
  },
  'BookingConfirmation': {
    title: 'Booking Confirmation',
    icon: 'checkmark-circle',
    parent: 'BookingScreen',
    showInBreadcrumb: true,
  },
  'BookingDetails': {
    title: 'Booking Details',
    icon: 'document-text',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
  'Notifications': {
    title: 'Notifications',
    icon: 'notifications',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
  'EditProfile': {
    title: 'Edit Profile',
    icon: 'person',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
  'AccountSettings': {
    title: 'Account Settings',
    icon: 'settings',
    parent: 'CustomerHome',
    showInBreadcrumb: true,
  },
};

export const useNavigationHelper = () => {
  const navigation = useNavigation();
  const route = useRoute();
  
  // State
  const [navigationHistory, setNavigationHistory] = useState<NavigationHistoryItem[]>([]);
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);

  // Add current route to history
  const addToHistory = useCallback((routeName: string, params?: any, title?: string) => {
    const historyItem: NavigationHistoryItem = {
      routeName,
      params,
      timestamp: Date.now(),
      title: title || ROUTE_CONFIGS[routeName]?.title || routeName,
    };

    setNavigationHistory(prev => {
      // Remove duplicate entries and limit history size
      const filtered = prev.filter(item => 
        item.routeName !== routeName || JSON.stringify(item.params) !== JSON.stringify(params)
      );
      return [...filtered, historyItem].slice(-10); // Keep last 10 items
    });
  }, []);

  // Generate breadcrumbs for current route
  const generateBreadcrumbs = useCallback((routeName: string, params?: any): BreadcrumbItem[] => {
    const breadcrumbItems: BreadcrumbItem[] = [];
    const config = ROUTE_CONFIGS[routeName];

    if (!config || !config.showInBreadcrumb) {
      return [];
    }

    // Build breadcrumb chain
    let currentRoute = routeName;
    const visited = new Set<string>();

    while (currentRoute && !visited.has(currentRoute)) {
      visited.add(currentRoute);
      const currentConfig = ROUTE_CONFIGS[currentRoute];
      
      if (currentConfig && currentConfig.showInBreadcrumb) {
        breadcrumbItems.unshift({
          id: currentRoute,
          label: currentConfig.title,
          path: currentRoute,
          icon: currentConfig.icon,
          isActive: currentRoute === routeName,
          metadata: { params },
        });
      }

      currentRoute = currentConfig?.parent || '';
    }

    return breadcrumbItems;
  }, []);

  // Handle breadcrumb navigation
  const handleBreadcrumbNavigation = useCallback((item: BreadcrumbItem) => {
    if (item.path && item.path !== route.name) {
      const params = item.metadata?.params;
      navigation.navigate(item.path as never, params as never);
    }
  }, [navigation, route.name]);

  // Handle back button
  const handleBackPress = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
      return true;
    }
    return false;
  }, [navigation]);

  // Get navigation context
  const getNavigationContext = useCallback(() => {
    const currentConfig = ROUTE_CONFIGS[route.name];
    const canGoBack = navigation.canGoBack();
    const hasHistory = navigationHistory.length > 0;

    return {
      currentRoute: route.name,
      currentTitle: currentConfig?.title || route.name,
      canGoBack,
      hasHistory,
      breadcrumbs,
      navigationHistory: navigationHistory.slice(-5), // Last 5 items
    };
  }, [route.name, navigation, breadcrumbs, navigationHistory]);

  // Navigate with context
  const navigateWithContext = useCallback((routeName: string, params?: any, title?: string) => {
    addToHistory(route.name, route.params, ROUTE_CONFIGS[route.name]?.title);
    navigation.navigate(routeName as never, params as never);
  }, [navigation, route, addToHistory]);

  // Go back with context
  const goBackWithContext = useCallback(() => {
    if (handleBackPress()) {
      // Remove current route from history if going back
      setNavigationHistory(prev => prev.slice(0, -1));
    }
  }, [handleBackPress]);

  // Update breadcrumbs when route changes
  useEffect(() => {
    const newBreadcrumbs = generateBreadcrumbs(route.name, route.params);
    setBreadcrumbs(newBreadcrumbs);
  }, [route.name, route.params, generateBreadcrumbs]);

  // Handle hardware back button on Android
  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android') {
        const onBackPress = () => {
          return handleBackPress();
        };

        const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
        return () => subscription.remove();
      }
    }, [handleBackPress])
  );

  // Track route changes
  useFocusEffect(
    useCallback(() => {
      addToHistory(route.name, route.params, ROUTE_CONFIGS[route.name]?.title);
    }, [route.name, route.params, addToHistory])
  );

  return {
    // Navigation functions
    navigateWithContext,
    goBackWithContext,
    handleBreadcrumbNavigation,
    
    // State
    breadcrumbs,
    navigationHistory,
    
    // Context
    getNavigationContext,
    
    // Utilities
    canGoBack: navigation.canGoBack(),
    currentRoute: route.name,
    currentParams: route.params,
  };
};

export default useNavigationHelper;
