/**
 * Enhanced Provider Profile Component
 * Comprehensive provider profile with portfolios, certifications, and detailed service offerings
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  images: string[];
  category: string;
  completedAt: string;
  clientTestimonial?: string;
  beforeAfter?: {
    before: string;
    after: string;
  };
}

interface Certification {
  id: string;
  name: string;
  issuer: string;
  dateObtained: string;
  expiryDate?: string;
  credentialId?: string;
  verificationUrl?: string;
  badgeImage?: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

interface ServiceOffering {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: number;
  price: {
    min: number;
    max: number;
    currency: string;
  };
  images: string[];
  features: string[];
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    description: string;
  }>;
  availability: {
    daysOfWeek: string[];
    timeSlots: string[];
  };
  requirements?: string[];
  cancellationPolicy: string;
}

interface EnhancedProvider {
  id: string;
  businessName: string;
  bio: string;
  profileImage: string;
  coverImage?: string;
  rating: number;
  reviewCount: number;
  completedJobs: number;
  yearsOfExperience: number;
  responseTime: string;
  location: {
    address: string;
    city: string;
    distance?: string;
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
  };
  portfolio: PortfolioItem[];
  certifications: Certification[];
  services: ServiceOffering[];
  specialties: string[];
  languages: string[];
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  policies: {
    cancellation: string;
    rescheduling: string;
    payment: string[];
  };
  verification: {
    isVerified: boolean;
    verifiedBadges: string[];
    backgroundCheck: boolean;
    insurance: boolean;
  };
}

interface EnhancedProviderProfileProps {
  provider: EnhancedProvider;
  onBookService: (service: ServiceOffering) => void;
  onContactProvider: () => void;
  onViewPortfolioItem: (item: PortfolioItem) => void;
  onViewCertification: (cert: Certification) => void;
}

const EnhancedProviderProfile: React.FC<EnhancedProviderProfileProps> = ({
  provider,
  onBookService,
  onContactProvider,
  onViewPortfolioItem,
  onViewCertification,
}) => {
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'portfolio' | 'certifications' | 'services'>('overview');
  const screenWidth = Dimensions.get('window').width;

  const renderHeader = () => (
    <View style={styles.header}>
      {provider.coverImage && (
        <Image source={{ uri: provider.coverImage }} style={styles.coverImage} />
      )}
      
      <View style={[styles.headerContent, { backgroundColor: colors?.background?.primary || Colors.background?.primary }]}>
        <View style={styles.profileSection}>
          <Image source={{ uri: provider.profileImage }} style={styles.profileImage} />
          
          <View style={styles.profileInfo}>
            <View style={styles.nameSection}>
              <Text style={[styles.businessName, { color: colors?.text?.primary || Colors.text?.primary }]}>
                {provider.businessName}
              </Text>
              {provider.verification.isVerified && (
                <Ionicons name="checkmark-circle" size={20} color={Colors.success?.main} />
              )}
            </View>
            
            <View style={styles.ratingSection}>
              <Ionicons name="star" size={16} color={Colors.warning?.main} />
              <Text style={[styles.rating, { color: colors?.text?.primary || Colors.text?.primary }]}>
                {provider.rating}
              </Text>
              <Text style={[styles.reviewCount, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                ({provider.reviewCount} reviews)
              </Text>
            </View>
            
            <View style={styles.statsSection}>
              <View style={styles.stat}>
                <Text style={[styles.statValue, { color: colors?.text?.primary || Colors.text?.primary }]}>
                  {provider.completedJobs}
                </Text>
                <Text style={[styles.statLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Jobs
                </Text>
              </View>
              
              <View style={styles.stat}>
                <Text style={[styles.statValue, { color: colors?.text?.primary || Colors.text?.primary }]}>
                  {provider.yearsOfExperience}
                </Text>
                <Text style={[styles.statLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Years
                </Text>
              </View>
              
              <View style={styles.stat}>
                <Text style={[styles.statValue, { color: colors?.text?.primary || Colors.text?.primary }]}>
                  {provider.responseTime}
                </Text>
                <Text style={[styles.statLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Response
                </Text>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.contactButton, { borderColor: colors?.primary?.main || Colors.primary?.main }]}
            onPress={onContactProvider}
          >
            <Ionicons name="chatbubble-outline" size={16} color={colors?.primary?.main || Colors.primary?.main} />
            <Text style={[styles.contactButtonText, { color: colors?.primary?.main || Colors.primary?.main }]}>
              Contact
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderTabs = () => (
    <View style={[styles.tabContainer, { borderBottomColor: colors?.border?.primary || Colors.border?.primary }]}>
      {[
        { key: 'overview', label: 'Overview' },
        { key: 'portfolio', label: 'Portfolio' },
        { key: 'certifications', label: 'Certifications' },
        { key: 'services', label: 'Services' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && {
              borderBottomColor: colors?.primary?.main || Colors.primary?.main,
            },
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Text
            style={[
              styles.tabText,
              {
                color: activeTab === tab.key
                  ? colors?.primary?.main || Colors.primary?.main
                  : colors?.text?.secondary || Colors.text?.secondary,
              },
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverview = () => (
    <View style={styles.tabContent}>
      <Text style={[styles.bio, { color: colors?.text?.primary || Colors.text?.primary }]}>
        {provider.bio}
      </Text>
      
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
          Specialties
        </Text>
        <View style={styles.specialtiesContainer}>
          {provider.specialties.map((specialty, index) => (
            <View
              key={index}
              style={[styles.specialtyTag, { backgroundColor: colors?.primary?.light || Colors.primary?.light }]}
            >
              <Text style={[styles.specialtyText, { color: colors?.primary?.dark || Colors.primary?.dark }]}>
                {specialty}
              </Text>
            </View>
          ))}
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
          Verification Badges
        </Text>
        <View style={styles.badgesContainer}>
          {provider.verification.verifiedBadges.map((badge, index) => (
            <View key={index} style={styles.badge}>
              <Ionicons name="shield-checkmark" size={16} color={Colors.success?.main} />
              <Text style={[styles.badgeText, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                {badge}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  const renderPortfolio = () => (
    <View style={styles.tabContent}>
      {provider.portfolio.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={[styles.portfolioItem, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}
          onPress={() => onViewPortfolioItem(item)}
        >
          <Image source={{ uri: item.images[0] }} style={styles.portfolioImage} />
          <View style={styles.portfolioContent}>
            <Text style={[styles.portfolioTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              {item.title}
            </Text>
            <Text style={[styles.portfolioDescription, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
              {item.description}
            </Text>
            <Text style={[styles.portfolioDate, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
              Completed {new Date(item.completedAt).toLocaleDateString()}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderCertifications = () => (
    <View style={styles.tabContent}>
      {provider.certifications.map((cert) => (
        <TouchableOpacity
          key={cert.id}
          style={[styles.certificationItem, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}
          onPress={() => onViewCertification(cert)}
        >
          {cert.badgeImage && (
            <Image source={{ uri: cert.badgeImage }} style={styles.certificationBadge} />
          )}
          <View style={styles.certificationContent}>
            <Text style={[styles.certificationName, { color: colors?.text?.primary || Colors.text?.primary }]}>
              {cert.name}
            </Text>
            <Text style={[styles.certificationIssuer, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
              {cert.issuer}
            </Text>
            <Text style={[styles.certificationDate, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
              Obtained {new Date(cert.dateObtained).toLocaleDateString()}
            </Text>
            <View style={[styles.levelBadge, { backgroundColor: getLevelColor(cert.level) }]}>
              <Text style={styles.levelText}>{cert.level.toUpperCase()}</Text>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderServices = () => (
    <View style={styles.tabContent}>
      {provider.services.map((service) => (
        <View
          key={service.id}
          style={[styles.serviceItem, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}
        >
          <Image source={{ uri: service.images[0] }} style={styles.serviceImage} />
          <View style={styles.serviceContent}>
            <Text style={[styles.serviceName, { color: colors?.text?.primary || Colors.text?.primary }]}>
              {service.name}
            </Text>
            <Text style={[styles.serviceDescription, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
              {service.description}
            </Text>
            <View style={styles.serviceDetails}>
              <Text style={[styles.serviceDuration, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
                {service.duration} min
              </Text>
              <Text style={[styles.servicePrice, { color: colors?.primary?.main || Colors.primary?.main }]}>
                ${service.price.min} - ${service.price.max}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.bookButton, { backgroundColor: colors?.primary?.main || Colors.primary?.main }]}
              onPress={() => onBookService(service)}
            >
              <Text style={styles.bookButtonText}>Book Now</Text>
            </TouchableOpacity>
          </View>
        </View>
      ))}
    </View>
  );

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return Colors.info?.light;
      case 'intermediate': return Colors.warning?.light;
      case 'advanced': return Colors.success?.light;
      case 'expert': return Colors.primary?.light;
      default: return Colors.gray?.light;
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'portfolio': return renderPortfolio();
      case 'certifications': return renderCertifications();
      case 'services': return renderServices();
      default: return renderOverview();
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderHeader()}
      {renderTabs()}
      {renderTabContent()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    position: 'relative',
  },
  coverImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  headerContent: {
    padding: getResponsiveSpacing(4),
    marginTop: -50,
    borderTopLeftRadius: getResponsiveSpacing(6),
    borderTopRightRadius: getResponsiveSpacing(6),
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(4),
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: Colors.white,
    marginRight: getResponsiveSpacing(4),
  },
  profileInfo: {
    flex: 1,
  },
  nameSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(1),
  },
  businessName: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    marginRight: getResponsiveSpacing(2),
  },
  ratingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  rating: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginLeft: getResponsiveSpacing(1),
    marginRight: getResponsiveSpacing(1),
  },
  reviewCount: {
    fontSize: getResponsiveFontSize(14),
  },
  statsSection: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(4),
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
  },
  statLabel: {
    fontSize: getResponsiveFontSize(12),
  },
  actionButtons: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(3),
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(2),
    borderWidth: 1,
    gap: getResponsiveSpacing(1),
  },
  contactButtonText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: getResponsiveSpacing(3),
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
  tabContent: {
    padding: getResponsiveSpacing(4),
  },
  bio: {
    fontSize: getResponsiveFontSize(16),
    lineHeight: getResponsiveFontSize(24),
    marginBottom: getResponsiveSpacing(4),
  },
  section: {
    marginBottom: getResponsiveSpacing(4),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(3),
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing(2),
  },
  specialtyTag: {
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(1),
    borderRadius: getResponsiveSpacing(4),
  },
  specialtyText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
  },
  badgesContainer: {
    gap: getResponsiveSpacing(2),
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  badgeText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
  },
  portfolioItem: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(3),
  },
  portfolioImage: {
    width: 80,
    height: 80,
    borderRadius: getResponsiveSpacing(2),
    marginRight: getResponsiveSpacing(3),
  },
  portfolioContent: {
    flex: 1,
  },
  portfolioTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(1),
  },
  portfolioDescription: {
    fontSize: getResponsiveFontSize(14),
    marginBottom: getResponsiveSpacing(1),
  },
  portfolioDate: {
    fontSize: getResponsiveFontSize(12),
  },
  certificationItem: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(3),
  },
  certificationBadge: {
    width: 60,
    height: 60,
    borderRadius: getResponsiveSpacing(2),
    marginRight: getResponsiveSpacing(3),
  },
  certificationContent: {
    flex: 1,
  },
  certificationName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(1),
  },
  certificationIssuer: {
    fontSize: getResponsiveFontSize(14),
    marginBottom: getResponsiveSpacing(1),
  },
  certificationDate: {
    fontSize: getResponsiveFontSize(12),
    marginBottom: getResponsiveSpacing(2),
  },
  levelBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: getResponsiveSpacing(2),
    paddingVertical: getResponsiveSpacing(0.5),
    borderRadius: getResponsiveSpacing(1),
  },
  levelText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '700',
    color: Colors.white,
  },
  serviceItem: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(3),
  },
  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: getResponsiveSpacing(2),
    marginRight: getResponsiveSpacing(3),
  },
  serviceContent: {
    flex: 1,
  },
  serviceName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(1),
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(14),
    marginBottom: getResponsiveSpacing(2),
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  serviceDuration: {
    fontSize: getResponsiveFontSize(12),
  },
  servicePrice: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
  },
  bookButton: {
    paddingVertical: getResponsiveSpacing(2),
    paddingHorizontal: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(2),
    alignItems: 'center',
  },
  bookButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
});

export default EnhancedProviderProfile;
export type { EnhancedProvider, PortfolioItem, Certification, ServiceOffering };
