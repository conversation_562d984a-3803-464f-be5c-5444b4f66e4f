{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "invariant", "require", "processAspectRatio", "aspectRatio", "__DEV__", "matches", "split", "map", "s", "trim", "includes", "length", "hasNonNumericValues", "some", "n", "Number", "isNaN", "_default"], "sources": ["processAspectRatio.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n'use strict';\n\nconst invariant = require('invariant');\n\nfunction processAspectRatio(aspectRatio?: number | string): ?number {\n  if (typeof aspectRatio === 'number') {\n    return aspectRatio;\n  }\n  if (typeof aspectRatio !== 'string') {\n    if (__DEV__) {\n      invariant(\n        !aspectRatio,\n        'aspectRatio must either be a number, a ratio string or `auto`. You passed: %s',\n        aspectRatio,\n      );\n    }\n    return;\n  }\n\n  const matches = aspectRatio.split('/').map(s => s.trim());\n\n  if (matches.includes('auto')) {\n    if (__DEV__) {\n      invariant(\n        matches.length,\n        'aspectRatio does not support `auto <ratio>`. You passed: %s',\n        aspectRatio,\n      );\n    }\n    return;\n  }\n\n  const hasNonNumericValues = matches.some(n => Number.isNaN(Number(n)));\n  if (__DEV__) {\n    invariant(\n      !hasNonNumericValues && (matches.length === 1 || matches.length === 2),\n      'aspectRatio must either be a number, a ratio string or `auto`. You passed: %s',\n      aspectRatio,\n    );\n  }\n\n  if (hasNonNumericValues) {\n    return;\n  }\n\n  if (matches.length === 2) {\n    return Number(matches[0]) / Number(matches[1]);\n  }\n\n  return Number(matches[0]);\n}\n\nexport default processAspectRatio;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAMC,SAAS,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEtC,SAASC,kBAAkBA,CAACC,WAA6B,EAAW;EAClE,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOA,WAAW;EACpB;EACA,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IACnC,IAAIC,OAAO,EAAE;MACXJ,SAAS,CACP,CAACG,WAAW,EACZ,+EAA+E,EAC/EA,WACF,CAAC;IACH;IACA;EACF;EAEA,IAAME,OAAO,GAAGF,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,IAAI,CAAC,CAAC;EAAA,EAAC;EAEzD,IAAIJ,OAAO,CAACK,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC5B,IAAIN,OAAO,EAAE;MACXJ,SAAS,CACPK,OAAO,CAACM,MAAM,EACd,6DAA6D,EAC7DR,WACF,CAAC;IACH;IACA;EACF;EAEA,IAAMS,mBAAmB,GAAGP,OAAO,CAACQ,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIC,MAAM,CAACC,KAAK,CAACD,MAAM,CAACD,CAAC,CAAC,CAAC;EAAA,EAAC;EACtE,IAAIV,OAAO,EAAE;IACXJ,SAAS,CACP,CAACY,mBAAmB,KAAKP,OAAO,CAACM,MAAM,KAAK,CAAC,IAAIN,OAAO,CAACM,MAAM,KAAK,CAAC,CAAC,EACtE,+EAA+E,EAC/ER,WACF,CAAC;EACH;EAEA,IAAIS,mBAAmB,EAAE;IACvB;EACF;EAEA,IAAIP,OAAO,CAACM,MAAM,KAAK,CAAC,EAAE;IACxB,OAAOI,MAAM,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGU,MAAM,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC;EAChD;EAEA,OAAOU,MAAM,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3B;AAAC,IAAAY,QAAA,GAAApB,OAAA,CAAAE,OAAA,GAEcG,kBAAkB", "ignoreList": []}