/**
 * Search Utilities
 *
 * Comprehensive search and discovery utilities with advanced filtering,
 * fuzzy matching, and intelligent suggestions for enhanced user experience.
 *
 * Features:
 * - Fuzzy search algorithms
 * - Smart filtering
 * - Search suggestions
 * - Result ranking
 * - Search analytics
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Search result interface
export interface SearchResult<T = any> {
  item: T;
  score: number;
  matches: SearchMatch[];
  relevance: number;
}

// Search match interface
export interface SearchMatch {
  field: string;
  value: string;
  indices: [number, number][];
  score: number;
}

// Search configuration
export interface SearchConfig {
  fields: string[];
  weights?: Record<string, number>;
  threshold?: number;
  maxResults?: number;
  fuzzyThreshold?: number;
  includeMatches?: boolean;
  includeScore?: boolean;
  sortByScore?: boolean;
}

// Filter configuration
export interface FilterConfig {
  field: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'range';
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte' | 'between';
  value: any;
  caseSensitive?: boolean;
}

// Search suggestion interface
export interface SearchSuggestion {
  text: string;
  type: 'query' | 'category' | 'service' | 'provider';
  count?: number;
  popularity?: number;
}

/**
 * Calculate Levenshtein distance between two strings
 */
export const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
};

/**
 * Calculate fuzzy match score
 */
export const fuzzyMatchScore = (query: string, target: string): number => {
  if (!query || !target) return 0;
  
  const queryLower = query.toLowerCase();
  const targetLower = target.toLowerCase();
  
  // Exact match
  if (queryLower === targetLower) return 1;
  
  // Starts with match
  if (targetLower.startsWith(queryLower)) return 0.9;
  
  // Contains match
  if (targetLower.includes(queryLower)) return 0.7;
  
  // Fuzzy match using Levenshtein distance
  const distance = levenshteinDistance(queryLower, targetLower);
  const maxLength = Math.max(queryLower.length, targetLower.length);
  const similarity = 1 - (distance / maxLength);
  
  return similarity > 0.6 ? similarity * 0.5 : 0;
};

/**
 * Find matches in text
 */
export const findMatches = (query: string, text: string): [number, number][] => {
  const matches: [number, number][] = [];
  const queryLower = query.toLowerCase();
  const textLower = text.toLowerCase();
  
  let index = textLower.indexOf(queryLower);
  while (index !== -1) {
    matches.push([index, index + query.length]);
    index = textLower.indexOf(queryLower, index + 1);
  }
  
  return matches;
};

/**
 * Search through items with fuzzy matching
 */
export const fuzzySearch = <T>(
  items: T[],
  query: string,
  config: SearchConfig
): SearchResult<T>[] => {
  if (!query.trim()) return [];
  
  const {
    fields,
    weights = {},
    threshold = 0.3,
    maxResults = 50,
    includeMatches = true,
    includeScore = true,
    sortByScore = true,
  } = config;
  
  const results: SearchResult<T>[] = [];
  
  for (const item of items) {
    let totalScore = 0;
    let maxFieldScore = 0;
    const matches: SearchMatch[] = [];
    
    for (const field of fields) {
      const value = getNestedValue(item, field);
      if (typeof value !== 'string') continue;
      
      const score = fuzzyMatchScore(query, value);
      const weight = weights[field] || 1;
      const weightedScore = score * weight;
      
      totalScore += weightedScore;
      maxFieldScore = Math.max(maxFieldScore, score);
      
      if (score > 0 && includeMatches) {
        const fieldMatches = findMatches(query, value);
        if (fieldMatches.length > 0) {
          matches.push({
            field,
            value,
            indices: fieldMatches,
            score,
          });
        }
      }
    }
    
    const averageScore = totalScore / fields.length;
    const finalScore = Math.max(averageScore, maxFieldScore * 0.8);
    
    if (finalScore >= threshold) {
      results.push({
        item,
        score: finalScore,
        matches,
        relevance: calculateRelevance(item, query, matches),
      });
    }
  }
  
  if (sortByScore) {
    results.sort((a, b) => b.score - a.score);
  }
  
  return results.slice(0, maxResults);
};

/**
 * Get nested value from object
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * Calculate relevance score
 */
const calculateRelevance = (item: any, query: string, matches: SearchMatch[]): number => {
  let relevance = 0;
  
  // Boost relevance for exact matches
  const exactMatches = matches.filter(match => 
    match.value.toLowerCase() === query.toLowerCase()
  );
  relevance += exactMatches.length * 0.5;
  
  // Boost relevance for title/name matches
  const titleMatches = matches.filter(match => 
    match.field.toLowerCase().includes('title') || 
    match.field.toLowerCase().includes('name')
  );
  relevance += titleMatches.length * 0.3;
  
  // Boost relevance for multiple field matches
  relevance += Math.min(matches.length * 0.1, 0.3);
  
  return Math.min(relevance, 1);
};

/**
 * Apply filters to search results
 */
export const applyFilters = <T>(
  items: T[],
  filters: FilterConfig[]
): T[] => {
  return items.filter(item => {
    return filters.every(filter => {
      const value = getNestedValue(item, filter.field);
      return matchesFilter(value, filter);
    });
  });
};

/**
 * Check if value matches filter
 */
const matchesFilter = (value: any, filter: FilterConfig): boolean => {
  const { type, operator = 'equals', value: filterValue, caseSensitive = false } = filter;
  
  if (value == null) return false;
  
  switch (type) {
    case 'text':
      const textValue = caseSensitive ? String(value) : String(value).toLowerCase();
      const textFilter = caseSensitive ? String(filterValue) : String(filterValue).toLowerCase();
      
      switch (operator) {
        case 'equals':
          return textValue === textFilter;
        case 'contains':
          return textValue.includes(textFilter);
        case 'startsWith':
          return textValue.startsWith(textFilter);
        case 'endsWith':
          return textValue.endsWith(textFilter);
        default:
          return textValue === textFilter;
      }
    
    case 'number':
      const numValue = Number(value);
      const numFilter = Number(filterValue);
      
      switch (operator) {
        case 'equals':
          return numValue === numFilter;
        case 'gt':
          return numValue > numFilter;
        case 'lt':
          return numValue < numFilter;
        case 'gte':
          return numValue >= numFilter;
        case 'lte':
          return numValue <= numFilter;
        default:
          return numValue === numFilter;
      }
    
    case 'range':
      const rangeValue = Number(value);
      const [min, max] = Array.isArray(filterValue) ? filterValue : [filterValue, filterValue];
      return rangeValue >= min && rangeValue <= max;
    
    case 'boolean':
      return Boolean(value) === Boolean(filterValue);
    
    case 'select':
      if (Array.isArray(filterValue)) {
        return filterValue.includes(value);
      }
      return value === filterValue;
    
    case 'date':
      const dateValue = new Date(value);
      const dateFilter = new Date(filterValue);
      
      switch (operator) {
        case 'equals':
          return dateValue.toDateString() === dateFilter.toDateString();
        case 'gt':
          return dateValue > dateFilter;
        case 'lt':
          return dateValue < dateFilter;
        case 'gte':
          return dateValue >= dateFilter;
        case 'lte':
          return dateValue <= dateFilter;
        default:
          return dateValue.toDateString() === dateFilter.toDateString();
      }
    
    default:
      return String(value) === String(filterValue);
  }
};

/**
 * Generate search suggestions
 */
export const generateSearchSuggestions = (
  query: string,
  items: any[],
  searchHistory: string[] = [],
  popularQueries: string[] = []
): SearchSuggestion[] => {
  const suggestions: SearchSuggestion[] = [];
  const queryLower = query.toLowerCase();
  
  // Add search history suggestions
  const historySuggestions = searchHistory
    .filter(term => term.toLowerCase().includes(queryLower))
    .slice(0, 3)
    .map(term => ({
      text: term,
      type: 'query' as const,
      popularity: 0.8,
    }));
  
  suggestions.push(...historySuggestions);
  
  // Add popular query suggestions
  const popularSuggestions = popularQueries
    .filter(term => term.toLowerCase().includes(queryLower))
    .slice(0, 3)
    .map(term => ({
      text: term,
      type: 'query' as const,
      popularity: 0.9,
    }));
  
  suggestions.push(...popularSuggestions);
  
  // Add item-based suggestions
  const itemSuggestions = items
    .filter(item => {
      const name = item.name || item.title || '';
      return name.toLowerCase().includes(queryLower);
    })
    .slice(0, 5)
    .map(item => ({
      text: item.name || item.title || '',
      type: item.type || 'service' as const,
      count: item.count || 1,
    }));
  
  suggestions.push(...itemSuggestions);
  
  // Remove duplicates and sort by relevance
  const uniqueSuggestions = suggestions.filter((suggestion, index, self) =>
    index === self.findIndex(s => s.text === suggestion.text)
  );
  
  return uniqueSuggestions
    .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
    .slice(0, 8);
};

/**
 * Highlight search matches in text
 */
export const highlightMatches = (
  text: string,
  matches: [number, number][],
  highlightStart: string = '<mark>',
  highlightEnd: string = '</mark>'
): string => {
  if (!matches.length) return text;
  
  let result = '';
  let lastIndex = 0;
  
  // Sort matches by start index
  const sortedMatches = matches.sort((a, b) => a[0] - b[0]);
  
  for (const [start, end] of sortedMatches) {
    // Add text before match
    result += text.slice(lastIndex, start);
    
    // Add highlighted match
    result += highlightStart + text.slice(start, end) + highlightEnd;
    
    lastIndex = end;
  }
  
  // Add remaining text
  result += text.slice(lastIndex);
  
  return result;
};

/**
 * Create search analytics
 */
export const createSearchAnalytics = () => {
  const searchHistory: string[] = [];
  const searchCounts: Record<string, number> = {};
  const resultCounts: Record<string, number> = {};
  
  return {
    recordSearch: (query: string, resultCount: number) => {
      searchHistory.push(query);
      searchCounts[query] = (searchCounts[query] || 0) + 1;
      resultCounts[query] = resultCount;
    },
    
    getPopularQueries: (limit: number = 10) => {
      return Object.entries(searchCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, limit)
        .map(([query]) => query);
    },
    
    getSearchHistory: () => [...searchHistory],
    
    getSearchStats: () => ({
      totalSearches: searchHistory.length,
      uniqueQueries: Object.keys(searchCounts).length,
      averageResults: Object.values(resultCounts).reduce((a, b) => a + b, 0) / Object.keys(resultCounts).length || 0,
    }),
  };
};

export default {
  fuzzySearch,
  applyFilters,
  generateSearchSuggestions,
  highlightMatches,
  createSearchAnalytics,
  fuzzyMatchScore,
  levenshteinDistance,
};
