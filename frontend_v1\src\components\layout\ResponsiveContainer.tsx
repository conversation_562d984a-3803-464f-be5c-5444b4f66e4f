/**
 * Responsive Container Component
 *
 * Adaptive container component that provides responsive layout capabilities
 * with breakpoint-based styling and device-specific optimizations.
 *
 * Features:
 * - Responsive breakpoints
 * - Adaptive padding and margins
 * - Device-specific optimizations
 * - Safe area handling
 * - Flexible layout options
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions, Platform } from 'react-native';
import { getDeviceType, getScreenDimensions, getSafeAreaInsets } from '../../utils/responsiveUtils';

// Container props
export interface ResponsiveContainerProps {
  children: React.ReactNode;
  
  // Layout options
  maxWidth?: number | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: number | 'none' | 'sm' | 'md' | 'lg' | 'xl';
  margin?: number | 'none' | 'sm' | 'md' | 'lg' | 'xl';
  
  // Responsive behavior
  centerContent?: boolean;
  fullHeight?: boolean;
  safeArea?: boolean | 'top' | 'bottom' | 'horizontal' | 'vertical';
  
  // Styling
  backgroundColor?: string;
  style?: any;
  
  // Testing
  testID?: string;
}

// Breakpoint values
const BREAKPOINT_VALUES = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  full: '100%',
} as const;

// Spacing values
const SPACING_VALUES = {
  none: 0,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
} as const;

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'full',
  padding = 'md',
  margin = 'none',
  centerContent = false,
  fullHeight = false,
  safeArea = false,
  backgroundColor,
  style,
  testID,
}) => {
  // State for screen dimensions
  const [screenData, setScreenData] = useState(() => ({
    ...getScreenDimensions(),
    deviceType: getDeviceType(),
    safeAreaInsets: getSafeAreaInsets(),
  }));

  // Update screen data on dimension changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({
        width: window.width,
        height: window.height,
        deviceType: getDeviceType(),
        safeAreaInsets: getSafeAreaInsets(),
      });
    });

    return () => subscription?.remove();
  }, []);

  // Calculate responsive values
  const getResponsiveValue = (value: number | string, type: 'width' | 'spacing'): number => {
    if (typeof value === 'number') return value;
    
    if (type === 'width') {
      if (value === 'full') return screenData.width;
      const breakpointValue = BREAKPOINT_VALUES[value as keyof typeof BREAKPOINT_VALUES];
      return typeof breakpointValue === 'number' 
        ? Math.min(breakpointValue, screenData.width) 
        : screenData.width;
    }
    
    if (type === 'spacing') {
      return SPACING_VALUES[value as keyof typeof SPACING_VALUES] || 0;
    }
    
    return 0;
  };

  // Calculate container styles
  const containerStyles = React.useMemo(() => {
    const styles: any = {
      width: '100%',
    };

    // Max width
    if (maxWidth !== 'full') {
      const maxWidthValue = getResponsiveValue(maxWidth, 'width');
      styles.maxWidth = maxWidthValue;
    }

    // Padding
    const paddingValue = getResponsiveValue(padding, 'spacing');
    if (paddingValue > 0) {
      styles.paddingHorizontal = paddingValue;
      styles.paddingVertical = paddingValue;
    }

    // Margin
    const marginValue = getResponsiveValue(margin, 'spacing');
    if (marginValue > 0) {
      styles.marginHorizontal = marginValue;
      styles.marginVertical = marginValue;
    }

    // Center content
    if (centerContent) {
      styles.alignSelf = 'center';
      styles.alignItems = 'center';
    }

    // Full height
    if (fullHeight) {
      styles.minHeight = screenData.height;
    }

    // Background color
    if (backgroundColor) {
      styles.backgroundColor = backgroundColor;
    }

    // Safe area handling
    if (safeArea) {
      const insets = screenData.safeAreaInsets;
      
      if (safeArea === true || safeArea === 'top' || safeArea === 'vertical') {
        styles.paddingTop = (styles.paddingTop || 0) + insets.top;
      }
      
      if (safeArea === true || safeArea === 'bottom' || safeArea === 'vertical') {
        styles.paddingBottom = (styles.paddingBottom || 0) + insets.bottom;
      }
      
      if (safeArea === true || safeArea === 'horizontal') {
        styles.paddingLeft = (styles.paddingLeft || 0) + insets.left;
        styles.paddingRight = (styles.paddingRight || 0) + insets.right;
      }
    }

    return styles;
  }, [
    maxWidth,
    padding,
    margin,
    centerContent,
    fullHeight,
    backgroundColor,
    safeArea,
    screenData,
  ]);

  return (
    <View
      style={[containerStyles, style]}
      testID={testID}
    >
      {children}
    </View>
  );
};

// Grid container for responsive layouts
export interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  spacing?: number;
  style?: any;
  testID?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 },
  spacing = 16,
  style,
  testID,
}) => {
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenWidth(window.width);
    });

    return () => subscription?.remove();
  }, []);

  // Get current columns based on screen size
  const getCurrentColumns = (): number => {
    if (typeof columns === 'number') return columns;

    // Determine breakpoint
    if (screenWidth >= 1200) return columns.xl || columns.lg || columns.md || columns.sm || columns.xs || 1;
    if (screenWidth >= 992) return columns.lg || columns.md || columns.sm || columns.xs || 1;
    if (screenWidth >= 768) return columns.md || columns.sm || columns.xs || 1;
    if (screenWidth >= 576) return columns.sm || columns.xs || 1;
    return columns.xs || 1;
  };

  const currentColumns = getCurrentColumns();
  const itemWidth = (screenWidth - (spacing * (currentColumns + 1))) / currentColumns;

  const gridStyles = {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    paddingHorizontal: spacing / 2,
  };

  const itemStyles = {
    width: itemWidth,
    marginHorizontal: spacing / 2,
    marginVertical: spacing / 2,
  };

  return (
    <View style={[gridStyles, style]} testID={testID}>
      {React.Children.map(children, (child, index) => (
        <View key={index} style={itemStyles}>
          {child}
        </View>
      ))}
    </View>
  );
};

// Responsive row component
export interface ResponsiveRowProps {
  children: React.ReactNode;
  spacing?: number;
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  wrap?: boolean;
  style?: any;
  testID?: string;
}

export const ResponsiveRow: React.FC<ResponsiveRowProps> = ({
  children,
  spacing = 0,
  align = 'flex-start',
  justify = 'flex-start',
  wrap = false,
  style,
  testID,
}) => {
  const rowStyles = {
    flexDirection: 'row' as const,
    alignItems: align,
    justifyContent: justify,
    flexWrap: wrap ? 'wrap' as const : 'nowrap' as const,
    marginHorizontal: -spacing / 2,
  };

  return (
    <View style={[rowStyles, style]} testID={testID}>
      {React.Children.map(children, (child, index) => (
        <View key={index} style={{ paddingHorizontal: spacing / 2 }}>
          {child}
        </View>
      ))}
    </View>
  );
};

// Responsive column component
export interface ResponsiveColumnProps {
  children: React.ReactNode;
  flex?: number;
  width?: number | string;
  spacing?: number;
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  style?: any;
  testID?: string;
}

export const ResponsiveColumn: React.FC<ResponsiveColumnProps> = ({
  children,
  flex,
  width,
  spacing = 0,
  align = 'flex-start',
  style,
  testID,
}) => {
  const columnStyles = {
    flexDirection: 'column' as const,
    alignItems: align,
    marginVertical: -spacing / 2,
    ...(flex !== undefined && { flex }),
    ...(width !== undefined && { width }),
  };

  return (
    <View style={[columnStyles, style]} testID={testID}>
      {React.Children.map(children, (child, index) => (
        <View key={index} style={{ paddingVertical: spacing / 2 }}>
          {child}
        </View>
      ))}
    </View>
  );
};

// Responsive spacer component
export interface ResponsiveSpacerProps {
  size?: number | 'sm' | 'md' | 'lg' | 'xl';
  horizontal?: boolean;
  testID?: string;
}

export const ResponsiveSpacer: React.FC<ResponsiveSpacerProps> = ({
  size = 'md',
  horizontal = false,
  testID,
}) => {
  const spacerSize = typeof size === 'number' ? size : SPACING_VALUES[size];
  
  const spacerStyles = {
    [horizontal ? 'width' : 'height']: spacerSize,
  };

  return <View style={spacerStyles} testID={testID} />;
};

const styles = StyleSheet.create({
  // Base styles if needed
});

export default ResponsiveContainer;
