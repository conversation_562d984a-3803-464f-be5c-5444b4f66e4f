/**
 * Standard Card Component
 *
 * Implements consistent card styling and behavior across the application
 * using the design system tokens and accessibility best practices.
 *
 * Features:
 * - Design system integration
 * - Multiple variants and elevations
 * - Accessibility compliance
 * - Pressable and non-pressable variants
 * - Consistent spacing and styling
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Text,
} from 'react-native';
import { useDesignSystem } from '../../contexts/DesignSystemContext';

// Card variant types
export type CardVariant = 'default' | 'outlined' | 'elevated' | 'filled';
export type CardElevation = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Card props interface
export interface StandardCardProps {
  children: React.ReactNode;
  
  // Styling
  variant?: CardVariant;
  elevation?: CardElevation;
  padding?: keyof typeof import('../../constants/DesignSystem').Spacing;
  
  // Interaction
  onPress?: () => void;
  disabled?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: string;
  
  // Testing
  testID?: string;
  
  // Custom styling
  style?: ViewStyle;
}

export const StandardCard: React.FC<StandardCardProps> = ({
  children,
  variant = 'default',
  elevation = 'sm',
  padding = 'md',
  onPress,
  disabled = false,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole,
  testID,
  style,
}) => {
  const { theme, getSpacing } = useDesignSystem();

  // Determine if card is pressable
  const isPressable = Boolean(onPress && !disabled);

  // Get card styles based on variant and elevation
  const cardStyles = React.useMemo(() => {
    const baseStyles: ViewStyle = {
      borderRadius: theme.borderRadius.component.card,
      padding: getSpacing(padding),
    };

    // Variant-specific styles
    let variantStyles: ViewStyle = {};
    switch (variant) {
      case 'default':
        variantStyles = {
          backgroundColor: theme.colors.background?.primary || '#FFFFFF',
          ...theme.elevation[elevation],
        };
        break;
      case 'outlined':
        variantStyles = {
          backgroundColor: theme.colors.background?.primary || '#FFFFFF',
          borderWidth: 1,
          borderColor: theme.colors.border?.primary || '#E5E7EB',
        };
        break;
      case 'elevated':
        variantStyles = {
          backgroundColor: theme.colors.background?.elevated || '#FFFFFF',
          ...theme.elevation[elevation],
        };
        break;
      case 'filled':
        variantStyles = {
          backgroundColor: theme.colors.background?.secondary || '#F8F9FA',
        };
        break;
    }

    // Add interaction styles for pressable cards
    if (isPressable) {
      variantStyles = {
        ...variantStyles,
        opacity: disabled ? theme.opacity.disabled : theme.opacity.visible,
      };
    }

    return {
      ...baseStyles,
      ...variantStyles,
    };
  }, [theme, variant, elevation, padding, isPressable, disabled, getSpacing]);

  // Render pressable card
  if (isPressable) {
    return (
      <TouchableOpacity
        style={[cardStyles, style]}
        onPress={onPress}
        disabled={disabled}
        accessibilityRole={accessibilityRole || 'button'}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        accessibilityState={{ disabled }}
        testID={testID}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }

  // Render non-pressable card
  return (
    <View
      style={[cardStyles, style]}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel}
      testID={testID}
    >
      {children}
    </View>
  );
};

// Card header component
export interface CardHeaderProps {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  style?: ViewStyle;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  action,
  style,
}) => {
  const { theme } = useDesignSystem();

  return (
    <View style={[styles.header, style]}>
      <View style={styles.headerContent}>
        {title && (
          <Text
            style={[
              theme.typography.styles.h6,
              { color: theme.colors.text?.primary },
            ]}
            numberOfLines={1}
          >
            {title}
          </Text>
        )}
        {subtitle && (
          <Text
            style={[
              theme.typography.styles.body2,
              { 
                color: theme.colors.text?.secondary,
                marginTop: theme.spacing.xs,
              },
            ]}
            numberOfLines={2}
          >
            {subtitle}
          </Text>
        )}
      </View>
      {action && <View style={styles.headerAction}>{action}</View>}
    </View>
  );
};

// Card content component
export interface CardContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  style,
}) => {
  const { theme } = useDesignSystem();

  return (
    <View style={[{ marginTop: theme.spacing.sm }, style]}>
      {children}
    </View>
  );
};

// Card footer component
export interface CardFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  style,
}) => {
  const { theme } = useDesignSystem();

  return (
    <View style={[{ marginTop: theme.spacing.md }, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  headerContent: {
    flex: 1,
    marginRight: 8,
  },
  headerAction: {
    flexShrink: 0,
  },
});

// Export compound component
export default Object.assign(StandardCard, {
  Header: CardHeader,
  Content: CardContent,
  Footer: CardFooter,
});
