e471f5e27eaa34789d8420ce039366b6
_getJestObj().mock('react-native/Libraries/Utilities/PixelRatio', function () {
  return {
    get: jest.fn(function () {
      return 2;
    }),
    getFontScale: jest.fn(function () {
      return 1;
    }),
    getPixelSizeForLayoutSize: jest.fn(function (layoutSize) {
      return layoutSize * 2;
    }),
    roundToNearestPixel: jest.fn(function (layoutSize) {
      return Math.round(layoutSize * 2) / 2;
    })
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Dimensions', function () {
  return {
    get: jest.fn(function () {
      return {
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1
      };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Platform', function () {
  return {
    OS: 'ios',
    Version: '14.0',
    select: jest.fn(function (obj) {
      return obj.ios || obj.default;
    }),
    isPad: false,
    isTesting: true
  };
});
_getJestObj().mock('react-native', function () {
  var RN = jest.requireActual('react-native');
  return Object.assign({}, RN, {
    Platform: {
      OS: 'ios',
      Version: '14.0',
      select: jest.fn(function (obj) {
        return obj.ios || obj.default;
      }),
      isPad: false,
      isTesting: true
    },
    PixelRatio: {
      get: jest.fn(function () {
        return 2;
      }),
      getFontScale: jest.fn(function () {
        return 1;
      }),
      getPixelSizeForLayoutSize: jest.fn(function (layoutSize) {
        return layoutSize * 2;
      }),
      roundToNearestPixel: jest.fn(function (layoutSize) {
        return Math.round(layoutSize * 2) / 2;
      })
    },
    Dimensions: {
      get: jest.fn(function () {
        return {
          width: 375,
          height: 812,
          scale: 2,
          fontScale: 1
        };
      }),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }
  });
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
jest.useFakeTimers();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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