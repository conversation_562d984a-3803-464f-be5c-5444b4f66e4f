/**
 * Booking Flow Context Provider
 *
 * Comprehensive booking flow state management with optimization features,
 * progress tracking, and conversion enhancement capabilities.
 *
 * Features:
 * - Multi-step flow management
 * - Progress persistence
 * - Smart defaults
 * - Conversion optimization
 * - Error recovery
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Platform, AccessibilityInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  BookingStep,
  BookingData,
  BookingFlowConfig,
  ConversionMetrics,
  calculateProgress,
  getNextStep,
  getPreviousStep,
  validateStepCompletion,
  generateSmartDefaults,
  generateBookingSummary,
  validateBookingData,
  generateBookingConfirmation,
  trackBookingStep,
} from '../utils/bookingFlowUtils';

// Booking flow state interface
export interface BookingFlowState {
  currentStepId: string;
  steps: BookingStep[];
  bookingData: Partial<BookingData>;
  progress: number;
  isLoading: boolean;
  errors: Record<string, string[]>;
  canProceed: boolean;
  canGoBack: boolean;
  estimatedTimeRemaining: number;
}

// Booking flow context interface
interface BookingFlowContextType {
  // State
  flowState: BookingFlowState;
  
  // Navigation
  goToStep: (stepId: string) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  
  // Data management
  updateBookingData: (data: Partial<BookingData>) => void;
  clearBookingData: () => void;
  
  // Step management
  completeStep: (stepId: string) => void;
  validateCurrentStep: () => boolean;
  
  // Flow control
  startBookingFlow: (serviceId: string, providerId: string) => void;
  completeBookingFlow: () => Promise<any>;
  abandonBookingFlow: () => void;
  
  // Progress management
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
  clearProgress: () => Promise<void>;
  
  // Utilities
  getBookingSummary: () => string[];
  getEstimatedTotal: () => number;
  canCompleteBooking: () => boolean;
  
  // Analytics
  trackStepAction: (action: 'start' | 'complete' | 'abandon') => void;
}

// Default booking steps
const DEFAULT_STEPS: BookingStep[] = [
  {
    id: 'service-selection',
    title: 'Select Service',
    description: 'Choose your service and provider',
    component: 'ServiceSelection',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 60,
  },
  {
    id: 'date-time',
    title: 'Date & Time',
    description: 'Pick your preferred date and time',
    component: 'DateTimeSelection',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 90,
  },
  {
    id: 'location',
    title: 'Location',
    description: 'Choose service location',
    component: 'LocationSelection',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 45,
  },
  {
    id: 'preferences',
    title: 'Preferences',
    description: 'Add special requests',
    component: 'PreferencesSelection',
    required: false,
    completed: false,
    skippable: true,
    estimatedTime: 30,
  },
  {
    id: 'contact-info',
    title: 'Contact Info',
    description: 'Provide contact details',
    component: 'ContactInfo',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 45,
  },
  {
    id: 'payment',
    title: 'Payment',
    description: 'Choose payment method',
    component: 'PaymentSelection',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 60,
  },
  {
    id: 'confirmation',
    title: 'Confirmation',
    description: 'Review and confirm booking',
    component: 'BookingConfirmation',
    required: true,
    completed: false,
    skippable: false,
    estimatedTime: 30,
  },
];

// Create context
const BookingFlowContext = createContext<BookingFlowContextType | undefined>(undefined);

// Storage key
const BOOKING_PROGRESS_KEY = '@vierla_booking_progress';

// Provider props
interface BookingFlowProviderProps {
  children: React.ReactNode;
  config?: Partial<BookingFlowConfig>;
  userProfile?: any;
}

export const BookingFlowProvider: React.FC<BookingFlowProviderProps> = ({
  children,
  config = {},
  userProfile,
}) => {
  // State
  const [flowState, setFlowState] = useState<BookingFlowState>({
    currentStepId: DEFAULT_STEPS[0].id,
    steps: DEFAULT_STEPS,
    bookingData: {},
    progress: 0,
    isLoading: false,
    errors: {},
    canProceed: false,
    canGoBack: false,
    estimatedTimeRemaining: 0,
  });

  // Refs
  const stepStartTimeRef = useRef<number>(Date.now());
  const configRef = useRef({
    allowBackNavigation: true,
    saveProgress: true,
    showProgress: true,
    optimizeForConversion: true,
    enableSmartDefaults: true,
    maxTimePerStep: 300, // 5 minutes
    ...config,
  });

  // Update flow state calculations
  const updateFlowState = useCallback((updates: Partial<BookingFlowState>) => {
    setFlowState(prev => {
      const newState = { ...prev, ...updates };
      
      // Recalculate progress
      newState.progress = calculateProgress(newState.steps);
      
      // Check if can proceed
      const currentStep = newState.steps.find(s => s.id === newState.currentStepId);
      if (currentStep) {
        const validation = validateStepCompletion(currentStep, newState.bookingData);
        newState.canProceed = validation.isValid;
        newState.errors = { ...newState.errors, [currentStep.id]: validation.errors };
      }
      
      // Check if can go back
      newState.canGoBack = configRef.current.allowBackNavigation && 
        newState.steps.findIndex(s => s.id === newState.currentStepId) > 0;
      
      // Calculate estimated time remaining
      const remainingSteps = newState.steps.filter(s => !s.completed);
      newState.estimatedTimeRemaining = remainingSteps.reduce((total, step) => 
        total + (step.estimatedTime || 60), 0
      );
      
      return newState;
    });
  }, []);

  // Go to specific step
  const goToStep = useCallback((stepId: string) => {
    const stepExists = flowState.steps.find(s => s.id === stepId);
    if (!stepExists) return;

    // Track step abandonment if moving away from current step
    if (flowState.currentStepId !== stepId) {
      trackStepAction('abandon');
    }

    updateFlowState({ currentStepId: stepId });
    stepStartTimeRef.current = Date.now();
    
    // Track new step start
    trackBookingStep(stepId, 'start');

    // Announce step change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        `Now on ${stepExists.title} step. ${stepExists.description || ''}`
      );
    }
  }, [flowState.currentStepId, flowState.steps, updateFlowState]);

  // Go to next step
  const goToNextStep = useCallback(() => {
    const nextStep = getNextStep(flowState.steps);
    if (nextStep && flowState.canProceed) {
      // Complete current step
      completeStep(flowState.currentStepId);
      goToStep(nextStep.id);
    }
  }, [flowState.steps, flowState.canProceed, flowState.currentStepId]);

  // Go to previous step
  const goToPreviousStep = useCallback(() => {
    const previousStep = getPreviousStep(flowState.steps, flowState.currentStepId);
    if (previousStep && flowState.canGoBack) {
      goToStep(previousStep.id);
    }
  }, [flowState.steps, flowState.currentStepId, flowState.canGoBack, goToStep]);

  // Update booking data
  const updateBookingData = useCallback((data: Partial<BookingData>) => {
    updateFlowState({
      bookingData: { ...flowState.bookingData, ...data },
    });
  }, [flowState.bookingData, updateFlowState]);

  // Clear booking data
  const clearBookingData = useCallback(() => {
    updateFlowState({
      bookingData: {},
      currentStepId: DEFAULT_STEPS[0].id,
      steps: DEFAULT_STEPS.map(step => ({ ...step, completed: false })),
    });
  }, [updateFlowState]);

  // Complete step
  const completeStep = useCallback((stepId: string) => {
    const timeSpent = Date.now() - stepStartTimeRef.current;
    
    updateFlowState({
      steps: flowState.steps.map(step =>
        step.id === stepId ? { ...step, completed: true } : step
      ),
    });

    // Track step completion
    trackBookingStep(stepId, 'complete', timeSpent);
  }, [flowState.steps, updateFlowState]);

  // Validate current step
  const validateCurrentStep = useCallback((): boolean => {
    const currentStep = flowState.steps.find(s => s.id === flowState.currentStepId);
    if (!currentStep) return false;

    const validation = validateStepCompletion(currentStep, flowState.bookingData);
    
    updateFlowState({
      errors: { ...flowState.errors, [currentStep.id]: validation.errors },
    });

    return validation.isValid;
  }, [flowState.currentStepId, flowState.steps, flowState.bookingData, flowState.errors, updateFlowState]);

  // Start booking flow
  const startBookingFlow = useCallback((serviceId: string, providerId: string) => {
    let initialData: Partial<BookingData> = { serviceId, providerId };

    // Apply smart defaults if enabled
    if (configRef.current.enableSmartDefaults && userProfile) {
      const smartDefaults = generateSmartDefaults(userProfile, { serviceId, providerId });
      initialData = { ...initialData, ...smartDefaults };
    }

    updateFlowState({
      bookingData: initialData,
      currentStepId: DEFAULT_STEPS[0].id,
      steps: DEFAULT_STEPS.map(step => ({ ...step, completed: false })),
    });

    stepStartTimeRef.current = Date.now();
    trackBookingStep(DEFAULT_STEPS[0].id, 'start');
  }, [userProfile, updateFlowState]);

  // Complete booking flow
  const completeBookingFlow = useCallback(async (): Promise<any> => {
    const validation = validateBookingData(flowState.bookingData as BookingData);
    
    if (!validation.isComplete) {
      throw new Error(`Booking incomplete: ${validation.missingFields.join(', ')}`);
    }

    updateFlowState({ isLoading: true });

    try {
      // Generate confirmation
      const confirmation = generateBookingConfirmation(flowState.bookingData as BookingData);
      
      // Here you would typically send the booking to your API
      // const response = await bookingAPI.createBooking(flowState.bookingData);
      
      // Clear progress after successful booking
      await clearProgress();
      
      updateFlowState({ isLoading: false });
      
      return {
        success: true,
        confirmation,
        bookingData: flowState.bookingData,
      };
    } catch (error) {
      updateFlowState({ isLoading: false });
      throw error;
    }
  }, [flowState.bookingData, updateFlowState]);

  // Abandon booking flow
  const abandonBookingFlow = useCallback(() => {
    trackStepAction('abandon');
    clearBookingData();
  }, [clearBookingData]);

  // Save progress
  const saveProgress = useCallback(async () => {
    if (!configRef.current.saveProgress) return;

    try {
      const progressData = {
        currentStepId: flowState.currentStepId,
        bookingData: flowState.bookingData,
        steps: flowState.steps,
        timestamp: Date.now(),
      };

      await AsyncStorage.setItem(BOOKING_PROGRESS_KEY, JSON.stringify(progressData));
    } catch (error) {
      console.warn('Failed to save booking progress:', error);
    }
  }, [flowState.currentStepId, flowState.bookingData, flowState.steps]);

  // Load progress
  const loadProgress = useCallback(async () => {
    if (!configRef.current.saveProgress) return;

    try {
      const saved = await AsyncStorage.getItem(BOOKING_PROGRESS_KEY);
      if (saved) {
        const progressData = JSON.parse(saved);
        
        // Check if progress is not too old (24 hours)
        const isRecent = Date.now() - progressData.timestamp < 24 * 60 * 60 * 1000;
        
        if (isRecent) {
          updateFlowState({
            currentStepId: progressData.currentStepId,
            bookingData: progressData.bookingData,
            steps: progressData.steps,
          });
        }
      }
    } catch (error) {
      console.warn('Failed to load booking progress:', error);
    }
  }, [updateFlowState]);

  // Clear progress
  const clearProgress = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(BOOKING_PROGRESS_KEY);
    } catch (error) {
      console.warn('Failed to clear booking progress:', error);
    }
  }, []);

  // Get booking summary
  const getBookingSummary = useCallback((): string[] => {
    return generateBookingSummary(flowState.bookingData as BookingData);
  }, [flowState.bookingData]);

  // Get estimated total
  const getEstimatedTotal = useCallback((): number => {
    return flowState.bookingData.pricing?.total || 0;
  }, [flowState.bookingData.pricing]);

  // Check if booking can be completed
  const canCompleteBooking = useCallback((): boolean => {
    const validation = validateBookingData(flowState.bookingData as BookingData);
    return validation.isComplete;
  }, [flowState.bookingData]);

  // Track step action
  const trackStepAction = useCallback((action: 'start' | 'complete' | 'abandon') => {
    const timeSpent = action !== 'start' ? Date.now() - stepStartTimeRef.current : undefined;
    trackBookingStep(flowState.currentStepId, action, timeSpent);
  }, [flowState.currentStepId]);

  // Auto-save progress
  useEffect(() => {
    if (configRef.current.saveProgress) {
      saveProgress();
    }
  }, [flowState.bookingData, saveProgress]);

  // Load progress on mount
  useEffect(() => {
    loadProgress();
  }, [loadProgress]);

  // Context value
  const contextValue: BookingFlowContextType = {
    // State
    flowState,
    
    // Navigation
    goToStep,
    goToNextStep,
    goToPreviousStep,
    
    // Data management
    updateBookingData,
    clearBookingData,
    
    // Step management
    completeStep,
    validateCurrentStep,
    
    // Flow control
    startBookingFlow,
    completeBookingFlow,
    abandonBookingFlow,
    
    // Progress management
    saveProgress,
    loadProgress,
    clearProgress,
    
    // Utilities
    getBookingSummary,
    getEstimatedTotal,
    canCompleteBooking,
    
    // Analytics
    trackStepAction,
  };

  return (
    <BookingFlowContext.Provider value={contextValue}>
      {children}
    </BookingFlowContext.Provider>
  );
};

// Hook to use booking flow context
export const useBookingFlow = (): BookingFlowContextType => {
  const context = useContext(BookingFlowContext);
  
  if (context === undefined) {
    throw new Error('useBookingFlow must be used within a BookingFlowProvider');
  }
  
  return context;
};

// Convenience hooks
export const useBookingProgress = () => {
  const { flowState } = useBookingFlow();
  return {
    progress: flowState.progress,
    currentStep: flowState.steps.find(s => s.id === flowState.currentStepId),
    totalSteps: flowState.steps.length,
    completedSteps: flowState.steps.filter(s => s.completed).length,
    estimatedTimeRemaining: flowState.estimatedTimeRemaining,
  };
};

export const useBookingNavigation = () => {
  const { goToStep, goToNextStep, goToPreviousStep, flowState } = useBookingFlow();
  return {
    goToStep,
    goToNextStep,
    goToPreviousStep,
    canProceed: flowState.canProceed,
    canGoBack: flowState.canGoBack,
  };
};

export const useBookingData = () => {
  const { flowState, updateBookingData } = useBookingFlow();
  return {
    bookingData: flowState.bookingData,
    updateBookingData,
    errors: flowState.errors,
  };
};

export default BookingFlowProvider;
