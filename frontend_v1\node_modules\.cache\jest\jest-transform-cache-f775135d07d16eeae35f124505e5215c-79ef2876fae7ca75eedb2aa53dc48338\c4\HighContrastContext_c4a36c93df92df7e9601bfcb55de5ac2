870ab8506bbf176c00d5df0603492376
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useHighContrastColors = exports.useHighContrast = exports.useAccessibleColor = exports.default = exports.HighContrastProvider = exports.HIGH_CONTRAST_COLORS = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _Colors = require("../constants/Colors");
var _colorContrastUtils = require("../utils/colorContrastUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var HIGH_CONTRAST_COLORS = exports.HIGH_CONTRAST_COLORS = {
  light: {
    background: {
      primary: '#FFFFFF',
      secondary: '#F0F0F0',
      elevated: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.8)'
    },
    text: {
      primary: '#000000',
      secondary: '#000000',
      tertiary: '#000000',
      inverse: '#FFFFFF',
      disabled: '#666666'
    },
    primary: {
      default: '#000000',
      light: '#333333',
      dark: '#000000',
      text: '#FFFFFF'
    },
    interactive: {
      primary: {
        default: '#000000',
        hover: '#333333',
        pressed: '#666666',
        disabled: '#CCCCCC',
        text: '#FFFFFF',
        textDisabled: '#999999'
      },
      secondary: {
        default: '#FFFFFF',
        hover: '#F0F0F0',
        pressed: '#E0E0E0',
        disabled: '#F8F8F8',
        border: '#000000',
        borderDisabled: '#CCCCCC',
        text: '#000000',
        textDisabled: '#999999'
      }
    },
    status: {
      success: '#000000',
      warning: '#000000',
      error: '#000000',
      info: '#000000'
    },
    border: {
      primary: '#000000',
      secondary: '#666666',
      tertiary: '#CCCCCC'
    }
  },
  dark: {
    background: {
      primary: '#000000',
      secondary: '#1A1A1A',
      elevated: '#000000',
      overlay: 'rgba(255, 255, 255, 0.8)'
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#FFFFFF',
      tertiary: '#FFFFFF',
      inverse: '#000000',
      disabled: '#999999'
    },
    primary: {
      default: '#FFFFFF',
      light: '#CCCCCC',
      dark: '#FFFFFF',
      text: '#000000'
    },
    interactive: {
      primary: {
        default: '#FFFFFF',
        hover: '#CCCCCC',
        pressed: '#999999',
        disabled: '#333333',
        text: '#000000',
        textDisabled: '#666666'
      },
      secondary: {
        default: '#000000',
        hover: '#1A1A1A',
        pressed: '#333333',
        disabled: '#0A0A0A',
        border: '#FFFFFF',
        borderDisabled: '#333333',
        text: '#FFFFFF',
        textDisabled: '#666666'
      }
    },
    status: {
      success: '#FFFFFF',
      warning: '#FFFFFF',
      error: '#FFFFFF',
      info: '#FFFFFF'
    },
    border: {
      primary: '#FFFFFF',
      secondary: '#999999',
      tertiary: '#333333'
    }
  }
};
var HighContrastContext = (0, _react.createContext)(undefined);
var HIGH_CONTRAST_STORAGE_KEY = '@vierla_high_contrast_mode';
var HIGH_CONTRAST_SCHEME_STORAGE_KEY = '@vierla_high_contrast_scheme';
var HighContrastProvider = exports.HighContrastProvider = function HighContrastProvider(_ref) {
  var children = _ref.children;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isHighContrastEnabled = _useState2[0],
    setIsHighContrastEnabled = _useState2[1];
  var _useState3 = (0, _react.useState)('light'),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    highContrastScheme = _useState4[0],
    setHighContrastSchemeState = _useState4[1];
  var _useState5 = (0, _react.useState)('light'),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    systemColorScheme = _useState6[0],
    setSystemColorScheme = _useState6[1];
  (0, _react.useEffect)(function () {
    var loadPreferences = function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var savedHighContrast = yield _asyncStorage.default.getItem(HIGH_CONTRAST_STORAGE_KEY);
          if (savedHighContrast !== null) {
            setIsHighContrastEnabled(JSON.parse(savedHighContrast));
          } else {
            if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
              var isReduceTransparencyEnabled = yield _reactNative.AccessibilityInfo.isReduceTransparencyEnabled == null ? void 0 : _reactNative.AccessibilityInfo.isReduceTransparencyEnabled();
              if (isReduceTransparencyEnabled) {
                setIsHighContrastEnabled(true);
              }
            }
          }
          var savedScheme = yield _asyncStorage.default.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY);
          if (savedScheme) {
            setHighContrastSchemeState(savedScheme);
          } else {
            var systemScheme = _reactNative.Appearance.getColorScheme() || 'light';
            setSystemColorScheme(systemScheme);
            setHighContrastSchemeState(systemScheme);
          }
        } catch (error) {
          console.warn('Failed to load high contrast preferences:', error);
        }
      });
      return function loadPreferences() {
        return _ref2.apply(this, arguments);
      };
    }();
    loadPreferences();
  }, []);
  (0, _react.useEffect)(function () {
    var subscription = _reactNative.Appearance.addChangeListener(function (_ref3) {
      var colorScheme = _ref3.colorScheme;
      var scheme = colorScheme || 'light';
      setSystemColorScheme(scheme);
      _asyncStorage.default.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY).then(function (savedScheme) {
        if (!savedScheme) {
          setHighContrastSchemeState(scheme);
        }
      });
    });
    return function () {
      return subscription == null ? void 0 : subscription.remove();
    };
  }, []);
  var saveHighContrastPreference = (0, _react.useCallback)(function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (enabled) {
      try {
        yield _asyncStorage.default.setItem(HIGH_CONTRAST_STORAGE_KEY, JSON.stringify(enabled));
      } catch (error) {
        console.warn('Failed to save high contrast preference:', error);
      }
    });
    return function (_x) {
      return _ref4.apply(this, arguments);
    };
  }(), []);
  var saveSchemePreference = (0, _react.useCallback)(function () {
    var _ref5 = (0, _asyncToGenerator2.default)(function* (scheme) {
      try {
        yield _asyncStorage.default.setItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY, scheme);
      } catch (error) {
        console.warn('Failed to save color scheme preference:', error);
      }
    });
    return function (_x2) {
      return _ref5.apply(this, arguments);
    };
  }(), []);
  var toggleHighContrast = (0, _react.useCallback)(function () {
    var newValue = !isHighContrastEnabled;
    setIsHighContrastEnabled(newValue);
    saveHighContrastPreference(newValue);
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(newValue ? 'High contrast mode enabled' : 'High contrast mode disabled');
    }
  }, [isHighContrastEnabled, saveHighContrastPreference]);
  var setHighContrastScheme = (0, _react.useCallback)(function (scheme) {
    setHighContrastSchemeState(scheme);
    saveSchemePreference(scheme);
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(`High contrast ${scheme} mode enabled`);
    }
  }, [saveSchemePreference]);
  var enableHighContrast = (0, _react.useCallback)(function () {
    setIsHighContrastEnabled(true);
    saveHighContrastPreference(true);
  }, [saveHighContrastPreference]);
  var disableHighContrast = (0, _react.useCallback)(function () {
    setIsHighContrastEnabled(false);
    saveHighContrastPreference(false);
  }, [saveHighContrastPreference]);
  var colors = _react.default.useMemo(function () {
    if (isHighContrastEnabled) {
      return HIGH_CONTRAST_COLORS[highContrastScheme];
    }
    return _Colors.Colors;
  }, [isHighContrastEnabled, highContrastScheme]);
  var getHighContrastColorUtil = (0, _react.useCallback)(function (color) {
    var background = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '#FFFFFF';
    if (isHighContrastEnabled) {
      return (0, _colorContrastUtils.getHighContrastColor)(color, background);
    }
    return color;
  }, [isHighContrastEnabled]);
  var adjustColorForAccessibility = (0, _react.useCallback)(function (color, background) {
    if (isHighContrastEnabled) {
      return (0, _colorContrastUtils.adjustColorForContrast)(color, background, 7.0);
    }
    return (0, _colorContrastUtils.adjustColorForContrast)(color, background, 4.5);
  }, [isHighContrastEnabled]);
  var contextValue = {
    isHighContrastEnabled: isHighContrastEnabled,
    highContrastScheme: highContrastScheme,
    colors: colors,
    toggleHighContrast: toggleHighContrast,
    setHighContrastScheme: setHighContrastScheme,
    enableHighContrast: enableHighContrast,
    disableHighContrast: disableHighContrast,
    getHighContrastColor: getHighContrastColorUtil,
    adjustColorForAccessibility: adjustColorForAccessibility
  };
  return (0, _jsxRuntime.jsx)(HighContrastContext.Provider, {
    value: contextValue,
    children: children
  });
};
var useHighContrast = exports.useHighContrast = function useHighContrast() {
  var context = (0, _react.useContext)(HighContrastContext);
  if (context === undefined) {
    throw new Error('useHighContrast must be used within a HighContrastProvider');
  }
  return context;
};
var useHighContrastColors = exports.useHighContrastColors = function useHighContrastColors() {
  var _useHighContrast = useHighContrast(),
    colors = _useHighContrast.colors,
    isHighContrastEnabled = _useHighContrast.isHighContrastEnabled;
  return {
    colors: colors,
    isHighContrastEnabled: isHighContrastEnabled
  };
};
var useAccessibleColor = exports.useAccessibleColor = function useAccessibleColor(color, background) {
  var _useHighContrast2 = useHighContrast(),
    getHighContrastColor = _useHighContrast2.getHighContrastColor,
    adjustColorForAccessibility = _useHighContrast2.adjustColorForAccessibility;
  return _react.default.useMemo(function () {
    if (background) {
      return adjustColorForAccessibility(color, background);
    }
    return getHighContrastColor(color);
  }, [color, background, getHighContrastColor, adjustColorForAccessibility]);
};
var _default = exports.default = HighContrastProvider;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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