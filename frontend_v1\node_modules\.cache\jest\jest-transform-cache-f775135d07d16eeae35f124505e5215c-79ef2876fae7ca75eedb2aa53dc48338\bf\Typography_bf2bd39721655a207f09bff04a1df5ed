0ad4279e4b768e401a429b91cb4691da
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Typography = exports.Subtitle = exports.Subtitle = exports.Overline = exports.Overline = exports.Label = exports.Label = exports.Heading = exports.Heading = exports.Display = exports.Display = exports.Code = exports.Code = exports.Caption = exports.Caption = exports.Body = exports.Body = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _Typography = require("../../constants/Typography");
var _HighContrastContext = require("../../contexts/HighContrastContext");
var _CognitiveAccessibilityContext = require("../../contexts/CognitiveAccessibilityContext");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "variant", "color", "align", "transform", "accessibilityLevel", "semanticLevel", "selectable", "numberOfLines", "ellipsizeMode", "style", "gutterBottom", "gutterTop", "testID"],
  _excluded2 = ["level"],
  _excluded3 = ["size"],
  _excluded4 = ["level"],
  _excluded5 = ["size"];
var Typography = exports.Typography = function Typography(_ref) {
  var _colors$text;
  var children = _ref.children,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'body1' : _ref$variant,
    color = _ref.color,
    _ref$align = _ref.align,
    align = _ref$align === void 0 ? 'left' : _ref$align,
    _ref$transform = _ref.transform,
    transform = _ref$transform === void 0 ? 'none' : _ref$transform,
    _ref$accessibilityLev = _ref.accessibilityLevel,
    accessibilityLevel = _ref$accessibilityLev === void 0 ? 'normal' : _ref$accessibilityLev,
    semanticLevel = _ref.semanticLevel,
    _ref$selectable = _ref.selectable,
    selectable = _ref$selectable === void 0 ? true : _ref$selectable,
    numberOfLines = _ref.numberOfLines,
    _ref$ellipsizeMode = _ref.ellipsizeMode,
    ellipsizeMode = _ref$ellipsizeMode === void 0 ? 'tail' : _ref$ellipsizeMode,
    style = _ref.style,
    _ref$gutterBottom = _ref.gutterBottom,
    gutterBottom = _ref$gutterBottom === void 0 ? false : _ref$gutterBottom,
    _ref$gutterTop = _ref.gutterTop,
    gutterTop = _ref$gutterTop === void 0 ? false : _ref$gutterTop,
    testID = _ref.testID,
    textProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useHighContrastColor = (0, _HighContrastContext.useHighContrastColors)(),
    colors = _useHighContrastColor.colors;
  var _useCognitiveAccessib = (0, _CognitiveAccessibilityContext.useCognitiveAccessibility)(),
    processText = _useCognitiveAccessib.processText,
    settings = _useCognitiveAccessib.settings;
  var variantStyles = _Typography.TYPOGRAPHY_VARIANTS[variant];
  var processedText = typeof children === 'string' ? processText(children) : children;
  var accessibleFontSize = (0, _Typography.getAccessibleFontSize)(variantStyles.fontSize, accessibilityLevel === 'normal' ? 'normal' : accessibilityLevel === 'large' ? 'large' : 'extraLarge');
  var effectiveColor = color || (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.primary) || '#333333';
  var getAccessibilityProps = function getAccessibilityProps() {
    var props = {};
    if (variant.startsWith('h') || semanticLevel) {
      props.accessibilityRole = 'header';
      props.accessibilityLevel = semanticLevel || parseInt(variant.charAt(1)) || 1;
    } else {
      props.accessibilityRole = 'text';
    }
    if (numberOfLines && typeof children === 'string') {
      props.accessibilityLabel = children;
    }
    return props;
  };
  var textStyles = [styles.base, {
    fontSize: accessibleFontSize,
    fontFamily: variantStyles.fontFamily,
    fontWeight: variantStyles.fontWeight,
    lineHeight: variantStyles.lineHeight,
    letterSpacing: variantStyles.letterSpacing,
    color: effectiveColor,
    textAlign: align,
    textTransform: transform
  }, gutterTop && styles.gutterTop, gutterBottom && styles.gutterBottom, style].filter(Boolean);
  if (__DEV__ && !(0, _Typography.isTextSizeAccessible)(accessibleFontSize)) {
    console.warn(`Typography: Font size ${accessibleFontSize}px may not be accessible`);
  }
  return (0, _jsxRuntime.jsx)(_reactNative.Text, Object.assign({
    style: textStyles,
    selectable: selectable,
    numberOfLines: numberOfLines,
    ellipsizeMode: ellipsizeMode,
    testID: testID
  }, getAccessibilityProps(), textProps, {
    children: processedText
  }));
};
var Heading = exports.Heading = exports.Heading = function Heading(_ref2) {
  var level = _ref2.level,
    props = (0, _objectWithoutProperties2.default)(_ref2, _excluded2);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: `h${level}`,
    semanticLevel: level,
    gutterBottom: true
  }, props));
};
var Body = exports.Body = exports.Body = function Body(_ref3) {
  var _ref3$size = _ref3.size,
    size = _ref3$size === void 0 ? 'medium' : _ref3$size,
    props = (0, _objectWithoutProperties2.default)(_ref3, _excluded3);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: size === 'small' ? 'body2' : 'body1',
    gutterBottom: true
  }, props));
};
var Caption = exports.Caption = exports.Caption = function Caption(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "caption"
  }, props));
};
var Label = exports.Label = exports.Label = function Label(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "label"
  }, props));
};
var Subtitle = exports.Subtitle = exports.Subtitle = function Subtitle(_ref4) {
  var _ref4$level = _ref4.level,
    level = _ref4$level === void 0 ? 1 : _ref4$level,
    props = (0, _objectWithoutProperties2.default)(_ref4, _excluded4);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: level === 1 ? 'subtitle1' : 'subtitle2',
    gutterBottom: true
  }, props));
};
var Overline = exports.Overline = exports.Overline = function Overline(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "overline"
  }, props));
};
var Display = exports.Display = exports.Display = function Display(_ref5) {
  var _ref5$size = _ref5.size,
    size = _ref5$size === void 0 ? 'medium' : _ref5$size,
    props = (0, _objectWithoutProperties2.default)(_ref5, _excluded5);
  var variantMap = {
    small: 'h2',
    medium: 'h1',
    large: 'h1'
  };
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: variantMap[size],
    gutterBottom: true
  }, props));
};
var Code = exports.Code = exports.Code = function Code(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "code",
    selectable: true
  }, props));
};
var styles = _reactNative.StyleSheet.create({
  base: {},
  gutterTop: {
    marginTop: 16
  },
  gutterBottom: {
    marginBottom: 16
  }
});
var _default = exports.default = Typography;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdE5hdGl2ZSIsIl9UeXBvZ3JhcGh5IiwiX0hpZ2hDb250cmFzdENvbnRleHQiLCJfQ29nbml0aXZlQWNjZXNzaWJpbGl0eUNvbnRleHQiLCJfanN4UnVudGltZSIsIl9leGNsdWRlZCIsIl9leGNsdWRlZDIiLCJfZXhjbHVkZWQzIiwiX2V4Y2x1ZGVkNCIsIl9leGNsdWRlZDUiLCJUeXBvZ3JhcGh5IiwiZXhwb3J0cyIsIl9yZWYiLCJfY29sb3JzJHRleHQiLCJjaGlsZHJlbiIsIl9yZWYkdmFyaWFudCIsInZhcmlhbnQiLCJjb2xvciIsIl9yZWYkYWxpZ24iLCJhbGlnbiIsIl9yZWYkdHJhbnNmb3JtIiwidHJhbnNmb3JtIiwiX3JlZiRhY2Nlc3NpYmlsaXR5TGV2IiwiYWNjZXNzaWJpbGl0eUxldmVsIiwic2VtYW50aWNMZXZlbCIsIl9yZWYkc2VsZWN0YWJsZSIsInNlbGVjdGFibGUiLCJudW1iZXJPZkxpbmVzIiwiX3JlZiRlbGxpcHNpemVNb2RlIiwiZWxsaXBzaXplTW9kZSIsInN0eWxlIiwiX3JlZiRndXR0ZXJCb3R0b20iLCJndXR0ZXJCb3R0b20iLCJfcmVmJGd1dHRlclRvcCIsImd1dHRlclRvcCIsInRlc3RJRCIsInRleHRQcm9wcyIsIl9vYmplY3RXaXRob3V0UHJvcGVydGllczIiLCJkZWZhdWx0IiwiX3VzZUhpZ2hDb250cmFzdENvbG9yIiwidXNlSGlnaENvbnRyYXN0Q29sb3JzIiwiY29sb3JzIiwiX3VzZUNvZ25pdGl2ZUFjY2Vzc2liIiwidXNlQ29nbml0aXZlQWNjZXNzaWJpbGl0eSIsInByb2Nlc3NUZXh0Iiwic2V0dGluZ3MiLCJ2YXJpYW50U3R5bGVzIiwiVFlQT0dSQVBIWV9WQVJJQU5UUyIsInByb2Nlc3NlZFRleHQiLCJhY2Nlc3NpYmxlRm9udFNpemUiLCJnZXRBY2Nlc3NpYmxlRm9udFNpemUiLCJmb250U2l6ZSIsImVmZmVjdGl2ZUNvbG9yIiwidGV4dCIsInByaW1hcnkiLCJnZXRBY2Nlc3NpYmlsaXR5UHJvcHMiLCJwcm9wcyIsInN0YXJ0c1dpdGgiLCJhY2Nlc3NpYmlsaXR5Um9sZSIsInBhcnNlSW50IiwiY2hhckF0IiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwidGV4dFN0eWxlcyIsInN0eWxlcyIsImJhc2UiLCJmb250RmFtaWx5IiwiZm9udFdlaWdodCIsImxpbmVIZWlnaHQiLCJsZXR0ZXJTcGFjaW5nIiwidGV4dEFsaWduIiwidGV4dFRyYW5zZm9ybSIsImZpbHRlciIsIkJvb2xlYW4iLCJfX0RFVl9fIiwiaXNUZXh0U2l6ZUFjY2Vzc2libGUiLCJjb25zb2xlIiwid2FybiIsImpzeCIsIlRleHQiLCJPYmplY3QiLCJhc3NpZ24iLCJIZWFkaW5nIiwiX3JlZjIiLCJsZXZlbCIsIkJvZHkiLCJfcmVmMyIsIl9yZWYzJHNpemUiLCJzaXplIiwiQ2FwdGlvbiIsIkxhYmVsIiwiU3VidGl0bGUiLCJfcmVmNCIsIl9yZWY0JGxldmVsIiwiT3ZlcmxpbmUiLCJEaXNwbGF5IiwiX3JlZjUiLCJfcmVmNSRzaXplIiwidmFyaWFudE1hcCIsInNtYWxsIiwibWVkaXVtIiwibGFyZ2UiLCJDb2RlIiwiU3R5bGVTaGVldCIsImNyZWF0ZSIsIm1hcmdpblRvcCIsIm1hcmdpbkJvdHRvbSIsIl9kZWZhdWx0Il0sInNvdXJjZXMiOlsiVHlwb2dyYXBoeS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUeXBvZ3JhcGh5IENvbXBvbmVudFxuICpcbiAqIENvbXByZWhlbnNpdmUgdHlwb2dyYXBoeSBjb21wb25lbnQgd2l0aCBzZW1hbnRpYyB2YXJpYW50cyxcbiAqIGFjY2Vzc2liaWxpdHkgZmVhdHVyZXMsIGFuZCB2aXN1YWwgaGllcmFyY2h5IHN1cHBvcnQuXG4gKlxuICogRmVhdHVyZXM6XG4gKiAtIFNlbWFudGljIHR5cG9ncmFwaHkgdmFyaWFudHNcbiAqIC0gQWNjZXNzaWJpbGl0eSBjb21wbGlhbmNlXG4gKiAtIFJlc3BvbnNpdmUgZm9udCBzY2FsaW5nXG4gKiAtIFZpc3VhbCBoaWVyYXJjaHkgc3VwcG9ydFxuICogLSBQbGF0Zm9ybSBvcHRpbWl6YXRpb25cbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBUZXh0LCBTdHlsZVNoZWV0LCBUZXh0UHJvcHMgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHsgVFlQT0dSQVBIWV9WQVJJQU5UUywgZ2V0QWNjZXNzaWJsZUZvbnRTaXplLCBpc1RleHRTaXplQWNjZXNzaWJsZSB9IGZyb20gJy4uLy4uL2NvbnN0YW50cy9UeXBvZ3JhcGh5JztcbmltcG9ydCB7IHVzZUhpZ2hDb250cmFzdENvbG9ycyB9IGZyb20gJy4uLy4uL2NvbnRleHRzL0hpZ2hDb250cmFzdENvbnRleHQnO1xuaW1wb3J0IHsgdXNlQ29nbml0aXZlQWNjZXNzaWJpbGl0eSB9IGZyb20gJy4uLy4uL2NvbnRleHRzL0NvZ25pdGl2ZUFjY2Vzc2liaWxpdHlDb250ZXh0JztcblxuLy8gVHlwb2dyYXBoeSB2YXJpYW50IHR5cGVzXG5leHBvcnQgdHlwZSBUeXBvZ3JhcGh5VmFyaWFudCA9IGtleW9mIHR5cGVvZiBUWVBPR1JBUEhZX1ZBUklBTlRTO1xuXG4vLyBDb21wb25lbnQgcHJvcHNcbmV4cG9ydCBpbnRlcmZhY2UgVHlwb2dyYXBoeVByb3BzIGV4dGVuZHMgT21pdDxUZXh0UHJvcHMsICdzdHlsZSc+IHtcbiAgLy8gQ29udGVudFxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBcbiAgLy8gVHlwb2dyYXBoeVxuICB2YXJpYW50PzogVHlwb2dyYXBoeVZhcmlhbnQ7XG4gIGNvbG9yPzogc3RyaW5nO1xuICBhbGlnbj86ICdsZWZ0JyB8ICdjZW50ZXInIHwgJ3JpZ2h0JyB8ICdqdXN0aWZ5JztcbiAgdHJhbnNmb3JtPzogJ25vbmUnIHwgJ3VwcGVyY2FzZScgfCAnbG93ZXJjYXNlJyB8ICdjYXBpdGFsaXplJztcbiAgXG4gIC8vIEFjY2Vzc2liaWxpdHlcbiAgYWNjZXNzaWJpbGl0eUxldmVsPzogJ25vcm1hbCcgfCAnbGFyZ2UnIHwgJ2V4dHJhTGFyZ2UnO1xuICBzZW1hbnRpY0xldmVsPzogMSB8IDIgfCAzIHwgNCB8IDUgfCA2OyAvLyBGb3IgaGVhZGluZ3NcbiAgXG4gIC8vIEJlaGF2aW9yXG4gIHNlbGVjdGFibGU/OiBib29sZWFuO1xuICBudW1iZXJPZkxpbmVzPzogbnVtYmVyO1xuICBlbGxpcHNpemVNb2RlPzogJ2hlYWQnIHwgJ21pZGRsZScgfCAndGFpbCcgfCAnY2xpcCc7XG4gIFxuICAvLyBTdHlsaW5nXG4gIHN0eWxlPzogYW55O1xuICBndXR0ZXJCb3R0b20/OiBib29sZWFuO1xuICBndXR0ZXJUb3A/OiBib29sZWFuO1xuICBcbiAgLy8gVGVzdGluZ1xuICB0ZXN0SUQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBUeXBvZ3JhcGh5OiBSZWFjdC5GQzxUeXBvZ3JhcGh5UHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIHZhcmlhbnQgPSAnYm9keTEnLFxuICBjb2xvcixcbiAgYWxpZ24gPSAnbGVmdCcsXG4gIHRyYW5zZm9ybSA9ICdub25lJyxcbiAgYWNjZXNzaWJpbGl0eUxldmVsID0gJ25vcm1hbCcsXG4gIHNlbWFudGljTGV2ZWwsXG4gIHNlbGVjdGFibGUgPSB0cnVlLFxuICBudW1iZXJPZkxpbmVzLFxuICBlbGxpcHNpemVNb2RlID0gJ3RhaWwnLFxuICBzdHlsZSxcbiAgZ3V0dGVyQm90dG9tID0gZmFsc2UsXG4gIGd1dHRlclRvcCA9IGZhbHNlLFxuICB0ZXN0SUQsXG4gIC4uLnRleHRQcm9wc1xufSkgPT4ge1xuICAvLyBIb29rc1xuICBjb25zdCB7IGNvbG9ycyB9ID0gdXNlSGlnaENvbnRyYXN0Q29sb3JzKCk7XG4gIGNvbnN0IHsgcHJvY2Vzc1RleHQsIHNldHRpbmdzIH0gPSB1c2VDb2duaXRpdmVBY2Nlc3NpYmlsaXR5KCk7XG5cbiAgLy8gR2V0IHR5cG9ncmFwaHkgdmFyaWFudCBzdHlsZXNcbiAgY29uc3QgdmFyaWFudFN0eWxlcyA9IFRZUE9HUkFQSFlfVkFSSUFOVFNbdmFyaWFudF07XG5cbiAgLy8gUHJvY2VzcyB0ZXh0IGZvciBjb2duaXRpdmUgYWNjZXNzaWJpbGl0eVxuICBjb25zdCBwcm9jZXNzZWRUZXh0ID0gdHlwZW9mIGNoaWxkcmVuID09PSAnc3RyaW5nJyA/IHByb2Nlc3NUZXh0KGNoaWxkcmVuKSA6IGNoaWxkcmVuO1xuXG4gIC8vIENhbGN1bGF0ZSBhY2Nlc3NpYmxlIGZvbnQgc2l6ZVxuICBjb25zdCBhY2Nlc3NpYmxlRm9udFNpemUgPSBnZXRBY2Nlc3NpYmxlRm9udFNpemUoXG4gICAgdmFyaWFudFN0eWxlcy5mb250U2l6ZSxcbiAgICBhY2Nlc3NpYmlsaXR5TGV2ZWwgPT09ICdub3JtYWwnID8gJ25vcm1hbCcgOlxuICAgIGFjY2Vzc2liaWxpdHlMZXZlbCA9PT0gJ2xhcmdlJyA/ICdsYXJnZScgOiAnZXh0cmFMYXJnZSdcbiAgKTtcblxuICAvLyBHZXQgZWZmZWN0aXZlIGNvbG9yXG4gIGNvbnN0IGVmZmVjdGl2ZUNvbG9yID0gY29sb3IgfHwgY29sb3JzPy50ZXh0Py5wcmltYXJ5IHx8ICcjMzMzMzMzJztcblxuICAvLyBHZW5lcmF0ZSBhY2Nlc3NpYmlsaXR5IHByb3BzXG4gIGNvbnN0IGdldEFjY2Vzc2liaWxpdHlQcm9wcyA9ICgpID0+IHtcbiAgICBjb25zdCBwcm9wczogYW55ID0ge307XG5cbiAgICAvLyBTZXQgYWNjZXNzaWJpbGl0eSByb2xlIGZvciBoZWFkaW5nc1xuICAgIGlmICh2YXJpYW50LnN0YXJ0c1dpdGgoJ2gnKSB8fCBzZW1hbnRpY0xldmVsKSB7XG4gICAgICBwcm9wcy5hY2Nlc3NpYmlsaXR5Um9sZSA9ICdoZWFkZXInO1xuICAgICAgcHJvcHMuYWNjZXNzaWJpbGl0eUxldmVsID0gc2VtYW50aWNMZXZlbCB8fCBwYXJzZUludCh2YXJpYW50LmNoYXJBdCgxKSkgfHwgMTtcbiAgICB9IGVsc2Uge1xuICAgICAgcHJvcHMuYWNjZXNzaWJpbGl0eVJvbGUgPSAndGV4dCc7XG4gICAgfVxuXG4gICAgLy8gQWRkIGFjY2Vzc2liaWxpdHkgbGFiZWwgaWYgdGV4dCBpcyB0cnVuY2F0ZWRcbiAgICBpZiAobnVtYmVyT2ZMaW5lcyAmJiB0eXBlb2YgY2hpbGRyZW4gPT09ICdzdHJpbmcnKSB7XG4gICAgICBwcm9wcy5hY2Nlc3NpYmlsaXR5TGFiZWwgPSBjaGlsZHJlbjtcbiAgICB9XG5cbiAgICByZXR1cm4gcHJvcHM7XG4gIH07XG5cbiAgLy8gR2VuZXJhdGUgc3R5bGVzXG4gIGNvbnN0IHRleHRTdHlsZXMgPSBbXG4gICAgc3R5bGVzLmJhc2UsXG4gICAge1xuICAgICAgZm9udFNpemU6IGFjY2Vzc2libGVGb250U2l6ZSxcbiAgICAgIGZvbnRGYW1pbHk6IHZhcmlhbnRTdHlsZXMuZm9udEZhbWlseSxcbiAgICAgIGZvbnRXZWlnaHQ6IHZhcmlhbnRTdHlsZXMuZm9udFdlaWdodCxcbiAgICAgIGxpbmVIZWlnaHQ6IHZhcmlhbnRTdHlsZXMubGluZUhlaWdodCxcbiAgICAgIGxldHRlclNwYWNpbmc6IHZhcmlhbnRTdHlsZXMubGV0dGVyU3BhY2luZyxcbiAgICAgIGNvbG9yOiBlZmZlY3RpdmVDb2xvcixcbiAgICAgIHRleHRBbGlnbjogYWxpZ24sXG4gICAgICB0ZXh0VHJhbnNmb3JtOiB0cmFuc2Zvcm0sXG4gICAgfSxcbiAgICBndXR0ZXJUb3AgJiYgc3R5bGVzLmd1dHRlclRvcCxcbiAgICBndXR0ZXJCb3R0b20gJiYgc3R5bGVzLmd1dHRlckJvdHRvbSxcbiAgICBzdHlsZSxcbiAgXS5maWx0ZXIoQm9vbGVhbik7XG5cbiAgLy8gV2FybiBhYm91dCBhY2Nlc3NpYmlsaXR5IGlzc3VlcyBpbiBkZXZlbG9wbWVudFxuICBpZiAoX19ERVZfXyAmJiAhaXNUZXh0U2l6ZUFjY2Vzc2libGUoYWNjZXNzaWJsZUZvbnRTaXplKSkge1xuICAgIGNvbnNvbGUud2FybihgVHlwb2dyYXBoeTogRm9udCBzaXplICR7YWNjZXNzaWJsZUZvbnRTaXplfXB4IG1heSBub3QgYmUgYWNjZXNzaWJsZWApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8VGV4dFxuICAgICAgc3R5bGU9e3RleHRTdHlsZXN9XG4gICAgICBzZWxlY3RhYmxlPXtzZWxlY3RhYmxlfVxuICAgICAgbnVtYmVyT2ZMaW5lcz17bnVtYmVyT2ZMaW5lc31cbiAgICAgIGVsbGlwc2l6ZU1vZGU9e2VsbGlwc2l6ZU1vZGV9XG4gICAgICB0ZXN0SUQ9e3Rlc3RJRH1cbiAgICAgIHsuLi5nZXRBY2Nlc3NpYmlsaXR5UHJvcHMoKX1cbiAgICAgIHsuLi50ZXh0UHJvcHN9XG4gICAgPlxuICAgICAge3Byb2Nlc3NlZFRleHR9XG4gICAgPC9UZXh0PlxuICApO1xufTtcblxuLy8gU3BlY2lhbGl6ZWQgdHlwb2dyYXBoeSBjb21wb25lbnRzIGZvciBjb21tb24gdXNlIGNhc2VzXG5leHBvcnQgY29uc3QgSGVhZGluZzogUmVhY3QuRkM8T21pdDxUeXBvZ3JhcGh5UHJvcHMsICd2YXJpYW50Jz4gJiB7XG4gIGxldmVsOiAxIHwgMiB8IDMgfCA0IHwgNSB8IDY7XG59PiA9ICh7IGxldmVsLCAuLi5wcm9wcyB9KSA9PiAoXG4gIDxUeXBvZ3JhcGh5XG4gICAgdmFyaWFudD17YGgke2xldmVsfWAgYXMgVHlwb2dyYXBoeVZhcmlhbnR9XG4gICAgc2VtYW50aWNMZXZlbD17bGV2ZWx9XG4gICAgZ3V0dGVyQm90dG9tXG4gICAgey4uLnByb3BzfVxuICAvPlxuKTtcblxuZXhwb3J0IGNvbnN0IEJvZHk6IFJlYWN0LkZDPE9taXQ8VHlwb2dyYXBoeVByb3BzLCAndmFyaWFudCc+ICYge1xuICBzaXplPzogJ3NtYWxsJyB8ICdtZWRpdW0nO1xufT4gPSAoeyBzaXplID0gJ21lZGl1bScsIC4uLnByb3BzIH0pID0+IChcbiAgPFR5cG9ncmFwaHlcbiAgICB2YXJpYW50PXtzaXplID09PSAnc21hbGwnID8gJ2JvZHkyJyA6ICdib2R5MSd9XG4gICAgZ3V0dGVyQm90dG9tXG4gICAgey4uLnByb3BzfVxuICAvPlxuKTtcblxuZXhwb3J0IGNvbnN0IENhcHRpb246IFJlYWN0LkZDPE9taXQ8VHlwb2dyYXBoeVByb3BzLCAndmFyaWFudCc+PiA9IChwcm9wcykgPT4gKFxuICA8VHlwb2dyYXBoeVxuICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCJcbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pO1xuXG5leHBvcnQgY29uc3QgTGFiZWw6IFJlYWN0LkZDPE9taXQ8VHlwb2dyYXBoeVByb3BzLCAndmFyaWFudCc+PiA9IChwcm9wcykgPT4gKFxuICA8VHlwb2dyYXBoeVxuICAgIHZhcmlhbnQ9XCJsYWJlbFwiXG4gICAgey4uLnByb3BzfVxuICAvPlxuKTtcblxuZXhwb3J0IGNvbnN0IFN1YnRpdGxlOiBSZWFjdC5GQzxPbWl0PFR5cG9ncmFwaHlQcm9wcywgJ3ZhcmlhbnQnPiAmIHtcbiAgbGV2ZWw/OiAxIHwgMjtcbn0+ID0gKHsgbGV2ZWwgPSAxLCAuLi5wcm9wcyB9KSA9PiAoXG4gIDxUeXBvZ3JhcGh5XG4gICAgdmFyaWFudD17bGV2ZWwgPT09IDEgPyAnc3VidGl0bGUxJyA6ICdzdWJ0aXRsZTInfVxuICAgIGd1dHRlckJvdHRvbVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbik7XG5cbmV4cG9ydCBjb25zdCBPdmVybGluZTogUmVhY3QuRkM8T21pdDxUeXBvZ3JhcGh5UHJvcHMsICd2YXJpYW50Jz4+ID0gKHByb3BzKSA9PiAoXG4gIDxUeXBvZ3JhcGh5XG4gICAgdmFyaWFudD1cIm92ZXJsaW5lXCJcbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pO1xuXG4vLyBEaXNwbGF5IGNvbXBvbmVudCBmb3IgbGFyZ2UgdGV4dFxuZXhwb3J0IGNvbnN0IERpc3BsYXk6IFJlYWN0LkZDPE9taXQ8VHlwb2dyYXBoeVByb3BzLCAndmFyaWFudCc+ICYge1xuICBzaXplPzogJ3NtYWxsJyB8ICdtZWRpdW0nIHwgJ2xhcmdlJztcbn0+ID0gKHsgc2l6ZSA9ICdtZWRpdW0nLCAuLi5wcm9wcyB9KSA9PiB7XG4gIGNvbnN0IHZhcmlhbnRNYXAgPSB7XG4gICAgc21hbGw6ICdoMicsXG4gICAgbWVkaXVtOiAnaDEnLFxuICAgIGxhcmdlOiAnaDEnLFxuICB9IGFzIGNvbnN0O1xuXG4gIHJldHVybiAoXG4gICAgPFR5cG9ncmFwaHlcbiAgICAgIHZhcmlhbnQ9e3ZhcmlhbnRNYXBbc2l6ZV19XG4gICAgICBndXR0ZXJCb3R0b21cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufTtcblxuLy8gQ29kZSBjb21wb25lbnQgZm9yIG1vbm9zcGFjZSB0ZXh0XG5leHBvcnQgY29uc3QgQ29kZTogUmVhY3QuRkM8T21pdDxUeXBvZ3JhcGh5UHJvcHMsICd2YXJpYW50Jz4+ID0gKHByb3BzKSA9PiAoXG4gIDxUeXBvZ3JhcGh5XG4gICAgdmFyaWFudD1cImNvZGVcIlxuICAgIHNlbGVjdGFibGVcbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pO1xuXG5jb25zdCBzdHlsZXMgPSBTdHlsZVNoZWV0LmNyZWF0ZSh7XG4gIGJhc2U6IHtcbiAgICAvLyBCYXNlIHN0eWxlcyBhcHBsaWVkIHRvIGFsbCB0eXBvZ3JhcGh5XG4gIH0sXG4gIGd1dHRlclRvcDoge1xuICAgIG1hcmdpblRvcDogMTYsXG4gIH0sXG4gIGd1dHRlckJvdHRvbToge1xuICAgIG1hcmdpbkJvdHRvbTogMTYsXG4gIH0sXG59KTtcblxuLy8gRXhwb3J0IGFsbCBjb21wb25lbnRzXG5leHBvcnQgZGVmYXVsdCBUeXBvZ3JhcGh5O1xuZXhwb3J0IHtcbiAgSGVhZGluZyxcbiAgQm9keSxcbiAgQ2FwdGlvbixcbiAgTGFiZWwsXG4gIFN1YnRpdGxlLFxuICBPdmVybGluZSxcbiAgRGlzcGxheSxcbiAgQ29kZSxcbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQWlCQSxJQUFBQSxNQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxZQUFBLEdBQUFELE9BQUE7QUFDQSxJQUFBRSxXQUFBLEdBQUFGLE9BQUE7QUFDQSxJQUFBRyxvQkFBQSxHQUFBSCxPQUFBO0FBQ0EsSUFBQUksOEJBQUEsR0FBQUosT0FBQTtBQUF5RixJQUFBSyxXQUFBLEdBQUFMLE9BQUE7QUFBQSxJQUFBTSxTQUFBO0VBQUFDLFVBQUE7RUFBQUMsVUFBQTtFQUFBQyxVQUFBO0VBQUFDLFVBQUE7QUFrQ2xGLElBQU1DLFVBQXFDLEdBQUFDLE9BQUEsQ0FBQUQsVUFBQSxHQUFHLFNBQXhDQSxVQUFxQ0EsQ0FBQUUsSUFBQSxFQWdCNUM7RUFBQSxJQUFBQyxZQUFBO0VBQUEsSUFmSkMsUUFBUSxHQUFBRixJQUFBLENBQVJFLFFBQVE7SUFBQUMsWUFBQSxHQUFBSCxJQUFBLENBQ1JJLE9BQU87SUFBUEEsT0FBTyxHQUFBRCxZQUFBLGNBQUcsT0FBTyxHQUFBQSxZQUFBO0lBQ2pCRSxLQUFLLEdBQUFMLElBQUEsQ0FBTEssS0FBSztJQUFBQyxVQUFBLEdBQUFOLElBQUEsQ0FDTE8sS0FBSztJQUFMQSxLQUFLLEdBQUFELFVBQUEsY0FBRyxNQUFNLEdBQUFBLFVBQUE7SUFBQUUsY0FBQSxHQUFBUixJQUFBLENBQ2RTLFNBQVM7SUFBVEEsU0FBUyxHQUFBRCxjQUFBLGNBQUcsTUFBTSxHQUFBQSxjQUFBO0lBQUFFLHFCQUFBLEdBQUFWLElBQUEsQ0FDbEJXLGtCQUFrQjtJQUFsQkEsa0JBQWtCLEdBQUFELHFCQUFBLGNBQUcsUUFBUSxHQUFBQSxxQkFBQTtJQUM3QkUsYUFBYSxHQUFBWixJQUFBLENBQWJZLGFBQWE7SUFBQUMsZUFBQSxHQUFBYixJQUFBLENBQ2JjLFVBQVU7SUFBVkEsVUFBVSxHQUFBRCxlQUFBLGNBQUcsSUFBSSxHQUFBQSxlQUFBO0lBQ2pCRSxhQUFhLEdBQUFmLElBQUEsQ0FBYmUsYUFBYTtJQUFBQyxrQkFBQSxHQUFBaEIsSUFBQSxDQUNiaUIsYUFBYTtJQUFiQSxhQUFhLEdBQUFELGtCQUFBLGNBQUcsTUFBTSxHQUFBQSxrQkFBQTtJQUN0QkUsS0FBSyxHQUFBbEIsSUFBQSxDQUFMa0IsS0FBSztJQUFBQyxpQkFBQSxHQUFBbkIsSUFBQSxDQUNMb0IsWUFBWTtJQUFaQSxZQUFZLEdBQUFELGlCQUFBLGNBQUcsS0FBSyxHQUFBQSxpQkFBQTtJQUFBRSxjQUFBLEdBQUFyQixJQUFBLENBQ3BCc0IsU0FBUztJQUFUQSxTQUFTLEdBQUFELGNBQUEsY0FBRyxLQUFLLEdBQUFBLGNBQUE7SUFDakJFLE1BQU0sR0FBQXZCLElBQUEsQ0FBTnVCLE1BQU07SUFDSEMsU0FBUyxPQUFBQyx5QkFBQSxDQUFBQyxPQUFBLEVBQUExQixJQUFBLEVBQUFQLFNBQUE7RUFHWixJQUFBa0MscUJBQUEsR0FBbUIsSUFBQUMsMENBQXFCLEVBQUMsQ0FBQztJQUFsQ0MsTUFBTSxHQUFBRixxQkFBQSxDQUFORSxNQUFNO0VBQ2QsSUFBQUMscUJBQUEsR0FBa0MsSUFBQUMsd0RBQXlCLEVBQUMsQ0FBQztJQUFyREMsV0FBVyxHQUFBRixxQkFBQSxDQUFYRSxXQUFXO0lBQUVDLFFBQVEsR0FBQUgscUJBQUEsQ0FBUkcsUUFBUTtFQUc3QixJQUFNQyxhQUFhLEdBQUdDLCtCQUFtQixDQUFDL0IsT0FBTyxDQUFDO0VBR2xELElBQU1nQyxhQUFhLEdBQUcsT0FBT2xDLFFBQVEsS0FBSyxRQUFRLEdBQUc4QixXQUFXLENBQUM5QixRQUFRLENBQUMsR0FBR0EsUUFBUTtFQUdyRixJQUFNbUMsa0JBQWtCLEdBQUcsSUFBQUMsaUNBQXFCLEVBQzlDSixhQUFhLENBQUNLLFFBQVEsRUFDdEI1QixrQkFBa0IsS0FBSyxRQUFRLEdBQUcsUUFBUSxHQUMxQ0Esa0JBQWtCLEtBQUssT0FBTyxHQUFHLE9BQU8sR0FBRyxZQUM3QyxDQUFDO0VBR0QsSUFBTTZCLGNBQWMsR0FBR25DLEtBQUssS0FBSXdCLE1BQU0sYUFBQTVCLFlBQUEsR0FBTjRCLE1BQU0sQ0FBRVksSUFBSSxxQkFBWnhDLFlBQUEsQ0FBY3lDLE9BQU8sS0FBSSxTQUFTO0VBR2xFLElBQU1DLHFCQUFxQixHQUFHLFNBQXhCQSxxQkFBcUJBLENBQUEsRUFBUztJQUNsQyxJQUFNQyxLQUFVLEdBQUcsQ0FBQyxDQUFDO0lBR3JCLElBQUl4QyxPQUFPLENBQUN5QyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUlqQyxhQUFhLEVBQUU7TUFDNUNnQyxLQUFLLENBQUNFLGlCQUFpQixHQUFHLFFBQVE7TUFDbENGLEtBQUssQ0FBQ2pDLGtCQUFrQixHQUFHQyxhQUFhLElBQUltQyxRQUFRLENBQUMzQyxPQUFPLENBQUM0QyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO0lBQzlFLENBQUMsTUFBTTtNQUNMSixLQUFLLENBQUNFLGlCQUFpQixHQUFHLE1BQU07SUFDbEM7SUFHQSxJQUFJL0IsYUFBYSxJQUFJLE9BQU9iLFFBQVEsS0FBSyxRQUFRLEVBQUU7TUFDakQwQyxLQUFLLENBQUNLLGtCQUFrQixHQUFHL0MsUUFBUTtJQUNyQztJQUVBLE9BQU8wQyxLQUFLO0VBQ2QsQ0FBQztFQUdELElBQU1NLFVBQVUsR0FBRyxDQUNqQkMsTUFBTSxDQUFDQyxJQUFJLEVBQ1g7SUFDRWIsUUFBUSxFQUFFRixrQkFBa0I7SUFDNUJnQixVQUFVLEVBQUVuQixhQUFhLENBQUNtQixVQUFVO0lBQ3BDQyxVQUFVLEVBQUVwQixhQUFhLENBQUNvQixVQUFVO0lBQ3BDQyxVQUFVLEVBQUVyQixhQUFhLENBQUNxQixVQUFVO0lBQ3BDQyxhQUFhLEVBQUV0QixhQUFhLENBQUNzQixhQUFhO0lBQzFDbkQsS0FBSyxFQUFFbUMsY0FBYztJQUNyQmlCLFNBQVMsRUFBRWxELEtBQUs7SUFDaEJtRCxhQUFhLEVBQUVqRDtFQUNqQixDQUFDLEVBQ0RhLFNBQVMsSUFBSTZCLE1BQU0sQ0FBQzdCLFNBQVMsRUFDN0JGLFlBQVksSUFBSStCLE1BQU0sQ0FBQy9CLFlBQVksRUFDbkNGLEtBQUssQ0FDTixDQUFDeUMsTUFBTSxDQUFDQyxPQUFPLENBQUM7RUFHakIsSUFBSUMsT0FBTyxJQUFJLENBQUMsSUFBQUMsZ0NBQW9CLEVBQUN6QixrQkFBa0IsQ0FBQyxFQUFFO0lBQ3hEMEIsT0FBTyxDQUFDQyxJQUFJLENBQUMseUJBQXlCM0Isa0JBQWtCLDBCQUEwQixDQUFDO0VBQ3JGO0VBRUEsT0FDRSxJQUFBN0MsV0FBQSxDQUFBeUUsR0FBQSxFQUFDN0UsWUFBQSxDQUFBOEUsSUFBSSxFQUFBQyxNQUFBLENBQUFDLE1BQUE7SUFDSGxELEtBQUssRUFBRWdDLFVBQVc7SUFDbEJwQyxVQUFVLEVBQUVBLFVBQVc7SUFDdkJDLGFBQWEsRUFBRUEsYUFBYztJQUM3QkUsYUFBYSxFQUFFQSxhQUFjO0lBQzdCTSxNQUFNLEVBQUVBO0VBQU8sR0FDWG9CLHFCQUFxQixDQUFDLENBQUMsRUFDdkJuQixTQUFTO0lBQUF0QixRQUFBLEVBRVprQztFQUFhLEVBQ1YsQ0FBQztBQUVYLENBQUM7QUFHTSxJQUFNaUMsT0FFWCxHQUFBdEUsT0FBQSxDQUFBc0UsT0FBQSxHQUFBdEUsT0FBQSxDQUFBc0UsT0FBQSxHQUFHLFNBRlFBLE9BRVhBLENBQUFDLEtBQUE7RUFBQSxJQUFNQyxLQUFLLEdBQUFELEtBQUEsQ0FBTEMsS0FBSztJQUFLM0IsS0FBSyxPQUFBbkIseUJBQUEsQ0FBQUMsT0FBQSxFQUFBNEMsS0FBQSxFQUFBNUUsVUFBQTtFQUFBLE9BQ3JCLElBQUFGLFdBQUEsQ0FBQXlFLEdBQUEsRUFBQ25FLFVBQVUsRUFBQXFFLE1BQUEsQ0FBQUMsTUFBQTtJQUNUaEUsT0FBTyxFQUFFLElBQUltRSxLQUFLLEVBQXdCO0lBQzFDM0QsYUFBYSxFQUFFMkQsS0FBTTtJQUNyQm5ELFlBQVk7RUFBQSxHQUNSd0IsS0FBSyxDQUNWLENBQUM7QUFBQSxDQUNIO0FBRU0sSUFBTTRCLElBRVgsR0FBQXpFLE9BQUEsQ0FBQXlFLElBQUEsR0FBQXpFLE9BQUEsQ0FBQXlFLElBQUEsR0FBRyxTQUZRQSxJQUVYQSxDQUFBQyxLQUFBO0VBQUEsSUFBQUMsVUFBQSxHQUFBRCxLQUFBLENBQU1FLElBQUk7SUFBSkEsSUFBSSxHQUFBRCxVQUFBLGNBQUcsUUFBUSxHQUFBQSxVQUFBO0lBQUs5QixLQUFLLE9BQUFuQix5QkFBQSxDQUFBQyxPQUFBLEVBQUErQyxLQUFBLEVBQUE5RSxVQUFBO0VBQUEsT0FDL0IsSUFBQUgsV0FBQSxDQUFBeUUsR0FBQSxFQUFDbkUsVUFBVSxFQUFBcUUsTUFBQSxDQUFBQyxNQUFBO0lBQ1RoRSxPQUFPLEVBQUV1RSxJQUFJLEtBQUssT0FBTyxHQUFHLE9BQU8sR0FBRyxPQUFRO0lBQzlDdkQsWUFBWTtFQUFBLEdBQ1J3QixLQUFLLENBQ1YsQ0FBQztBQUFBLENBQ0g7QUFFTSxJQUFNZ0MsT0FBbUQsR0FBQTdFLE9BQUEsQ0FBQTZFLE9BQUEsR0FBQTdFLE9BQUEsQ0FBQTZFLE9BQUEsR0FBRyxTQUF0REEsT0FBbURBLENBQUloQyxLQUFLO0VBQUEsT0FDdkUsSUFBQXBELFdBQUEsQ0FBQXlFLEdBQUEsRUFBQ25FLFVBQVUsRUFBQXFFLE1BQUEsQ0FBQUMsTUFBQTtJQUNUaEUsT0FBTyxFQUFDO0VBQVMsR0FDYndDLEtBQUssQ0FDVixDQUFDO0FBQUEsQ0FDSDtBQUVNLElBQU1pQyxLQUFpRCxHQUFBOUUsT0FBQSxDQUFBOEUsS0FBQSxHQUFBOUUsT0FBQSxDQUFBOEUsS0FBQSxHQUFHLFNBQXBEQSxLQUFpREEsQ0FBSWpDLEtBQUs7RUFBQSxPQUNyRSxJQUFBcEQsV0FBQSxDQUFBeUUsR0FBQSxFQUFDbkUsVUFBVSxFQUFBcUUsTUFBQSxDQUFBQyxNQUFBO0lBQ1RoRSxPQUFPLEVBQUM7RUFBTyxHQUNYd0MsS0FBSyxDQUNWLENBQUM7QUFBQSxDQUNIO0FBRU0sSUFBTWtDLFFBRVgsR0FBQS9FLE9BQUEsQ0FBQStFLFFBQUEsR0FBQS9FLE9BQUEsQ0FBQStFLFFBQUEsR0FBRyxTQUZRQSxRQUVYQSxDQUFBQyxLQUFBO0VBQUEsSUFBQUMsV0FBQSxHQUFBRCxLQUFBLENBQU1SLEtBQUs7SUFBTEEsS0FBSyxHQUFBUyxXQUFBLGNBQUcsQ0FBQyxHQUFBQSxXQUFBO0lBQUtwQyxLQUFLLE9BQUFuQix5QkFBQSxDQUFBQyxPQUFBLEVBQUFxRCxLQUFBLEVBQUFuRixVQUFBO0VBQUEsT0FDekIsSUFBQUosV0FBQSxDQUFBeUUsR0FBQSxFQUFDbkUsVUFBVSxFQUFBcUUsTUFBQSxDQUFBQyxNQUFBO0lBQ1RoRSxPQUFPLEVBQUVtRSxLQUFLLEtBQUssQ0FBQyxHQUFHLFdBQVcsR0FBRyxXQUFZO0lBQ2pEbkQsWUFBWTtFQUFBLEdBQ1J3QixLQUFLLENBQ1YsQ0FBQztBQUFBLENBQ0g7QUFFTSxJQUFNcUMsUUFBb0QsR0FBQWxGLE9BQUEsQ0FBQWtGLFFBQUEsR0FBQWxGLE9BQUEsQ0FBQWtGLFFBQUEsR0FBRyxTQUF2REEsUUFBb0RBLENBQUlyQyxLQUFLO0VBQUEsT0FDeEUsSUFBQXBELFdBQUEsQ0FBQXlFLEdBQUEsRUFBQ25FLFVBQVUsRUFBQXFFLE1BQUEsQ0FBQUMsTUFBQTtJQUNUaEUsT0FBTyxFQUFDO0VBQVUsR0FDZHdDLEtBQUssQ0FDVixDQUFDO0FBQUEsQ0FDSDtBQUdNLElBQU1zQyxPQUVYLEdBQUFuRixPQUFBLENBQUFtRixPQUFBLEdBQUFuRixPQUFBLENBQUFtRixPQUFBLEdBQUcsU0FGUUEsT0FFWEEsQ0FBQUMsS0FBQSxFQUFzQztFQUFBLElBQUFDLFVBQUEsR0FBQUQsS0FBQSxDQUFoQ1IsSUFBSTtJQUFKQSxJQUFJLEdBQUFTLFVBQUEsY0FBRyxRQUFRLEdBQUFBLFVBQUE7SUFBS3hDLEtBQUssT0FBQW5CLHlCQUFBLENBQUFDLE9BQUEsRUFBQXlELEtBQUEsRUFBQXRGLFVBQUE7RUFDL0IsSUFBTXdGLFVBQVUsR0FBRztJQUNqQkMsS0FBSyxFQUFFLElBQUk7SUFDWEMsTUFBTSxFQUFFLElBQUk7SUFDWkMsS0FBSyxFQUFFO0VBQ1QsQ0FBVTtFQUVWLE9BQ0UsSUFBQWhHLFdBQUEsQ0FBQXlFLEdBQUEsRUFBQ25FLFVBQVUsRUFBQXFFLE1BQUEsQ0FBQUMsTUFBQTtJQUNUaEUsT0FBTyxFQUFFaUYsVUFBVSxDQUFDVixJQUFJLENBQUU7SUFDMUJ2RCxZQUFZO0VBQUEsR0FDUndCLEtBQUssQ0FDVixDQUFDO0FBRU4sQ0FBQztBQUdNLElBQU02QyxJQUFnRCxHQUFBMUYsT0FBQSxDQUFBMEYsSUFBQSxHQUFBMUYsT0FBQSxDQUFBMEYsSUFBQSxHQUFHLFNBQW5EQSxJQUFnREEsQ0FBSTdDLEtBQUs7RUFBQSxPQUNwRSxJQUFBcEQsV0FBQSxDQUFBeUUsR0FBQSxFQUFDbkUsVUFBVSxFQUFBcUUsTUFBQSxDQUFBQyxNQUFBO0lBQ1RoRSxPQUFPLEVBQUMsTUFBTTtJQUNkVSxVQUFVO0VBQUEsR0FDTjhCLEtBQUssQ0FDVixDQUFDO0FBQUEsQ0FDSDtBQUVELElBQU1PLE1BQU0sR0FBR3VDLHVCQUFVLENBQUNDLE1BQU0sQ0FBQztFQUMvQnZDLElBQUksRUFBRSxDQUVOLENBQUM7RUFDRDlCLFNBQVMsRUFBRTtJQUNUc0UsU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEeEUsWUFBWSxFQUFFO0lBQ1p5RSxZQUFZLEVBQUU7RUFDaEI7QUFDRixDQUFDLENBQUM7QUFBQyxJQUFBQyxRQUFBLEdBQUEvRixPQUFBLENBQUEyQixPQUFBLEdBR1k1QixVQUFVIiwiaWdub3JlTGlzdCI6W119