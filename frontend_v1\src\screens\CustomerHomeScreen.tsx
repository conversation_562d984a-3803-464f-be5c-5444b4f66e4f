import React, { useState, useCallback, useMemo } from 'react';
import {
  ScrollView,
  StyleSheet,
  RefreshControl,
  Alert,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Import components
import NavigationHeader, { HeaderAction } from '../components/navigation/NavigationHeader';
import { useNavigationHelper } from '../hooks/useNavigationHelper';
import { useDesignSystem, getCardStyles } from '../contexts/DesignSystemContext';

// Import types and utilities
import { CustomerStackParamList } from '../navigation/types';

type CustomerHomeScreenNavigationProp = StackNavigationProp<CustomerStackParamList, 'CustomerTabs'>;

interface Category {
  id: string;
  name: string;
  category: string;
  color: string;
  serviceCount: number;
  icon: string;
}

const CustomerHomeScreen: React.FC = () => {
  const navigation = useNavigation<CustomerHomeScreenNavigationProp>();
  const { navigateWithContext } = useNavigationHelper();
  const { theme, getSpacing, getColor } = useDesignSystem();
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Local state
  const [refreshing, setRefreshing] = useState(false);

  // Categories data
  const categories: Category[] = [
    {
      id: '1',
      name: 'Barber',
      category: 'barber',
      color: '#5A7A63',
      serviceCount: 12,
      icon: 'cut-outline',
    },
    {
      id: '2',
      name: 'Salon',
      category: 'salon',
      color: '#6B8A74',
      serviceCount: 8,
      icon: 'brush-outline',
    },
    {
      id: '3',
      name: 'Nail Services',
      category: 'nail-services',
      color: '#5A7A63',
      serviceCount: 15,
      icon: 'hand-left-outline',
    },
    {
      id: '4',
      name: 'Lash Services',
      category: 'lash-services',
      color: '#4A6B52',
      serviceCount: 6,
      icon: 'eye-outline',
    },
    {
      id: '5',
      name: 'Braiding',
      category: 'braiding',
      color: '#3A5B42',
      serviceCount: 10,
      icon: 'flower-outline',
    },
    {
      id: '6',
      name: 'Skincare',
      category: 'skincare',
      color: '#6B8A74',
      serviceCount: 7,
      icon: 'heart-outline',
    },
    {
      id: '7',
      name: 'Massage',
      category: 'massage',
      color: '#5A7A63',
      serviceCount: 8,
      icon: 'hand-right-outline',
    },
  ];

  // Refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error refreshing data:', error);
      Alert.alert('Error', 'Failed to refresh data. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Navigation handlers
  const handleCategoryPress = useCallback((category: string) => {
    console.log('Category pressed:', category);
  }, []);

  const handleNotificationPress = useCallback(() => {
    navigateWithContext('Notifications');
  }, [navigateWithContext]);

  const handleSearchPress = useCallback(() => {
    console.log('Search pressed');
  }, []);

  const handleProfilePress = useCallback(() => {
    navigateWithContext('EditProfile');
  }, [navigateWithContext]);

  // Header actions
  const headerActions: HeaderAction[] = useMemo(() => [
    {
      id: 'search',
      icon: 'search',
      label: 'Search',
      onPress: handleSearchPress,
      accessibilityLabel: 'Search services and providers',
    },
    {
      id: 'notifications',
      icon: 'notifications-outline',
      label: 'Notifications',
      onPress: handleNotificationPress,
      badge: 3,
      accessibilityLabel: 'View notifications',
    },
    {
      id: 'profile',
      icon: 'person-outline',
      label: 'Profile',
      onPress: handleProfilePress,
      accessibilityLabel: 'View profile',
    },
  ], [handleSearchPress, handleNotificationPress, handleProfilePress]);

  return (
    <SafeAreaView style={styles.container}>
      <NavigationHeader
        title="Home"
        showBackButton={false}
        showBreadcrumbs={false}
        actions={headerActions}
      />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#5A7A63']}
            tintColor={'#5A7A63'}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.greeting}>Good morning</Text>
          <Text style={styles.userName}>User</Text>
        </View>

        {/* Browse Services Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse Services</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            <View style={styles.categoriesRow}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[styles.categoryCard, { backgroundColor: category.color }]}
                  onPress={() => handleCategoryPress(category.category)}
                >
                  <Ionicons name={category.icon as any} size={24} color="#FFFFFF" />
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryCount}>{category.serviceCount} services</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Providers</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.placeholderText}>Featured providers will appear here</Text>
        </View>

        {/* Favorite Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Favorite Providers</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.placeholderText}>Your favorite providers will appear here</Text>
        </View>

        {/* Nearby Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Providers</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.placeholderText}>Nearby providers will appear here</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background?.primary || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: theme.spacing.xxxl,
  },
  welcomeSection: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    backgroundColor: theme.colors.background?.primary || '#FFFFFF',
  },
  greeting: {
    ...theme.typography.styles.body2,
    color: theme.colors.text?.secondary || '#666',
  },
  userName: {
    ...theme.typography.styles.h4,
    color: theme.colors.text?.primary || '#333',
    marginTop: theme.spacing.xs,
  },

  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  sectionTitle: {
    ...theme.typography.styles.h5,
    color: theme.colors.text?.primary || '#333',
  },
  seeAllText: {
    ...theme.typography.styles.body2,
    color: theme.colors.primary?.default || '#5A7A63',
    fontWeight: theme.typography.fontWeight.medium,
  },
  categoriesScroll: {
    paddingLeft: theme.spacing.sm,
  },
  categoriesRow: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.xs,
  },
  categoryCard: {
    width: 120,
    height: 100,
    borderRadius: theme.borderRadius.component.card,
    padding: theme.spacing.sm,
    marginHorizontal: theme.spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.elevation.sm,
  },
  categoryName: {
    ...theme.typography.styles.body2,
    color: '#FFFFFF',
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  categoryCount: {
    ...theme.typography.styles.caption,
    color: '#FFFFFF',
    marginTop: theme.spacing.xs,
    textAlign: 'center',
    opacity: theme.opacity.secondary,
  },
  placeholderText: {
    ...theme.typography.styles.body1,
    color: theme.colors.text?.tertiary || '#999',
    textAlign: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
});

export default CustomerHomeScreen;