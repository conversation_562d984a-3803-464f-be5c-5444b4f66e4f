1b48e98556d480c94f3a3149c32f7706
_getJestObj().mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
_getJestObj().mock('react-native', function () {
  var RN = jest.requireActual('react-native');
  return Object.assign({}, RN, {
    PixelRatio: Object.assign({}, RN.PixelRatio, {
      getFontScale: jest.fn(function () {
        return 1;
      }),
      get: jest.fn(function () {
        return 2;
      })
    })
  });
});
_getJestObj().mock('react-native/Libraries/Utilities/Dimensions', function () {
  return {
    get: jest.fn(function () {
      return {
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1
      };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Platform', function () {
  return {
    OS: 'ios',
    Version: '14.0',
    select: jest.fn(function (obj) {
      return obj.ios;
    })
  };
});
_getJestObj().mock('expo-status-bar', function () {
  return {
    StatusBar: 'StatusBar',
    setStatusBarStyle: jest.fn(),
    setStatusBarBackgroundColor: jest.fn()
  };
});
_getJestObj().mock('expo-constants', function () {
  return {
    default: {
      statusBarHeight: 44,
      deviceName: 'iPhone',
      platform: {
        ios: {
          platform: 'ios'
        }
      },
      appOwnership: 'standalone',
      expoVersion: '45.0.0'
    }
  };
});
_getJestObj().mock('expo-font', function () {
  return {
    loadAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    isLoaded: jest.fn(function () {
      return true;
    }),
    isLoading: jest.fn(function () {
      return false;
    })
  };
});
_getJestObj().mock('expo-linear-gradient', function () {
  return {
    LinearGradient: 'LinearGradient'
  };
});
_getJestObj().mock('expo-haptics', function () {
  return {
    impactAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    notificationAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    selectionAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    ImpactFeedbackStyle: {
      Light: 'light',
      Medium: 'medium',
      Heavy: 'heavy'
    },
    NotificationFeedbackType: {
      Success: 'success',
      Warning: 'warning',
      Error: 'error'
    }
  };
});
_getJestObj().mock('expo-crypto', function () {
  return {
    randomUUID: jest.fn(function () {
      return 'mock-uuid-1234-5678-9012';
    }),
    digestStringAsync: jest.fn(function () {
      return Promise.resolve('mock-hash');
    })
  };
});
_getJestObj().mock('expo-location', function () {
  return {
    requestForegroundPermissionsAsync: jest.fn(function () {
      return Promise.resolve({
        status: 'granted'
      });
    }),
    getCurrentPositionAsync: jest.fn(function () {
      return Promise.resolve({
        coords: {
          latitude: 43.6532,
          longitude: -79.3832,
          accuracy: 10,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          speed: 0
        },
        timestamp: Date.now()
      });
    }),
    watchPositionAsync: jest.fn(function () {
      return Promise.resolve({
        remove: jest.fn()
      });
    })
  };
});
_getJestObj().mock('expo-local-authentication', function () {
  return {
    hasHardwareAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    supportedAuthenticationTypesAsync: jest.fn(function () {
      return Promise.resolve([1, 2]);
    }),
    isEnrolledAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    authenticateAsync: jest.fn(function () {
      return Promise.resolve({
        success: true,
        error: undefined
      });
    }),
    AuthenticationType: {
      FINGERPRINT: 1,
      FACIAL_RECOGNITION: 2
    }
  };
});
_getJestObj().mock('@react-navigation/native', function () {
  return {
    useNavigation: function useNavigation() {
      return {
        navigate: jest.fn(),
        goBack: jest.fn(),
        reset: jest.fn(),
        setParams: jest.fn(),
        dispatch: jest.fn(),
        isFocused: jest.fn(function () {
          return true;
        }),
        canGoBack: jest.fn(function () {
          return true;
        }),
        getId: jest.fn(function () {
          return 'mock-route-id';
        }),
        getParent: jest.fn(),
        getState: jest.fn(function () {
          return {
            index: 0,
            routes: [{
              name: 'Home',
              key: 'home-key'
            }]
          };
        })
      };
    },
    useRoute: function useRoute() {
      return {
        key: 'mock-route-key',
        name: 'MockScreen',
        params: {}
      };
    },
    useFocusEffect: jest.fn(),
    useIsFocused: jest.fn(function () {
      return true;
    }),
    NavigationContainer: function NavigationContainer(_ref) {
      var children = _ref.children;
      return children;
    },
    createNavigationContainerRef: jest.fn(function () {
      return {
        current: {
          navigate: jest.fn(),
          reset: jest.fn(),
          goBack: jest.fn()
        }
      };
    })
  };
});
_getJestObj().mock('@react-navigation/stack', function () {
  return {
    createStackNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref2) {
          var children = _ref2.children;
          return children;
        },
        Screen: function Screen(_ref3) {
          var children = _ref3.children;
          return children;
        }
      };
    }),
    CardStyleInterpolators: {
      forHorizontalIOS: {},
      forVerticalIOS: {},
      forModalPresentationIOS: {}
    },
    TransitionPresets: {
      SlideFromRightIOS: {},
      ModalSlideFromBottomIOS: {}
    }
  };
});
_getJestObj().mock('@react-navigation/bottom-tabs', function () {
  return {
    createBottomTabNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref4) {
          var children = _ref4.children;
          return children;
        },
        Screen: function Screen(_ref5) {
          var children = _ref5.children;
          return children;
        }
      };
    })
  };
});
Object.defineProperty(exports, "__esModule", {
  value: true
});
require("react-native-gesture-handler/jestSetup");
require("@testing-library/jest-native/extend-expect");
var _testingUtils = require("../utils/testingUtils");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
(0, _testingUtils.setupTestEnvironment)({
  enableAccessibilityTesting: true,
  enablePerformanceTesting: true,
  mockNetworkRequests: true,
  mockLocationServices: true,
  mockNotifications: true,
  logLevel: 'warn'
});
try {
  _getJestObj().mock('react-i18next', function () {
    return {
      useTranslation: function useTranslation() {
        return {
          t: function t(key, options) {
            if (options && typeof options === 'object') {
              var result = key;
              Object.keys(options).forEach(function (optionKey) {
                result = result.replace(`{{${optionKey}}}`, options[optionKey]);
              });
              return result;
            }
            return key;
          },
          i18n: {
            language: 'en',
            changeLanguage: jest.fn(function () {
              return Promise.resolve();
            })
          }
        };
      },
      initReactI18next: {
        type: '3rdParty',
        init: jest.fn()
      }
    };
  });
  require.resolve('react-i18next');
} catch (e) {}
try {
  _getJestObj().mock('zustand', function () {
    return {
      create: jest.fn(function (fn) {
        var store = fn(function () {
          return {};
        }, function () {
          return {};
        });
        return function () {
          return store;
        };
      })
    };
  });
  require.resolve('zustand');
} catch (e) {}
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
afterEach(function () {
  jest.clearAllMocks();
});
process.on('unhandledRejection', function (reason, promise) {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
jest.setTimeout(10000);
beforeEach(function () {
  jest.useFakeTimers();
});
afterEach(function () {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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