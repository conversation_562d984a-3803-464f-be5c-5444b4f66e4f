0e4f44ce602c65fd129f262bc135f8d9
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.beginAsyncEvent = beginAsyncEvent;
exports.beginEvent = beginEvent;
exports.counterEvent = counterEvent;
exports.endAsyncEvent = endAsyncEvent;
exports.endEvent = endEvent;
exports.isEnabled = isEnabled;
exports.setEnabled = setEnabled;
var TRACE_TAG_REACT_APPS = 1 << 17;
var _asyncCookie = 0;
function isEnabled() {
  return global.nativeTraceIsTracing ? global.nativeTraceIsTracing(TRACE_TAG_REACT_APPS) : Boolean(global.__RCTProfileIsProfiling);
}
function setEnabled(_doEnable) {}
function beginEvent(eventName, args) {
  if (isEnabled()) {
    var eventNameString = typeof eventName === 'function' ? eventName() : eventName;
    global.nativeTraceBeginSection(TRACE_TAG_REACT_APPS, eventNameString, args);
  }
}
function endEvent(args) {
  if (isEnabled()) {
    global.nativeTraceEndSection(TRACE_TAG_REACT_APPS, args);
  }
}
function beginAsyncEvent(eventName, args) {
  var cookie = _asyncCookie;
  if (isEnabled()) {
    _asyncCookie++;
    var eventNameString = typeof eventName === 'function' ? eventName() : eventName;
    global.nativeTraceBeginAsyncSection(TRACE_TAG_REACT_APPS, eventNameString, cookie, args);
  }
  return cookie;
}
function endAsyncEvent(eventName, cookie, args) {
  if (isEnabled()) {
    var eventNameString = typeof eventName === 'function' ? eventName() : eventName;
    global.nativeTraceEndAsyncSection(TRACE_TAG_REACT_APPS, eventNameString, cookie, args);
  }
}
function counterEvent(eventName, value) {
  if (isEnabled()) {
    var eventNameString = typeof eventName === 'function' ? eventName() : eventName;
    global.nativeTraceCounter && global.nativeTraceCounter(TRACE_TAG_REACT_APPS, eventNameString, value);
  }
}
if (__DEV__) {
  var Systrace = {
    isEnabled: isEnabled,
    setEnabled: setEnabled,
    beginEvent: beginEvent,
    endEvent: endEvent,
    beginAsyncEvent: beginAsyncEvent,
    endAsyncEvent: endAsyncEvent,
    counterEvent: counterEvent
  };
  global[(global.__METRO_GLOBAL_PREFIX__ || '') + '__SYSTRACE'] = Systrace;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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