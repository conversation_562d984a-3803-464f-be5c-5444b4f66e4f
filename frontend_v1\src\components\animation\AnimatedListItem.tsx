/**
 * Animated List Item Component
 *
 * Enhanced list item component with entrance animations and
 * micro-interactions for improved user experience.
 *
 * Features:
 * - Entrance animations
 * - Staggered animations
 * - Press feedback
 * - Loading states
 * - Accessibility compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef, useEffect, useCallback } from 'react';
import {
  TouchableOpacity,
  Animated,
  StyleSheet,
  View,
} from 'react-native';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';
import {
  createEntranceAnimation,
  createPressAnimation,
  staggerAnimations,
  shouldReduceMotion,
  ANIMATION_DURATIONS,
} from '../../utils/animationUtils';

// Component props
export interface AnimatedListItemProps {
  children: React.ReactNode;
  
  // Behavior
  onPress?: () => void;
  disabled?: boolean;
  
  // Animation
  index?: number;
  staggerDelay?: number;
  enableAnimations?: boolean;
  
  // Styling
  style?: any;
  pressable?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: string;
  
  // Testing
  testID?: string;
}

export const AnimatedListItem: React.FC<AnimatedListItemProps> = ({
  children,
  onPress,
  disabled = false,
  index = 0,
  staggerDelay = 100,
  enableAnimations = true,
  style,
  pressable = true,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  testID,
}) => {
  // Animation values
  const opacityValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.8)).current;
  const translateYValue = useRef(new Animated.Value(30)).current;
  const pressScaleValue = useRef(new Animated.Value(1)).current;

  // Hooks
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();
  const triggerHapticFeedback = useHapticFeedback();

  // Press animation handlers
  const { pressIn, pressOut } = createPressAnimation(pressScaleValue, 0.98);

  // Entrance animation
  useEffect(() => {
    if (enableAnimations && !shouldReduceMotion()) {
      const entranceAnimation = createEntranceAnimation(
        opacityValue,
        scaleValue,
        translateYValue,
        {
          duration: ANIMATION_DURATIONS.normal,
          delay: index * staggerDelay,
        }
      );

      entranceAnimation.start();
    } else {
      // Set final values immediately if animations are disabled
      opacityValue.setValue(1);
      scaleValue.setValue(1);
      translateYValue.setValue(0);
    }
  }, [
    enableAnimations,
    index,
    staggerDelay,
    opacityValue,
    scaleValue,
    translateYValue,
  ]);

  // Handle press in
  const handlePressIn = useCallback(() => {
    if (!disabled && pressable && enableAnimations && !shouldReduceMotion()) {
      pressIn();
    }
    if (!disabled) {
      triggerHapticFeedback('light');
    }
  }, [disabled, pressable, enableAnimations, pressIn, triggerHapticFeedback]);

  // Handle press out
  const handlePressOut = useCallback(() => {
    if (!disabled && pressable && enableAnimations && !shouldReduceMotion()) {
      pressOut();
    }
  }, [disabled, pressable, enableAnimations, pressOut]);

  // Handle press
  const handlePress = useCallback(() => {
    if (!disabled && onPress) {
      triggerHapticFeedback('medium');
      onPress();
    }
  }, [disabled, onPress, triggerHapticFeedback]);

  // Get container styles
  const containerStyles = [
    styles.container,
    {
      backgroundColor: colors?.background?.primary || '#FFFFFF',
      borderColor: colors?.border?.primary || '#E0E0E0',
    },
    style,
  ];

  // Render content
  const renderContent = () => (
    <Animated.View
      style={[
        containerStyles,
        {
          opacity: opacityValue,
          transform: [
            { scale: scaleValue },
            { translateY: translateYValue },
            { scale: pressScaleValue },
          ],
        },
      ]}
    >
      {children}
    </Animated.View>
  );

  // If not pressable, return just the animated view
  if (!pressable || !onPress) {
    return renderContent();
  }

  // Return touchable version
  return (
    <TouchableOpacity
      style={touchTargetStyles}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}
      activeOpacity={1} // We handle opacity with our own animation
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityState={{ disabled }}
      testID={testID}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

// Animated list container for staggered animations
export interface AnimatedListProps {
  children: React.ReactNode;
  staggerDelay?: number;
  enableAnimations?: boolean;
  style?: any;
  testID?: string;
}

export const AnimatedList: React.FC<AnimatedListProps> = ({
  children,
  staggerDelay = 100,
  enableAnimations = true,
  style,
  testID,
}) => {
  return (
    <View style={[styles.list, style]} testID={testID}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === AnimatedListItem) {
          return React.cloneElement(child, {
            index,
            staggerDelay,
            enableAnimations,
            ...child.props,
          });
        }
        return child;
      })}
    </View>
  );
};

// Skeleton loading item for list placeholders
export interface SkeletonListItemProps {
  height?: number;
  enableAnimations?: boolean;
  style?: any;
}

export const SkeletonListItem: React.FC<SkeletonListItemProps> = ({
  height = 60,
  enableAnimations = true,
  style,
}) => {
  const opacityValue = useRef(new Animated.Value(0.3)).current;
  const { colors } = useHighContrastColors();

  useEffect(() => {
    if (enableAnimations && !shouldReduceMotion()) {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(opacityValue, {
            toValue: 0.7,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(opacityValue, {
            toValue: 0.3,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );

      pulseAnimation.start();
      return () => pulseAnimation.stop();
    }
  }, [enableAnimations, opacityValue]);

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          height,
          backgroundColor: colors?.background?.secondary || '#F0F0F0',
          opacity: opacityValue,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 4,
    marginHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  list: {
    flex: 1,
  },
  skeleton: {
    marginVertical: 4,
    marginHorizontal: 16,
    borderRadius: 8,
  },
});

export default AnimatedListItem;
