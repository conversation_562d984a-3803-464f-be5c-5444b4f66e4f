/**
 * Motor Accessibility Context Provider
 *
 * Provides motor accessibility features and adaptations for users with
 * motor impairments, following WCAG 2.2 AA guidelines.
 *
 * Features:
 * - Adaptive touch target sizing
 * - Gesture customization
 * - Timing adjustments
 * - Haptic feedback
 * - Switch control support
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Platform, AccessibilityInfo, Haptics } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  MotorDifficultyLevel,
  getRecommendedTouchTargetSize,
  getRecommendedSpacing,
  generateTouchTargetStyles,
  getMotorAccessibilitySupport,
} from '../utils/touchTargetUtils';

// Motor accessibility settings
export interface MotorAccessibilitySettings {
  difficultyLevel: MotorDifficultyLevel;
  touchTargetSize: number;
  touchTargetSpacing: number;
  gestureTimeout: number;
  hapticFeedbackEnabled: boolean;
  longPressDelay: number;
  doubleTapDelay: number;
  swipeThreshold: number;
  adaptiveSizing: boolean;
  voiceControlEnabled: boolean;
  switchControlEnabled: boolean;
}

// Context interface
interface MotorAccessibilityContextType {
  // Settings
  settings: MotorAccessibilitySettings;
  
  // Actions
  updateSettings: (newSettings: Partial<MotorAccessibilitySettings>) => void;
  setDifficultyLevel: (level: MotorDifficultyLevel) => void;
  resetToDefaults: () => void;
  
  // Utilities
  getTouchTargetStyles: (contentSize?: { width?: number; height?: number }) => any;
  triggerHapticFeedback: (type?: 'light' | 'medium' | 'heavy') => void;
  isGestureEnabled: (gestureType: string) => boolean;
  getGestureTimeout: () => number;
  
  // Capabilities
  capabilities: ReturnType<typeof getMotorAccessibilitySupport>;
}

// Default settings
const DEFAULT_SETTINGS: MotorAccessibilitySettings = {
  difficultyLevel: 'none',
  touchTargetSize: 44,
  touchTargetSpacing: 8,
  gestureTimeout: 3000,
  hapticFeedbackEnabled: true,
  longPressDelay: 500,
  doubleTapDelay: 300,
  swipeThreshold: 50,
  adaptiveSizing: true,
  voiceControlEnabled: false,
  switchControlEnabled: false,
};

// Create context
const MotorAccessibilityContext = createContext<MotorAccessibilityContextType | undefined>(undefined);

// Storage key
const MOTOR_ACCESSIBILITY_STORAGE_KEY = '@vierla_motor_accessibility_settings';

// Provider props
interface MotorAccessibilityProviderProps {
  children: React.ReactNode;
}

export const MotorAccessibilityProvider: React.FC<MotorAccessibilityProviderProps> = ({ children }) => {
  // State
  const [settings, setSettings] = useState<MotorAccessibilitySettings>(DEFAULT_SETTINGS);
  const [capabilities] = useState(() => getMotorAccessibilitySupport());

  // Load saved settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await AsyncStorage.getItem(MOTOR_ACCESSIBILITY_STORAGE_KEY);
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...DEFAULT_SETTINGS, ...parsed });
        } else {
          // Check system accessibility preferences
          await detectSystemPreferences();
        }
      } catch (error) {
        console.warn('Failed to load motor accessibility settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Detect system accessibility preferences
  const detectSystemPreferences = useCallback(async () => {
    try {
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        // Check for switch control
        const isSwitchControlRunning = await AccessibilityInfo.isSwitchControlEnabled?.();
        
        // Check for voice control (iOS)
        const isVoiceOverRunning = await AccessibilityInfo.isScreenReaderEnabled();
        
        if (isSwitchControlRunning || isVoiceOverRunning) {
          setSettings(prev => ({
            ...prev,
            difficultyLevel: 'moderate',
            touchTargetSize: getRecommendedTouchTargetSize('moderate'),
            touchTargetSpacing: getRecommendedSpacing('moderate'),
            switchControlEnabled: isSwitchControlRunning || false,
            voiceControlEnabled: isVoiceOverRunning,
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to detect system accessibility preferences:', error);
    }
  }, []);

  // Save settings
  const saveSettings = useCallback(async (newSettings: MotorAccessibilitySettings) => {
    try {
      await AsyncStorage.setItem(MOTOR_ACCESSIBILITY_STORAGE_KEY, JSON.stringify(newSettings));
    } catch (error) {
      console.warn('Failed to save motor accessibility settings:', error);
    }
  }, []);

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<MotorAccessibilitySettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      saveSettings(updated);
      return updated;
    });
  }, [saveSettings]);

  // Set difficulty level
  const setDifficultyLevel = useCallback((level: MotorDifficultyLevel) => {
    const newSettings: Partial<MotorAccessibilitySettings> = {
      difficultyLevel: level,
      touchTargetSize: getRecommendedTouchTargetSize(level),
      touchTargetSpacing: getRecommendedSpacing(level),
    };

    // Adjust timing based on difficulty level
    switch (level) {
      case 'mild':
        newSettings.gestureTimeout = 4000;
        newSettings.longPressDelay = 600;
        newSettings.doubleTapDelay = 400;
        break;
      case 'moderate':
        newSettings.gestureTimeout = 5000;
        newSettings.longPressDelay = 700;
        newSettings.doubleTapDelay = 500;
        break;
      case 'severe':
        newSettings.gestureTimeout = 6000;
        newSettings.longPressDelay = 800;
        newSettings.doubleTapDelay = 600;
        break;
      default:
        newSettings.gestureTimeout = 3000;
        newSettings.longPressDelay = 500;
        newSettings.doubleTapDelay = 300;
        break;
    }

    updateSettings(newSettings);

    // Announce change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        `Motor accessibility level set to ${level === 'none' ? 'default' : level}`
      );
    }
  }, [updateSettings]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    saveSettings(DEFAULT_SETTINGS);
  }, [saveSettings]);

  // Get touch target styles
  const getTouchTargetStyles = useCallback((contentSize?: { width?: number; height?: number }) => {
    return generateTouchTargetStyles(contentSize, settings.difficultyLevel);
  }, [settings.difficultyLevel]);

  // Trigger haptic feedback
  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!settings.hapticFeedbackEnabled || !capabilities.supportsHapticFeedback) {
      return;
    }

    try {
      if (Platform.OS === 'ios') {
        switch (type) {
          case 'light':
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            break;
        }
      } else if (Platform.OS === 'android') {
        // Android haptic feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.warn('Failed to trigger haptic feedback:', error);
    }
  }, [settings.hapticFeedbackEnabled, capabilities.supportsHapticFeedback]);

  // Check if gesture is enabled
  const isGestureEnabled = useCallback((gestureType: string) => {
    // Disable complex gestures for severe motor difficulties
    if (settings.difficultyLevel === 'severe') {
      const disabledGestures = ['pinch', 'rotation', 'multi-touch'];
      return !disabledGestures.includes(gestureType);
    }
    
    return true;
  }, [settings.difficultyLevel]);

  // Get gesture timeout
  const getGestureTimeout = useCallback(() => {
    return settings.gestureTimeout;
  }, [settings.gestureTimeout]);

  // Listen for accessibility changes
  useEffect(() => {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      const subscription = AccessibilityInfo.addEventListener(
        'screenReaderChanged',
        (isEnabled) => {
          if (isEnabled && settings.difficultyLevel === 'none') {
            setDifficultyLevel('mild');
          }
        }
      );

      return () => subscription?.remove();
    }
  }, [settings.difficultyLevel, setDifficultyLevel]);

  // Context value
  const contextValue: MotorAccessibilityContextType = {
    // Settings
    settings,
    
    // Actions
    updateSettings,
    setDifficultyLevel,
    resetToDefaults,
    
    // Utilities
    getTouchTargetStyles,
    triggerHapticFeedback,
    isGestureEnabled,
    getGestureTimeout,
    
    // Capabilities
    capabilities,
  };

  return (
    <MotorAccessibilityContext.Provider value={contextValue}>
      {children}
    </MotorAccessibilityContext.Provider>
  );
};

// Hook to use motor accessibility context
export const useMotorAccessibility = (): MotorAccessibilityContextType => {
  const context = useContext(MotorAccessibilityContext);
  
  if (context === undefined) {
    throw new Error('useMotorAccessibility must be used within a MotorAccessibilityProvider');
  }
  
  return context;
};

// Convenience hooks
export const useTouchTargetStyles = (contentSize?: { width?: number; height?: number }) => {
  const { getTouchTargetStyles } = useMotorAccessibility();
  return getTouchTargetStyles(contentSize);
};

export const useHapticFeedback = () => {
  const { triggerHapticFeedback } = useMotorAccessibility();
  return triggerHapticFeedback;
};

export const useGestureSettings = () => {
  const { settings, isGestureEnabled, getGestureTimeout } = useMotorAccessibility();
  
  return {
    longPressDelay: settings.longPressDelay,
    doubleTapDelay: settings.doubleTapDelay,
    swipeThreshold: settings.swipeThreshold,
    isGestureEnabled,
    getGestureTimeout,
  };
};

export default MotorAccessibilityProvider;
