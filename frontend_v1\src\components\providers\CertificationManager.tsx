/**
 * Certification Manager Component
 * Allows providers to manage their certifications and professional credentials
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { Certification } from './EnhancedProviderProfile';

interface CertificationManagerProps {
  certifications: Certification[];
  onAddCertification: (cert: Omit<Certification, 'id'>) => Promise<void>;
  onUpdateCertification: (id: string, cert: Partial<Certification>) => Promise<void>;
  onDeleteCertification: (id: string) => Promise<void>;
  onUploadBadge: (image: string) => Promise<string>;
}

const CertificationManager: React.FC<CertificationManagerProps> = ({
  certifications,
  onAddCertification,
  onUpdateCertification,
  onDeleteCertification,
  onUploadBadge,
}) => {
  const { colors } = useTheme();
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [editingCert, setEditingCert] = useState<Certification | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    issuer: '',
    dateObtained: '',
    expiryDate: '',
    credentialId: '',
    verificationUrl: '',
    badgeImage: '',
    level: 'beginner' as 'beginner' | 'intermediate' | 'advanced' | 'expert',
  });

  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      issuer: '',
      dateObtained: '',
      expiryDate: '',
      credentialId: '',
      verificationUrl: '',
      badgeImage: '',
      level: 'beginner',
    });
  }, []);

  const handleAddCertification = useCallback(async () => {
    if (!formData.name.trim() || !formData.issuer.trim() || !formData.dateObtained) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const newCert: Omit<Certification, 'id'> = {
        name: formData.name.trim(),
        issuer: formData.issuer.trim(),
        dateObtained: formData.dateObtained,
        expiryDate: formData.expiryDate || undefined,
        credentialId: formData.credentialId.trim() || undefined,
        verificationUrl: formData.verificationUrl.trim() || undefined,
        badgeImage: formData.badgeImage || undefined,
        level: formData.level,
      };

      await onAddCertification(newCert);
      resetForm();
      setIsAddModalVisible(false);
      Alert.alert('Success', 'Certification added successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to add certification');
    }
  }, [formData, onAddCertification, resetForm]);

  const handleUpdateCertification = useCallback(async () => {
    if (!editingCert) return;

    try {
      await onUpdateCertification(editingCert.id, {
        name: formData.name.trim(),
        issuer: formData.issuer.trim(),
        dateObtained: formData.dateObtained,
        expiryDate: formData.expiryDate || undefined,
        credentialId: formData.credentialId.trim() || undefined,
        verificationUrl: formData.verificationUrl.trim() || undefined,
        badgeImage: formData.badgeImage || undefined,
        level: formData.level,
      });

      setEditingCert(null);
      resetForm();
      setIsAddModalVisible(false);
      Alert.alert('Success', 'Certification updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update certification');
    }
  }, [editingCert, formData, onUpdateCertification, resetForm]);

  const handleDeleteCertification = useCallback(async (id: string) => {
    Alert.alert(
      'Delete Certification',
      'Are you sure you want to delete this certification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await onDeleteCertification(id);
              Alert.alert('Success', 'Certification deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete certification');
            }
          },
        },
      ]
    );
  }, [onDeleteCertification]);

  const handleEditCertification = useCallback((cert: Certification) => {
    setEditingCert(cert);
    setFormData({
      name: cert.name,
      issuer: cert.issuer,
      dateObtained: cert.dateObtained,
      expiryDate: cert.expiryDate || '',
      credentialId: cert.credentialId || '',
      verificationUrl: cert.verificationUrl || '',
      badgeImage: cert.badgeImage || '',
      level: cert.level,
    });
    setIsAddModalVisible(true);
  }, []);

  const handleBadgeUpload = useCallback(async () => {
    try {
      const mockImage = 'https://via.placeholder.com/100x100';
      const uploadedUrl = await onUploadBadge(mockImage);
      setFormData(prev => ({ ...prev, badgeImage: uploadedUrl }));
    } catch (error) {
      Alert.alert('Error', 'Failed to upload badge image');
    }
  }, [onUploadBadge]);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return Colors.info?.main;
      case 'intermediate': return Colors.warning?.main;
      case 'advanced': return Colors.success?.main;
      case 'expert': return Colors.primary?.main;
      default: return Colors.gray?.main;
    }
  };

  const isExpired = (expiryDate?: string) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  const renderCertificationItem = (cert: Certification) => (
    <View
      key={cert.id}
      style={[
        styles.certificationItem,
        {
          backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
          borderLeftColor: getLevelColor(cert.level),
        },
      ]}
    >
      <View style={styles.certificationHeader}>
        {cert.badgeImage && (
          <Image source={{ uri: cert.badgeImage }} style={styles.badgeImage} />
        )}
        
        <View style={styles.certificationInfo}>
          <Text style={[styles.certificationName, { color: colors?.text?.primary || Colors.text?.primary }]}>
            {cert.name}
          </Text>
          <Text style={[styles.certificationIssuer, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
            {cert.issuer}
          </Text>
          <Text style={[styles.certificationDate, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
            Obtained: {new Date(cert.dateObtained).toLocaleDateString()}
          </Text>
          {cert.expiryDate && (
            <Text
              style={[
                styles.expiryDate,
                {
                  color: isExpired(cert.expiryDate)
                    ? Colors.error?.main
                    : colors?.text?.tertiary || Colors.text?.tertiary,
                },
              ]}
            >
              {isExpired(cert.expiryDate) ? 'Expired: ' : 'Expires: '}
              {new Date(cert.expiryDate).toLocaleDateString()}
            </Text>
          )}
        </View>
        
        <View style={styles.certificationActions}>
          <View style={[styles.levelBadge, { backgroundColor: getLevelColor(cert.level) }]}>
            <Text style={styles.levelText}>{cert.level.toUpperCase()}</Text>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors?.primary?.light || Colors.primary?.light }]}
              onPress={() => handleEditCertification(cert)}
            >
              <Ionicons name="pencil" size={16} color={colors?.primary?.dark || Colors.primary?.dark} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: Colors.error?.light }]}
              onPress={() => handleDeleteCertification(cert.id)}
            >
              <Ionicons name="trash" size={16} color={Colors.error?.dark} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {cert.credentialId && (
        <View style={styles.credentialInfo}>
          <Text style={[styles.credentialLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
            Credential ID: {cert.credentialId}
          </Text>
        </View>
      )}
      
      {cert.verificationUrl && (
        <TouchableOpacity style={styles.verificationButton}>
          <Ionicons name="link" size={14} color={colors?.primary?.main || Colors.primary?.main} />
          <Text style={[styles.verificationText, { color: colors?.primary?.main || Colors.primary?.main }]}>
            Verify Credential
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderAddModal = () => (
    <Modal
      visible={isAddModalVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => {
        setIsAddModalVisible(false);
        setEditingCert(null);
        resetForm();
      }}
    >
      <View style={[styles.modalContainer, { backgroundColor: colors?.background?.primary || Colors.background?.primary }]}>
        <View style={[styles.modalHeader, { borderBottomColor: colors?.border?.primary || Colors.border?.primary }]}>
          <Text style={[styles.modalTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
            {editingCert ? 'Edit Certification' : 'Add Certification'}
          </Text>
          <TouchableOpacity
            onPress={() => {
              setIsAddModalVisible(false);
              setEditingCert(null);
              resetForm();
            }}
          >
            <Ionicons name="close" size={24} color={colors?.text?.secondary || Colors.text?.secondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Certification Name *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="e.g., Certified Beauty Specialist"
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Issuing Organization *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.issuer}
              onChangeText={(text) => setFormData(prev => ({ ...prev, issuer: text }))}
              placeholder="e.g., International Beauty Association"
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Level *
            </Text>
            <View style={styles.levelContainer}>
              {['beginner', 'intermediate', 'advanced', 'expert'].map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.levelOption,
                    formData.level === level && {
                      backgroundColor: getLevelColor(level),
                    },
                    {
                      borderColor: colors?.border?.primary || Colors.border?.primary,
                    },
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, level: level as any }))}
                >
                  <Text
                    style={[
                      styles.levelOptionText,
                      {
                        color: formData.level === level
                          ? Colors.white
                          : colors?.text?.primary || Colors.text?.primary,
                      },
                    ]}
                  >
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Date Obtained *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.dateObtained}
              onChangeText={(text) => setFormData(prev => ({ ...prev, dateObtained: text }))}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Expiry Date (Optional)
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.expiryDate}
              onChangeText={(text) => setFormData(prev => ({ ...prev, expiryDate: text }))}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Credential ID (Optional)
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.credentialId}
              onChangeText={(text) => setFormData(prev => ({ ...prev, credentialId: text }))}
              placeholder="Unique credential identifier"
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Verification URL (Optional)
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors?.background?.secondary || Colors.background?.secondary,
                  borderColor: colors?.border?.primary || Colors.border?.primary,
                  color: colors?.text?.primary || Colors.text?.primary,
                },
              ]}
              value={formData.verificationUrl}
              onChangeText={(text) => setFormData(prev => ({ ...prev, verificationUrl: text }))}
              placeholder="https://..."
              placeholderTextColor={colors?.text?.tertiary || Colors.text?.tertiary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Badge Image (Optional)
            </Text>
            <TouchableOpacity
              style={[
                styles.uploadButton,
                {
                  backgroundColor: colors?.primary?.light || Colors.primary?.light,
                  borderColor: colors?.primary?.main || Colors.primary?.main,
                },
              ]}
              onPress={handleBadgeUpload}
            >
              <Ionicons name="image" size={20} color={colors?.primary?.dark || Colors.primary?.dark} />
              <Text style={[styles.uploadButtonText, { color: colors?.primary?.dark || Colors.primary?.dark }]}>
                Upload Badge
              </Text>
            </TouchableOpacity>

            {formData.badgeImage && (
              <View style={styles.badgePreview}>
                <Image source={{ uri: formData.badgeImage }} style={styles.previewBadge} />
                <TouchableOpacity
                  style={styles.removeBadgeButton}
                  onPress={() => setFormData(prev => ({ ...prev, badgeImage: '' }))}
                >
                  <Ionicons name="close-circle" size={20} color={Colors.error?.main} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ScrollView>

        <View style={[styles.modalFooter, { borderTopColor: colors?.border?.primary || Colors.border?.primary }]}>
          <TouchableOpacity
            style={[
              styles.saveButton,
              { backgroundColor: colors?.primary?.main || Colors.primary?.main },
            ]}
            onPress={editingCert ? handleUpdateCertification : handleAddCertification}
          >
            <Text style={styles.saveButtonText}>
              {editingCert ? 'Update Certification' : 'Add Certification'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors?.text?.primary || Colors.text?.primary }]}>
          Certifications & Credentials
        </Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors?.primary?.main || Colors.primary?.main }]}
          onPress={() => setIsAddModalVisible(true)}
        >
          <Ionicons name="add" size={20} color={Colors.white} />
          <Text style={styles.addButtonText}>Add Certification</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {certifications.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons
              name="school-outline"
              size={48}
              color={colors?.text?.tertiary || Colors.text?.tertiary}
            />
            <Text style={[styles.emptyText, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
              No certifications yet
            </Text>
            <Text style={[styles.emptySubtext, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
              Add your professional certifications to build trust with clients
            </Text>
          </View>
        ) : (
          certifications.map(renderCertificationItem)
        )}
      </ScrollView>

      {renderAddModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
  },
  title: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    flex: 1,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(2),
    gap: getResponsiveSpacing(1),
  },
  addButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: getResponsiveSpacing(4),
  },
  certificationItem: {
    padding: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(3),
    borderLeftWidth: 4,
  },
  certificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  badgeImage: {
    width: 60,
    height: 60,
    borderRadius: getResponsiveSpacing(2),
    marginRight: getResponsiveSpacing(3),
  },
  certificationInfo: {
    flex: 1,
  },
  certificationName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(0.5),
  },
  certificationIssuer: {
    fontSize: getResponsiveFontSize(14),
    marginBottom: getResponsiveSpacing(0.5),
  },
  certificationDate: {
    fontSize: getResponsiveFontSize(12),
    marginBottom: getResponsiveSpacing(0.5),
  },
  expiryDate: {
    fontSize: getResponsiveFontSize(12),
  },
  certificationActions: {
    alignItems: 'flex-end',
  },
  levelBadge: {
    paddingHorizontal: getResponsiveSpacing(2),
    paddingVertical: getResponsiveSpacing(0.5),
    borderRadius: getResponsiveSpacing(1),
    marginBottom: getResponsiveSpacing(2),
  },
  levelText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '700',
    color: Colors.white,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(2),
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  credentialInfo: {
    marginTop: getResponsiveSpacing(2),
    paddingTop: getResponsiveSpacing(2),
    borderTopWidth: 1,
    borderTopColor: Colors.gray?.light,
  },
  credentialLabel: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
  },
  verificationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getResponsiveSpacing(2),
    gap: getResponsiveSpacing(1),
  },
  verificationText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(8),
  },
  emptyText: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    marginTop: getResponsiveSpacing(3),
  },
  emptySubtext: {
    fontSize: getResponsiveFontSize(14),
    textAlign: 'center',
    marginTop: getResponsiveSpacing(1),
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: getResponsiveSpacing(4),
  },
  formGroup: {
    marginBottom: getResponsiveSpacing(4),
  },
  label: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(2),
  },
  input: {
    borderWidth: 1,
    borderRadius: getResponsiveSpacing(2),
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(3),
    fontSize: getResponsiveFontSize(16),
  },
  levelContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing(2),
  },
  levelOption: {
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(4),
    borderWidth: 1,
  },
  levelOptionText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(2),
    borderWidth: 1,
    borderStyle: 'dashed',
    gap: getResponsiveSpacing(2),
  },
  uploadButtonText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
  },
  badgePreview: {
    position: 'relative',
    alignSelf: 'flex-start',
    marginTop: getResponsiveSpacing(3),
  },
  previewBadge: {
    width: 80,
    height: 80,
    borderRadius: getResponsiveSpacing(2),
  },
  removeBadgeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.white,
    borderRadius: 10,
  },
  modalFooter: {
    padding: getResponsiveSpacing(4),
    borderTopWidth: 1,
  },
  saveButton: {
    paddingVertical: getResponsiveSpacing(3),
    borderRadius: getResponsiveSpacing(2),
    alignItems: 'center',
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
  },
});

export default CertificationManager;
