/**
 * Advanced Analytics Dashboard Component
 * Comprehensive analytics dashboard with performance metrics, business intelligence, and optimization insights
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import {
  advancedAnalyticsService,
  PerformanceMetrics,
  BusinessIntelligence,
  PerformanceAlert,
  OptimizationRecommendation,
} from '../../services/advancedAnalyticsService';

interface AdvancedAnalyticsDashboardProps {
  onExportData?: (dataType: string) => void;
  onViewReport?: (reportId: string) => void;
  onImplementRecommendation?: (recommendation: OptimizationRecommendation) => void;
}

const AdvancedAnalyticsDashboard: React.FC<AdvancedAnalyticsDashboardProps> = ({
  onExportData,
  onViewReport,
  onImplementRecommendation,
}) => {
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'business' | 'optimization'>('overview');
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('7d');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [dashboardData, setDashboardData] = useState<{
    realTime: any;
    performance: PerformanceMetrics[];
    business: BusinessIntelligence | null;
    alerts: PerformanceAlert[];
    recommendations: OptimizationRecommendation[];
  }>({
    realTime: null,
    performance: [],
    business: null,
    alerts: [],
    recommendations: [],
  });

  const loadDashboardData = useCallback(async () => {
    setIsLoading(true);
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '24h':
          startDate.setDate(endDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
      }

      const timeRangeObj = {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      };

      const [realTime, performance, business, alerts, recommendations] = await Promise.all([
        advancedAnalyticsService.getRealTimeDashboard(),
        advancedAnalyticsService.getPerformanceMetrics(timeRangeObj),
        advancedAnalyticsService.getBusinessIntelligence(timeRangeObj),
        advancedAnalyticsService.getPerformanceAlerts(),
        advancedAnalyticsService.getOptimizationRecommendations(),
      ]);

      setDashboardData({
        realTime,
        performance: performance.metrics,
        business,
        alerts,
        recommendations,
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [timeRange]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  }, [loadDashboardData]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const renderTimeRangeSelector = () => (
    <View style={styles.timeRangeContainer}>
      {(['24h', '7d', '30d', '90d'] as const).map((range) => (
        <TouchableOpacity
          key={range}
          style={[
            styles.timeRangeButton,
            timeRange === range && {
              backgroundColor: colors?.primary?.main || Colors.primary?.main,
            },
            {
              borderColor: colors?.border?.primary || Colors.border?.primary,
            },
          ]}
          onPress={() => setTimeRange(range)}
        >
          <Text
            style={[
              styles.timeRangeText,
              {
                color: timeRange === range
                  ? Colors.white
                  : colors?.text?.primary || Colors.text?.primary,
              },
            ]}
          >
            {range.toUpperCase()}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverview = () => (
    <View style={styles.tabContent}>
      {/* Real-time Metrics */}
      {dashboardData.realTime && (
        <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
            Live Metrics
          </Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricItem}>
              <Text style={[styles.metricValue, { color: colors?.primary?.main || Colors.primary?.main }]}>
                {dashboardData.realTime.liveMetrics.activeUsers}
              </Text>
              <Text style={[styles.metricLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                Active Users
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={[styles.metricValue, { color: Colors.success?.main }]}>
                {dashboardData.realTime.liveMetrics.currentBookings}
              </Text>
              <Text style={[styles.metricLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                Live Bookings
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={[styles.metricValue, { color: Colors.warning?.main }]}>
                {Math.round(dashboardData.realTime.liveMetrics.systemLoad * 100)}%
              </Text>
              <Text style={[styles.metricLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                System Load
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={[styles.metricValue, { color: Colors.error?.main }]}>
                {(dashboardData.realTime.liveMetrics.errorRate * 100).toFixed(2)}%
              </Text>
              <Text style={[styles.metricLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                Error Rate
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Active Alerts */}
      {dashboardData.alerts.length > 0 && (
        <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
            Active Alerts ({dashboardData.alerts.length})
          </Text>
          {dashboardData.alerts.slice(0, 3).map((alert) => (
            <View key={alert.id} style={styles.alertItem}>
              <View style={styles.alertHeader}>
                <Ionicons
                  name={getAlertIcon(alert.severity)}
                  size={16}
                  color={getAlertColor(alert.severity)}
                />
                <Text style={[styles.alertTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
                  {alert.metric}
                </Text>
                <View style={[styles.severityBadge, { backgroundColor: getAlertColor(alert.severity) }]}>
                  <Text style={styles.severityText}>{alert.severity.toUpperCase()}</Text>
                </View>
              </View>
              <Text style={[styles.alertMessage, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                {alert.message}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Top Recommendations */}
      {dashboardData.recommendations.length > 0 && (
        <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
            Top Optimization Opportunities
          </Text>
          {dashboardData.recommendations.slice(0, 3).map((rec) => (
            <TouchableOpacity
              key={rec.id}
              style={styles.recommendationItem}
              onPress={() => onImplementRecommendation?.(rec)}
            >
              <View style={styles.recommendationHeader}>
                <Text style={[styles.recommendationTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
                  {rec.title}
                </Text>
                <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(rec.priority) }]}>
                  <Text style={styles.priorityText}>{rec.priority.toUpperCase()}</Text>
                </View>
              </View>
              <Text style={[styles.recommendationDescription, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                {rec.description}
              </Text>
              <View style={styles.impactMetrics}>
                <Text style={[styles.impactText, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
                  Performance: +{rec.impact.performance}% | UX: +{rec.impact.userExperience}% | Business: +{rec.impact.business}%
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );

  const renderPerformance = () => (
    <View style={styles.tabContent}>
      {dashboardData.performance.length > 0 && (
        <>
          {/* Core Web Vitals */}
          <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
            <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Core Web Vitals
            </Text>
            <View style={styles.webVitalsGrid}>
              {Object.entries(dashboardData.performance[0].coreWebVitals).map(([key, value]) => (
                <View key={key} style={styles.webVitalItem}>
                  <Text style={[styles.webVitalValue, { color: getWebVitalColor(key, value) }]}>
                    {formatWebVitalValue(key, value)}
                  </Text>
                  <Text style={[styles.webVitalLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                    {key}
                  </Text>
                  <Text style={[styles.webVitalStatus, { color: getWebVitalColor(key, value) }]}>
                    {getWebVitalStatus(key, value)}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          {/* User Experience Metrics */}
          <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
            <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              User Experience
            </Text>
            <View style={styles.uxMetricsGrid}>
              {Object.entries(dashboardData.performance[0].userExperience).map(([key, value]) => (
                <View key={key} style={styles.uxMetricItem}>
                  <Text style={[styles.uxMetricValue, { color: colors?.primary?.main || Colors.primary?.main }]}>
                    {formatUXMetricValue(key, value)}
                  </Text>
                  <Text style={[styles.uxMetricLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                    {formatMetricLabel(key)}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </>
      )}
    </View>
  );

  const renderBusiness = () => (
    <View style={styles.tabContent}>
      {dashboardData.business && (
        <>
          {/* Revenue Analytics */}
          <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
            <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Revenue Analytics
            </Text>
            <View style={styles.revenueGrid}>
              <View style={styles.revenueItem}>
                <Text style={[styles.revenueValue, { color: Colors.success?.main }]}>
                  ${dashboardData.business.revenueAnalytics.totalRevenue.toLocaleString()}
                </Text>
                <Text style={[styles.revenueLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Total Revenue
                </Text>
              </View>
              <View style={styles.revenueItem}>
                <Text style={[styles.revenueValue, { color: colors?.primary?.main || Colors.primary?.main }]}>
                  {dashboardData.business.revenueAnalytics.revenueGrowth > 0 ? '+' : ''}
                  {(dashboardData.business.revenueAnalytics.revenueGrowth * 100).toFixed(1)}%
                </Text>
                <Text style={[styles.revenueLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Growth Rate
                </Text>
              </View>
              <View style={styles.revenueItem}>
                <Text style={[styles.revenueValue, { color: Colors.warning?.main }]}>
                  ${dashboardData.business.revenueAnalytics.averageOrderValue.toFixed(2)}
                </Text>
                <Text style={[styles.revenueLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Avg Order Value
                </Text>
              </View>
            </View>
          </View>

          {/* Customer Analytics */}
          <View style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}>
            <Text style={[styles.cardTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Customer Analytics
            </Text>
            <View style={styles.customerGrid}>
              <View style={styles.customerItem}>
                <Text style={[styles.customerValue, { color: colors?.primary?.main || Colors.primary?.main }]}>
                  {dashboardData.business.customerAnalytics.totalCustomers.toLocaleString()}
                </Text>
                <Text style={[styles.customerLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Total Customers
                </Text>
              </View>
              <View style={styles.customerItem}>
                <Text style={[styles.customerValue, { color: Colors.success?.main }]}>
                  {dashboardData.business.customerAnalytics.newCustomers}
                </Text>
                <Text style={[styles.customerLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  New This Period
                </Text>
              </View>
              <View style={styles.customerItem}>
                <Text style={[styles.customerValue, { color: Colors.error?.main }]}>
                  {(dashboardData.business.customerAnalytics.churnRate * 100).toFixed(1)}%
                </Text>
                <Text style={[styles.customerLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Churn Rate
                </Text>
              </View>
            </View>
          </View>
        </>
      )}
    </View>
  );

  const renderOptimization = () => (
    <View style={styles.tabContent}>
      {dashboardData.recommendations.map((rec) => (
        <View
          key={rec.id}
          style={[styles.card, { backgroundColor: colors?.background?.secondary || Colors.background?.secondary }]}
        >
          <View style={styles.optimizationHeader}>
            <Text style={[styles.optimizationTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              {rec.title}
            </Text>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(rec.priority) }]}>
              <Text style={styles.priorityText}>{rec.priority.toUpperCase()}</Text>
            </View>
          </View>
          
          <Text style={[styles.optimizationDescription, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
            {rec.description}
          </Text>
          
          <View style={styles.impactSection}>
            <Text style={[styles.impactTitle, { color: colors?.text?.primary || Colors.text?.primary }]}>
              Expected Impact:
            </Text>
            <View style={styles.impactBars}>
              <View style={styles.impactBar}>
                <Text style={[styles.impactBarLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Performance
                </Text>
                <View style={[styles.impactBarTrack, { backgroundColor: colors?.background?.tertiary || Colors.background?.tertiary }]}>
                  <View
                    style={[
                      styles.impactBarFill,
                      {
                        width: `${rec.impact.performance}%`,
                        backgroundColor: Colors.success?.main,
                      },
                    ]}
                  />
                </View>
                <Text style={[styles.impactBarValue, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
                  +{rec.impact.performance}%
                </Text>
              </View>
              
              <View style={styles.impactBar}>
                <Text style={[styles.impactBarLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  User Experience
                </Text>
                <View style={[styles.impactBarTrack, { backgroundColor: colors?.background?.tertiary || Colors.background?.tertiary }]}>
                  <View
                    style={[
                      styles.impactBarFill,
                      {
                        width: `${rec.impact.userExperience}%`,
                        backgroundColor: Colors.primary?.main,
                      },
                    ]}
                  />
                </View>
                <Text style={[styles.impactBarValue, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
                  +{rec.impact.userExperience}%
                </Text>
              </View>
              
              <View style={styles.impactBar}>
                <Text style={[styles.impactBarLabel, { color: colors?.text?.secondary || Colors.text?.secondary }]}>
                  Business
                </Text>
                <View style={[styles.impactBarTrack, { backgroundColor: colors?.background?.tertiary || Colors.background?.tertiary }]}>
                  <View
                    style={[
                      styles.impactBarFill,
                      {
                        width: `${rec.impact.business}%`,
                        backgroundColor: Colors.warning?.main,
                      },
                    ]}
                  />
                </View>
                <Text style={[styles.impactBarValue, { color: colors?.text?.tertiary || Colors.text?.tertiary }]}>
                  +{rec.impact.business}%
                </Text>
              </View>
            </View>
          </View>
          
          <TouchableOpacity
            style={[styles.implementButton, { backgroundColor: colors?.primary?.main || Colors.primary?.main }]}
            onPress={() => onImplementRecommendation?.(rec)}
          >
            <Text style={styles.implementButtonText}>View Implementation Plan</Text>
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return 'alert-circle';
      case 'high': return 'warning';
      case 'medium': return 'information-circle';
      default: return 'checkmark-circle';
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': return Colors.error?.main;
      case 'high': return Colors.error?.light;
      case 'medium': return Colors.warning?.main;
      default: return Colors.info?.main;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return Colors.error?.main;
      case 'high': return Colors.warning?.main;
      case 'medium': return Colors.info?.main;
      default: return Colors.gray?.main;
    }
  };

  const getWebVitalColor = (metric: string, value: number) => {
    // Simplified thresholds - in reality these would be more nuanced
    const thresholds: Record<string, { good: number; poor: number }> = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      FCP: { good: 1800, poor: 3000 },
      TTI: { good: 3800, poor: 7300 },
      TTFB: { good: 800, poor: 1800 },
    };

    const threshold = thresholds[metric];
    if (!threshold) return Colors.gray?.main;

    if (value <= threshold.good) return Colors.success?.main;
    if (value <= threshold.poor) return Colors.warning?.main;
    return Colors.error?.main;
  };

  const getWebVitalStatus = (metric: string, value: number) => {
    const color = getWebVitalColor(metric, value);
    if (color === Colors.success?.main) return 'Good';
    if (color === Colors.warning?.main) return 'Needs Improvement';
    return 'Poor';
  };

  const formatWebVitalValue = (metric: string, value: number) => {
    if (metric === 'CLS') return value.toFixed(3);
    return `${Math.round(value)}ms`;
  };

  const formatUXMetricValue = (metric: string, value: number) => {
    switch (metric) {
      case 'errorRate':
      case 'crashRate':
        return `${(value * 100).toFixed(2)}%`;
      case 'animationFrameRate':
        return `${Math.round(value)} fps`;
      default:
        return `${Math.round(value)}ms`;
    }
  };

  const formatMetricLabel = (key: string) => {
    return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  const renderTabs = () => (
    <View style={[styles.tabContainer, { borderBottomColor: colors?.border?.primary || Colors.border?.primary }]}>
      {[
        { key: 'overview', label: 'Overview', icon: 'home' },
        { key: 'performance', label: 'Performance', icon: 'speedometer' },
        { key: 'business', label: 'Business', icon: 'trending-up' },
        { key: 'optimization', label: 'Optimization', icon: 'bulb' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && {
              borderBottomColor: colors?.primary?.main || Colors.primary?.main,
            },
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Ionicons
            name={tab.icon as any}
            size={16}
            color={
              activeTab === tab.key
                ? colors?.primary?.main || Colors.primary?.main
                : colors?.text?.secondary || Colors.text?.secondary
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color: activeTab === tab.key
                  ? colors?.primary?.main || Colors.primary?.main
                  : colors?.text?.secondary || Colors.text?.secondary,
              },
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'performance': return renderPerformance();
      case 'business': return renderBusiness();
      case 'optimization': return renderOptimization();
      default: return renderOverview();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors?.text?.primary || Colors.text?.primary }]}>
          Advanced Analytics
        </Text>
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: colors?.primary?.main || Colors.primary?.main }]}
          onPress={() => onExportData?.('all')}
        >
          <Ionicons name="download" size={16} color={Colors.white} />
          <Text style={styles.exportButtonText}>Export</Text>
        </TouchableOpacity>
      </View>

      {renderTimeRangeSelector()}
      {renderTabs()}
      
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors?.primary?.main || Colors.primary?.main]}
          />
        }
      >
        {renderTabContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(2),
    gap: getResponsiveSpacing(1),
  },
  exportButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingBottom: getResponsiveSpacing(3),
    gap: getResponsiveSpacing(2),
  },
  timeRangeButton: {
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(4),
    borderWidth: 1,
  },
  timeRangeText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(3),
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    gap: getResponsiveSpacing(1),
  },
  tabText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: getResponsiveSpacing(4),
  },
  card: {
    padding: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(4),
  },
  cardTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(3),
  },
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
  },
  metricValue: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
  },
  metricLabel: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(1),
    textAlign: 'center',
  },
  alertItem: {
    marginBottom: getResponsiveSpacing(3),
    paddingBottom: getResponsiveSpacing(3),
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray?.light,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(1),
    gap: getResponsiveSpacing(2),
  },
  alertTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    flex: 1,
  },
  severityBadge: {
    paddingHorizontal: getResponsiveSpacing(2),
    paddingVertical: getResponsiveSpacing(0.5),
    borderRadius: getResponsiveSpacing(1),
  },
  severityText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '700',
    color: Colors.white,
  },
  alertMessage: {
    fontSize: getResponsiveFontSize(12),
  },
  recommendationItem: {
    marginBottom: getResponsiveSpacing(3),
    paddingBottom: getResponsiveSpacing(3),
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray?.light,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(1),
  },
  recommendationTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    flex: 1,
    marginRight: getResponsiveSpacing(2),
  },
  priorityBadge: {
    paddingHorizontal: getResponsiveSpacing(2),
    paddingVertical: getResponsiveSpacing(0.5),
    borderRadius: getResponsiveSpacing(1),
  },
  priorityText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '700',
    color: Colors.white,
  },
  recommendationDescription: {
    fontSize: getResponsiveFontSize(12),
    marginBottom: getResponsiveSpacing(2),
  },
  impactMetrics: {
    marginTop: getResponsiveSpacing(1),
  },
  impactText: {
    fontSize: getResponsiveFontSize(10),
  },
  webVitalsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing(3),
  },
  webVitalItem: {
    alignItems: 'center',
    minWidth: '30%',
  },
  webVitalValue: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
  },
  webVitalLabel: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(0.5),
  },
  webVitalStatus: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '600',
    marginTop: getResponsiveSpacing(0.5),
  },
  uxMetricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing(3),
  },
  uxMetricItem: {
    alignItems: 'center',
    minWidth: '30%',
  },
  uxMetricValue: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
  },
  uxMetricLabel: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(0.5),
    textAlign: 'center',
  },
  revenueGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  revenueItem: {
    alignItems: 'center',
    flex: 1,
  },
  revenueValue: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
  },
  revenueLabel: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(1),
    textAlign: 'center',
  },
  customerGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  customerItem: {
    alignItems: 'center',
    flex: 1,
  },
  customerValue: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
  },
  customerLabel: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(1),
    textAlign: 'center',
  },
  optimizationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(2),
  },
  optimizationTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    flex: 1,
    marginRight: getResponsiveSpacing(2),
  },
  optimizationDescription: {
    fontSize: getResponsiveFontSize(14),
    marginBottom: getResponsiveSpacing(3),
    lineHeight: getResponsiveFontSize(20),
  },
  impactSection: {
    marginBottom: getResponsiveSpacing(3),
  },
  impactTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(2),
  },
  impactBars: {
    gap: getResponsiveSpacing(2),
  },
  impactBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  impactBarLabel: {
    fontSize: getResponsiveFontSize(12),
    width: 80,
  },
  impactBarTrack: {
    flex: 1,
    height: 8,
    borderRadius: 4,
  },
  impactBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  impactBarValue: {
    fontSize: getResponsiveFontSize(12),
    width: 40,
    textAlign: 'right',
  },
  implementButton: {
    paddingVertical: getResponsiveSpacing(3),
    paddingHorizontal: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(2),
    alignItems: 'center',
  },
  implementButtonText: {
    color: Colors.white,
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },
});

export default AdvancedAnalyticsDashboard;
