/**
 * Canadian Payment Form Component
 *
 * Comprehensive payment form component designed for Canadian market
 * with proper tax calculations, payment methods, and compliance.
 *
 * Features:
 * - Canadian payment methods
 * - Provincial tax calculations
 * - Currency formatting
 * - Postal code validation
 * - Bilingual support
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Heading, Body } from '../typography/Typography';
import { AnimatedButton } from '../animation/AnimatedButton';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles } from '../../contexts/MotorAccessibilityContext';
import {
  CANADIAN_PROVINCES,
  CANADIAN_PAYMENT_METHODS,
  CanadianProvince,
  calculateTotalPayment,
  generatePaymentSummary,
  validateCanadianPostalCode,
  formatCanadianPostalCode,
  getProvinceFromPostalCode,
  validateCreditCard,
  formatCreditCardNumber,
  getCreditCardType,
} from '../../utils/canadianPaymentUtils';

// Form data interface
interface PaymentFormData {
  // Billing information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: CanadianProvince;
  postalCode: string;
  
  // Payment information
  paymentMethod: keyof typeof CANADIAN_PAYMENT_METHODS;
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  
  // Preferences
  savePaymentMethod: boolean;
  agreeToTerms: boolean;
}

// Component props
export interface CanadianPaymentFormProps {
  // Payment details
  subtotal: number;
  onPaymentSubmit: (data: PaymentFormData, calculation: any) => void;
  
  // Configuration
  allowedPaymentMethods?: Array<keyof typeof CANADIAN_PAYMENT_METHODS>;
  defaultProvince?: CanadianProvince;
  
  // Styling
  style?: any;
  
  // Testing
  testID?: string;
}

export const CanadianPaymentForm: React.FC<CanadianPaymentFormProps> = ({
  subtotal,
  onPaymentSubmit,
  allowedPaymentMethods = ['CREDIT_CARD', 'DEBIT_CARD', 'PAYPAL'],
  defaultProvince = 'ON',
  style,
  testID,
}) => {
  // Hooks
  const { t, i18n } = useTranslation();
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();
  
  // Form state
  const [formData, setFormData] = useState<PaymentFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    province: defaultProvince,
    postalCode: '',
    paymentMethod: 'CREDIT_CARD',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    savePaymentMethod: false,
    agreeToTerms: false,
  });

  // Validation errors
  const [errors, setErrors] = useState<Partial<Record<keyof PaymentFormData, string>>>({});
  
  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate payment total
  const paymentCalculation = calculateTotalPayment(
    subtotal,
    formData.province,
    formData.paymentMethod
  );

  // Generate payment summary
  const paymentSummary = generatePaymentSummary(
    subtotal,
    formData.province,
    formData.paymentMethod,
    i18n.language as 'en-CA' | 'fr-CA'
  );

  // Update form field
  const updateField = useCallback((field: keyof PaymentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  // Auto-detect province from postal code
  useEffect(() => {
    if (formData.postalCode.length >= 3) {
      const detectedProvince = getProvinceFromPostalCode(formData.postalCode);
      if (detectedProvince && detectedProvince !== formData.province) {
        updateField('province', detectedProvince);
      }
    }
  }, [formData.postalCode, formData.province, updateField]);

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof PaymentFormData, string>> = {};

    // Required fields
    if (!formData.firstName.trim()) {
      newErrors.firstName = t('validation.required');
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('validation.required');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('validation.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.email_invalid');
    }
    if (!formData.address.trim()) {
      newErrors.address = t('validation.required');
    }
    if (!formData.city.trim()) {
      newErrors.city = t('validation.required');
    }
    if (!formData.postalCode.trim()) {
      newErrors.postalCode = t('validation.required');
    } else if (!validateCanadianPostalCode(formData.postalCode)) {
      newErrors.postalCode = t('validation.postal_code_invalid');
    }

    // Payment method specific validation
    if (formData.paymentMethod === 'CREDIT_CARD' || formData.paymentMethod === 'DEBIT_CARD') {
      if (!formData.cardNumber.trim()) {
        newErrors.cardNumber = t('validation.required');
      } else if (!validateCreditCard(formData.cardNumber)) {
        newErrors.cardNumber = t('validation.card_invalid');
      }
      
      if (!formData.expiryDate.trim()) {
        newErrors.expiryDate = t('validation.required');
      } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(formData.expiryDate)) {
        newErrors.expiryDate = t('validation.expiry_invalid');
      }
      
      if (!formData.cvv.trim()) {
        newErrors.cvv = t('validation.required');
      } else if (!/^\d{3,4}$/.test(formData.cvv)) {
        newErrors.cvv = t('validation.cvv_invalid');
      }
      
      if (!formData.cardholderName.trim()) {
        newErrors.cardholderName = t('validation.required');
      }
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = t('validation.terms_required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert(
        t('errors.validation_error'),
        t('forms.please_correct_errors')
      );
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onPaymentSubmit(formData, paymentCalculation);
    } catch (error) {
      Alert.alert(
        t('errors.payment_error'),
        t('errors.payment_processing_failed')
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, paymentCalculation, onPaymentSubmit, t]);

  // Render input field
  const renderInput = (
    field: keyof PaymentFormData,
    label: string,
    options: {
      placeholder?: string;
      keyboardType?: any;
      maxLength?: number;
      secureTextEntry?: boolean;
      autoCapitalize?: any;
      formatter?: (value: string) => string;
    } = {}
  ) => {
    const value = formData[field] as string;
    const error = errors[field];

    return (
      <View style={styles.inputContainer}>
        <Typography
          variant="label"
          color={colors?.text?.primary}
          style={styles.inputLabel}
        >
          {label}
        </Typography>
        
        <TextInput
          style={[
            styles.input,
            {
              borderColor: error ? colors?.status?.error : colors?.border?.primary,
              backgroundColor: colors?.background?.secondary,
              color: colors?.text?.primary,
            },
          ]}
          value={value}
          onChangeText={(text) => {
            const formattedText = options.formatter ? options.formatter(text) : text;
            updateField(field, formattedText);
          }}
          placeholder={options.placeholder}
          keyboardType={options.keyboardType}
          maxLength={options.maxLength}
          secureTextEntry={options.secureTextEntry}
          autoCapitalize={options.autoCapitalize}
          placeholderTextColor={colors?.text?.tertiary}
          accessibilityLabel={label}
          accessibilityHint={error ? `Error: ${error}` : undefined}
        />
        
        {error && (
          <Body
            color={colors?.status?.error}
            style={styles.errorText}
          >
            {error}
          </Body>
        )}
      </View>
    );
  };

  // Render payment method selector
  const renderPaymentMethods = () => (
    <View style={styles.paymentMethodsContainer}>
      <Typography
        variant="label"
        color={colors?.text?.primary}
        style={styles.sectionLabel}
      >
        {t('booking.payment_method')}
      </Typography>
      
      {allowedPaymentMethods.map((method) => {
        const methodInfo = CANADIAN_PAYMENT_METHODS[method];
        const isSelected = formData.paymentMethod === method;
        const methodName = i18n.language === 'fr-CA' ? methodInfo.nameFr : methodInfo.name;
        
        return (
          <TouchableOpacity
            key={method}
            style={[
              styles.paymentMethodOption,
              {
                borderColor: isSelected ? colors?.primary?.default : colors?.border?.primary,
                backgroundColor: isSelected ? colors?.primary?.light : colors?.background?.primary,
              },
              touchTargetStyles,
            ]}
            onPress={() => updateField('paymentMethod', method)}
            accessibilityRole="radio"
            accessibilityState={{ checked: isSelected }}
            accessibilityLabel={methodName}
          >
            <View style={styles.paymentMethodContent}>
              <View style={styles.paymentMethodInfo}>
                <Typography
                  variant="body1"
                  color={isSelected ? colors?.primary?.default : colors?.text?.primary}
                  style={styles.paymentMethodName}
                >
                  {methodName}
                </Typography>
              </View>
              
              {isSelected && (
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={colors?.primary?.default}
                />
              )}
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  // Render payment summary
  const renderPaymentSummary = () => (
    <View style={[styles.summaryContainer, { backgroundColor: colors?.background?.secondary }]}>
      <Heading
        level={3}
        color={colors?.text?.primary}
        style={styles.summaryTitle}
      >
        {t('booking.payment_summary')}
      </Heading>
      
      {paymentSummary.items.map((item, index) => (
        <View key={index} style={styles.summaryRow}>
          <Body
            color={item.type === 'total' ? colors?.text?.primary : colors?.text?.secondary}
            style={[
              styles.summaryLabel,
              item.type === 'total' && styles.summaryTotalLabel,
            ]}
          >
            {item.label}
          </Body>
          
          <Typography
            variant={item.type === 'total' ? 'subtitle1' : 'body1'}
            color={item.type === 'total' ? colors?.text?.primary : colors?.text?.secondary}
            style={[
              styles.summaryAmount,
              item.type === 'total' && styles.summaryTotalAmount,
            ]}
          >
            {item.amount}
          </Typography>
        </View>
      ))}
    </View>
  );

  return (
    <ScrollView
      style={[styles.container, style]}
      contentContainerStyle={styles.content}
      showsVerticalScrollIndicator={false}
      testID={testID}
    >
      {/* Payment Summary */}
      {renderPaymentSummary()}
      
      {/* Billing Information */}
      <View style={styles.section}>
        <Heading
          level={3}
          color={colors?.text?.primary}
          style={styles.sectionTitle}
        >
          {t('forms.billing_information')}
        </Heading>
        
        <View style={styles.row}>
          <View style={styles.halfWidth}>
            {renderInput('firstName', t('forms.first_name'), {
              placeholder: t('forms.first_name'),
              autoCapitalize: 'words',
            })}
          </View>
          
          <View style={styles.halfWidth}>
            {renderInput('lastName', t('forms.last_name'), {
              placeholder: t('forms.last_name'),
              autoCapitalize: 'words',
            })}
          </View>
        </View>
        
        {renderInput('email', t('forms.email'), {
          placeholder: '<EMAIL>',
          keyboardType: 'email-address',
          autoCapitalize: 'none',
        })}
        
        {renderInput('phone', t('forms.phone'), {
          placeholder: '(*************',
          keyboardType: 'phone-pad',
        })}
        
        {renderInput('address', t('forms.address'), {
          placeholder: t('forms.street_address'),
          autoCapitalize: 'words',
        })}
        
        <View style={styles.row}>
          <View style={styles.halfWidth}>
            {renderInput('city', t('forms.city'), {
              placeholder: t('forms.city'),
              autoCapitalize: 'words',
            })}
          </View>
          
          <View style={styles.halfWidth}>
            {renderInput('postalCode', t('forms.postal_code'), {
              placeholder: 'A1A 1A1',
              maxLength: 7,
              autoCapitalize: 'characters',
              formatter: formatCanadianPostalCode,
            })}
          </View>
        </View>
      </View>
      
      {/* Payment Method */}
      <View style={styles.section}>
        {renderPaymentMethods()}
      </View>
      
      {/* Card Information */}
      {(formData.paymentMethod === 'CREDIT_CARD' || formData.paymentMethod === 'DEBIT_CARD') && (
        <View style={styles.section}>
          <Heading
            level={3}
            color={colors?.text?.primary}
            style={styles.sectionTitle}
          >
            {t('forms.card_information')}
          </Heading>
          
          {renderInput('cardholderName', t('forms.cardholder_name'), {
            placeholder: t('forms.name_on_card'),
            autoCapitalize: 'words',
          })}
          
          {renderInput('cardNumber', t('forms.card_number'), {
            placeholder: '1234 5678 9012 3456',
            keyboardType: 'numeric',
            maxLength: 19,
            formatter: formatCreditCardNumber,
          })}
          
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              {renderInput('expiryDate', t('forms.expiry_date'), {
                placeholder: 'MM/YY',
                keyboardType: 'numeric',
                maxLength: 5,
                formatter: (value) => {
                  const cleaned = value.replace(/\D/g, '');
                  if (cleaned.length >= 2) {
                    return `${cleaned.slice(0, 2)}/${cleaned.slice(2, 4)}`;
                  }
                  return cleaned;
                },
              })}
            </View>
            
            <View style={styles.halfWidth}>
              {renderInput('cvv', t('forms.cvv'), {
                placeholder: '123',
                keyboardType: 'numeric',
                maxLength: 4,
                secureTextEntry: true,
              })}
            </View>
          </View>
        </View>
      )}
      
      {/* Submit Button */}
      <View style={styles.submitContainer}>
        <AnimatedButton
          title={`${t('booking.confirm_payment')} ${paymentSummary.total}`}
          onPress={handleSubmit}
          disabled={isSubmitting}
          state={isSubmitting ? 'loading' : 'idle'}
          variant="primary"
          fullWidth
          style={styles.submitButton}
          accessibilityLabel={`Confirm payment of ${paymentSummary.total}`}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  sectionLabel: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  errorText: {
    marginTop: 4,
    fontSize: 12,
  },
  paymentMethodsContainer: {
    marginBottom: 16,
  },
  paymentMethodOption: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontWeight: '600',
  },
  summaryContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  summaryTitle: {
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    flex: 1,
  },
  summaryAmount: {
    fontWeight: '600',
  },
  summaryTotalLabel: {
    fontWeight: '700',
    fontSize: 16,
  },
  summaryTotalAmount: {
    fontWeight: '700',
    fontSize: 18,
  },
  submitContainer: {
    marginTop: 24,
    marginBottom: 32,
  },
  submitButton: {
    paddingVertical: 16,
  },
});

export default CanadianPaymentForm;
