/**
 * Keyboard Navigation Provider
 *
 * Provides keyboard navigation context and utilities throughout the app
 * for enhanced accessibility and power user experience.
 *
 * Features:
 * - Global keyboard shortcuts
 * - Focus management
 * - Navigation between screens
 * - Accessibility announcements
 * - Platform-specific optimizations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useCallback, useRef } from 'react';
import { Platform, Alert, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import useKeyboardNavigation, { KeyboardShortcut } from '../../hooks/useKeyboardNavigation';
import { ScreenReaderUtils } from '../../utils/accessibilityUtils';

// Context interface
interface KeyboardNavigationContextType {
  // Navigation functions
  registerFocusableElement: (element: any) => void;
  unregisterFocusableElement: (id: string) => void;
  moveFocus: (direction: any) => boolean;
  setFocusToElement: (index: number) => boolean;
  activateCurrentElement: () => boolean;
  
  // State
  focusableElements: any[];
  currentFocusIndex: number;
  isKeyboardVisible: boolean;
  
  // Utilities
  announceShortcuts: () => void;
  showShortcutsHelp: () => void;
}

// Create context
const KeyboardNavigationContext = createContext<KeyboardNavigationContextType | undefined>(undefined);

// Provider props
interface KeyboardNavigationProviderProps {
  children: React.ReactNode;
}

export const KeyboardNavigationProvider: React.FC<KeyboardNavigationProviderProps> = ({
  children,
}) => {
  const navigation = useNavigation();
  const shortcutsAnnouncedRef = useRef(false);

  // Define global keyboard shortcuts
  const globalShortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'h',
      altKey: true,
      action: () => {
        navigation.navigate('Home' as never);
        ScreenReaderUtils.announceForAccessibility('Navigated to Home');
      },
      description: 'Navigate to Home',
    },
    {
      key: 's',
      altKey: true,
      action: () => {
        navigation.navigate('Search' as never);
        ScreenReaderUtils.announceForAccessibility('Navigated to Search');
      },
      description: 'Navigate to Search',
    },
    {
      key: 'b',
      altKey: true,
      action: () => {
        navigation.navigate('Bookings' as never);
        ScreenReaderUtils.announceForAccessibility('Navigated to Bookings');
      },
      description: 'Navigate to Bookings',
    },
    {
      key: 'm',
      altKey: true,
      action: () => {
        navigation.navigate('Messages' as never);
        ScreenReaderUtils.announceForAccessibility('Navigated to Messages');
      },
      description: 'Navigate to Messages',
    },
    {
      key: 'p',
      altKey: true,
      action: () => {
        navigation.navigate('Profile' as never);
        ScreenReaderUtils.announceForAccessibility('Navigated to Profile');
      },
      description: 'Navigate to Profile',
    },
    
    // Utility shortcuts
    {
      key: '?',
      shiftKey: true,
      action: () => showShortcutsHelp(),
      description: 'Show keyboard shortcuts help',
    },
    {
      key: 'k',
      ctrlKey: Platform.OS === 'web',
      metaKey: Platform.OS === 'web',
      action: () => {
        // Focus search if available
        ScreenReaderUtils.announceForAccessibility('Search focused');
      },
      description: 'Focus search',
    },
    
    // Accessibility shortcuts
    {
      key: 'a',
      ctrlKey: true,
      shiftKey: true,
      action: () => announceShortcuts(),
      description: 'Announce available shortcuts',
    },
  ];

  // Initialize keyboard navigation
  const keyboardNavigation = useKeyboardNavigation({
    enabled: true,
    autoFocus: false,
    shortcuts: globalShortcuts,
    onEscape: () => {
      if (navigation.canGoBack()) {
        navigation.goBack();
        ScreenReaderUtils.announceForAccessibility('Navigated back');
      }
    },
  });

  // Announce available shortcuts
  const announceShortcuts = useCallback(() => {
    const shortcutDescriptions = globalShortcuts
      .map(shortcut => {
        let keys = [];
        if (shortcut.ctrlKey) keys.push('Control');
        if (shortcut.altKey) keys.push('Alt');
        if (shortcut.shiftKey) keys.push('Shift');
        if (shortcut.metaKey) keys.push('Command');
        keys.push(shortcut.key.toUpperCase());
        
        return `${keys.join(' + ')}: ${shortcut.description}`;
      })
      .join('. ');

    ScreenReaderUtils.announceForAccessibility(
      `Available keyboard shortcuts: ${shortcutDescriptions}`
    );
    
    shortcutsAnnouncedRef.current = true;
  }, [globalShortcuts]);

  // Show shortcuts help dialog
  const showShortcutsHelp = useCallback(() => {
    const shortcutsList = globalShortcuts
      .map(shortcut => {
        let keys = [];
        if (shortcut.ctrlKey) keys.push('Ctrl');
        if (shortcut.altKey) keys.push('Alt');
        if (shortcut.shiftKey) keys.push('Shift');
        if (shortcut.metaKey) keys.push('Cmd');
        keys.push(shortcut.key.toUpperCase());
        
        return `${keys.join(' + ')}: ${shortcut.description}`;
      })
      .join('\n');

    Alert.alert(
      'Keyboard Shortcuts',
      shortcutsList,
      [{ text: 'OK', style: 'default' }],
      { cancelable: true }
    );

    // Also announce for screen readers
    announceShortcuts();
  }, [globalShortcuts, announceShortcuts]);

  // Context value
  const contextValue: KeyboardNavigationContextType = {
    ...keyboardNavigation,
    announceShortcuts,
    showShortcutsHelp,
  };

  return (
    <KeyboardNavigationContext.Provider value={contextValue}>
      <View
        ref={keyboardNavigation.containerRef}
        style={{ flex: 1 }}
        accessible={false}
        importantForAccessibility="no-hide-descendants"
      >
        {children}
      </View>
    </KeyboardNavigationContext.Provider>
  );
};

// Hook to use keyboard navigation
export const useKeyboardNavigationContext = (): KeyboardNavigationContextType => {
  const context = useContext(KeyboardNavigationContext);
  
  if (context === undefined) {
    throw new Error('useKeyboardNavigationContext must be used within a KeyboardNavigationProvider');
  }
  
  return context;
};

// Focusable component wrapper
interface FocusableProps {
  children: React.ReactNode;
  id: string;
  priority?: number;
  group?: string;
  disabled?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onActivate?: () => void;
  style?: any;
}

export const Focusable: React.FC<FocusableProps> = ({
  children,
  id,
  priority = 0,
  group,
  disabled = false,
  onFocus,
  onBlur,
  onActivate,
  style,
}) => {
  const { registerFocusableElement, unregisterFocusableElement } = useKeyboardNavigationContext();
  const elementRef = useRef<any>(null);

  // Register element on mount
  React.useEffect(() => {
    const element = {
      id,
      ref: elementRef,
      priority,
      group,
      disabled,
      onFocus,
      onBlur,
      onActivate,
    };

    registerFocusableElement(element);

    return () => {
      unregisterFocusableElement(id);
    };
  }, [id, priority, group, disabled, onFocus, onBlur, onActivate, registerFocusableElement, unregisterFocusableElement]);

  return (
    <View
      ref={elementRef}
      style={style}
      accessible={!disabled}
      accessibilityRole="button"
      onAccessibilityTap={onActivate}
    >
      {children}
    </View>
  );
};

export default KeyboardNavigationProvider;
