8d31306d704ba688b54e1e6ad5c557f7
'use strict';
'use client';

var warnOnce = require("./Libraries/Utilities/warnOnce").default;
var invariant = require('invariant');
module.exports = {
  get registerCallableModule() {
    return require("./Libraries/Core/registerCallableModule").default;
  },
  get AccessibilityInfo() {
    return require("./Libraries/Components/AccessibilityInfo/AccessibilityInfo").default;
  },
  get ActivityIndicator() {
    return require("./Libraries/Components/ActivityIndicator/ActivityIndicator").default;
  },
  get Button() {
    return require("./Libraries/Components/Button").default;
  },
  get DrawerLayoutAndroid() {
    return require("./Libraries/Components/DrawerAndroid/DrawerLayoutAndroid").default;
  },
  get FlatList() {
    return require("./Libraries/Lists/FlatList").default;
  },
  get Image() {
    return require("./Libraries/Image/Image").default;
  },
  get ImageBackground() {
    return require("./Libraries/Image/ImageBackground").default;
  },
  get InputAccessoryView() {
    return require("./Libraries/Components/TextInput/InputAccessoryView").default;
  },
  get experimental_LayoutConformance() {
    return require("./Libraries/Components/LayoutConformance/LayoutConformance").default;
  },
  get KeyboardAvoidingView() {
    return require("./Libraries/Components/Keyboard/KeyboardAvoidingView").default;
  },
  get Modal() {
    return require("./Libraries/Modal/Modal").default;
  },
  get Pressable() {
    return require("./Libraries/Components/Pressable/Pressable").default;
  },
  get ProgressBarAndroid() {
    warnOnce('progress-bar-android-moved', 'ProgressBarAndroid has been extracted from react-native core and will be removed in a future release. ' + "It can now be installed and imported from '@react-native-community/progress-bar-android' instead of 'react-native'. " + 'See https://github.com/react-native-progress-view/progress-bar-android');
    return require("./Libraries/Components/ProgressBarAndroid/ProgressBarAndroid").default;
  },
  get RefreshControl() {
    return require("./Libraries/Components/RefreshControl/RefreshControl").default;
  },
  get SafeAreaView() {
    return require("./Libraries/Components/SafeAreaView/SafeAreaView").default;
  },
  get ScrollView() {
    return require("./Libraries/Components/ScrollView/ScrollView").default;
  },
  get SectionList() {
    return require("./Libraries/Lists/SectionList").default;
  },
  get StatusBar() {
    return require("./Libraries/Components/StatusBar/StatusBar").default;
  },
  get Switch() {
    return require("./Libraries/Components/Switch/Switch").default;
  },
  get Text() {
    return require("./Libraries/Text/Text").default;
  },
  get TextInput() {
    return require("./Libraries/Components/TextInput/TextInput").default;
  },
  get Touchable() {
    return require("./Libraries/Components/Touchable/Touchable").default;
  },
  get TouchableHighlight() {
    return require("./Libraries/Components/Touchable/TouchableHighlight").default;
  },
  get TouchableNativeFeedback() {
    return require("./Libraries/Components/Touchable/TouchableNativeFeedback").default;
  },
  get TouchableOpacity() {
    return require("./Libraries/Components/Touchable/TouchableOpacity").default;
  },
  get TouchableWithoutFeedback() {
    return require("./Libraries/Components/Touchable/TouchableWithoutFeedback").default;
  },
  get View() {
    return require("./Libraries/Components/View/View").default;
  },
  get VirtualizedList() {
    return require("./Libraries/Lists/VirtualizedList").default;
  },
  get VirtualizedSectionList() {
    return require("./Libraries/Lists/VirtualizedSectionList").default;
  },
  get ActionSheetIOS() {
    return require("./Libraries/ActionSheetIOS/ActionSheetIOS").default;
  },
  get Alert() {
    return require("./Libraries/Alert/Alert").default;
  },
  get Animated() {
    return require("./Libraries/Animated/Animated").default;
  },
  get Appearance() {
    return require("./Libraries/Utilities/Appearance");
  },
  get AppRegistry() {
    return require("./Libraries/ReactNative/AppRegistry").default;
  },
  get AppState() {
    return require("./Libraries/AppState/AppState").default;
  },
  get BackHandler() {
    return require("./Libraries/Utilities/BackHandler").default;
  },
  get Clipboard() {
    warnOnce('clipboard-moved', 'Clipboard has been extracted from react-native core and will be removed in a future release. ' + "It can now be installed and imported from '@react-native-clipboard/clipboard' instead of 'react-native'. " + 'See https://github.com/react-native-clipboard/clipboard');
    return require("./Libraries/Components/Clipboard/Clipboard").default;
  },
  get DeviceInfo() {
    return require("./Libraries/Utilities/DeviceInfo").default;
  },
  get DevMenu() {
    return require("./src/private/devmenu/DevMenu").default;
  },
  get DevSettings() {
    return require("./Libraries/Utilities/DevSettings").default;
  },
  get Dimensions() {
    return require("./Libraries/Utilities/Dimensions").default;
  },
  get Easing() {
    return require("./Libraries/Animated/Easing").default;
  },
  get findNodeHandle() {
    return require("./Libraries/ReactNative/RendererProxy").findNodeHandle;
  },
  get I18nManager() {
    return require("./Libraries/ReactNative/I18nManager").default;
  },
  get InteractionManager() {
    return require("./Libraries/Interaction/InteractionManager").default;
  },
  get Keyboard() {
    return require("./Libraries/Components/Keyboard/Keyboard").default;
  },
  get LayoutAnimation() {
    return require("./Libraries/LayoutAnimation/LayoutAnimation").default;
  },
  get Linking() {
    return require("./Libraries/Linking/Linking").default;
  },
  get LogBox() {
    return require("./Libraries/LogBox/LogBox").default;
  },
  get NativeDialogManagerAndroid() {
    return require("./Libraries/NativeModules/specs/NativeDialogManagerAndroid").default;
  },
  get NativeEventEmitter() {
    return require("./Libraries/EventEmitter/NativeEventEmitter").default;
  },
  get Networking() {
    return require("./Libraries/Network/RCTNetworking").default;
  },
  get PanResponder() {
    return require("./Libraries/Interaction/PanResponder").default;
  },
  get PermissionsAndroid() {
    return require("./Libraries/PermissionsAndroid/PermissionsAndroid").default;
  },
  get PixelRatio() {
    return require("./Libraries/Utilities/PixelRatio").default;
  },
  get PushNotificationIOS() {
    warnOnce('pushNotificationIOS-moved', 'PushNotificationIOS has been extracted from react-native core and will be removed in a future release. ' + "It can now be installed and imported from '@react-native-community/push-notification-ios' instead of 'react-native'. " + 'See https://github.com/react-native-push-notification/ios');
    return require("./Libraries/PushNotificationIOS/PushNotificationIOS").default;
  },
  get Settings() {
    return require("./Libraries/Settings/Settings").default;
  },
  get Share() {
    return require("./Libraries/Share/Share").default;
  },
  get StyleSheet() {
    return require("./Libraries/StyleSheet/StyleSheet").default;
  },
  get Systrace() {
    return require("./Libraries/Performance/Systrace");
  },
  get ToastAndroid() {
    return require("./Libraries/Components/ToastAndroid/ToastAndroid").default;
  },
  get TurboModuleRegistry() {
    return require("./Libraries/TurboModule/TurboModuleRegistry");
  },
  get UIManager() {
    return require("./Libraries/ReactNative/UIManager").default;
  },
  get unstable_batchedUpdates() {
    return require("./Libraries/ReactNative/RendererProxy").unstable_batchedUpdates;
  },
  get useAnimatedValue() {
    return require("./Libraries/Animated/useAnimatedValue").default;
  },
  get useColorScheme() {
    return require("./Libraries/Utilities/useColorScheme").default;
  },
  get useWindowDimensions() {
    return require("./Libraries/Utilities/useWindowDimensions").default;
  },
  get UTFSequence() {
    return require("./Libraries/UTFSequence").default;
  },
  get Vibration() {
    return require("./Libraries/Vibration/Vibration").default;
  },
  get DeviceEventEmitter() {
    return require("./Libraries/EventEmitter/RCTDeviceEventEmitter").default;
  },
  get DynamicColorIOS() {
    return require("./Libraries/StyleSheet/PlatformColorValueTypesIOS").DynamicColorIOS;
  },
  get NativeAppEventEmitter() {
    return require("./Libraries/EventEmitter/RCTNativeAppEventEmitter").default;
  },
  get NativeModules() {
    return require("./Libraries/BatchedBridge/NativeModules").default;
  },
  get Platform() {
    return require("./Libraries/Utilities/Platform").default;
  },
  get PlatformColor() {
    return require("./Libraries/StyleSheet/PlatformColorValueTypes").PlatformColor;
  },
  get processColor() {
    return require("./Libraries/StyleSheet/processColor").default;
  },
  get requireNativeComponent() {
    return require("./Libraries/ReactNative/requireNativeComponent").default;
  },
  get RootTagContext() {
    return require("./Libraries/ReactNative/RootTag").RootTagContext;
  }
};
if (__DEV__) {
  Object.defineProperty(module.exports, 'ART', {
    configurable: true,
    get: function get() {
      invariant(false, 'ART has been removed from React Native. ' + "Please upgrade to use either 'react-native-svg' or a similar package. " + "If you cannot upgrade to a different library, please install the deprecated '@react-native-community/art' package. " + 'See https://github.com/react-native-art/art');
    }
  });
  Object.defineProperty(module.exports, 'ListView', {
    configurable: true,
    get: function get() {
      invariant(false, 'ListView has been removed from React Native. ' + 'See https://fb.me/nolistview for more information or use ' + '`deprecated-react-native-listview`.');
    }
  });
  Object.defineProperty(module.exports, 'SwipeableListView', {
    configurable: true,
    get: function get() {
      invariant(false, 'SwipeableListView has been removed from React Native. ' + 'See https://fb.me/nolistview for more information or use ' + '`deprecated-react-native-swipeable-listview`.');
    }
  });
  Object.defineProperty(module.exports, 'WebView', {
    configurable: true,
    get: function get() {
      invariant(false, 'WebView has been removed from React Native. ' + "It can now be installed and imported from 'react-native-webview' instead of 'react-native'. " + 'See https://github.com/react-native-webview/react-native-webview');
    }
  });
  Object.defineProperty(module.exports, 'NetInfo', {
    configurable: true,
    get: function get() {
      invariant(false, 'NetInfo has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/netinfo' instead of 'react-native'. " + 'See https://github.com/react-native-netinfo/react-native-netinfo');
    }
  });
  Object.defineProperty(module.exports, 'CameraRoll', {
    configurable: true,
    get: function get() {
      invariant(false, 'CameraRoll has been removed from React Native. ' + "It can now be installed and imported from '@react-native-camera-roll/camera-roll' instead of 'react-native'. " + 'See https://github.com/react-native-cameraroll/react-native-cameraroll');
    }
  });
  Object.defineProperty(module.exports, 'ImageStore', {
    configurable: true,
    get: function get() {
      invariant(false, 'ImageStore has been removed from React Native. ' + 'To get a base64-encoded string from a local image use either of the following third-party libraries:' + "* expo-file-system: `readAsStringAsync(filepath, 'base64')`" + "* react-native-fs: `readFile(filepath, 'base64')`");
    }
  });
  Object.defineProperty(module.exports, 'ImageEditor', {
    configurable: true,
    get: function get() {
      invariant(false, 'ImageEditor has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/image-editor' instead of 'react-native'. " + 'See https://github.com/callstack/react-native-image-editor');
    }
  });
  Object.defineProperty(module.exports, 'TimePickerAndroid', {
    configurable: true,
    get: function get() {
      invariant(false, 'TimePickerAndroid has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. " + 'See https://github.com/react-native-datetimepicker/datetimepicker');
    }
  });
  Object.defineProperty(module.exports, 'ToolbarAndroid', {
    configurable: true,
    get: function get() {
      invariant(false, 'ToolbarAndroid has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/toolbar-android' instead of 'react-native'. " + 'See https://github.com/react-native-toolbar-android/toolbar-android');
    }
  });
  Object.defineProperty(module.exports, 'ViewPagerAndroid', {
    configurable: true,
    get: function get() {
      invariant(false, 'ViewPagerAndroid has been removed from React Native. ' + "It can now be installed and imported from 'react-native-pager-view' instead of 'react-native'. " + 'See https://github.com/callstack/react-native-pager-view');
    }
  });
  Object.defineProperty(module.exports, 'CheckBox', {
    configurable: true,
    get: function get() {
      invariant(false, 'CheckBox has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/checkbox' instead of 'react-native'. " + 'See https://github.com/react-native-checkbox/react-native-checkbox');
    }
  });
  Object.defineProperty(module.exports, 'SegmentedControlIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'SegmentedControlIOS has been removed from React Native. ' + "It can now be installed and imported from '@react-native-segmented-control/segmented-control' instead of 'react-native'." + 'See https://github.com/react-native-segmented-control/segmented-control');
    }
  });
  Object.defineProperty(module.exports, 'StatusBarIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'StatusBarIOS has been removed from React Native. ' + 'Has been merged with StatusBar. ' + 'See https://reactnative.dev/docs/statusbar');
    }
  });
  Object.defineProperty(module.exports, 'PickerIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'PickerIOS has been removed from React Native. ' + "It can now be installed and imported from '@react-native-picker/picker' instead of 'react-native'. " + 'See https://github.com/react-native-picker/picker');
    }
  });
  Object.defineProperty(module.exports, 'Picker', {
    configurable: true,
    get: function get() {
      invariant(false, 'Picker has been removed from React Native. ' + "It can now be installed and imported from '@react-native-picker/picker' instead of 'react-native'. " + 'See https://github.com/react-native-picker/picker');
    }
  });
  Object.defineProperty(module.exports, 'DatePickerAndroid', {
    configurable: true,
    get: function get() {
      invariant(false, 'DatePickerAndroid has been removed from React Native. ' + "It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. " + 'See https://github.com/react-native-datetimepicker/datetimepicker');
    }
  });
  Object.defineProperty(module.exports, 'MaskedViewIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'MaskedViewIOS has been removed from React Native. ' + "It can now be installed and imported from '@react-native-masked-view/masked-view' instead of 'react-native'. " + 'See https://github.com/react-native-masked-view/masked-view');
    }
  });
  Object.defineProperty(module.exports, 'AsyncStorage', {
    configurable: true,
    get: function get() {
      invariant(false, 'AsyncStorage has been removed from react-native core. ' + "It can now be installed and imported from '@react-native-async-storage/async-storage' instead of 'react-native'. " + 'See https://github.com/react-native-async-storage/async-storage');
    }
  });
  Object.defineProperty(module.exports, 'ImagePickerIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'ImagePickerIOS has been removed from React Native. ' + "Please upgrade to use either 'react-native-image-picker' or 'expo-image-picker'. " + "If you cannot upgrade to a different library, please install the deprecated '@react-native-community/image-picker-ios' package. " + 'See https://github.com/rnc-archive/react-native-image-picker-ios');
    }
  });
  Object.defineProperty(module.exports, 'ProgressViewIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'ProgressViewIOS has been removed from react-native core. ' + "It can now be installed and imported from '@react-native-community/progress-view' instead of 'react-native'. " + 'See https://github.com/react-native-progress-view/progress-view');
    }
  });
  Object.defineProperty(module.exports, 'DatePickerIOS', {
    configurable: true,
    get: function get() {
      invariant(false, 'DatePickerIOS has been removed from react-native core. ' + "It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. " + 'See https://github.com/react-native-datetimepicker/datetimepicker');
    }
  });
  Object.defineProperty(module.exports, 'Slider', {
    configurable: true,
    get: function get() {
      invariant(false, 'Slider has been removed from react-native core. ' + "It can now be installed and imported from '@react-native-community/slider' instead of 'react-native'. " + 'See https://github.com/callstack/react-native-slider');
    }
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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