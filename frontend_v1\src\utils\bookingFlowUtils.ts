/**
 * Booking Flow Utilities
 *
 * Comprehensive utilities for optimizing booking flows with reduced friction,
 * smart defaults, and conversion optimization techniques.
 *
 * Features:
 * - Multi-step flow management
 * - Progress tracking
 * - Smart form prefilling
 * - Conversion optimization
 * - Error recovery
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Booking step interface
export interface BookingStep {
  id: string;
  title: string;
  description?: string;
  component: string;
  required: boolean;
  completed: boolean;
  skippable: boolean;
  estimatedTime?: number;
  validationRules?: any[];
}

// Booking flow configuration
export interface BookingFlowConfig {
  steps: BookingStep[];
  allowBackNavigation: boolean;
  saveProgress: boolean;
  showProgress: boolean;
  optimizeForConversion: boolean;
  enableSmartDefaults: boolean;
  maxTimePerStep?: number;
}

// Booking data interface
export interface BookingData {
  serviceId: string;
  providerId: string;
  customerId?: string;
  selectedDate?: Date;
  selectedTime?: string;
  duration?: number;
  location?: {
    type: 'provider' | 'customer' | 'custom';
    address?: string;
    coordinates?: { lat: number; lng: number };
  };
  preferences?: Record<string, any>;
  pricing?: {
    basePrice: number;
    taxes: number;
    fees: number;
    discounts: number;
    total: number;
  };
  paymentMethod?: {
    type: 'card' | 'cash' | 'digital';
    details?: any;
  };
  specialRequests?: string;
  contactInfo?: {
    phone: string;
    email: string;
    preferredContact: 'phone' | 'email' | 'app';
  };
}

// Conversion optimization metrics
export interface ConversionMetrics {
  stepCompletionRates: Record<string, number>;
  averageTimePerStep: Record<string, number>;
  dropOffPoints: string[];
  conversionRate: number;
  averageBookingValue: number;
  completionTime: number;
}

/**
 * Calculate booking flow progress
 */
export const calculateProgress = (steps: BookingStep[]): number => {
  const completedSteps = steps.filter(step => step.completed).length;
  return steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;
};

/**
 * Get next incomplete step
 */
export const getNextStep = (steps: BookingStep[]): BookingStep | null => {
  return steps.find(step => !step.completed && step.required) || null;
};

/**
 * Get previous step
 */
export const getPreviousStep = (steps: BookingStep[], currentStepId: string): BookingStep | null => {
  const currentIndex = steps.findIndex(step => step.id === currentStepId);
  return currentIndex > 0 ? steps[currentIndex - 1] : null;
};

/**
 * Validate step completion
 */
export const validateStepCompletion = (
  step: BookingStep,
  data: Partial<BookingData>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  switch (step.id) {
    case 'service-selection':
      if (!data.serviceId) errors.push('Please select a service');
      if (!data.providerId) errors.push('Please select a provider');
      break;
      
    case 'date-time':
      if (!data.selectedDate) errors.push('Please select a date');
      if (!data.selectedTime) errors.push('Please select a time');
      break;
      
    case 'location':
      if (!data.location?.type) errors.push('Please select a location');
      if (data.location?.type === 'custom' && !data.location?.address) {
        errors.push('Please provide an address');
      }
      break;
      
    case 'contact-info':
      if (!data.contactInfo?.phone && !data.contactInfo?.email) {
        errors.push('Please provide contact information');
      }
      break;
      
    case 'payment':
      if (!data.paymentMethod?.type) errors.push('Please select a payment method');
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Generate smart defaults based on user data and preferences
 */
export const generateSmartDefaults = (
  userProfile: any,
  serviceData: any,
  previousBookings: any[] = []
): Partial<BookingData> => {
  const defaults: Partial<BookingData> = {};
  
  // Smart location defaults
  if (previousBookings.length > 0) {
    const mostUsedLocation = getMostFrequentLocation(previousBookings);
    if (mostUsedLocation) {
      defaults.location = mostUsedLocation;
    }
  }
  
  // Smart time defaults based on user patterns
  const preferredTime = getPreferredTimeSlot(previousBookings);
  if (preferredTime) {
    defaults.selectedTime = preferredTime;
  }
  
  // Smart contact preferences
  if (userProfile?.contactPreferences) {
    defaults.contactInfo = {
      phone: userProfile.phone,
      email: userProfile.email,
      preferredContact: userProfile.contactPreferences.preferred,
    };
  }
  
  // Smart payment method
  if (userProfile?.defaultPaymentMethod) {
    defaults.paymentMethod = userProfile.defaultPaymentMethod;
  }
  
  return defaults;
};

/**
 * Get most frequent location from booking history
 */
const getMostFrequentLocation = (bookings: any[]): any => {
  const locationCounts: Record<string, { count: number; location: any }> = {};
  
  bookings.forEach(booking => {
    if (booking.location) {
      const key = `${booking.location.type}-${booking.location.address || 'default'}`;
      if (locationCounts[key]) {
        locationCounts[key].count++;
      } else {
        locationCounts[key] = { count: 1, location: booking.location };
      }
    }
  });
  
  const mostFrequent = Object.values(locationCounts)
    .sort((a, b) => b.count - a.count)[0];
  
  return mostFrequent?.location || null;
};

/**
 * Get preferred time slot from booking history
 */
const getPreferredTimeSlot = (bookings: any[]): string | null => {
  const timeCounts: Record<string, number> = {};
  
  bookings.forEach(booking => {
    if (booking.selectedTime) {
      const hour = booking.selectedTime.split(':')[0];
      timeCounts[hour] = (timeCounts[hour] || 0) + 1;
    }
  });
  
  const preferredHour = Object.entries(timeCounts)
    .sort(([, a], [, b]) => b - a)[0]?.[0];
  
  return preferredHour ? `${preferredHour}:00` : null;
};

/**
 * Optimize booking flow for conversion
 */
export const optimizeForConversion = (
  steps: BookingStep[],
  metrics: ConversionMetrics
): BookingStep[] => {
  const optimizedSteps = [...steps];
  
  // Reorder steps based on completion rates
  const stepsByCompletion = optimizedSteps.sort((a, b) => {
    const aRate = metrics.stepCompletionRates[a.id] || 0;
    const bRate = metrics.stepCompletionRates[b.id] || 0;
    return bRate - aRate;
  });
  
  // Mark low-completion steps as skippable if possible
  stepsByCompletion.forEach(step => {
    const completionRate = metrics.stepCompletionRates[step.id] || 0;
    if (completionRate < 0.7 && !step.required) {
      step.skippable = true;
    }
  });
  
  return stepsByCompletion;
};

/**
 * Calculate estimated completion time
 */
export const calculateEstimatedTime = (steps: BookingStep[]): number => {
  return steps.reduce((total, step) => {
    return total + (step.estimatedTime || 60); // Default 60 seconds per step
  }, 0);
};

/**
 * Generate booking summary
 */
export const generateBookingSummary = (data: BookingData): string[] => {
  const summary: string[] = [];
  
  if (data.selectedDate && data.selectedTime) {
    const date = new Date(data.selectedDate);
    summary.push(`Date: ${date.toLocaleDateString()} at ${data.selectedTime}`);
  }
  
  if (data.duration) {
    summary.push(`Duration: ${data.duration} minutes`);
  }
  
  if (data.location) {
    switch (data.location.type) {
      case 'provider':
        summary.push('Location: Provider\'s location');
        break;
      case 'customer':
        summary.push('Location: Your location');
        break;
      case 'custom':
        summary.push(`Location: ${data.location.address}`);
        break;
    }
  }
  
  if (data.pricing) {
    summary.push(`Total: $${data.pricing.total.toFixed(2)}`);
  }
  
  if (data.specialRequests) {
    summary.push(`Special requests: ${data.specialRequests}`);
  }
  
  return summary;
};

/**
 * Validate booking data completeness
 */
export const validateBookingData = (data: BookingData): {
  isComplete: boolean;
  missingFields: string[];
  warnings: string[];
} => {
  const missingFields: string[] = [];
  const warnings: string[] = [];
  
  // Required fields
  if (!data.serviceId) missingFields.push('Service selection');
  if (!data.providerId) missingFields.push('Provider selection');
  if (!data.selectedDate) missingFields.push('Date selection');
  if (!data.selectedTime) missingFields.push('Time selection');
  if (!data.location?.type) missingFields.push('Location selection');
  
  // Contact info validation
  if (!data.contactInfo?.phone && !data.contactInfo?.email) {
    missingFields.push('Contact information');
  }
  
  // Payment validation
  if (!data.paymentMethod?.type) {
    missingFields.push('Payment method');
  }
  
  // Warnings for optional but recommended fields
  if (!data.specialRequests) {
    warnings.push('Consider adding special requests if needed');
  }
  
  if (data.location?.type === 'custom' && !data.location?.coordinates) {
    warnings.push('Location coordinates not provided - may affect service delivery');
  }
  
  return {
    isComplete: missingFields.length === 0,
    missingFields,
    warnings,
  };
};

/**
 * Generate booking confirmation data
 */
export const generateBookingConfirmation = (data: BookingData): {
  confirmationNumber: string;
  estimatedArrival?: Date;
  cancellationDeadline?: Date;
  rescheduleDeadline?: Date;
} => {
  const confirmationNumber = generateConfirmationNumber();
  
  let estimatedArrival: Date | undefined;
  if (data.selectedDate && data.selectedTime) {
    const [hours, minutes] = data.selectedTime.split(':').map(Number);
    estimatedArrival = new Date(data.selectedDate);
    estimatedArrival.setHours(hours, minutes, 0, 0);
  }
  
  let cancellationDeadline: Date | undefined;
  let rescheduleDeadline: Date | undefined;
  
  if (estimatedArrival) {
    // 24 hours before for cancellation
    cancellationDeadline = new Date(estimatedArrival.getTime() - 24 * 60 * 60 * 1000);
    // 2 hours before for rescheduling
    rescheduleDeadline = new Date(estimatedArrival.getTime() - 2 * 60 * 60 * 1000);
  }
  
  return {
    confirmationNumber,
    estimatedArrival,
    cancellationDeadline,
    rescheduleDeadline,
  };
};

/**
 * Generate unique confirmation number
 */
const generateConfirmationNumber = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `VRL-${timestamp}-${random}`.toUpperCase();
};

/**
 * Track booking flow analytics
 */
export const trackBookingStep = (
  stepId: string,
  action: 'start' | 'complete' | 'abandon',
  timeSpent?: number
): void => {
  // Analytics tracking implementation
  const event = {
    type: 'booking_flow',
    stepId,
    action,
    timeSpent,
    timestamp: new Date().toISOString(),
  };
  
  // Send to analytics service
  console.log('Booking analytics:', event);
};

/**
 * Get booking flow recommendations
 */
export const getFlowRecommendations = (
  metrics: ConversionMetrics
): string[] => {
  const recommendations: string[] = [];
  
  // Check conversion rate
  if (metrics.conversionRate < 0.6) {
    recommendations.push('Consider simplifying the booking flow to improve conversion');
  }
  
  // Check step completion rates
  Object.entries(metrics.stepCompletionRates).forEach(([stepId, rate]) => {
    if (rate < 0.7) {
      recommendations.push(`Optimize the ${stepId} step - low completion rate (${(rate * 100).toFixed(1)}%)`);
    }
  });
  
  // Check time spent
  Object.entries(metrics.averageTimePerStep).forEach(([stepId, time]) => {
    if (time > 120) { // More than 2 minutes
      recommendations.push(`Reduce complexity in ${stepId} step - users spend too much time (${time}s)`);
    }
  });
  
  return recommendations;
};

export default {
  calculateProgress,
  getNextStep,
  getPreviousStep,
  validateStepCompletion,
  generateSmartDefaults,
  optimizeForConversion,
  calculateEstimatedTime,
  generateBookingSummary,
  validateBookingData,
  generateBookingConfirmation,
  trackBookingStep,
  getFlowRecommendations,
};
