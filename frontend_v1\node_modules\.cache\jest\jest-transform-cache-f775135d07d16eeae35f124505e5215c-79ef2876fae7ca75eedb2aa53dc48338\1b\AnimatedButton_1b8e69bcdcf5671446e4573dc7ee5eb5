2ec41865121929454f383f1e3af5352b
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.AnimatedButton = void 0;
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _HighContrastContext = require("../../contexts/HighContrastContext");
var _MotorAccessibilityContext = require("../../contexts/MotorAccessibilityContext");
var _Typography = require("../typography/Typography");
var _animationUtils = require("../../utils/animationUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var AnimatedButton = exports.AnimatedButton = function AnimatedButton(_ref) {
  var title = _ref.title,
    icon = _ref.icon,
    _ref$iconPosition = _ref.iconPosition,
    iconPosition = _ref$iconPosition === void 0 ? 'left' : _ref$iconPosition,
    onPress = _ref.onPress,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$state = _ref.state,
    state = _ref$state === void 0 ? 'idle' : _ref$state,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    style = _ref.style,
    _ref$enableAnimations = _ref.enableAnimations,
    enableAnimations = _ref$enableAnimations === void 0 ? true : _ref$enableAnimations,
    _ref$pressScale = _ref.pressScale,
    pressScale = _ref$pressScale === void 0 ? 0.95 : _ref$pressScale,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    testID = _ref.testID;
  var scaleValue = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var rotationValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var opacityValue = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var shakeValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var _useHighContrastColor = (0, _HighContrastContext.useHighContrastColors)(),
    colors = _useHighContrastColor.colors;
  var touchTargetStyles = (0, _MotorAccessibilityContext.useTouchTargetStyles)();
  var triggerHapticFeedback = (0, _MotorAccessibilityContext.useHapticFeedback)();
  var _createPressAnimation = (0, _animationUtils.createPressAnimation)(scaleValue, pressScale),
    pressIn = _createPressAnimation.pressIn,
    pressOut = _createPressAnimation.pressOut;
  var handlePressIn = (0, _react.useCallback)(function () {
    if (!disabled && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      pressIn();
    }
    triggerHapticFeedback('light');
  }, [disabled, enableAnimations, pressIn, triggerHapticFeedback]);
  var handlePressOut = (0, _react.useCallback)(function () {
    if (!disabled && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      pressOut();
    }
  }, [disabled, enableAnimations, pressOut]);
  var handlePress = (0, _react.useCallback)(function () {
    if (!disabled && state !== 'loading') {
      triggerHapticFeedback('medium');
      onPress();
    }
  }, [disabled, state, onPress, triggerHapticFeedback]);
  (0, _react.useEffect)(function () {
    if (state === 'loading' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var loadingAnimation = (0, _animationUtils.createLoadingAnimation)(rotationValue);
      loadingAnimation.start();
      return function () {
        return loadingAnimation.stop();
      };
    } else {
      rotationValue.setValue(0);
    }
  }, [state, enableAnimations, rotationValue]);
  (0, _react.useEffect)(function () {
    if (state === 'success' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var successAnimation = (0, _animationUtils.pulse)(scaleValue, 1.1);
      successAnimation.start(function () {
        if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
          _reactNative.AccessibilityInfo.announceForAccessibility('Action completed successfully');
        }
      });
    }
  }, [state, enableAnimations, scaleValue]);
  (0, _react.useEffect)(function () {
    if (state === 'error' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var errorAnimation = (0, _animationUtils.shake)(shakeValue, 8);
      errorAnimation.start(function () {
        if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
          _reactNative.AccessibilityInfo.announceForAccessibility('Action failed. Please try again.');
        }
      });
    }
  }, [state, enableAnimations, shakeValue]);
  var getButtonStyles = function getButtonStyles() {
    var _colors$button, _colors$primary, _colors$button2, _colors$secondary, _colors$border, _colors$button3, _colors$primary2;
    var baseStyles = [styles.button, styles[`${size}Button`]];
    if (fullWidth) {
      baseStyles.push(styles.fullWidth);
    }
    switch (variant) {
      case 'primary':
        baseStyles.push({
          backgroundColor: disabled ? (colors == null || (_colors$button = colors.button) == null ? void 0 : _colors$button.disabled) || '#CCC' : (colors == null || (_colors$primary = colors.primary) == null ? void 0 : _colors$primary.default) || '#5A7A63'
        });
        break;
      case 'secondary':
        baseStyles.push({
          backgroundColor: disabled ? (colors == null || (_colors$button2 = colors.button) == null ? void 0 : _colors$button2.disabled) || '#CCC' : (colors == null || (_colors$secondary = colors.secondary) == null ? void 0 : _colors$secondary.default) || '#F0F0F0',
          borderWidth: 1,
          borderColor: (colors == null || (_colors$border = colors.border) == null ? void 0 : _colors$border.primary) || '#DDD'
        });
        break;
      case 'outline':
        baseStyles.push({
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor: disabled ? (colors == null || (_colors$button3 = colors.button) == null ? void 0 : _colors$button3.disabled) || '#CCC' : (colors == null || (_colors$primary2 = colors.primary) == null ? void 0 : _colors$primary2.default) || '#5A7A63'
        });
        break;
      case 'ghost':
        baseStyles.push({
          backgroundColor: 'transparent'
        });
        break;
    }
    if (state === 'loading') {
      baseStyles.push(styles.loadingButton);
    }
    return baseStyles;
  };
  var getTextColor = function getTextColor() {
    var _colors$text, _colors$text2, _colors$text3, _colors$primary3, _colors$primary4, _colors$text4;
    if (disabled) return (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.disabled) || '#999';
    switch (variant) {
      case 'primary':
        return (colors == null || (_colors$text2 = colors.text) == null ? void 0 : _colors$text2.inverse) || '#FFFFFF';
      case 'secondary':
        return (colors == null || (_colors$text3 = colors.text) == null ? void 0 : _colors$text3.primary) || '#333';
      case 'outline':
        return (colors == null || (_colors$primary3 = colors.primary) == null ? void 0 : _colors$primary3.default) || '#5A7A63';
      case 'ghost':
        return (colors == null || (_colors$primary4 = colors.primary) == null ? void 0 : _colors$primary4.default) || '#5A7A63';
      default:
        return (colors == null || (_colors$text4 = colors.text) == null ? void 0 : _colors$text4.primary) || '#333';
    }
  };
  var getIconSize = function getIconSize() {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };
  var renderLoadingIcon = function renderLoadingIcon() {
    if (state !== 'loading') return null;
    return (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: [styles.iconContainer, {
        transform: [{
          rotate: rotationValue.interpolate({
            inputRange: [0, 1],
            outputRange: ['0deg', '360deg']
          })
        }]
      }],
      children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: "refresh",
        size: getIconSize(),
        color: getTextColor()
      })
    });
  };
  var renderIcon = function renderIcon() {
    if (state === 'loading') return renderLoadingIcon();
    if (!icon) return null;
    var iconName = icon;
    if (state === 'success') iconName = 'checkmark';
    if (state === 'error') iconName = 'close';
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.iconContainer,
      children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: iconName,
        size: getIconSize(),
        color: getTextColor()
      })
    });
  };
  var getButtonTitle = function getButtonTitle() {
    switch (state) {
      case 'loading':
        return 'Loading...';
      case 'success':
        return 'Success!';
      case 'error':
        return 'Try Again';
      default:
        return title;
    }
  };
  return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
    style: [touchTargetStyles, style],
    onPress: handlePress,
    onPressIn: handlePressIn,
    onPressOut: handlePressOut,
    disabled: disabled || state === 'loading',
    activeOpacity: 0.8,
    accessibilityRole: "button",
    accessibilityLabel: accessibilityLabel || title,
    accessibilityHint: accessibilityHint,
    accessibilityState: {
      disabled: disabled || state === 'loading',
      busy: state === 'loading'
    },
    testID: testID,
    children: (0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
      style: [getButtonStyles(), {
        transform: [{
          scale: scaleValue
        }, {
          translateX: shakeValue
        }],
        opacity: opacityValue
      }],
      children: [iconPosition === 'left' && renderIcon(), (0, _jsxRuntime.jsx)(_Typography.Typography, {
        variant: "button",
        color: getTextColor(),
        style: [styles.buttonText, icon && iconPosition === 'left' && styles.textWithLeftIcon, icon && iconPosition === 'right' && styles.textWithRightIcon],
        children: getButtonTitle()
      }), iconPosition === 'right' && renderIcon()]
    })
  });
};
var styles = _reactNative.StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  smallButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6
  },
  mediumButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8
  },
  largeButton: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 10
  },
  fullWidth: {
    width: '100%'
  },
  loadingButton: {
    opacity: 0.8
  },
  buttonText: {
    textAlign: 'center'
  },
  textWithLeftIcon: {
    marginLeft: 8
  },
  textWithRightIcon: {
    marginRight: 8
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center'
  }
});
var _default = exports.default = AnimatedButton;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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