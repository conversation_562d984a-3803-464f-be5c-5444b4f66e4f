/**
 * Jest Setup - Early Mocks
 * 
 * This file runs before any tests and sets up essential mocks
 * that need to be available during module loading.
 */

// Mock PixelRatio early to prevent import errors
jest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({
  get: jest.fn(() => 2),
  getFontScale: jest.fn(() => 1),
  getPixelSizeForLayoutSize: jest.fn((layoutSize) => layoutSize * 2),
  roundToNearestPixel: jest.fn((layoutSize) => Math.round(layoutSize * 2) / 2),
}));

// Mock Dimensions early
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({
    width: 375,
    height: 812,
    scale: 2,
    fontScale: 1,
  })),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

// Mock Platform early
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  Version: '14.0',
  select: jest.fn((obj) => obj.ios || obj.default),
  isPad: false,
  isTesting: true,
}));

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  const message = args[0];
  if (
    typeof message === 'string' &&
    (message.includes('Warning: ReactDOM.render is no longer supported') ||
     message.includes('Warning: componentWillMount has been renamed') ||
     message.includes('Warning: componentWillReceiveProps has been renamed') ||
     message.includes('VirtualizedLists should never be nested'))
  ) {
    return;
  }
  originalConsoleError(...args);
};

console.warn = (...args) => {
  const message = args[0];
  if (
    typeof message === 'string' &&
    (message.includes('Animated: `useNativeDriver`') ||
     message.includes('source.uri should not be an empty string'))
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Global test utilities
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();

// Mock timers
jest.useFakeTimers();
