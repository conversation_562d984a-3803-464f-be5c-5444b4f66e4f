<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="330" failures="0" errors="0" time="7.292">
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:26:58" time="1.53" tests="25">
    <testcase classname="Test Setup" name="should configure test environment" time="0.005">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle basic errors correctly" time="0.044">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle errors with context" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should preserve AppError properties" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › handleNetworkError" name="should handle network errors correctly" time="0.013">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect network errors" time="0.01">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authentication errors" time="0.013">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authorization errors" time="0.012">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect not found errors" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect server errors" time="0.01">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect critical errors" time="0.01">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect high severity errors" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect medium severity errors" time="0.01">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should default to low severity" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate network error messages" time="0.017">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate auth error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate authorization error messages" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate not found error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate server error messages" time="0.01">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate default error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should track error statistics correctly" time="0.025">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should clear errors correctly" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › error queue management" name="should maintain queue size limit" time="0.794">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate unique error IDs" time="0.016">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate IDs with correct format" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:26:59" time="1.716" tests="7">
    <testcase classname="Test Setup" name="should configure test environment" time="0.034">
    </testcase>
    <testcase classname="useProvidersStore › initial state" name="should have correct initial state" time="0.017">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should fetch providers successfully" time="0.821">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should handle loading error" time="0.003">
    </testcase>
    <testcase classname="useProvidersStore › updateFilters" name="should update filters correctly" time="0.002">
    </testcase>
    <testcase classname="useProvidersStore › clearFilters" name="should clear all filters" time="0.002">
    </testcase>
    <testcase classname="useProvidersStore › searchProviders" name="should search providers with filters" time="0.609">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:01" time="0.179" tests="48">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct contrast ratios" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct touch target sizes" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct animation timing" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="generates basic label" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds required indicator" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds error message" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="combines required and error" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="generates basic hint" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="adds additional info" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for white" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for black" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for gray" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates maximum contrast ratio" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates minimum contrast ratio" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates intermediate contrast ratio" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="passes for high contrast combinations" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="fails for low contrast combinations" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="uses different thresholds for large text" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="returns null for already accessible combinations" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="suggests darker color for low contrast" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="generates heading props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="handles different heading levels" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListProps" name="generates list props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListItemProps" name="generates list item props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="generates basic button props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes action hint" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes state" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="generates basic input props" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes value" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes required and error info" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for well-formed interactive element" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility role" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility label" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags small touch targets" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for adequate touch targets" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="generates report for component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="handles empty component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with polite priority" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with assertive priority" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceContentChange" name="should announce content changes" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce valid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce invalid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceProgress" name="should announce progress updates" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading start" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading complete" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › trackAccessibilityUsage" name="should track accessibility usage statistics" time="0.012">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › validateCompliance" name="should validate accessibility compliance" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:01" time="0.121" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate individual fields correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect invalid fields" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate entire form" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect form validation errors" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service name correctly" time="0.003">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service description" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate price correctly" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate duration correctly" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate profile data correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should handle optional fields correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate date of birth format" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should provide immediate feedback for email validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should handle password confirmation validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide clear, actionable error messages" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide specific validation feedback" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:02" time="0.402" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders correctly with default props" time="0.243">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders with different variants" time="0.003">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="calls onPress when pressed" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="does not call onPress when disabled" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Accessibility" name="has proper accessibility properties" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:02" time="0.587" tests="27">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should initialize with default configuration" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should support custom configuration" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should validate default configuration values" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should validate performance metrics" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should calculate performance scores correctly" time="0.007">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should validate accessibility for elements with labels" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should detect accessibility issues" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should calculate accessibility scores" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should detect code quality issues" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should pass clean code validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should calculate code quality scores" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data with overrides" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create service test data" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create booking test data" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create provider test data" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should provide enhanced assertion helpers" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should validate test coverage expectations" time="0.015">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should provide test suite creation utilities" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should create comprehensive test suites" time="0.027">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should generate quality reports" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should provide current metrics" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should provide singleton instance" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should export utility functions" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle missing performance object gracefully" time="0">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle invalid container in accessibility validation" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:03" time="0.175" tests="23">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="creates singleton instance" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="records metrics correctly" time="0.007">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="gets all metrics when no name specified" time="0.003">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="generates performance summary" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="limits metrics to prevent memory leaks" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="clears metrics" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="measures timing correctly" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="includes tags in metrics" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="sets and gets critical CSS" time="0">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="loads non-critical CSS asynchronously" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="preloads critical resources" time="0.003">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="imports module with performance tracking" time="0.005">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="handles import errors" time="0.012">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="preloads chunks" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="tracks loaded chunks" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="gets memory usage information" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="returns null when memory API not available" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="starts memory monitoring" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="measurePerformance creates PerformanceTimer" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps synchronous functions" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps asynchronous functions" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking handles errors" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:03" time="0.197" tests="24">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should send magic link successfully" time="0.005">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should reject invalid email format" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should enforce rate limiting" time="0.004">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should verify valid magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject expired magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject already used magic link" time="0.003">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject invalid token" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should send OTP successfully" time="0.003">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should reject invalid phone format" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should enforce rate limiting for phone numbers" time="0.005">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should verify correct OTP" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject incorrect OTP with attempt tracking" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject expired OTP" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should block after max attempts" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should authenticate successfully with biometrics" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric hardware available" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric credentials enrolled" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when biometric authentication fails" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric user data found" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › setupBiometricAuth" name="should setup biometric authentication successfully" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return true when biometric is setup" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return false when biometric is not setup" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Utility Methods › clearAuthData" name="should clear all authentication data" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:03" time="0.16" tests="18">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should generate appropriate alt-text for different image types" time="0.008">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should use custom description when provided" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should handle missing entity names gracefully" time="0.004">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate comprehensive accessibility props for informative images" time="0.005">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate proper props for functional images" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should handle decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should use custom alt-text when provided" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate informative images correctly" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should identify missing accessibility labels" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should warn about overly long accessibility labels" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for app logo" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for user avatar" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for functional icons" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for decorative images" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Batch Validation" name="should validate multiple images and provide comprehensive results" time="0.003">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all image accessibility requirements" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:03" time="0.16" tests="33">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return true for test mode in development" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return false for test mode in production" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should set test mode enabled state" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should not set test mode in production" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return all test accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return customer accounts only" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return service provider accounts only" time="0.006">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return providers by category" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account by role" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should find account by email" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return undefined for non-existent email" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return quick login accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return account statistics" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should successfully login with test account" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should handle login failure" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should not login when test mode is disabled" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should perform quick login" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should login with random account" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should store last test account" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should retrieve last test account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should return null when no last account stored" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should clear test account data" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should validate correct test account credentials" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for invalid credentials" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for correct email but wrong password" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for booking scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for messaging scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for payments scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should generate test account credentials for UI" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should log test accounts summary in development" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should not log in production" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:03" time="0.187" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate comprehensive accessibility props for text input" time="0.008">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate error-specific accessibility props" time="0.004">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should handle checkbox accessibility props" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Label Generation" name="should generate proper labels for different field types" time="0.006">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Hint Generation" name="should generate appropriate hints for different field types" time="0.005">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Error Message Accessibility" name="should generate proper error message props" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Helper Text Accessibility" name="should generate proper helper text props" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should generate proper label props" time="0.004">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should handle non-required labels" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should validate complete form accessibility" time="0.005">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should identify accessibility issues" time="0.005">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Field Type Specific Accessibility" name="should handle different field types correctly" time="0.01">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all form accessibility requirements" time="0.011">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:04" time="0.371" tests="17">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should meet WCAG 2.1 AA minimum thickness requirement (2px)" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should use recommended thickness for better visibility" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should enforce minimum thickness even when smaller width is requested" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have high z-index to prevent obscuring by sticky elements" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have visible overflow to show focus ring" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have shadow for enhanced visibility" time="0.003">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide web-specific outline properties" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide Android-specific elevation" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should support different focus indicator colors" time="0.005">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should provide subtle background highlight" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should ensure focus is not obscured by sticky elements" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should handle empty sticky elements array" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should preserve existing high z-index if already sufficient" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should return empty style when not focused" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should merge with base styles when focused" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › WCAG Standards Compliance" name="should meet all WCAG 2.1 AA focus indicator requirements" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:04" time="0.137" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="AuthSlice › Initial State" name="should have correct initial state" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginSuccess" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginFailure" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerSuccess" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerFailure" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateProfile" time="0.003">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateTokens" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Logout" name="should handle logout" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should load stored authentication data" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle missing stored data" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle corrupted stored user data" time="0.012">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:04" time="0.127" tests="20">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should validate compliant colors" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should enhance non-compliant colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should identify optimal colors (AAA)" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › validateInteractiveColors" name="should validate all interactive colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should generate comprehensive contrast report" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should have high compliance rate" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return white for dark backgrounds" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return black for light backgrounds" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return appropriate color for sage green" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return original color if contrast is sufficient" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return high contrast alternative if insufficient" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have all required color constants" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant primary colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant secondary colors" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Hover" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Pressed" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Secondary Button" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Error Button" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:04" time="0.135" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should meet WCAG AA standards for primary button text" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should have appropriate disabled state contrast" time="0.001">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button text on white background" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button border" time="0.001">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Destructive Button Contrast" name="should meet WCAG AA standards for destructive button text" time="0.003">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Ghost Button Contrast" name="should meet WCAG AA standards for ghost button text on white background" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Focus States Contrast" name="should meet WCAG AA standards for focus indicators" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Comprehensive Color Combinations" name="should validate all interactive color combinations" time="0.008">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Large Text Contrast" name="should meet WCAG AA standards for large text (18pt+)" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:05" time="0.127" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthService › login" name="should successfully login with valid credentials" time="0.002">
    </testcase>
    <testcase classname="AuthService › login" name="should handle login failure with error message" time="0.008">
    </testcase>
    <testcase classname="AuthService › login" name="should handle network errors" time="0.005">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new customer" time="0.001">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new service provider" time="0.001">
    </testcase>
    <testcase classname="AuthService › register" name="should handle registration validation errors" time="0.001">
    </testcase>
    <testcase classname="AuthService › refreshToken" name="should successfully refresh access token" time="0.003">
    </testcase>
    <testcase classname="AuthService › logout" name="should successfully logout" time="0.001">
    </testcase>
    <testcase classname="AuthService › logout" name="should handle logout errors gracefully" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:05" time="0.104" tests="8">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct TOUCH_TARGETS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have backward compatibility TARGET_SIZE alias" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct FOCUS_INDICATORS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct CONTRAST_RATIOS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should be frozen to prevent modification" time="0">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should not throw runtime errors when accessing properties" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should maintain consistency between TOUCH_TARGETS.MINIMUM_SIZE and TARGET_SIZE.MINIMUM" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T05:27:05" time="0.103" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define RootStackParamList correctly" time="0.003">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define AuthStackParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define CustomerTabParamList correctly" time="0.002">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define ProviderTabParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Safety" name="should enforce correct parameter types" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="3" timestamp="2025-07-21T05:27:05" time="0.102" tests="4">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should successfully login with test credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should fail with invalid credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › register integration" name="should handle registration attempt" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>