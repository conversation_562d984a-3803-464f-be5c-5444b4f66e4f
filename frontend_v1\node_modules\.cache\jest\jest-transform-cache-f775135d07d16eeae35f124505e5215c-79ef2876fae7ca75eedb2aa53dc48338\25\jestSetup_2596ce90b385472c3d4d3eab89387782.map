{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "require", "_require", "jest"], "sources": ["jestSetup.js"], "sourcesContent": ["jest.mock('./src/RNGestureHandlerModule', () => require('./src/mocks'));\njest.mock('./lib/commonjs/RNGestureHandlerModule', () =>\n  require('./lib/commonjs/mocks')\n);\njest.mock('./lib/module/RNGestureHandlerModule', () =>\n  require('./lib/module/mocks')\n);\n"], "mappings": "AAAAA,WAAA,GAAKC,IAAI,iCAAiC;EAAA,OAAMC,OAAO,cAAc,CAAC;AAAA,EAAC;AACvEF,WAAA,GAAKC,IAAI,0CAA0C;EAAA,OACjDC,OAAO,uBAAuB,CAAC;AAAA,CACjC,CAAC;AACDF,WAAA,GAAKC,IAAI,wCAAwC;EAAA,OAC/CC,OAAO,qBAAqB,CAAC;AAAA,CAC/B,CAAC;AAAC,SAAAF,YAAA;EAAA,IAAAG,QAAA,GAAAD,OAAA;IAAAE,IAAA,GAAAD,QAAA,CAAAC,IAAA;EAAAJ,WAAA,YAAAA,YAAA;IAAA,OAAAI,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA", "ignoreList": []}