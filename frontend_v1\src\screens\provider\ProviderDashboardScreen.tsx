/**
 * Provider Dashboard Screen
 *
 * Comprehensive dashboard for service providers with analytics,
 * booking management, and business insights.
 *
 * Features:
 * - Revenue analytics
 * - Booking management
 * - Performance metrics
 * - Customer insights
 * - Business tools
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Heading, Body } from '../../components/typography/Typography';
import { AnimatedButton } from '../../components/animation/AnimatedButton';
import { LoadingState } from '../../components/loading/LoadingStates';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles } from '../../contexts/MotorAccessibilityContext';

// Screen dimensions
const { width: screenWidth } = Dimensions.get('window');

// Dashboard data interfaces
interface RevenueMetrics {
  totalRevenue: number;
  monthlyRevenue: number;
  weeklyRevenue: number;
  dailyRevenue: number;
  revenueGrowth: number;
  averageBookingValue: number;
}

interface BookingMetrics {
  totalBookings: number;
  pendingBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  bookingRate: number;
  customerSatisfaction: number;
}

interface BusinessMetrics {
  profileViews: number;
  inquiries: number;
  conversionRate: number;
  responseTime: number;
  rating: number;
  reviewCount: number;
}

// Component props
export interface ProviderDashboardScreenProps {
  navigation: any;
  route: any;
}

export const ProviderDashboardScreen: React.FC<ProviderDashboardScreenProps> = ({
  navigation,
  route,
}) => {
  // Hooks
  const { t } = useTranslation();
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [revenueMetrics, setRevenueMetrics] = useState<RevenueMetrics | null>(null);
  const [bookingMetrics, setBookingMetrics] = useState<BookingMetrics | null>(null);
  const [businessMetrics, setBusinessMetrics] = useState<BusinessMetrics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month' | 'year'>('month');

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    try {
      // Mock data - in real app, this would come from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setRevenueMetrics({
        totalRevenue: 15420.50,
        monthlyRevenue: 3240.75,
        weeklyRevenue: 810.25,
        dailyRevenue: 115.50,
        revenueGrowth: 12.5,
        averageBookingValue: 85.75,
      });

      setBookingMetrics({
        totalBookings: 187,
        pendingBookings: 8,
        completedBookings: 165,
        cancelledBookings: 14,
        bookingRate: 92.5,
        customerSatisfaction: 4.8,
      });

      setBusinessMetrics({
        profileViews: 1250,
        inquiries: 89,
        conversionRate: 68.5,
        responseTime: 2.5,
        rating: 4.8,
        reviewCount: 142,
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    loadDashboardData();
  }, [loadDashboardData]);

  // Render metric card
  const renderMetricCard = (
    title: string,
    value: string | number,
    subtitle?: string,
    icon?: string,
    trend?: number,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={[
        styles.metricCard,
        { backgroundColor: colors?.background?.secondary },
        touchTargetStyles,
      ]}
      onPress={onPress}
      disabled={!onPress}
      accessibilityRole="button"
      accessibilityLabel={`${title}: ${value}${subtitle ? `, ${subtitle}` : ''}`}
    >
      <View style={styles.metricHeader}>
        {icon && (
          <Ionicons
            name={icon as any}
            size={24}
            color={colors?.primary?.default}
            style={styles.metricIcon}
          />
        )}
        <Typography
          variant="caption"
          color={colors?.text?.secondary}
          style={styles.metricTitle}
        >
          {title}
        </Typography>
      </View>
      
      <Typography
        variant="h3"
        color={colors?.text?.primary}
        style={styles.metricValue}
      >
        {value}
      </Typography>
      
      {subtitle && (
        <Typography
          variant="caption"
          color={colors?.text?.tertiary}
          style={styles.metricSubtitle}
        >
          {subtitle}
        </Typography>
      )}
      
      {trend !== undefined && (
        <View style={styles.trendContainer}>
          <Ionicons
            name={trend >= 0 ? 'trending-up' : 'trending-down'}
            size={16}
            color={trend >= 0 ? colors?.status?.success : colors?.status?.error}
          />
          <Typography
            variant="caption"
            color={trend >= 0 ? colors?.status?.success : colors?.status?.error}
            style={styles.trendText}
          >
            {Math.abs(trend)}%
          </Typography>
        </View>
      )}
    </TouchableOpacity>
  );

  // Render period selector
  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {(['day', 'week', 'month', 'year'] as const).map((period) => (
        <TouchableOpacity
          key={period}
          style={[
            styles.periodButton,
            {
              backgroundColor: selectedPeriod === period 
                ? colors?.primary?.default 
                : colors?.background?.tertiary,
            },
            touchTargetStyles,
          ]}
          onPress={() => setSelectedPeriod(period)}
          accessibilityRole="tab"
          accessibilityState={{ selected: selectedPeriod === period }}
          accessibilityLabel={t(`provider.dashboard.period_${period}`)}
        >
          <Typography
            variant="caption"
            color={selectedPeriod === period ? colors?.text?.inverse : colors?.text?.secondary}
            style={styles.periodButtonText}
          >
            {t(`provider.dashboard.period_${period}`)}
          </Typography>
        </TouchableOpacity>
      ))}
    </View>
  );

  // Render quick actions
  const renderQuickActions = () => (
    <View style={styles.quickActionsContainer}>
      <Heading
        level={3}
        color={colors?.text?.primary}
        style={styles.sectionTitle}
      >
        {t('provider.dashboard.quick_actions')}
      </Heading>
      
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity
          style={[
            styles.quickActionCard,
            { backgroundColor: colors?.background?.secondary },
            touchTargetStyles,
          ]}
          onPress={() => navigation.navigate('ProviderBookings')}
          accessibilityRole="button"
          accessibilityLabel={t('provider.dashboard.manage_bookings')}
        >
          <Ionicons
            name="calendar"
            size={32}
            color={colors?.primary?.default}
            style={styles.quickActionIcon}
          />
          <Typography
            variant="subtitle2"
            color={colors?.text?.primary}
            style={styles.quickActionTitle}
          >
            {t('provider.dashboard.manage_bookings')}
          </Typography>
          <Body
            color={colors?.text?.secondary}
            style={styles.quickActionDescription}
          >
            {bookingMetrics?.pendingBookings} {t('provider.dashboard.pending')}
          </Body>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.quickActionCard,
            { backgroundColor: colors?.background?.secondary },
            touchTargetStyles,
          ]}
          onPress={() => navigation.navigate('ProviderServices')}
          accessibilityRole="button"
          accessibilityLabel={t('provider.dashboard.manage_services')}
        >
          <Ionicons
            name="construct"
            size={32}
            color={colors?.primary?.default}
            style={styles.quickActionIcon}
          />
          <Typography
            variant="subtitle2"
            color={colors?.text?.primary}
            style={styles.quickActionTitle}
          >
            {t('provider.dashboard.manage_services')}
          </Typography>
          <Body
            color={colors?.text?.secondary}
            style={styles.quickActionDescription}
          >
            {t('provider.dashboard.update_offerings')}
          </Body>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.quickActionCard,
            { backgroundColor: colors?.background?.secondary },
            touchTargetStyles,
          ]}
          onPress={() => navigation.navigate('ProviderMessages')}
          accessibilityRole="button"
          accessibilityLabel={t('provider.dashboard.messages')}
        >
          <Ionicons
            name="chatbubbles"
            size={32}
            color={colors?.primary?.default}
            style={styles.quickActionIcon}
          />
          <Typography
            variant="subtitle2"
            color={colors?.text?.primary}
            style={styles.quickActionTitle}
          >
            {t('provider.dashboard.messages')}
          </Typography>
          <Body
            color={colors?.text?.secondary}
            style={styles.quickActionDescription}
          >
            {businessMetrics?.inquiries} {t('provider.dashboard.new_inquiries')}
          </Body>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.quickActionCard,
            { backgroundColor: colors?.background?.secondary },
            touchTargetStyles,
          ]}
          onPress={() => navigation.navigate('ProviderProfile')}
          accessibilityRole="button"
          accessibilityLabel={t('provider.dashboard.profile_settings')}
        >
          <Ionicons
            name="person-circle"
            size={32}
            color={colors?.primary?.default}
            style={styles.quickActionIcon}
          />
          <Typography
            variant="subtitle2"
            color={colors?.text?.primary}
            style={styles.quickActionTitle}
          >
            {t('provider.dashboard.profile_settings')}
          </Typography>
          <Body
            color={colors?.text?.secondary}
            style={styles.quickActionDescription}
          >
            {businessMetrics?.profileViews} {t('provider.dashboard.profile_views')}
          </Body>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: colors?.background?.primary }]}>
        <LoadingState
          variant="spinner"
          message={t('provider.dashboard.loading')}
          size="large"
        />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors?.background?.primary }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={colors?.primary?.default}
            colors={[colors?.primary?.default || '#5A7A63']}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Heading
            level={1}
            color={colors?.text?.primary}
            style={styles.headerTitle}
          >
            {t('provider.dashboard.title')}
          </Heading>
          
          <Body
            color={colors?.text?.secondary}
            style={styles.headerSubtitle}
          >
            {t('provider.dashboard.welcome_back')}
          </Body>
        </View>

        {/* Period Selector */}
        {renderPeriodSelector()}

        {/* Revenue Metrics */}
        <View style={styles.metricsSection}>
          <Heading
            level={3}
            color={colors?.text?.primary}
            style={styles.sectionTitle}
          >
            {t('provider.dashboard.revenue_overview')}
          </Heading>
          
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              t('provider.dashboard.total_revenue'),
              `$${revenueMetrics?.totalRevenue.toLocaleString('en-CA', { minimumFractionDigits: 2 })}`,
              t('provider.dashboard.all_time'),
              'cash',
              revenueMetrics?.revenueGrowth,
              () => navigation.navigate('ProviderRevenue')
            )}
            
            {renderMetricCard(
              t('provider.dashboard.monthly_revenue'),
              `$${revenueMetrics?.monthlyRevenue.toLocaleString('en-CA', { minimumFractionDigits: 2 })}`,
              t('provider.dashboard.this_month'),
              'trending-up'
            )}
          </View>
        </View>

        {/* Booking Metrics */}
        <View style={styles.metricsSection}>
          <Heading
            level={3}
            color={colors?.text?.primary}
            style={styles.sectionTitle}
          >
            {t('provider.dashboard.booking_overview')}
          </Heading>
          
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              t('provider.dashboard.total_bookings'),
              bookingMetrics?.totalBookings.toString() || '0',
              t('provider.dashboard.all_time'),
              'calendar',
              undefined,
              () => navigation.navigate('ProviderBookings')
            )}
            
            {renderMetricCard(
              t('provider.dashboard.customer_satisfaction'),
              `${bookingMetrics?.customerSatisfaction}/5`,
              `${bookingMetrics?.reviewCount} ${t('provider.dashboard.reviews')}`,
              'star'
            )}
          </View>
        </View>

        {/* Business Metrics */}
        <View style={styles.metricsSection}>
          <Heading
            level={3}
            color={colors?.text?.primary}
            style={styles.sectionTitle}
          >
            {t('provider.dashboard.business_insights')}
          </Heading>
          
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              t('provider.dashboard.conversion_rate'),
              `${businessMetrics?.conversionRate}%`,
              t('provider.dashboard.inquiry_to_booking'),
              'funnel'
            )}
            
            {renderMetricCard(
              t('provider.dashboard.response_time'),
              `${businessMetrics?.responseTime}h`,
              t('provider.dashboard.average_response'),
              'time'
            )}
          </View>
        </View>

        {/* Quick Actions */}
        {renderQuickActions()}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  header: {
    padding: 16,
    paddingTop: 24,
  },
  headerTitle: {
    marginBottom: 4,
  },
  headerSubtitle: {
    opacity: 0.8,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  periodButtonText: {
    fontWeight: '600',
  },
  metricsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  metricCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    minHeight: 120,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricIcon: {
    marginRight: 8,
  },
  metricTitle: {
    flex: 1,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  metricValue: {
    marginBottom: 4,
    fontWeight: '700',
  },
  metricSubtitle: {
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    marginLeft: 4,
    fontWeight: '600',
  },
  quickActionsContainer: {
    paddingHorizontal: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: (screenWidth - 44) / 2, // Account for padding and gap
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    minHeight: 120,
  },
  quickActionIcon: {
    marginBottom: 8,
  },
  quickActionTitle: {
    textAlign: 'center',
    marginBottom: 4,
    fontWeight: '600',
  },
  quickActionDescription: {
    textAlign: 'center',
    fontSize: 12,
  },
  bottomSpacing: {
    height: 32,
  },
});

export default ProviderDashboardScreen;
