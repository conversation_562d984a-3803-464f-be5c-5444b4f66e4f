c6f219d453e1a851ba454cce48bddf5e
_getJestObj().mock('react-native/Libraries/Utilities/PixelRatio', function () {
  return {
    get: jest.fn(function () {
      return 2;
    }),
    getFontScale: jest.fn(function () {
      return 1;
    }),
    getPixelSizeForLayoutSize: jest.fn(function (layoutSize) {
      return layoutSize * 2;
    }),
    roundToNearestPixel: jest.fn(function (layoutSize) {
      return Math.round(layoutSize * 2) / 2;
    })
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Dimensions', function () {
  return {
    get: jest.fn(function () {
      return {
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1
      };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Platform', function () {
  return {
    OS: 'ios',
    Version: '14.0',
    select: jest.fn(function (obj) {
      return obj.ios || obj.default;
    }),
    isPad: false,
    isTesting: true
  };
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
jest.useFakeTimers();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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