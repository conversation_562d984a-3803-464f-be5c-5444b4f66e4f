/**
 * State Management Optimization Utilities
 *
 * Utilities for optimizing state management performance,
 * implementing efficient patterns, and monitoring state usage.
 *
 * Features:
 * - State normalization
 * - Selector optimization
 * - State persistence
 * - Performance monitoring
 * - Memory optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useMemo, useRef, useEffect } from 'react';

// State optimization configuration
export interface StateOptimizationConfig {
  enableNormalization: boolean;
  enablePersistence: boolean;
  enablePerformanceMonitoring: boolean;
  enableMemoryOptimization: boolean;
  maxCacheSize: number;
  persistenceKey: string;
  debounceMs: number;
}

// State performance metrics
export interface StatePerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage: number;
  stateSize: number;
  selectorCalls: number;
  cacheHits: number;
  cacheMisses: number;
}

// Normalized state structure
export interface NormalizedState<T> {
  entities: Record<string, T>;
  ids: string[];
  loading: boolean;
  error: string | null;
  lastUpdated: number;
}

// Selector cache entry
interface SelectorCacheEntry<T> {
  result: T;
  dependencies: any[];
  timestamp: number;
  hitCount: number;
}

// Default configuration
const DEFAULT_CONFIG: StateOptimizationConfig = {
  enableNormalization: true,
  enablePersistence: true,
  enablePerformanceMonitoring: __DEV__,
  enableMemoryOptimization: true,
  maxCacheSize: 100,
  persistenceKey: 'vierla_state',
  debounceMs: 300,
};

// Global state metrics
const stateMetrics: StatePerformanceMetrics = {
  renderCount: 0,
  lastRenderTime: 0,
  averageRenderTime: 0,
  memoryUsage: 0,
  stateSize: 0,
  selectorCalls: 0,
  cacheHits: 0,
  cacheMisses: 0,
};

// Selector cache
const selectorCache = new Map<string, SelectorCacheEntry<any>>();

/**
 * Normalize array data into entities and ids
 */
export const normalizeData = <T extends { id: string }>(
  data: T[]
): NormalizedState<T> => {
  const entities: Record<string, T> = {};
  const ids: string[] = [];

  data.forEach(item => {
    entities[item.id] = item;
    ids.push(item.id);
  });

  return {
    entities,
    ids,
    loading: false,
    error: null,
    lastUpdated: Date.now(),
  };
};

/**
 * Denormalize entities back to array
 */
export const denormalizeData = <T>(
  normalizedState: NormalizedState<T>
): T[] => {
  return normalizedState.ids.map(id => normalizedState.entities[id]);
};

/**
 * Update normalized state with new data
 */
export const updateNormalizedState = <T extends { id: string }>(
  state: NormalizedState<T>,
  updates: Partial<T>[],
  operation: 'add' | 'update' | 'remove' = 'update'
): NormalizedState<T> => {
  const newEntities = { ...state.entities };
  const newIds = [...state.ids];

  updates.forEach(update => {
    if (!update.id) return;

    switch (operation) {
      case 'add':
        if (!newEntities[update.id]) {
          newEntities[update.id] = update as T;
          newIds.push(update.id);
        }
        break;
      case 'update':
        if (newEntities[update.id]) {
          newEntities[update.id] = { ...newEntities[update.id], ...update };
        }
        break;
      case 'remove':
        delete newEntities[update.id];
        const index = newIds.indexOf(update.id);
        if (index > -1) {
          newIds.splice(index, 1);
        }
        break;
    }
  });

  return {
    ...state,
    entities: newEntities,
    ids: newIds,
    lastUpdated: Date.now(),
  };
};

/**
 * Create memoized selector with caching
 */
export const createMemoizedSelector = <TState, TResult>(
  selector: (state: TState) => TResult,
  dependencies: (state: TState) => any[] = () => [],
  cacheKey?: string
) => {
  return (state: TState): TResult => {
    stateMetrics.selectorCalls++;

    const key = cacheKey || selector.toString();
    const deps = dependencies(state);
    const cached = selectorCache.get(key);

    // Check cache validity
    if (cached && areEqual(cached.dependencies, deps)) {
      cached.hitCount++;
      stateMetrics.cacheHits++;
      return cached.result;
    }

    // Calculate new result
    const startTime = performance.now();
    const result = selector(state);
    const endTime = performance.now();

    // Update metrics
    stateMetrics.cacheMisses++;
    stateMetrics.lastRenderTime = endTime - startTime;
    stateMetrics.averageRenderTime = 
      (stateMetrics.averageRenderTime + stateMetrics.lastRenderTime) / 2;

    // Cache result
    selectorCache.set(key, {
      result,
      dependencies: deps,
      timestamp: Date.now(),
      hitCount: 1,
    });

    // Clean cache if too large
    if (selectorCache.size > DEFAULT_CONFIG.maxCacheSize) {
      cleanSelectorCache();
    }

    return result;
  };
};

/**
 * Clean selector cache (LRU strategy)
 */
const cleanSelectorCache = (): void => {
  const entries = Array.from(selectorCache.entries());
  
  // Sort by hit count and timestamp (LRU)
  entries.sort((a, b) => {
    const aScore = a[1].hitCount + (Date.now() - a[1].timestamp) / 1000;
    const bScore = b[1].hitCount + (Date.now() - b[1].timestamp) / 1000;
    return aScore - bScore;
  });

  // Remove oldest 20% of entries
  const toRemove = Math.ceil(entries.length * 0.2);
  for (let i = 0; i < toRemove; i++) {
    selectorCache.delete(entries[i][0]);
  }
};

/**
 * Deep equality check for dependencies
 */
const areEqual = (a: any[], b: any[]): boolean => {
  if (a.length !== b.length) return false;
  
  for (let i = 0; i < a.length; i++) {
    if (typeof a[i] === 'object' && typeof b[i] === 'object') {
      if (JSON.stringify(a[i]) !== JSON.stringify(b[i])) return false;
    } else if (a[i] !== b[i]) {
      return false;
    }
  }
  
  return true;
};

/**
 * Optimized useSelector hook with performance monitoring
 */
export const useOptimizedSelector = <TState, TResult>(
  selector: (state: TState) => TResult,
  dependencies?: (state: TState) => any[]
) => {
  const renderCountRef = useRef(0);
  const memoizedSelector = useMemo(
    () => createMemoizedSelector(selector, dependencies),
    [selector, dependencies]
  );

  useEffect(() => {
    renderCountRef.current++;
    stateMetrics.renderCount++;
  });

  return memoizedSelector;
};

/**
 * Debounced state update hook
 */
export const useDebouncedState = <T>(
  initialValue: T,
  delay: number = DEFAULT_CONFIG.debounceMs
) => {
  const [state, setState] = React.useState(initialValue);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedSetState = useCallback((newValue: T | ((prev: T) => T)) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(newValue);
    }, delay);
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, debouncedSetState] as const;
};

/**
 * State persistence utilities
 */
export const statePersistenceUtils = {
  // Save state to storage
  saveState: async <T>(key: string, state: T): Promise<void> => {
    try {
      const serialized = JSON.stringify(state);
      if (typeof window !== 'undefined') {
        localStorage.setItem(key, serialized);
      }
    } catch (error) {
      console.error('Failed to save state:', error);
    }
  },

  // Load state from storage
  loadState: async <T>(key: string): Promise<T | null> => {
    try {
      if (typeof window !== 'undefined') {
        const serialized = localStorage.getItem(key);
        if (serialized) {
          return JSON.parse(serialized);
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to load state:', error);
      return null;
    }
  },

  // Remove state from storage
  removeState: async (key: string): Promise<void> => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Failed to remove state:', error);
    }
  },
};

/**
 * State size calculator
 */
export const calculateStateSize = (state: any): number => {
  try {
    const serialized = JSON.stringify(state);
    return new Blob([serialized]).size;
  } catch (error) {
    console.error('Failed to calculate state size:', error);
    return 0;
  }
};

/**
 * Memory optimization utilities
 */
export const memoryOptimizationUtils = {
  // Clean up unused state
  cleanupUnusedState: <T extends Record<string, any>>(
    state: T,
    usedKeys: string[]
  ): Partial<T> => {
    const cleaned: Partial<T> = {};
    usedKeys.forEach(key => {
      if (state[key] !== undefined) {
        cleaned[key] = state[key];
      }
    });
    return cleaned;
  },

  // Compress large arrays
  compressArray: <T>(array: T[], maxSize: number = 1000): T[] => {
    if (array.length <= maxSize) return array;
    
    // Keep first and last items, sample middle
    const step = Math.ceil(array.length / maxSize);
    const compressed: T[] = [];
    
    for (let i = 0; i < array.length; i += step) {
      compressed.push(array[i]);
    }
    
    return compressed;
  },

  // Remove old cache entries
  cleanupOldEntries: <T extends { timestamp: number }>(
    cache: Map<string, T>,
    maxAge: number = 5 * 60 * 1000 // 5 minutes
  ): void => {
    const now = Date.now();
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp > maxAge) {
        cache.delete(key);
      }
    }
  },
};

/**
 * Get state performance metrics
 */
export const getStatePerformanceMetrics = (): StatePerformanceMetrics => {
  return { ...stateMetrics };
};

/**
 * Reset state performance metrics
 */
export const resetStatePerformanceMetrics = (): void => {
  stateMetrics.renderCount = 0;
  stateMetrics.lastRenderTime = 0;
  stateMetrics.averageRenderTime = 0;
  stateMetrics.memoryUsage = 0;
  stateMetrics.stateSize = 0;
  stateMetrics.selectorCalls = 0;
  stateMetrics.cacheHits = 0;
  stateMetrics.cacheMisses = 0;
};

/**
 * State optimization recommendations
 */
export const getStateOptimizationRecommendations = (): string[] => {
  const recommendations: string[] = [];
  const metrics = getStatePerformanceMetrics();
  
  // Check cache hit rate
  const hitRate = metrics.selectorCalls > 0 
    ? metrics.cacheHits / metrics.selectorCalls 
    : 0;
  
  if (hitRate < 0.7) {
    recommendations.push('Consider optimizing selectors for better cache performance');
  }
  
  // Check render frequency
  if (metrics.renderCount > 100) {
    recommendations.push('High render count detected, consider memoization');
  }
  
  // Check average render time
  if (metrics.averageRenderTime > 16) {
    recommendations.push('Slow renders detected, optimize state selectors');
  }
  
  // Check state size
  if (metrics.stateSize > 1024 * 1024) { // 1MB
    recommendations.push('Large state size detected, consider normalization');
  }
  
  return recommendations;
};

/**
 * Monitor state performance
 */
export const useStatePerformanceMonitor = () => {
  const [metrics, setMetrics] = React.useState<StatePerformanceMetrics>(
    getStatePerformanceMetrics()
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(getStatePerformanceMetrics());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    recommendations: getStateOptimizationRecommendations(),
    resetMetrics: resetStatePerformanceMetrics,
  };
};

export default {
  normalizeData,
  denormalizeData,
  updateNormalizedState,
  createMemoizedSelector,
  useOptimizedSelector,
  useDebouncedState,
  statePersistenceUtils,
  calculateStateSize,
  memoryOptimizationUtils,
  getStatePerformanceMetrics,
  resetStatePerformanceMetrics,
  getStateOptimizationRecommendations,
  useStatePerformanceMonitor,
  DEFAULT_CONFIG,
};
