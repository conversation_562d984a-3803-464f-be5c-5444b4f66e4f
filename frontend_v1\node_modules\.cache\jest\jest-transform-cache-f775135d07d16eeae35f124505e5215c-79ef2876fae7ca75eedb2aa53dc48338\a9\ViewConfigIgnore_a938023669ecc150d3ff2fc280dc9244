03926fc85bb3459ca00e4579b8031285
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ConditionallyIgnoredEventHandlers = ConditionallyIgnoredEventHandlers;
exports.DynamicallyInjectedByGestureHandler = DynamicallyInjectedByGestureHandler;
exports.isIgnored = isIgnored;
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var ignoredViewConfigProps = new WeakSet();
function DynamicallyInjectedByGestureHandler(object) {
  ignoredViewConfigProps.add(object);
  return object;
}
function ConditionallyIgnoredEventHandlers(value) {
  if (_Platform.default.OS === 'ios') {
    return value;
  }
  return undefined;
}
function isIgnored(value) {
  if (typeof value === 'object' && value != null) {
    return ignoredViewConfigProps.has(value);
  }
  return false;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************