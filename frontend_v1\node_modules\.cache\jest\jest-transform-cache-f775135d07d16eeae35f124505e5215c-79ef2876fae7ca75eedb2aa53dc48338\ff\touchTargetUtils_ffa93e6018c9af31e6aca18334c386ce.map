{"version": 3, "names": ["_reactNative", "require", "TOUCH_TARGET_SIZES", "exports", "MINIMUM", "COMFORTABLE", "LARGE", "EXTRA_LARGE", "IOS_MINIMUM", "ANDROID_MINIMUM", "ICON_BUTTON", "TEXT_BUTTON", "FAB", "NAVIGATION_ITEM", "FORM_INPUT", "TOUCH_TARGET_SPACING", "getPlatformMinimumTouchTarget", "Platform", "select", "ios", "android", "default", "getRecommendedTouchTargetSize", "motorLevel", "arguments", "length", "undefined", "getRecommendedSpacing", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>get", "width", "height", "issues", "recommendations", "minSize", "recommendedSize", "push", "aspectRatio", "Math", "max", "min", "valid", "calculateTouchArea", "contentWidth", "contentHeight", "minTouchSize", "paddingHorizontal", "paddingVertical", "minTouchableArea", "generateTouchTargetStyles", "contentSize", "spacing", "min<PERSON><PERSON><PERSON>", "minHeight", "marginHorizontal", "marginVertical", "getMotorAccessibilitySupport", "supportsHapticFeedback", "OS", "supportsVoiceControl", "supportsSwitchControl", "supportsAssistiveTouch", "screenSize", "Dimensions", "get", "isLargeScreen", "recommendLargerTargets", "recommendHapticFeedback", "recommendVoiceAlternatives", "calculateAdaptiveTouchTargetSize", "baseSize", "userInteractionData", "adaptedSize", "missRate", "averageAccuracy", "preferredSize", "round", "generateGestureFriendlyArea", "gestureType", "hitSlop", "top", "bottom", "left", "right", "validateTouchTargetSpacing", "targets", "minSpacing", "conflicts", "i", "j", "target1", "target2", "horizontalDistance", "x", "verticalDistance", "y", "distance", "sqrt", "_default"], "sources": ["touchTargetUtils.ts"], "sourcesContent": ["/**\n * Touch Target Utilities\n *\n * Utilities for ensuring proper touch target sizes and motor accessibility\n * compliance following WCAG 2.2 AA guidelines and platform best practices.\n *\n * Features:\n * - Touch target size validation\n * - Motor accessibility helpers\n * - Gesture recognition utilities\n * - Adaptive touch areas\n * - Accessibility measurements\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Dimensions, Platform } from 'react-native';\n\n// Touch target size constants (in dp/pt)\nexport const TOUCH_TARGET_SIZES = {\n  // WCAG 2.2 AA minimum\n  MINIMUM: 44,\n  \n  // Recommended comfortable sizes\n  COMFORTABLE: 48,\n  LARGE: 56,\n  EXTRA_LARGE: 64,\n  \n  // Platform-specific recommendations\n  IOS_MINIMUM: 44,\n  ANDROID_MINIMUM: 48,\n  \n  // Context-specific sizes\n  ICON_BUTTON: 44,\n  TEXT_BUTTON: 44,\n  FAB: 56,\n  NAVIGATION_ITEM: 48,\n  FORM_INPUT: 44,\n} as const;\n\n// Touch target spacing constants\nexport const TOUCH_TARGET_SPACING = {\n  MINIMUM: 8,\n  COMFORTABLE: 12,\n  LARGE: 16,\n} as const;\n\n// Motor accessibility difficulty levels\nexport type MotorDifficultyLevel = 'none' | 'mild' | 'moderate' | 'severe';\n\n// Touch target configuration\nexport interface TouchTargetConfig {\n  minSize: number;\n  preferredSize: number;\n  spacing: number;\n  adaptiveSize: boolean;\n  motorAccessibility: MotorDifficultyLevel;\n}\n\n// Touch area interface\nexport interface TouchArea {\n  width: number;\n  height: number;\n  paddingHorizontal?: number;\n  paddingVertical?: number;\n  minTouchableArea?: number;\n}\n\n/**\n * Get platform-specific minimum touch target size\n */\nexport const getPlatformMinimumTouchTarget = (): number => {\n  return Platform.select({\n    ios: TOUCH_TARGET_SIZES.IOS_MINIMUM,\n    android: TOUCH_TARGET_SIZES.ANDROID_MINIMUM,\n    default: TOUCH_TARGET_SIZES.MINIMUM,\n  });\n};\n\n/**\n * Get recommended touch target size based on motor accessibility level\n */\nexport const getRecommendedTouchTargetSize = (\n  motorLevel: MotorDifficultyLevel = 'none'\n): number => {\n  switch (motorLevel) {\n    case 'none':\n      return TOUCH_TARGET_SIZES.COMFORTABLE;\n    case 'mild':\n      return TOUCH_TARGET_SIZES.LARGE;\n    case 'moderate':\n      return TOUCH_TARGET_SIZES.EXTRA_LARGE;\n    case 'severe':\n      return TOUCH_TARGET_SIZES.EXTRA_LARGE + 8;\n    default:\n      return TOUCH_TARGET_SIZES.COMFORTABLE;\n  }\n};\n\n/**\n * Get recommended spacing between touch targets\n */\nexport const getRecommendedSpacing = (\n  motorLevel: MotorDifficultyLevel = 'none'\n): number => {\n  switch (motorLevel) {\n    case 'none':\n      return TOUCH_TARGET_SPACING.MINIMUM;\n    case 'mild':\n      return TOUCH_TARGET_SPACING.COMFORTABLE;\n    case 'moderate':\n    case 'severe':\n      return TOUCH_TARGET_SPACING.LARGE;\n    default:\n      return TOUCH_TARGET_SPACING.MINIMUM;\n  }\n};\n\n/**\n * Validate if a touch target meets accessibility requirements\n */\nexport const validateTouchTarget = (\n  width: number,\n  height: number,\n  motorLevel: MotorDifficultyLevel = 'none'\n): {\n  valid: boolean;\n  issues: string[];\n  recommendations: string[];\n} => {\n  const issues: string[] = [];\n  const recommendations: string[] = [];\n  \n  const minSize = getPlatformMinimumTouchTarget();\n  const recommendedSize = getRecommendedTouchTargetSize(motorLevel);\n  \n  // Check minimum size requirements\n  if (width < minSize || height < minSize) {\n    issues.push(`Touch target too small: ${width}x${height}px (minimum: ${minSize}x${minSize}px)`);\n    recommendations.push(`Increase touch target size to at least ${minSize}x${minSize}px`);\n  }\n  \n  // Check recommended size for motor accessibility\n  if (motorLevel !== 'none' && (width < recommendedSize || height < recommendedSize)) {\n    recommendations.push(\n      `For ${motorLevel} motor difficulty, consider increasing to ${recommendedSize}x${recommendedSize}px`\n    );\n  }\n  \n  // Check aspect ratio\n  const aspectRatio = Math.max(width, height) / Math.min(width, height);\n  if (aspectRatio > 3) {\n    recommendations.push('Consider more balanced aspect ratio for better usability');\n  }\n  \n  return {\n    valid: issues.length === 0,\n    issues,\n    recommendations,\n  };\n};\n\n/**\n * Calculate optimal touch area with padding\n */\nexport const calculateTouchArea = (\n  contentWidth: number,\n  contentHeight: number,\n  motorLevel: MotorDifficultyLevel = 'none'\n): TouchArea => {\n  const minTouchSize = getRecommendedTouchTargetSize(motorLevel);\n  \n  // Calculate required padding to meet minimum touch target\n  const paddingHorizontal = Math.max(0, (minTouchSize - contentWidth) / 2);\n  const paddingVertical = Math.max(0, (minTouchSize - contentHeight) / 2);\n  \n  return {\n    width: Math.max(contentWidth, minTouchSize),\n    height: Math.max(contentHeight, minTouchSize),\n    paddingHorizontal,\n    paddingVertical,\n    minTouchableArea: minTouchSize,\n  };\n};\n\n/**\n * Generate touch target styles for React Native components\n */\nexport const generateTouchTargetStyles = (\n  contentSize: { width?: number; height?: number } = {},\n  motorLevel: MotorDifficultyLevel = 'none'\n) => {\n  const minSize = getRecommendedTouchTargetSize(motorLevel);\n  const spacing = getRecommendedSpacing(motorLevel);\n  \n  return {\n    minWidth: contentSize.width ? Math.max(contentSize.width, minSize) : minSize,\n    minHeight: contentSize.height ? Math.max(contentSize.height, minSize) : minSize,\n    paddingHorizontal: contentSize.width ? Math.max(0, (minSize - contentSize.width) / 2) : 0,\n    paddingVertical: contentSize.height ? Math.max(0, (minSize - contentSize.height) / 2) : 0,\n    marginHorizontal: spacing / 2,\n    marginVertical: spacing / 2,\n  };\n};\n\n/**\n * Check if device supports motor accessibility features\n */\nexport const getMotorAccessibilitySupport = () => {\n  return {\n    // Platform capabilities\n    supportsHapticFeedback: Platform.OS === 'ios' || Platform.OS === 'android',\n    supportsVoiceControl: Platform.OS === 'ios',\n    supportsSwitchControl: Platform.OS === 'ios',\n    supportsAssistiveTouch: Platform.OS === 'ios',\n    \n    // Screen size considerations\n    screenSize: Dimensions.get('window'),\n    isLargeScreen: Dimensions.get('window').width > 768,\n    \n    // Recommended adaptations\n    recommendLargerTargets: true,\n    recommendHapticFeedback: true,\n    recommendVoiceAlternatives: true,\n  };\n};\n\n/**\n * Calculate adaptive touch target size based on user interaction patterns\n */\nexport const calculateAdaptiveTouchTargetSize = (\n  baseSize: number,\n  userInteractionData: {\n    missRate?: number;\n    averageAccuracy?: number;\n    preferredSize?: number;\n  } = {}\n): number => {\n  let adaptedSize = baseSize;\n  \n  // Increase size based on miss rate\n  if (userInteractionData.missRate && userInteractionData.missRate > 0.1) {\n    adaptedSize += Math.min(16, userInteractionData.missRate * 80);\n  }\n  \n  // Adjust based on accuracy\n  if (userInteractionData.averageAccuracy && userInteractionData.averageAccuracy < 0.8) {\n    adaptedSize += Math.min(12, (1 - userInteractionData.averageAccuracy) * 60);\n  }\n  \n  // Use preferred size if available\n  if (userInteractionData.preferredSize) {\n    adaptedSize = Math.max(adaptedSize, userInteractionData.preferredSize);\n  }\n  \n  // Ensure minimum requirements\n  adaptedSize = Math.max(adaptedSize, getPlatformMinimumTouchTarget());\n  \n  return Math.round(adaptedSize);\n};\n\n/**\n * Generate gesture-friendly touch area\n */\nexport const generateGestureFriendlyArea = (\n  gestureType: 'tap' | 'swipe' | 'pinch' | 'long-press' | 'drag',\n  motorLevel: MotorDifficultyLevel = 'none'\n) => {\n  const baseSize = getRecommendedTouchTargetSize(motorLevel);\n  \n  switch (gestureType) {\n    case 'tap':\n      return {\n        width: baseSize,\n        height: baseSize,\n        hitSlop: { top: 8, bottom: 8, left: 8, right: 8 },\n      };\n    \n    case 'swipe':\n      return {\n        width: baseSize * 1.5,\n        height: baseSize,\n        hitSlop: { top: 12, bottom: 12, left: 16, right: 16 },\n      };\n    \n    case 'pinch':\n      return {\n        width: baseSize * 2,\n        height: baseSize * 2,\n        hitSlop: { top: 16, bottom: 16, left: 16, right: 16 },\n      };\n    \n    case 'long-press':\n      return {\n        width: baseSize * 1.2,\n        height: baseSize * 1.2,\n        hitSlop: { top: 10, bottom: 10, left: 10, right: 10 },\n      };\n    \n    case 'drag':\n      return {\n        width: baseSize * 1.3,\n        height: baseSize * 1.3,\n        hitSlop: { top: 12, bottom: 12, left: 12, right: 12 },\n      };\n    \n    default:\n      return {\n        width: baseSize,\n        height: baseSize,\n        hitSlop: { top: 8, bottom: 8, left: 8, right: 8 },\n      };\n  }\n};\n\n/**\n * Validate touch target spacing\n */\nexport const validateTouchTargetSpacing = (\n  targets: Array<{ x: number; y: number; width: number; height: number }>,\n  motorLevel: MotorDifficultyLevel = 'none'\n): {\n  valid: boolean;\n  conflicts: Array<{ target1: number; target2: number; distance: number }>;\n  recommendations: string[];\n} => {\n  const minSpacing = getRecommendedSpacing(motorLevel);\n  const conflicts: Array<{ target1: number; target2: number; distance: number }> = [];\n  const recommendations: string[] = [];\n  \n  for (let i = 0; i < targets.length; i++) {\n    for (let j = i + 1; j < targets.length; j++) {\n      const target1 = targets[i];\n      const target2 = targets[j];\n      \n      // Calculate distance between target edges\n      const horizontalDistance = Math.max(0, \n        Math.max(target1.x, target2.x) - Math.min(target1.x + target1.width, target2.x + target2.width)\n      );\n      const verticalDistance = Math.max(0,\n        Math.max(target1.y, target2.y) - Math.min(target1.y + target1.height, target2.y + target2.height)\n      );\n      \n      const distance = Math.sqrt(horizontalDistance ** 2 + verticalDistance ** 2);\n      \n      if (distance < minSpacing) {\n        conflicts.push({ target1: i, target2: j, distance });\n      }\n    }\n  }\n  \n  if (conflicts.length > 0) {\n    recommendations.push(`Increase spacing between touch targets to at least ${minSpacing}px`);\n    if (motorLevel !== 'none') {\n      recommendations.push(`Consider additional spacing for ${motorLevel} motor accessibility`);\n    }\n  }\n  \n  return {\n    valid: conflicts.length === 0,\n    conflicts,\n    recommendations,\n  };\n};\n\nexport default {\n  TOUCH_TARGET_SIZES,\n  TOUCH_TARGET_SPACING,\n  getPlatformMinimumTouchTarget,\n  getRecommendedTouchTargetSize,\n  getRecommendedSpacing,\n  validateTouchTarget,\n  calculateTouchArea,\n  generateTouchTargetStyles,\n  getMotorAccessibilitySupport,\n  calculateAdaptiveTouchTargetSize,\n  generateGestureFriendlyArea,\n  validateTouchTargetSpacing,\n};\n"], "mappings": ";;;;AAiBA,IAAAA,YAAA,GAAAC,OAAA;AAGO,IAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG;EAEhCE,OAAO,EAAE,EAAE;EAGXC,WAAW,EAAE,EAAE;EACfC,KAAK,EAAE,EAAE;EACTC,WAAW,EAAE,EAAE;EAGfC,WAAW,EAAE,EAAE;EACfC,eAAe,EAAE,EAAE;EAGnBC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,EAAE;EACfC,GAAG,EAAE,EAAE;EACPC,eAAe,EAAE,EAAE;EACnBC,UAAU,EAAE;AACd,CAAU;AAGH,IAAMC,oBAAoB,GAAAZ,OAAA,CAAAY,oBAAA,GAAG;EAClCX,OAAO,EAAE,CAAC;EACVC,WAAW,EAAE,EAAE;EACfC,KAAK,EAAE;AACT,CAAU;AA0BH,IAAMU,6BAA6B,GAAAb,OAAA,CAAAa,6BAAA,GAAG,SAAhCA,6BAA6BA,CAAA,EAAiB;EACzD,OAAOC,qBAAQ,CAACC,MAAM,CAAC;IACrBC,GAAG,EAAEjB,kBAAkB,CAACM,WAAW;IACnCY,OAAO,EAAElB,kBAAkB,CAACO,eAAe;IAC3CY,OAAO,EAAEnB,kBAAkB,CAACE;EAC9B,CAAC,CAAC;AACJ,CAAC;AAKM,IAAMkB,6BAA6B,GAAAnB,OAAA,CAAAmB,6BAAA,GAAG,SAAhCA,6BAA6BA,CAAA,EAE7B;EAAA,IADXC,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAEzC,QAAQD,UAAU;IAChB,KAAK,MAAM;MACT,OAAOrB,kBAAkB,CAACG,WAAW;IACvC,KAAK,MAAM;MACT,OAAOH,kBAAkB,CAACI,KAAK;IACjC,KAAK,UAAU;MACb,OAAOJ,kBAAkB,CAACK,WAAW;IACvC,KAAK,QAAQ;MACX,OAAOL,kBAAkB,CAACK,WAAW,GAAG,CAAC;IAC3C;MACE,OAAOL,kBAAkB,CAACG,WAAW;EACzC;AACF,CAAC;AAKM,IAAMsB,qBAAqB,GAAAxB,OAAA,CAAAwB,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA,EAErB;EAAA,IADXJ,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAEzC,QAAQD,UAAU;IAChB,KAAK,MAAM;MACT,OAAOR,oBAAoB,CAACX,OAAO;IACrC,KAAK,MAAM;MACT,OAAOW,oBAAoB,CAACV,WAAW;IACzC,KAAK,UAAU;IACf,KAAK,QAAQ;MACX,OAAOU,oBAAoB,CAACT,KAAK;IACnC;MACE,OAAOS,oBAAoB,CAACX,OAAO;EACvC;AACF,CAAC;AAKM,IAAMwB,mBAAmB,GAAAzB,OAAA,CAAAyB,mBAAA,GAAG,SAAtBA,mBAAmBA,CAC9BC,KAAa,EACbC,MAAc,EAMX;EAAA,IALHP,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAMzC,IAAMO,MAAgB,GAAG,EAAE;EAC3B,IAAMC,eAAyB,GAAG,EAAE;EAEpC,IAAMC,OAAO,GAAGjB,6BAA6B,CAAC,CAAC;EAC/C,IAAMkB,eAAe,GAAGZ,6BAA6B,CAACC,UAAU,CAAC;EAGjE,IAAIM,KAAK,GAAGI,OAAO,IAAIH,MAAM,GAAGG,OAAO,EAAE;IACvCF,MAAM,CAACI,IAAI,CAAC,2BAA2BN,KAAK,IAAIC,MAAM,gBAAgBG,OAAO,IAAIA,OAAO,KAAK,CAAC;IAC9FD,eAAe,CAACG,IAAI,CAAC,0CAA0CF,OAAO,IAAIA,OAAO,IAAI,CAAC;EACxF;EAGA,IAAIV,UAAU,KAAK,MAAM,KAAKM,KAAK,GAAGK,eAAe,IAAIJ,MAAM,GAAGI,eAAe,CAAC,EAAE;IAClFF,eAAe,CAACG,IAAI,CAClB,OAAOZ,UAAU,6CAA6CW,eAAe,IAAIA,eAAe,IAClG,CAAC;EACH;EAGA,IAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACT,KAAK,EAAEC,MAAM,CAAC,GAAGO,IAAI,CAACE,GAAG,CAACV,KAAK,EAAEC,MAAM,CAAC;EACrE,IAAIM,WAAW,GAAG,CAAC,EAAE;IACnBJ,eAAe,CAACG,IAAI,CAAC,0DAA0D,CAAC;EAClF;EAEA,OAAO;IACLK,KAAK,EAAET,MAAM,CAACN,MAAM,KAAK,CAAC;IAC1BM,MAAM,EAANA,MAAM;IACNC,eAAe,EAAfA;EACF,CAAC;AACH,CAAC;AAKM,IAAMS,kBAAkB,GAAAtC,OAAA,CAAAsC,kBAAA,GAAG,SAArBA,kBAAkBA,CAC7BC,YAAoB,EACpBC,aAAqB,EAEP;EAAA,IADdpB,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAEzC,IAAMoB,YAAY,GAAGtB,6BAA6B,CAACC,UAAU,CAAC;EAG9D,IAAMsB,iBAAiB,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACM,YAAY,GAAGF,YAAY,IAAI,CAAC,CAAC;EACxE,IAAMI,eAAe,GAAGT,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACM,YAAY,GAAGD,aAAa,IAAI,CAAC,CAAC;EAEvE,OAAO;IACLd,KAAK,EAAEQ,IAAI,CAACC,GAAG,CAACI,YAAY,EAAEE,YAAY,CAAC;IAC3Cd,MAAM,EAAEO,IAAI,CAACC,GAAG,CAACK,aAAa,EAAEC,YAAY,CAAC;IAC7CC,iBAAiB,EAAjBA,iBAAiB;IACjBC,eAAe,EAAfA,eAAe;IACfC,gBAAgB,EAAEH;EACpB,CAAC;AACH,CAAC;AAKM,IAAMI,yBAAyB,GAAA7C,OAAA,CAAA6C,yBAAA,GAAG,SAA5BA,yBAAyBA,CAAA,EAGjC;EAAA,IAFHC,WAAgD,GAAAzB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IACrDD,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAEzC,IAAMS,OAAO,GAAGX,6BAA6B,CAACC,UAAU,CAAC;EACzD,IAAM2B,OAAO,GAAGvB,qBAAqB,CAACJ,UAAU,CAAC;EAEjD,OAAO;IACL4B,QAAQ,EAAEF,WAAW,CAACpB,KAAK,GAAGQ,IAAI,CAACC,GAAG,CAACW,WAAW,CAACpB,KAAK,EAAEI,OAAO,CAAC,GAAGA,OAAO;IAC5EmB,SAAS,EAAEH,WAAW,CAACnB,MAAM,GAAGO,IAAI,CAACC,GAAG,CAACW,WAAW,CAACnB,MAAM,EAAEG,OAAO,CAAC,GAAGA,OAAO;IAC/EY,iBAAiB,EAAEI,WAAW,CAACpB,KAAK,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACL,OAAO,GAAGgB,WAAW,CAACpB,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC;IACzFiB,eAAe,EAAEG,WAAW,CAACnB,MAAM,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACL,OAAO,GAAGgB,WAAW,CAACnB,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;IACzFuB,gBAAgB,EAAEH,OAAO,GAAG,CAAC;IAC7BI,cAAc,EAAEJ,OAAO,GAAG;EAC5B,CAAC;AACH,CAAC;AAKM,IAAMK,4BAA4B,GAAApD,OAAA,CAAAoD,4BAAA,GAAG,SAA/BA,4BAA4BA,CAAA,EAAS;EAChD,OAAO;IAELC,sBAAsB,EAAEvC,qBAAQ,CAACwC,EAAE,KAAK,KAAK,IAAIxC,qBAAQ,CAACwC,EAAE,KAAK,SAAS;IAC1EC,oBAAoB,EAAEzC,qBAAQ,CAACwC,EAAE,KAAK,KAAK;IAC3CE,qBAAqB,EAAE1C,qBAAQ,CAACwC,EAAE,KAAK,KAAK;IAC5CG,sBAAsB,EAAE3C,qBAAQ,CAACwC,EAAE,KAAK,KAAK;IAG7CI,UAAU,EAAEC,uBAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;IACpCC,aAAa,EAAEF,uBAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAAClC,KAAK,GAAG,GAAG;IAGnDoC,sBAAsB,EAAE,IAAI;IAC5BC,uBAAuB,EAAE,IAAI;IAC7BC,0BAA0B,EAAE;EAC9B,CAAC;AACH,CAAC;AAKM,IAAMC,gCAAgC,GAAAjE,OAAA,CAAAiE,gCAAA,GAAG,SAAnCA,gCAAgCA,CAC3CC,QAAgB,EAML;EAAA,IALXC,mBAIC,GAAA9C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEN,IAAI+C,WAAW,GAAGF,QAAQ;EAG1B,IAAIC,mBAAmB,CAACE,QAAQ,IAAIF,mBAAmB,CAACE,QAAQ,GAAG,GAAG,EAAE;IACtED,WAAW,IAAIlC,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE+B,mBAAmB,CAACE,QAAQ,GAAG,EAAE,CAAC;EAChE;EAGA,IAAIF,mBAAmB,CAACG,eAAe,IAAIH,mBAAmB,CAACG,eAAe,GAAG,GAAG,EAAE;IACpFF,WAAW,IAAIlC,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG+B,mBAAmB,CAACG,eAAe,IAAI,EAAE,CAAC;EAC7E;EAGA,IAAIH,mBAAmB,CAACI,aAAa,EAAE;IACrCH,WAAW,GAAGlC,IAAI,CAACC,GAAG,CAACiC,WAAW,EAAED,mBAAmB,CAACI,aAAa,CAAC;EACxE;EAGAH,WAAW,GAAGlC,IAAI,CAACC,GAAG,CAACiC,WAAW,EAAEvD,6BAA6B,CAAC,CAAC,CAAC;EAEpE,OAAOqB,IAAI,CAACsC,KAAK,CAACJ,WAAW,CAAC;AAChC,CAAC;AAKM,IAAMK,2BAA2B,GAAAzE,OAAA,CAAAyE,2BAAA,GAAG,SAA9BA,2BAA2BA,CACtCC,WAA8D,EAE3D;EAAA,IADHtD,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAEzC,IAAM6C,QAAQ,GAAG/C,6BAA6B,CAACC,UAAU,CAAC;EAE1D,QAAQsD,WAAW;IACjB,KAAK,KAAK;MACR,OAAO;QACLhD,KAAK,EAAEwC,QAAQ;QACfvC,MAAM,EAAEuC,QAAQ;QAChBS,OAAO,EAAE;UAAEC,GAAG,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE;MAClD,CAAC;IAEH,KAAK,OAAO;MACV,OAAO;QACLrD,KAAK,EAAEwC,QAAQ,GAAG,GAAG;QACrBvC,MAAM,EAAEuC,QAAQ;QAChBS,OAAO,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG;MACtD,CAAC;IAEH,KAAK,OAAO;MACV,OAAO;QACLrD,KAAK,EAAEwC,QAAQ,GAAG,CAAC;QACnBvC,MAAM,EAAEuC,QAAQ,GAAG,CAAC;QACpBS,OAAO,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG;MACtD,CAAC;IAEH,KAAK,YAAY;MACf,OAAO;QACLrD,KAAK,EAAEwC,QAAQ,GAAG,GAAG;QACrBvC,MAAM,EAAEuC,QAAQ,GAAG,GAAG;QACtBS,OAAO,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG;MACtD,CAAC;IAEH,KAAK,MAAM;MACT,OAAO;QACLrD,KAAK,EAAEwC,QAAQ,GAAG,GAAG;QACrBvC,MAAM,EAAEuC,QAAQ,GAAG,GAAG;QACtBS,OAAO,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG;MACtD,CAAC;IAEH;MACE,OAAO;QACLrD,KAAK,EAAEwC,QAAQ;QACfvC,MAAM,EAAEuC,QAAQ;QAChBS,OAAO,EAAE;UAAEC,GAAG,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE;MAClD,CAAC;EACL;AACF,CAAC;AAKM,IAAMC,0BAA0B,GAAAhF,OAAA,CAAAgF,0BAAA,GAAG,SAA7BA,0BAA0BA,CACrCC,OAAuE,EAMpE;EAAA,IALH7D,UAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAMzC,IAAM6D,UAAU,GAAG1D,qBAAqB,CAACJ,UAAU,CAAC;EACpD,IAAM+D,SAAwE,GAAG,EAAE;EACnF,IAAMtD,eAAyB,GAAG,EAAE;EAEpC,KAAK,IAAIuD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAAC3D,MAAM,EAAE8D,CAAC,EAAE,EAAE;IACvC,KAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,OAAO,CAAC3D,MAAM,EAAE+D,CAAC,EAAE,EAAE;MAC3C,IAAMC,OAAO,GAAGL,OAAO,CAACG,CAAC,CAAC;MAC1B,IAAMG,OAAO,GAAGN,OAAO,CAACI,CAAC,CAAC;MAG1B,IAAMG,kBAAkB,GAAGtD,IAAI,CAACC,GAAG,CAAC,CAAC,EACnCD,IAAI,CAACC,GAAG,CAACmD,OAAO,CAACG,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,GAAGvD,IAAI,CAACE,GAAG,CAACkD,OAAO,CAACG,CAAC,GAAGH,OAAO,CAAC5D,KAAK,EAAE6D,OAAO,CAACE,CAAC,GAAGF,OAAO,CAAC7D,KAAK,CAChG,CAAC;MACD,IAAMgE,gBAAgB,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAAC,EACjCD,IAAI,CAACC,GAAG,CAACmD,OAAO,CAACK,CAAC,EAAEJ,OAAO,CAACI,CAAC,CAAC,GAAGzD,IAAI,CAACE,GAAG,CAACkD,OAAO,CAACK,CAAC,GAAGL,OAAO,CAAC3D,MAAM,EAAE4D,OAAO,CAACI,CAAC,GAAGJ,OAAO,CAAC5D,MAAM,CAClG,CAAC;MAED,IAAMiE,QAAQ,GAAG1D,IAAI,CAAC2D,IAAI,CAACL,kBAAkB,IAAI,CAAC,GAAGE,gBAAgB,IAAI,CAAC,CAAC;MAE3E,IAAIE,QAAQ,GAAGV,UAAU,EAAE;QACzBC,SAAS,CAACnD,IAAI,CAAC;UAAEsD,OAAO,EAAEF,CAAC;UAAEG,OAAO,EAAEF,CAAC;UAAEO,QAAQ,EAARA;QAAS,CAAC,CAAC;MACtD;IACF;EACF;EAEA,IAAIT,SAAS,CAAC7D,MAAM,GAAG,CAAC,EAAE;IACxBO,eAAe,CAACG,IAAI,CAAC,sDAAsDkD,UAAU,IAAI,CAAC;IAC1F,IAAI9D,UAAU,KAAK,MAAM,EAAE;MACzBS,eAAe,CAACG,IAAI,CAAC,mCAAmCZ,UAAU,sBAAsB,CAAC;IAC3F;EACF;EAEA,OAAO;IACLiB,KAAK,EAAE8C,SAAS,CAAC7D,MAAM,KAAK,CAAC;IAC7B6D,SAAS,EAATA,SAAS;IACTtD,eAAe,EAAfA;EACF,CAAC;AACH,CAAC;AAAC,IAAAiE,QAAA,GAAA9F,OAAA,CAAAkB,OAAA,GAEa;EACbnB,kBAAkB,EAAlBA,kBAAkB;EAClBa,oBAAoB,EAApBA,oBAAoB;EACpBC,6BAA6B,EAA7BA,6BAA6B;EAC7BM,6BAA6B,EAA7BA,6BAA6B;EAC7BK,qBAAqB,EAArBA,qBAAqB;EACrBC,mBAAmB,EAAnBA,mBAAmB;EACnBa,kBAAkB,EAAlBA,kBAAkB;EAClBO,yBAAyB,EAAzBA,yBAAyB;EACzBO,4BAA4B,EAA5BA,4BAA4B;EAC5Ba,gCAAgC,EAAhCA,gCAAgC;EAChCQ,2BAA2B,EAA3BA,2BAA2B;EAC3BO,0BAA0B,EAA1BA;AACF,CAAC", "ignoreList": []}