/**
 * Form Context Provider
 *
 * Comprehensive form state management with enhanced UX patterns,
 * validation, and accessibility features.
 *
 * Features:
 * - Centralized form state
 * - Real-time validation
 * - Auto-save functionality
 * - Progress tracking
 * - Error recovery
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Platform, AccessibilityInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  FormValidationConfig,
  ValidationResult,
  validateForm,
  isFormValid,
  getFormErrorsSummary,
} from '../utils/formValidationUtils';

// Form state interface
export interface FormState {
  values: Record<string, string>;
  errors: Record<string, ValidationResult>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
  submitCount: number;
}

// Form context interface
interface FormContextType {
  // State
  formState: FormState;
  
  // Field operations
  setValue: (field: string, value: string) => void;
  setFieldTouched: (field: string, touched?: boolean) => void;
  setFieldError: (field: string, error: ValidationResult) => void;
  
  // Form operations
  validateField: (field: string) => ValidationResult;
  validateForm: () => boolean;
  resetForm: (values?: Record<string, string>) => void;
  submitForm: (onSubmit: (values: Record<string, string>) => Promise<void>) => Promise<void>;
  
  // Auto-save
  enableAutoSave: (key: string, interval?: number) => void;
  disableAutoSave: () => void;
  
  // Progress tracking
  getFormProgress: () => number;
  getCompletedFields: () => string[];
  getRequiredFields: () => string[];
  
  // Utilities
  getFieldProps: (field: string) => any;
  getFormErrorsSummary: () => string[];
  canSubmit: () => boolean;
}

// Create context
const FormContext = createContext<FormContextType | undefined>(undefined);

// Provider props
interface FormProviderProps {
  children: React.ReactNode;
  initialValues?: Record<string, string>;
  validationConfig?: FormValidationConfig;
  onSubmit?: (values: Record<string, string>) => Promise<void>;
  enableProgressTracking?: boolean;
}

export const FormProvider: React.FC<FormProviderProps> = ({
  children,
  initialValues = {},
  validationConfig,
  onSubmit,
  enableProgressTracking = true,
}) => {
  // State
  const [formState, setFormState] = useState<FormState>({
    values: initialValues,
    errors: {},
    touched: {},
    isSubmitting: false,
    isValid: true,
    isDirty: false,
    submitCount: 0,
  });

  // Refs
  const autoSaveIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveKeyRef = useRef<string | null>(null);
  const validationConfigRef = useRef(validationConfig);

  // Update validation config ref
  useEffect(() => {
    validationConfigRef.current = validationConfig;
  }, [validationConfig]);

  // Set field value
  const setValue = useCallback((field: string, value: string) => {
    setFormState(prev => {
      const newValues = { ...prev.values, [field]: value };
      const newState = {
        ...prev,
        values: newValues,
        isDirty: true,
      };

      // Validate field if config exists
      if (validationConfigRef.current?.fields[field]) {
        const fieldValidation = validationConfigRef.current.fields[field];
        const result = validateForm(newValues, validationConfigRef.current);
        newState.errors = result;
        newState.isValid = isFormValid(result);
      }

      return newState;
    });
  }, []);

  // Set field touched
  const setFieldTouched = useCallback((field: string, touched: boolean = true) => {
    setFormState(prev => ({
      ...prev,
      touched: { ...prev.touched, [field]: touched },
    }));
  }, []);

  // Set field error
  const setFieldError = useCallback((field: string, error: ValidationResult) => {
    setFormState(prev => ({
      ...prev,
      errors: { ...prev.errors, [field]: error },
      isValid: isFormValid({ ...prev.errors, [field]: error }),
    }));
  }, []);

  // Validate single field
  const validateFieldFn = useCallback((field: string): ValidationResult => {
    if (!validationConfigRef.current?.fields[field]) {
      return { isValid: true, errors: [], warnings: [], suggestions: [] };
    }

    const fieldValidation = validationConfigRef.current.fields[field];
    const value = formState.values[field] || '';
    
    // Import validateField function here to avoid circular dependency
    const { validateField } = require('../utils/formValidationUtils');
    return validateField(value, field, fieldValidation, formState.values);
  }, [formState.values]);

  // Validate entire form
  const validateFormFn = useCallback((): boolean => {
    if (!validationConfigRef.current) return true;

    const results = validateForm(formState.values, validationConfigRef.current);
    const valid = isFormValid(results);

    setFormState(prev => ({
      ...prev,
      errors: results,
      isValid: valid,
    }));

    return valid;
  }, [formState.values]);

  // Reset form
  const resetForm = useCallback((values?: Record<string, string>) => {
    setFormState({
      values: values || initialValues,
      errors: {},
      touched: {},
      isSubmitting: false,
      isValid: true,
      isDirty: false,
      submitCount: 0,
    });
  }, [initialValues]);

  // Submit form
  const submitForm = useCallback(async (submitFn?: (values: Record<string, string>) => Promise<void>) => {
    setFormState(prev => ({ ...prev, isSubmitting: true, submitCount: prev.submitCount + 1 }));

    try {
      // Validate form before submission
      const isValid = validateFormFn();
      
      if (!isValid) {
        // Announce validation errors to screen readers
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          const errorSummary = getFormErrorsSummary(formState.errors);
          if (errorSummary.length > 0) {
            AccessibilityInfo.announceForAccessibility(
              `Form has ${errorSummary.length} errors. ${errorSummary[0]}`
            );
          }
        }
        return;
      }

      // Submit form
      const submitFunction = submitFn || onSubmit;
      if (submitFunction) {
        await submitFunction(formState.values);
        
        // Announce success
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          AccessibilityInfo.announceForAccessibility('Form submitted successfully');
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      
      // Announce error
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        AccessibilityInfo.announceForAccessibility('Form submission failed. Please try again.');
      }
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [validateFormFn, formState.errors, formState.values, onSubmit]);

  // Auto-save functionality
  const enableAutoSave = useCallback((key: string, interval: number = 30000) => {
    autoSaveKeyRef.current = key;
    
    if (autoSaveIntervalRef.current) {
      clearInterval(autoSaveIntervalRef.current);
    }

    autoSaveIntervalRef.current = setInterval(async () => {
      if (formState.isDirty) {
        try {
          await AsyncStorage.setItem(key, JSON.stringify(formState.values));
        } catch (error) {
          console.warn('Auto-save failed:', error);
        }
      }
    }, interval);
  }, [formState.isDirty, formState.values]);

  const disableAutoSave = useCallback(() => {
    if (autoSaveIntervalRef.current) {
      clearInterval(autoSaveIntervalRef.current);
      autoSaveIntervalRef.current = null;
    }
    autoSaveKeyRef.current = null;
  }, []);

  // Progress tracking
  const getFormProgress = useCallback((): number => {
    if (!enableProgressTracking || !validationConfigRef.current) return 0;

    const fields = Object.keys(validationConfigRef.current.fields);
    const completedFields = fields.filter(field => {
      const value = formState.values[field];
      const fieldConfig = validationConfigRef.current!.fields[field];
      
      if (fieldConfig.required) {
        return value && value.trim().length > 0;
      }
      
      return true; // Optional fields are considered complete
    });

    return fields.length > 0 ? (completedFields.length / fields.length) * 100 : 0;
  }, [enableProgressTracking, formState.values]);

  const getCompletedFields = useCallback((): string[] => {
    if (!validationConfigRef.current) return [];

    return Object.keys(validationConfigRef.current.fields).filter(field => {
      const value = formState.values[field];
      return value && value.trim().length > 0;
    });
  }, [formState.values]);

  const getRequiredFields = useCallback((): string[] => {
    if (!validationConfigRef.current) return [];

    return Object.keys(validationConfigRef.current.fields).filter(field => {
      return validationConfigRef.current!.fields[field].required;
    });
  }, []);

  // Get field props for easy integration
  const getFieldProps = useCallback((field: string) => {
    return {
      value: formState.values[field] || '',
      onChangeText: (value: string) => setValue(field, value),
      onBlur: () => setFieldTouched(field, true),
      error: formState.errors[field],
      touched: formState.touched[field],
    };
  }, [formState.values, formState.errors, formState.touched, setValue, setFieldTouched]);

  // Get form errors summary
  const getFormErrorsSummaryFn = useCallback((): string[] => {
    return getFormErrorsSummary(formState.errors);
  }, [formState.errors]);

  // Check if form can be submitted
  const canSubmit = useCallback((): boolean => {
    return formState.isValid && !formState.isSubmitting && formState.isDirty;
  }, [formState.isValid, formState.isSubmitting, formState.isDirty]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, []);

  // Load auto-saved data on mount
  useEffect(() => {
    const loadAutoSavedData = async () => {
      if (autoSaveKeyRef.current) {
        try {
          const saved = await AsyncStorage.getItem(autoSaveKeyRef.current);
          if (saved) {
            const parsedValues = JSON.parse(saved);
            setFormState(prev => ({
              ...prev,
              values: { ...initialValues, ...parsedValues },
            }));
          }
        } catch (error) {
          console.warn('Failed to load auto-saved data:', error);
        }
      }
    };

    loadAutoSavedData();
  }, [initialValues]);

  // Context value
  const contextValue: FormContextType = {
    // State
    formState,
    
    // Field operations
    setValue,
    setFieldTouched,
    setFieldError,
    
    // Form operations
    validateField: validateFieldFn,
    validateForm: validateFormFn,
    resetForm,
    submitForm,
    
    // Auto-save
    enableAutoSave,
    disableAutoSave,
    
    // Progress tracking
    getFormProgress,
    getCompletedFields,
    getRequiredFields,
    
    // Utilities
    getFieldProps,
    getFormErrorsSummary: getFormErrorsSummaryFn,
    canSubmit,
  };

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  );
};

// Hook to use form context
export const useForm = (): FormContextType => {
  const context = useContext(FormContext);
  
  if (context === undefined) {
    throw new Error('useForm must be used within a FormProvider');
  }
  
  return context;
};

// Convenience hooks
export const useFormField = (fieldName: string) => {
  const { getFieldProps, validateField, setFieldTouched } = useForm();
  return {
    ...getFieldProps(fieldName),
    validate: () => validateField(fieldName),
    touch: () => setFieldTouched(fieldName, true),
  };
};

export const useFormProgress = () => {
  const { getFormProgress, getCompletedFields, getRequiredFields } = useForm();
  return {
    progress: getFormProgress(),
    completedFields: getCompletedFields(),
    requiredFields: getRequiredFields(),
  };
};

export default FormProvider;
