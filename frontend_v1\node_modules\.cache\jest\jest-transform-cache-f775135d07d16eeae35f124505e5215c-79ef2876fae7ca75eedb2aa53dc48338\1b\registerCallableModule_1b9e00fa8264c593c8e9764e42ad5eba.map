{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "registerCallableModule", "global", "RN$Bridgeless", "name", "moduleOrFactory", "RN$registerCallableModule", "BatchedBridge", "require", "registerLazyCallableModule", "_default"], "sources": ["registerCallableModule.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\ntype Module = {...};\ntype RegisterCallableModule = (\n  name: string,\n  moduleOrFactory: Module | (void => Module),\n) => void;\n\nconst registerCallableModule: RegisterCallableModule = (function () {\n  if (global.RN$Bridgeless === true) {\n    return (name, moduleOrFactory) => {\n      if (typeof moduleOrFactory === 'function') {\n        global.RN$registerCallableModule(name, moduleOrFactory);\n        return;\n      }\n\n      global.RN$registerCallableModule(name, () => moduleOrFactory);\n    };\n  }\n\n  const BatchedBridge = require('../BatchedBridge/BatchedBridge').default;\n  return (name, moduleOrFactory) => {\n    if (typeof moduleOrFactory === 'function') {\n      BatchedBridge.registerLazyCallableModule(name, moduleOrFactory);\n      return;\n    }\n\n    BatchedBridge.registerCallableModule(name, moduleOrFactory);\n  };\n})();\n\nexport default registerCallableModule;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAQb,IAAMC,sBAA8C,GAAI,YAAY;EAClE,IAAIC,MAAM,CAACC,aAAa,KAAK,IAAI,EAAE;IACjC,OAAO,UAACC,IAAI,EAAEC,eAAe,EAAK;MAChC,IAAI,OAAOA,eAAe,KAAK,UAAU,EAAE;QACzCH,MAAM,CAACI,yBAAyB,CAACF,IAAI,EAAEC,eAAe,CAAC;QACvD;MACF;MAEAH,MAAM,CAACI,yBAAyB,CAACF,IAAI,EAAE;QAAA,OAAMC,eAAe;MAAA,EAAC;IAC/D,CAAC;EACH;EAEA,IAAME,aAAa,GAAGC,OAAO,iCAAiC,CAAC,CAACR,OAAO;EACvE,OAAO,UAACI,IAAI,EAAEC,eAAe,EAAK;IAChC,IAAI,OAAOA,eAAe,KAAK,UAAU,EAAE;MACzCE,aAAa,CAACE,0BAA0B,CAACL,IAAI,EAAEC,eAAe,CAAC;MAC/D;IACF;IAEAE,aAAa,CAACN,sBAAsB,CAACG,IAAI,EAAEC,eAAe,CAAC;EAC7D,CAAC;AACH,CAAC,CAAE,CAAC;AAAC,IAAAK,QAAA,GAAAZ,OAAA,CAAAE,OAAA,GAEUC,sBAAsB", "ignoreList": []}