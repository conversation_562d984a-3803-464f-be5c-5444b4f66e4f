{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "_NativeReactNativeFeatureFlags", "_interopRequireDefault", "_processAspectRatio", "_processBackgroundImage", "_processBoxShadow", "_processColor", "_processFilter", "_processFontVariant", "_processTransform", "_processTransformOrigin", "_<PERSON><PERSON><PERSON><PERSON>", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "colorAttributes", "process", "processColor", "ReactNativeStyleAttributes", "align<PERSON><PERSON><PERSON>", "alignItems", "alignSelf", "aspectRatio", "processAspectRatio", "borderBottomWidth", "borderEndWidth", "borderLeftWidth", "borderRightWidth", "borderStartWidth", "borderTopWidth", "boxSizing", "columnGap", "borderWidth", "bottom", "direction", "display", "end", "flex", "flexBasis", "flexDirection", "flexGrow", "flexShrink", "flexWrap", "gap", "height", "inset", "insetBlock", "insetBlockEnd", "insetBlockStart", "insetInline", "insetInlineEnd", "insetInlineStart", "justifyContent", "left", "margin", "marginBlock", "marginBlockEnd", "marginBlockStart", "marginBottom", "marginEnd", "marginHorizontal", "marginInline", "marginInlineEnd", "marginInlineStart", "marginLeft", "marginRight", "marginStart", "marginTop", "marginVertical", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "overflow", "padding", "paddingBlock", "paddingBlockEnd", "paddingBlockStart", "paddingBottom", "paddingEnd", "paddingHorizontal", "paddingInline", "paddingInlineEnd", "paddingInlineStart", "paddingLeft", "paddingRight", "paddingStart", "paddingTop", "paddingVertical", "position", "right", "rowGap", "start", "top", "width", "zIndex", "elevation", "shadowColor", "shadowOffset", "diff", "<PERSON><PERSON><PERSON><PERSON>", "shadowOpacity", "shadowRadius", "transform", "processTransform", "transform<PERSON><PERSON>in", "processTransformOrigin", "filter", "NativeReactNativeFeatureFlags", "enableNativeCSSParsing", "processFilter", "mixBlendMode", "isolation", "boxShadow", "processBoxShadow", "experimental_backgroundImage", "processBackgroundImage", "backfaceVisibility", "backgroundColor", "borderBlockColor", "borderBlockEndColor", "borderBlockStartColor", "borderBottomColor", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderColor", "borderCurve", "borderEndColor", "borderEndEndRadius", "borderEndStartRadius", "borderLeftColor", "borderRadius", "borderRightColor", "borderStartColor", "borderStartEndRadius", "borderStartStartRadius", "borderStyle", "borderTopColor", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "cursor", "opacity", "outlineColor", "outlineOffset", "outlineStyle", "outlineWidth", "pointerEvents", "color", "fontFamily", "fontSize", "fontStyle", "fontVariant", "processFontVariant", "fontWeight", "includeFontPadding", "letterSpacing", "lineHeight", "textAlign", "textAlignVertical", "textDecorationColor", "textDecorationLine", "textDecorationStyle", "textShadowColor", "textShadowOffset", "textShadowRadius", "textTransform", "userSelect", "verticalAlign", "writingDirection", "overlayColor", "resizeMode", "tintColor", "objectFit", "_default", "exports"], "sources": ["ReactNativeStyleAttributes.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format strict-local\n * @flow strict-local\n */\n\nimport type {AnyAttributeType} from '../../Renderer/shims/ReactNativeTypes';\n\nimport * as ReactNativeFeatureFlags from '../../../src/private/featureflags/ReactNativeFeatureFlags';\nimport NativeReactNativeFeatureFlags from '../../../src/private/featureflags/specs/NativeReactNativeFeatureFlags';\nimport processAspectRatio from '../../StyleSheet/processAspectRatio';\nimport processBackgroundImage from '../../StyleSheet/processBackgroundImage';\nimport processBoxShadow from '../../StyleSheet/processBoxShadow';\nimport processColor from '../../StyleSheet/processColor';\nimport processFilter from '../../StyleSheet/processFilter';\nimport processFontVariant from '../../StyleSheet/processFontVariant';\nimport processTransform from '../../StyleSheet/processTransform';\nimport processTransformOrigin from '../../StyleSheet/processTransformOrigin';\nimport sizesDiffer from '../../Utilities/differ/sizesDiffer';\n\nconst colorAttributes = {process: processColor};\n\nconst ReactNativeStyleAttributes: {[string]: AnyAttributeType, ...} = {\n  /**\n   * Layout\n   */\n  alignContent: true,\n  alignItems: true,\n  alignSelf: true,\n  aspectRatio: {process: processAspectRatio},\n  borderBottomWidth: true,\n  borderEndWidth: true,\n  borderLeftWidth: true,\n  borderRightWidth: true,\n  borderStartWidth: true,\n  borderTopWidth: true,\n  boxSizing: true,\n  columnGap: true,\n  borderWidth: true,\n  bottom: true,\n  direction: true,\n  display: true,\n  end: true,\n  flex: true,\n  flexBasis: true,\n  flexDirection: true,\n  flexGrow: true,\n  flexShrink: true,\n  flexWrap: true,\n  gap: true,\n  height: true,\n  inset: true,\n  insetBlock: true,\n  insetBlockEnd: true,\n  insetBlockStart: true,\n  insetInline: true,\n  insetInlineEnd: true,\n  insetInlineStart: true,\n  justifyContent: true,\n  left: true,\n  margin: true,\n  marginBlock: true,\n  marginBlockEnd: true,\n  marginBlockStart: true,\n  marginBottom: true,\n  marginEnd: true,\n  marginHorizontal: true,\n  marginInline: true,\n  marginInlineEnd: true,\n  marginInlineStart: true,\n  marginLeft: true,\n  marginRight: true,\n  marginStart: true,\n  marginTop: true,\n  marginVertical: true,\n  maxHeight: true,\n  maxWidth: true,\n  minHeight: true,\n  minWidth: true,\n  overflow: true,\n  padding: true,\n  paddingBlock: true,\n  paddingBlockEnd: true,\n  paddingBlockStart: true,\n  paddingBottom: true,\n  paddingEnd: true,\n  paddingHorizontal: true,\n  paddingInline: true,\n  paddingInlineEnd: true,\n  paddingInlineStart: true,\n  paddingLeft: true,\n  paddingRight: true,\n  paddingStart: true,\n  paddingTop: true,\n  paddingVertical: true,\n  position: true,\n  right: true,\n  rowGap: true,\n  start: true,\n  top: true,\n  width: true,\n  zIndex: true,\n\n  /**\n   * Shadow\n   */\n  elevation: true,\n  shadowColor: colorAttributes,\n  shadowOffset: {diff: sizesDiffer},\n  shadowOpacity: true,\n  shadowRadius: true,\n\n  /**\n   * Transform\n   */\n  transform: {process: processTransform},\n  transformOrigin: {process: processTransformOrigin},\n\n  /**\n   * Filter\n   */\n  filter:\n    NativeReactNativeFeatureFlags != null &&\n    ReactNativeFeatureFlags.enableNativeCSSParsing()\n      ? true\n      : {\n          process: processFilter,\n        },\n\n  /**\n   * MixBlendMode\n   */\n  mixBlendMode: true,\n\n  /**\n   * Isolation\n   */\n  isolation: true,\n\n  /*\n   * BoxShadow\n   */\n  boxShadow:\n    NativeReactNativeFeatureFlags != null &&\n    ReactNativeFeatureFlags.enableNativeCSSParsing()\n      ? true\n      : {\n          process: processBoxShadow,\n        },\n\n  /**\n   * Linear Gradient\n   */\n  experimental_backgroundImage: {process: processBackgroundImage},\n\n  /**\n   * View\n   */\n  backfaceVisibility: true,\n  backgroundColor: colorAttributes,\n  borderBlockColor: colorAttributes,\n  borderBlockEndColor: colorAttributes,\n  borderBlockStartColor: colorAttributes,\n  borderBottomColor: colorAttributes,\n  borderBottomEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderColor: colorAttributes,\n  borderCurve: true,\n  borderEndColor: colorAttributes,\n  borderEndEndRadius: true,\n  borderEndStartRadius: true,\n  borderLeftColor: colorAttributes,\n  borderRadius: true,\n  borderRightColor: colorAttributes,\n  borderStartColor: colorAttributes,\n  borderStartEndRadius: true,\n  borderStartStartRadius: true,\n  borderStyle: true,\n  borderTopColor: colorAttributes,\n  borderTopEndRadius: true,\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  cursor: true,\n  opacity: true,\n  outlineColor: colorAttributes,\n  outlineOffset: true,\n  outlineStyle: true,\n  outlineWidth: true,\n  pointerEvents: true,\n\n  /**\n   * Text\n   */\n  color: colorAttributes,\n  fontFamily: true,\n  fontSize: true,\n  fontStyle: true,\n  fontVariant: {process: processFontVariant},\n  fontWeight: true,\n  includeFontPadding: true,\n  letterSpacing: true,\n  lineHeight: true,\n  textAlign: true,\n  textAlignVertical: true,\n  textDecorationColor: colorAttributes,\n  textDecorationLine: true,\n  textDecorationStyle: true,\n  textShadowColor: colorAttributes,\n  textShadowOffset: true,\n  textShadowRadius: true,\n  textTransform: true,\n  userSelect: true,\n  verticalAlign: true,\n  writingDirection: true,\n\n  /**\n   * Image\n   */\n  overlayColor: colorAttributes,\n  resizeMode: true,\n  tintColor: colorAttributes,\n  objectFit: true,\n};\n\nexport default ReactNativeStyleAttributes;\n"], "mappings": ";;;;;AAYA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,8BAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,uBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,iBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,aAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,cAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,mBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,iBAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,uBAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,YAAA,GAAAT,sBAAA,CAAAF,OAAA;AAA6D,SAAAD,wBAAAa,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAf,uBAAA,YAAAA,wBAAAa,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE7D,IAAMmB,eAAe,GAAG;EAACC,OAAO,EAAEC;AAAY,CAAC;AAE/C,IAAMC,0BAA6D,GAAG;EAIpEC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE;IAACN,OAAO,EAAEO;EAAkB,CAAC;EAC1CC,iBAAiB,EAAE,IAAI;EACvBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,IAAI;EACfC,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,GAAG,EAAE,IAAI;EACTC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,IAAI;EACnBC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,IAAI;EACxBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EAKZC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAElF,eAAe;EAC5BmF,YAAY,EAAE;IAACC,IAAI,EAAEC;EAAW,CAAC;EACjCC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAKlBC,SAAS,EAAE;IAACvF,OAAO,EAAEwF;EAAgB,CAAC;EACtCC,eAAe,EAAE;IAACzF,OAAO,EAAE0F;EAAsB,CAAC;EAKlDC,MAAM,EACJC,sCAA6B,IAAI,IAAI,IACrC/H,uBAAuB,CAACgI,sBAAsB,CAAC,CAAC,GAC5C,IAAI,GACJ;IACE7F,OAAO,EAAE8F;EACX,CAAC;EAKPC,YAAY,EAAE,IAAI;EAKlBC,SAAS,EAAE,IAAI;EAKfC,SAAS,EACPL,sCAA6B,IAAI,IAAI,IACrC/H,uBAAuB,CAACgI,sBAAsB,CAAC,CAAC,GAC5C,IAAI,GACJ;IACE7F,OAAO,EAAEkG;EACX,CAAC;EAKPC,4BAA4B,EAAE;IAACnG,OAAO,EAAEoG;EAAsB,CAAC;EAK/DC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAEvG,eAAe;EAChCwG,gBAAgB,EAAExG,eAAe;EACjCyG,mBAAmB,EAAEzG,eAAe;EACpC0G,qBAAqB,EAAE1G,eAAe;EACtC2G,iBAAiB,EAAE3G,eAAe;EAClC4G,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE,IAAI;EAC5BC,uBAAuB,EAAE,IAAI;EAC7BC,uBAAuB,EAAE,IAAI;EAC7BC,WAAW,EAAEhH,eAAe;EAC5BiH,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAElH,eAAe;EAC/BmH,kBAAkB,EAAE,IAAI;EACxBC,oBAAoB,EAAE,IAAI;EAC1BC,eAAe,EAAErH,eAAe;EAChCsH,YAAY,EAAE,IAAI;EAClBC,gBAAgB,EAAEvH,eAAe;EACjCwH,gBAAgB,EAAExH,eAAe;EACjCyH,oBAAoB,EAAE,IAAI;EAC1BC,sBAAsB,EAAE,IAAI;EAC5BC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE5H,eAAe;EAC/B6H,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAEnI,eAAe;EAC7BoI,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EAKnBC,KAAK,EAAExI,eAAe;EACtByI,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE;IAAC3I,OAAO,EAAE4I;EAAkB,CAAC;EAC1CC,UAAU,EAAE,IAAI;EAChBC,kBAAkB,EAAE,IAAI;EACxBC,aAAa,EAAE,IAAI;EACnBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,iBAAiB,EAAE,IAAI;EACvBC,mBAAmB,EAAEpJ,eAAe;EACpCqJ,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,eAAe,EAAEvJ,eAAe;EAChCwJ,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,IAAI;EACnBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,IAAI;EAKtBC,YAAY,EAAE9J,eAAe;EAC7B+J,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAEhK,eAAe;EAC1BiK,SAAS,EAAE;AACb,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA7K,OAAA,GAEaa,0BAA0B", "ignoreList": []}