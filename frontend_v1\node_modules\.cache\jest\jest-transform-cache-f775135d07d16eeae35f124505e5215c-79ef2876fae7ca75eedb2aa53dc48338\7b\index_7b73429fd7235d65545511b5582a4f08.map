{"version": 3, "names": ["normalizeColor", "color", "matchers", "getMatchers", "match", "hex6", "exec", "parseInt", "colorFromKeyword", "normalizeKeyword", "rgb", "parse255", "rgba", "undefined", "parse1", "hex3", "hex8", "hex4", "hsl", "hslToRgb", "parse360", "parsePercentage", "hsla", "hwb", "hwbToRgb", "hue2rgb", "p", "q", "t", "h", "s", "l", "r", "g", "b", "Math", "round", "w", "gray", "red", "green", "blue", "NUMBER", "PERCENTAGE", "call", "_len", "arguments", "length", "args", "Array", "_key", "join", "callModern", "_len2", "_key2", "callWithSlashSeparator", "_len3", "_key3", "slice", "commaSeparatedCall", "_len4", "_key4", "cachedMatchers", "RegExp", "str", "int", "parseFloat", "num", "name", "module", "exports"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @noflow\n */\n\n/* eslint no-bitwise: 0 */\n\n'use strict';\n\nfunction normalizeColor(color) {\n  if (typeof color === 'number') {\n    if (color >>> 0 === color && color >= 0 && color <= 0xffffffff) {\n      return color;\n    }\n    return null;\n  }\n\n  if (typeof color !== 'string') {\n    return null;\n  }\n\n  const matchers = getMatchers();\n  let match;\n\n  // Ordered based on occurrences on Facebook codebase\n  if ((match = matchers.hex6.exec(color))) {\n    return parseInt(match[1] + 'ff', 16) >>> 0;\n  }\n\n  const colorFromKeyword = normalizeKeyword(color);\n  if (colorFromKeyword != null) {\n    return colorFromKeyword;\n  }\n\n  if ((match = matchers.rgb.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        0x000000ff) >>> // a\n      0\n    );\n  }\n\n  if ((match = matchers.rgba.exec(color))) {\n    // rgba(R G B / A) notation\n    if (match[6] !== undefined) {\n      return (\n        ((parse255(match[6]) << 24) | // r\n          (parse255(match[7]) << 16) | // g\n          (parse255(match[8]) << 8) | // b\n          parse1(match[9])) >>> // a\n        0\n      );\n    }\n\n    // rgba(R, G, B, A) notation\n    return (\n      ((parse255(match[2]) << 24) | // r\n        (parse255(match[3]) << 16) | // g\n        (parse255(match[4]) << 8) | // b\n        parse1(match[5])) >>> // a\n      0\n    );\n  }\n\n  if ((match = matchers.hex3.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          'ff', // a\n        16,\n      ) >>> 0\n    );\n  }\n\n  // https://drafts.csswg.org/css-color-4/#hex-notation\n  if ((match = matchers.hex8.exec(color))) {\n    return parseInt(match[1], 16) >>> 0;\n  }\n\n  if ((match = matchers.hex4.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          match[4] +\n          match[4], // a\n        16,\n      ) >>> 0\n    );\n  }\n\n  if ((match = matchers.hsl.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]), // l\n      ) |\n        0x000000ff) >>> // a\n      0\n    );\n  }\n\n  if ((match = matchers.hsla.exec(color))) {\n    // hsla(H S L / A) notation\n    if (match[6] !== undefined) {\n      return (\n        (hslToRgb(\n          parse360(match[6]), // h\n          parsePercentage(match[7]), // s\n          parsePercentage(match[8]), // l\n        ) |\n          parse1(match[9])) >>> // a\n        0\n      );\n    }\n\n    // hsla(H, S, L, A) notation\n    return (\n      (hslToRgb(\n        parse360(match[2]), // h\n        parsePercentage(match[3]), // s\n        parsePercentage(match[4]), // l\n      ) |\n        parse1(match[5])) >>> // a\n      0\n    );\n  }\n\n  if ((match = matchers.hwb.exec(color))) {\n    return (\n      (hwbToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // w\n        parsePercentage(match[3]), // b\n      ) |\n        0x000000ff) >>> // a\n      0\n    );\n  }\n\n  return null;\n}\n\nfunction hue2rgb(p, q, t) {\n  if (t < 0) {\n    t += 1;\n  }\n  if (t > 1) {\n    t -= 1;\n  }\n  if (t < 1 / 6) {\n    return p + (q - p) * 6 * t;\n  }\n  if (t < 1 / 2) {\n    return q;\n  }\n  if (t < 2 / 3) {\n    return p + (q - p) * (2 / 3 - t) * 6;\n  }\n  return p;\n}\n\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n\n  return (\n    (Math.round(r * 255) << 24) |\n    (Math.round(g * 255) << 16) |\n    (Math.round(b * 255) << 8)\n  );\n}\n\nfunction hwbToRgb(h, w, b) {\n  if (w + b >= 1) {\n    const gray = Math.round((w * 255) / (w + b));\n\n    return (gray << 24) | (gray << 16) | (gray << 8);\n  }\n\n  const red = hue2rgb(0, 1, h + 1 / 3) * (1 - w - b) + w;\n  const green = hue2rgb(0, 1, h) * (1 - w - b) + w;\n  const blue = hue2rgb(0, 1, h - 1 / 3) * (1 - w - b) + w;\n\n  return (\n    (Math.round(red * 255) << 24) |\n    (Math.round(green * 255) << 16) |\n    (Math.round(blue * 255) << 8)\n  );\n}\n\nconst NUMBER = '[-+]?\\\\d*\\\\.?\\\\d+';\nconst PERCENTAGE = NUMBER + '%';\n\nfunction call(...args) {\n  return '\\\\(\\\\s*(' + args.join(')\\\\s*,?\\\\s*(') + ')\\\\s*\\\\)';\n}\n\nfunction callModern(...args) {\n  return '\\\\(\\\\s*(' + args.join(')\\\\s*(') + ')\\\\s*\\\\)';\n}\n\nfunction callWithSlashSeparator(...args) {\n  return (\n    '\\\\(\\\\s*(' +\n    args.slice(0, args.length - 1).join(')\\\\s*,?\\\\s*(') +\n    ')\\\\s*/\\\\s*(' +\n    args[args.length - 1] +\n    ')\\\\s*\\\\)'\n  );\n}\n\nfunction commaSeparatedCall(...args) {\n  return '\\\\(\\\\s*(' + args.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)';\n}\n\nlet cachedMatchers;\n\nfunction getMatchers() {\n  if (cachedMatchers === undefined) {\n    cachedMatchers = {\n      rgb: new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER)),\n      rgba: new RegExp(\n        'rgba(' +\n          commaSeparatedCall(NUMBER, NUMBER, NUMBER, NUMBER) +\n          '|' +\n          callWithSlashSeparator(NUMBER, NUMBER, NUMBER, NUMBER) +\n          ')',\n      ),\n      hsl: new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE)),\n      hsla: new RegExp(\n        'hsla(' +\n          commaSeparatedCall(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER) +\n          '|' +\n          callWithSlashSeparator(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER) +\n          ')',\n      ),\n      hwb: new RegExp('hwb' + callModern(NUMBER, PERCENTAGE, PERCENTAGE)),\n      hex3: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n      hex4: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n      hex6: /^#([0-9a-fA-F]{6})$/,\n      hex8: /^#([0-9a-fA-F]{8})$/,\n    };\n  }\n  return cachedMatchers;\n}\n\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) {\n    return 0;\n  }\n  if (int > 255) {\n    return 255;\n  }\n  return int;\n}\n\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (((int % 360) + 360) % 360) / 360;\n}\n\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) {\n    return 0;\n  }\n  if (num > 1) {\n    return 255;\n  }\n  return Math.round(num * 255);\n}\n\nfunction parsePercentage(str) {\n  // parseFloat conveniently ignores the final %\n  const int = parseFloat(str);\n  if (int < 0) {\n    return 0;\n  }\n  if (int > 100) {\n    return 1;\n  }\n  return int / 100;\n}\n\nfunction normalizeKeyword(name) {\n  // prettier-ignore\n  switch (name) {\n    case 'transparent': return 0x00000000;\n    // http://www.w3.org/TR/css3-color/#svg-color\n    case 'aliceblue': return 0xf0f8ffff;\n    case 'antiquewhite': return 0xfaebd7ff;\n    case 'aqua': return 0x00ffffff;\n    case 'aquamarine': return 0x7fffd4ff;\n    case 'azure': return 0xf0ffffff;\n    case 'beige': return 0xf5f5dcff;\n    case 'bisque': return 0xffe4c4ff;\n    case 'black': return 0x000000ff;\n    case 'blanchedalmond': return 0xffebcdff;\n    case 'blue': return 0x0000ffff;\n    case 'blueviolet': return 0x8a2be2ff;\n    case 'brown': return 0xa52a2aff;\n    case 'burlywood': return 0xdeb887ff;\n    case 'burntsienna': return 0xea7e5dff;\n    case 'cadetblue': return 0x5f9ea0ff;\n    case 'chartreuse': return 0x7fff00ff;\n    case 'chocolate': return 0xd2691eff;\n    case 'coral': return 0xff7f50ff;\n    case 'cornflowerblue': return 0x6495edff;\n    case 'cornsilk': return 0xfff8dcff;\n    case 'crimson': return 0xdc143cff;\n    case 'cyan': return 0x00ffffff;\n    case 'darkblue': return 0x00008bff;\n    case 'darkcyan': return 0x008b8bff;\n    case 'darkgoldenrod': return 0xb8860bff;\n    case 'darkgray': return 0xa9a9a9ff;\n    case 'darkgreen': return 0x006400ff;\n    case 'darkgrey': return 0xa9a9a9ff;\n    case 'darkkhaki': return 0xbdb76bff;\n    case 'darkmagenta': return 0x8b008bff;\n    case 'darkolivegreen': return 0x556b2fff;\n    case 'darkorange': return 0xff8c00ff;\n    case 'darkorchid': return 0x9932ccff;\n    case 'darkred': return 0x8b0000ff;\n    case 'darksalmon': return 0xe9967aff;\n    case 'darkseagreen': return 0x8fbc8fff;\n    case 'darkslateblue': return 0x483d8bff;\n    case 'darkslategray': return 0x2f4f4fff;\n    case 'darkslategrey': return 0x2f4f4fff;\n    case 'darkturquoise': return 0x00ced1ff;\n    case 'darkviolet': return 0x9400d3ff;\n    case 'deeppink': return 0xff1493ff;\n    case 'deepskyblue': return 0x00bfffff;\n    case 'dimgray': return 0x696969ff;\n    case 'dimgrey': return 0x696969ff;\n    case 'dodgerblue': return 0x1e90ffff;\n    case 'firebrick': return 0xb22222ff;\n    case 'floralwhite': return 0xfffaf0ff;\n    case 'forestgreen': return 0x228b22ff;\n    case 'fuchsia': return 0xff00ffff;\n    case 'gainsboro': return 0xdcdcdcff;\n    case 'ghostwhite': return 0xf8f8ffff;\n    case 'gold': return 0xffd700ff;\n    case 'goldenrod': return 0xdaa520ff;\n    case 'gray': return 0x808080ff;\n    case 'green': return 0x008000ff;\n    case 'greenyellow': return 0xadff2fff;\n    case 'grey': return 0x808080ff;\n    case 'honeydew': return 0xf0fff0ff;\n    case 'hotpink': return 0xff69b4ff;\n    case 'indianred': return 0xcd5c5cff;\n    case 'indigo': return 0x4b0082ff;\n    case 'ivory': return 0xfffff0ff;\n    case 'khaki': return 0xf0e68cff;\n    case 'lavender': return 0xe6e6faff;\n    case 'lavenderblush': return 0xfff0f5ff;\n    case 'lawngreen': return 0x7cfc00ff;\n    case 'lemonchiffon': return 0xfffacdff;\n    case 'lightblue': return 0xadd8e6ff;\n    case 'lightcoral': return 0xf08080ff;\n    case 'lightcyan': return 0xe0ffffff;\n    case 'lightgoldenrodyellow': return 0xfafad2ff;\n    case 'lightgray': return 0xd3d3d3ff;\n    case 'lightgreen': return 0x90ee90ff;\n    case 'lightgrey': return 0xd3d3d3ff;\n    case 'lightpink': return 0xffb6c1ff;\n    case 'lightsalmon': return 0xffa07aff;\n    case 'lightseagreen': return 0x20b2aaff;\n    case 'lightskyblue': return 0x87cefaff;\n    case 'lightslategray': return 0x778899ff;\n    case 'lightslategrey': return 0x778899ff;\n    case 'lightsteelblue': return 0xb0c4deff;\n    case 'lightyellow': return 0xffffe0ff;\n    case 'lime': return 0x00ff00ff;\n    case 'limegreen': return 0x32cd32ff;\n    case 'linen': return 0xfaf0e6ff;\n    case 'magenta': return 0xff00ffff;\n    case 'maroon': return 0x800000ff;\n    case 'mediumaquamarine': return 0x66cdaaff;\n    case 'mediumblue': return 0x0000cdff;\n    case 'mediumorchid': return 0xba55d3ff;\n    case 'mediumpurple': return 0x9370dbff;\n    case 'mediumseagreen': return 0x3cb371ff;\n    case 'mediumslateblue': return 0x7b68eeff;\n    case 'mediumspringgreen': return 0x00fa9aff;\n    case 'mediumturquoise': return 0x48d1ccff;\n    case 'mediumvioletred': return 0xc71585ff;\n    case 'midnightblue': return 0x191970ff;\n    case 'mintcream': return 0xf5fffaff;\n    case 'mistyrose': return 0xffe4e1ff;\n    case 'moccasin': return 0xffe4b5ff;\n    case 'navajowhite': return 0xffdeadff;\n    case 'navy': return 0x000080ff;\n    case 'oldlace': return 0xfdf5e6ff;\n    case 'olive': return 0x808000ff;\n    case 'olivedrab': return 0x6b8e23ff;\n    case 'orange': return 0xffa500ff;\n    case 'orangered': return 0xff4500ff;\n    case 'orchid': return 0xda70d6ff;\n    case 'palegoldenrod': return 0xeee8aaff;\n    case 'palegreen': return 0x98fb98ff;\n    case 'paleturquoise': return 0xafeeeeff;\n    case 'palevioletred': return 0xdb7093ff;\n    case 'papayawhip': return 0xffefd5ff;\n    case 'peachpuff': return 0xffdab9ff;\n    case 'peru': return 0xcd853fff;\n    case 'pink': return 0xffc0cbff;\n    case 'plum': return 0xdda0ddff;\n    case 'powderblue': return 0xb0e0e6ff;\n    case 'purple': return 0x800080ff;\n    case 'rebeccapurple': return 0x663399ff;\n    case 'red': return 0xff0000ff;\n    case 'rosybrown': return 0xbc8f8fff;\n    case 'royalblue': return 0x4169e1ff;\n    case 'saddlebrown': return 0x8b4513ff;\n    case 'salmon': return 0xfa8072ff;\n    case 'sandybrown': return 0xf4a460ff;\n    case 'seagreen': return 0x2e8b57ff;\n    case 'seashell': return 0xfff5eeff;\n    case 'sienna': return 0xa0522dff;\n    case 'silver': return 0xc0c0c0ff;\n    case 'skyblue': return 0x87ceebff;\n    case 'slateblue': return 0x6a5acdff;\n    case 'slategray': return 0x708090ff;\n    case 'slategrey': return 0x708090ff;\n    case 'snow': return 0xfffafaff;\n    case 'springgreen': return 0x00ff7fff;\n    case 'steelblue': return 0x4682b4ff;\n    case 'tan': return 0xd2b48cff;\n    case 'teal': return 0x008080ff;\n    case 'thistle': return 0xd8bfd8ff;\n    case 'tomato': return 0xff6347ff;\n    case 'turquoise': return 0x40e0d0ff;\n    case 'violet': return 0xee82eeff;\n    case 'wheat': return 0xf5deb3ff;\n    case 'white': return 0xffffffff;\n    case 'whitesmoke': return 0xf5f5f5ff;\n    case 'yellow': return 0xffff00ff;\n    case 'yellowgreen': return 0x9acd32ff;\n  }\n  return null;\n}\n\nmodule.exports = normalizeColor;\n"], "mappings": "AAYA,YAAY;;AAEZ,SAASA,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIA,KAAK,KAAK,CAAC,KAAKA,KAAK,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,UAAU,EAAE;MAC9D,OAAOA,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,IAAI;EACb;EAEA,IAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;EAC9B,IAAIC,KAAK;EAGT,IAAKA,KAAK,GAAGF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC,EAAG;IACvC,OAAOM,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC;EAC5C;EAEA,IAAMI,gBAAgB,GAAGC,gBAAgB,CAACR,KAAK,CAAC;EAChD,IAAIO,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAOA,gBAAgB;EACzB;EAEA,IAAKJ,KAAK,GAAGF,QAAQ,CAACQ,GAAG,CAACJ,IAAI,CAACL,KAAK,CAAC,EAAG;IACtC,OACE,CAAEU,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GACvBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,GACzBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,GACzB,UAAU,MACZ,CAAC;EAEL;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACU,IAAI,CAACN,IAAI,CAACL,KAAK,CAAC,EAAG;IAEvC,IAAIG,KAAK,CAAC,CAAC,CAAC,KAAKS,SAAS,EAAE;MAC1B,OACE,CAAEF,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GACvBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,GACzBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,GACzBU,MAAM,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,MAClB,CAAC;IAEL;IAGA,OACE,CAAEO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GACvBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,GACzBO,QAAQ,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,GACzBU,MAAM,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,MAClB,CAAC;EAEL;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACa,IAAI,CAACT,IAAI,CAACL,KAAK,CAAC,EAAG;IACvC,OACEM,QAAQ,CACNH,KAAK,CAAC,CAAC,CAAC,GACNA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACR,IAAI,EACN,EACF,CAAC,KAAK,CAAC;EAEX;EAGA,IAAKA,KAAK,GAAGF,QAAQ,CAACc,IAAI,CAACV,IAAI,CAACL,KAAK,CAAC,EAAG;IACvC,OAAOM,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;EACrC;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACe,IAAI,CAACX,IAAI,CAACL,KAAK,CAAC,EAAG;IACvC,OACEM,QAAQ,CACNH,KAAK,CAAC,CAAC,CAAC,GACNA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC,EACV,EACF,CAAC,KAAK,CAAC;EAEX;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACgB,GAAG,CAACZ,IAAI,CAACL,KAAK,CAAC,EAAG;IACtC,OACE,CAACkB,QAAQ,CACPC,QAAQ,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC,EAClBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAAC,EACzBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAC1B,CAAC,GACC,UAAU,MACZ,CAAC;EAEL;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACoB,IAAI,CAAChB,IAAI,CAACL,KAAK,CAAC,EAAG;IAEvC,IAAIG,KAAK,CAAC,CAAC,CAAC,KAAKS,SAAS,EAAE;MAC1B,OACE,CAACM,QAAQ,CACPC,QAAQ,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC,EAClBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAAC,EACzBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAC1B,CAAC,GACCU,MAAM,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,MAClB,CAAC;IAEL;IAGA,OACE,CAACe,QAAQ,CACPC,QAAQ,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC,EAClBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAAC,EACzBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAC1B,CAAC,GACCU,MAAM,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,MAClB,CAAC;EAEL;EAEA,IAAKA,KAAK,GAAGF,QAAQ,CAACqB,GAAG,CAACjB,IAAI,CAACL,KAAK,CAAC,EAAG;IACtC,OACE,CAACuB,QAAQ,CACPJ,QAAQ,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC,EAClBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAAC,EACzBiB,eAAe,CAACjB,KAAK,CAAC,CAAC,CAAC,CAC1B,CAAC,GACC,UAAU,MACZ,CAAC;EAEL;EAEA,OAAO,IAAI;AACb;AAEA,SAASqB,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAIA,CAAC,GAAG,CAAC,EAAE;IACTA,CAAC,IAAI,CAAC;EACR;EACA,IAAIA,CAAC,GAAG,CAAC,EAAE;IACTA,CAAC,IAAI,CAAC;EACR;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGE,CAAC;EAC5B;EACA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOD,CAAC;EACV;EACA,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IACb,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;EACtC;EACA,OAAOF,CAAC;AACV;AAEA,SAASP,QAAQA,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAMJ,CAAC,GAAGI,CAAC,GAAG,GAAG,GAAGA,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGD,CAAC;EAC/C,IAAMJ,CAAC,GAAG,CAAC,GAAGK,CAAC,GAAGJ,CAAC;EACnB,IAAMK,CAAC,GAAGP,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClC,IAAMI,CAAC,GAAGR,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC;EAC1B,IAAMK,CAAC,GAAGT,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAElC,OACGM,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,GACzBG,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,GAAG,CAAC,IAAI,EAAG,GAC1BE,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,IAAI,CAAE;AAE9B;AAEA,SAASV,QAAQA,CAACK,CAAC,EAAEQ,CAAC,EAAEH,CAAC,EAAE;EACzB,IAAIG,CAAC,GAAGH,CAAC,IAAI,CAAC,EAAE;IACd,IAAMI,IAAI,GAAGH,IAAI,CAACC,KAAK,CAAEC,CAAC,GAAG,GAAG,IAAKA,CAAC,GAAGH,CAAC,CAAC,CAAC;IAE5C,OAAQI,IAAI,IAAI,EAAE,GAAKA,IAAI,IAAI,EAAG,GAAIA,IAAI,IAAI,CAAE;EAClD;EAEA,IAAMC,GAAG,GAAGd,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGQ,CAAC,GAAGH,CAAC,CAAC,GAAGG,CAAC;EACtD,IAAMG,KAAK,GAAGf,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEI,CAAC,CAAC,IAAI,CAAC,GAAGQ,CAAC,GAAGH,CAAC,CAAC,GAAGG,CAAC;EAChD,IAAMI,IAAI,GAAGhB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGQ,CAAC,GAAGH,CAAC,CAAC,GAAGG,CAAC;EAEvD,OACGF,IAAI,CAACC,KAAK,CAACG,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,GAC3BJ,IAAI,CAACC,KAAK,CAACI,KAAK,GAAG,GAAG,CAAC,IAAI,EAAG,GAC9BL,IAAI,CAACC,KAAK,CAACK,IAAI,GAAG,GAAG,CAAC,IAAI,CAAE;AAEjC;AAEA,IAAMC,MAAM,GAAG,mBAAmB;AAClC,IAAMC,UAAU,GAAGD,MAAM,GAAG,GAAG;AAE/B,SAASE,IAAIA,CAAA,EAAU;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACnB,OAAO,UAAU,GAAGF,IAAI,CAACG,IAAI,CAAC,cAAc,CAAC,GAAG,UAAU;AAC5D;AAEA,SAASC,UAAUA,CAAA,EAAU;EAAA,SAAAC,KAAA,GAAAP,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJN,IAAI,CAAAM,KAAA,IAAAR,SAAA,CAAAQ,KAAA;EAAA;EACzB,OAAO,UAAU,GAAGN,IAAI,CAACG,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU;AACtD;AAEA,SAASI,sBAAsBA,CAAA,EAAU;EAAA,SAAAC,KAAA,GAAAV,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJT,IAAI,CAAAS,KAAA,IAAAX,SAAA,CAAAW,KAAA;EAAA;EACrC,OACE,UAAU,GACVT,IAAI,CAACU,KAAK,CAAC,CAAC,EAAEV,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,CAACI,IAAI,CAAC,cAAc,CAAC,GACnD,aAAa,GACbH,IAAI,CAACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,GACrB,UAAU;AAEd;AAEA,SAASY,kBAAkBA,CAAA,EAAU;EAAA,SAAAC,KAAA,GAAAd,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAW,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJb,IAAI,CAAAa,KAAA,IAAAf,SAAA,CAAAe,KAAA;EAAA;EACjC,OAAO,UAAU,GAAGb,IAAI,CAACG,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU;AAC3D;AAEA,IAAIW,cAAc;AAElB,SAAS3D,WAAWA,CAAA,EAAG;EACrB,IAAI2D,cAAc,KAAKjD,SAAS,EAAE;IAChCiD,cAAc,GAAG;MACfpD,GAAG,EAAE,IAAIqD,MAAM,CAAC,KAAK,GAAGnB,IAAI,CAACF,MAAM,EAAEA,MAAM,EAAEA,MAAM,CAAC,CAAC;MACrD9B,IAAI,EAAE,IAAImD,MAAM,CACd,OAAO,GACLJ,kBAAkB,CAACjB,MAAM,EAAEA,MAAM,EAAEA,MAAM,EAAEA,MAAM,CAAC,GAClD,GAAG,GACHa,sBAAsB,CAACb,MAAM,EAAEA,MAAM,EAAEA,MAAM,EAAEA,MAAM,CAAC,GACtD,GACJ,CAAC;MACDxB,GAAG,EAAE,IAAI6C,MAAM,CAAC,KAAK,GAAGnB,IAAI,CAACF,MAAM,EAAEC,UAAU,EAAEA,UAAU,CAAC,CAAC;MAC7DrB,IAAI,EAAE,IAAIyC,MAAM,CACd,OAAO,GACLJ,kBAAkB,CAACjB,MAAM,EAAEC,UAAU,EAAEA,UAAU,EAAED,MAAM,CAAC,GAC1D,GAAG,GACHa,sBAAsB,CAACb,MAAM,EAAEC,UAAU,EAAEA,UAAU,EAAED,MAAM,CAAC,GAC9D,GACJ,CAAC;MACDnB,GAAG,EAAE,IAAIwC,MAAM,CAAC,KAAK,GAAGX,UAAU,CAACV,MAAM,EAAEC,UAAU,EAAEA,UAAU,CAAC,CAAC;MACnE5B,IAAI,EAAE,qDAAqD;MAC3DE,IAAI,EAAE,qEAAqE;MAC3EZ,IAAI,EAAE,qBAAqB;MAC3BW,IAAI,EAAE;IACR,CAAC;EACH;EACA,OAAO8C,cAAc;AACvB;AAEA,SAASnD,QAAQA,CAACqD,GAAG,EAAE;EACrB,IAAMC,GAAG,GAAG1D,QAAQ,CAACyD,GAAG,EAAE,EAAE,CAAC;EAC7B,IAAIC,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,GAAG,EAAE;IACb,OAAO,GAAG;EACZ;EACA,OAAOA,GAAG;AACZ;AAEA,SAAS7C,QAAQA,CAAC4C,GAAG,EAAE;EACrB,IAAMC,GAAG,GAAGC,UAAU,CAACF,GAAG,CAAC;EAC3B,OAAQ,CAAEC,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,GAAG;AAC1C;AAEA,SAASnD,MAAMA,CAACkD,GAAG,EAAE;EACnB,IAAMG,GAAG,GAAGD,UAAU,CAACF,GAAG,CAAC;EAC3B,IAAIG,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,GAAG;EACZ;EACA,OAAOhC,IAAI,CAACC,KAAK,CAAC+B,GAAG,GAAG,GAAG,CAAC;AAC9B;AAEA,SAAS9C,eAAeA,CAAC2C,GAAG,EAAE;EAE5B,IAAMC,GAAG,GAAGC,UAAU,CAACF,GAAG,CAAC;EAC3B,IAAIC,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,CAAC;EACV;EACA,IAAIA,GAAG,GAAG,GAAG,EAAE;IACb,OAAO,CAAC;EACV;EACA,OAAOA,GAAG,GAAG,GAAG;AAClB;AAEA,SAASxD,gBAAgBA,CAAC2D,IAAI,EAAE;EAE9B,QAAQA,IAAI;IACV,KAAK,aAAa;MAAE,OAAO,UAAU;IAErC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,sBAAsB;MAAE,OAAO,UAAU;IAC9C,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,kBAAkB;MAAE,OAAO,UAAU;IAC1C,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,gBAAgB;MAAE,OAAO,UAAU;IACxC,KAAK,iBAAiB;MAAE,OAAO,UAAU;IACzC,KAAK,mBAAmB;MAAE,OAAO,UAAU;IAC3C,KAAK,iBAAiB;MAAE,OAAO,UAAU;IACzC,KAAK,iBAAiB;MAAE,OAAO,UAAU;IACzC,KAAK,cAAc;MAAE,OAAO,UAAU;IACtC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,eAAe;MAAE,OAAO,UAAU;IACvC,KAAK,KAAK;MAAE,OAAO,UAAU;IAC7B,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,UAAU;MAAE,OAAO,UAAU;IAClC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,aAAa;MAAE,OAAO,UAAU;IACrC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,KAAK;MAAE,OAAO,UAAU;IAC7B,KAAK,MAAM;MAAE,OAAO,UAAU;IAC9B,KAAK,SAAS;MAAE,OAAO,UAAU;IACjC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,WAAW;MAAE,OAAO,UAAU;IACnC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,OAAO;MAAE,OAAO,UAAU;IAC/B,KAAK,YAAY;MAAE,OAAO,UAAU;IACpC,KAAK,QAAQ;MAAE,OAAO,UAAU;IAChC,KAAK,aAAa;MAAE,OAAO,UAAU;EACvC;EACA,OAAO,IAAI;AACb;AAEAC,MAAM,CAACC,OAAO,GAAGtE,cAAc", "ignoreList": []}