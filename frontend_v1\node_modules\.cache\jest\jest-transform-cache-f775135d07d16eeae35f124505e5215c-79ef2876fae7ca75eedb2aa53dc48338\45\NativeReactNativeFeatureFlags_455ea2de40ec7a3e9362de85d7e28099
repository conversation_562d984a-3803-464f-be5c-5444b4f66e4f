771d09e30b61063ba3b963686fbbc307
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var NativeReactNativeFeatureFlags = TurboModuleRegistry.get('NativeReactNativeFeatureFlagsCxx');
var _default = exports.default = NativeReactNativeFeatureFlags;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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