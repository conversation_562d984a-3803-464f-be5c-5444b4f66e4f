{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "get", "jest", "fn", "getFontScale", "getPixelSizeForLayoutSize", "layoutSize", "roundToNearestPixel", "Math", "round", "width", "height", "scale", "fontScale", "addEventListener", "removeEventListener", "OS", "Version", "select", "obj", "ios", "default", "isPad", "isTesting", "_require", "require", "originalConsoleError", "console", "error", "originalConsoleWarn", "warn", "message", "arguments", "length", "undefined", "includes", "apply", "global", "mockNavigate", "mockGoBack", "mockReset", "useFakeTimers"], "sources": ["jestSetup.js"], "sourcesContent": ["/**\n * Jest Setup - Early Mocks\n * \n * This file runs before any tests and sets up essential mocks\n * that need to be available during module loading.\n */\n\n// Mock PixelRatio early to prevent import errors\njest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({\n  get: jest.fn(() => 2),\n  getFontScale: jest.fn(() => 1),\n  getPixelSizeForLayoutSize: jest.fn((layoutSize) => layoutSize * 2),\n  roundToNearestPixel: jest.fn((layoutSize) => Math.round(layoutSize * 2) / 2),\n}));\n\n// Mock Dimensions early\njest.mock('react-native/Libraries/Utilities/Dimensions', () => ({\n  get: jest.fn(() => ({\n    width: 375,\n    height: 812,\n    scale: 2,\n    fontScale: 1,\n  })),\n  addEventListener: jest.fn(),\n  removeEventListener: jest.fn(),\n}));\n\n// Mock Platform early\njest.mock('react-native/Libraries/Utilities/Platform', () => ({\n  OS: 'ios',\n  Version: '14.0',\n  select: jest.fn((obj) => obj.ios || obj.default),\n  isPad: false,\n  isTesting: true,\n}));\n\n// Mock console methods to reduce noise in tests\nconst originalConsoleError = console.error;\nconst originalConsoleWarn = console.warn;\n\nconsole.error = (...args) => {\n  const message = args[0];\n  if (\n    typeof message === 'string' &&\n    (message.includes('Warning: ReactDOM.render is no longer supported') ||\n     message.includes('Warning: componentWillMount has been renamed') ||\n     message.includes('Warning: componentWillReceiveProps has been renamed') ||\n     message.includes('VirtualizedLists should never be nested'))\n  ) {\n    return;\n  }\n  originalConsoleError(...args);\n};\n\nconsole.warn = (...args) => {\n  const message = args[0];\n  if (\n    typeof message === 'string' &&\n    (message.includes('Animated: `useNativeDriver`') ||\n     message.includes('source.uri should not be an empty string'))\n  ) {\n    return;\n  }\n  originalConsoleWarn(...args);\n};\n\n// Global test utilities\nglobal.mockNavigate = jest.fn();\nglobal.mockGoBack = jest.fn();\nglobal.mockReset = jest.fn();\n\n// Mock timers\njest.useFakeTimers();\n"], "mappings": "AAQAA,WAAA,GAAKC,IAAI,CAAC,6CAA6C,EAAE;EAAA,OAAO;IAC9DC,GAAG,EAAEC,IAAI,CAACC,EAAE,CAAC;MAAA,OAAM,CAAC;IAAA,EAAC;IACrBC,YAAY,EAAEF,IAAI,CAACC,EAAE,CAAC;MAAA,OAAM,CAAC;IAAA,EAAC;IAC9BE,yBAAyB,EAAEH,IAAI,CAACC,EAAE,CAAC,UAACG,UAAU;MAAA,OAAKA,UAAU,GAAG,CAAC;IAAA,EAAC;IAClEC,mBAAmB,EAAEL,IAAI,CAACC,EAAE,CAAC,UAACG,UAAU;MAAA,OAAKE,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IAAA;EAC7E,CAAC;AAAA,CAAC,CAAC;AAGHP,WAAA,GAAKC,IAAI,CAAC,6CAA6C,EAAE;EAAA,OAAO;IAC9DC,GAAG,EAAEC,IAAI,CAACC,EAAE,CAAC;MAAA,OAAO;QAClBO,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,CAAC;QACRC,SAAS,EAAE;MACb,CAAC;IAAA,CAAC,CAAC;IACHC,gBAAgB,EAAEZ,IAAI,CAACC,EAAE,CAAC,CAAC;IAC3BY,mBAAmB,EAAEb,IAAI,CAACC,EAAE,CAAC;EAC/B,CAAC;AAAA,CAAC,CAAC;AAGHJ,WAAA,GAAKC,IAAI,CAAC,2CAA2C,EAAE;EAAA,OAAO;IAC5DgB,EAAE,EAAE,KAAK;IACTC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAEhB,IAAI,CAACC,EAAE,CAAC,UAACgB,GAAG;MAAA,OAAKA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACE,OAAO;IAAA,EAAC;IAChDC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC;AAAA,CAAC,CAAC;AAAC,SAAAxB,YAAA;EAAA,IAAAyB,QAAA,GAAAC,OAAA;IAAAvB,IAAA,GAAAsB,QAAA,CAAAtB,IAAA;EAAAH,WAAA,YAAAA,YAAA;IAAA,OAAAG,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGJ,IAAMwB,oBAAoB,GAAGC,OAAO,CAACC,KAAK;AAC1C,IAAMC,mBAAmB,GAAGF,OAAO,CAACG,IAAI;AAExCH,OAAO,CAACC,KAAK,GAAG,YAAa;EAC3B,IAAMG,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;EACvB,IACE,OAAOD,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACI,QAAQ,CAAC,iDAAiD,CAAC,IACnEJ,OAAO,CAACI,QAAQ,CAAC,8CAA8C,CAAC,IAChEJ,OAAO,CAACI,QAAQ,CAAC,qDAAqD,CAAC,IACvEJ,OAAO,CAACI,QAAQ,CAAC,yCAAyC,CAAC,CAAC,EAC7D;IACA;EACF;EACAT,oBAAoB,CAAAU,KAAA,SAAAJ,SAAQ,CAAC;AAC/B,CAAC;AAEDL,OAAO,CAACG,IAAI,GAAG,YAAa;EAC1B,IAAMC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;EACvB,IACE,OAAOD,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACI,QAAQ,CAAC,6BAA6B,CAAC,IAC/CJ,OAAO,CAACI,QAAQ,CAAC,0CAA0C,CAAC,CAAC,EAC9D;IACA;EACF;EACAN,mBAAmB,CAAAO,KAAA,SAAAJ,SAAQ,CAAC;AAC9B,CAAC;AAGDK,MAAM,CAACC,YAAY,GAAGpC,IAAI,CAACC,EAAE,CAAC,CAAC;AAC/BkC,MAAM,CAACE,UAAU,GAAGrC,IAAI,CAACC,EAAE,CAAC,CAAC;AAC7BkC,MAAM,CAACG,SAAS,GAAGtC,IAAI,CAACC,EAAE,CAAC,CAAC;AAG5BD,IAAI,CAACuC,aAAa,CAAC,CAAC", "ignoreList": []}