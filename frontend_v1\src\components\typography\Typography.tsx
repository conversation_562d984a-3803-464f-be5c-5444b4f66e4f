/**
 * Typography Component
 *
 * Comprehensive typography component with semantic variants,
 * accessibility features, and visual hierarchy support.
 *
 * Features:
 * - Semantic typography variants
 * - Accessibility compliance
 * - Responsive font scaling
 * - Visual hierarchy support
 * - Platform optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { Text, StyleSheet, TextProps } from 'react-native';
import { TYPOGRAPHY_VARIANTS, getAccessibleFontSize, isTextSizeAccessible } from '../../constants/Typography';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useCognitiveAccessibility } from '../../contexts/CognitiveAccessibilityContext';

// Typography variant types
export type TypographyVariant = keyof typeof TYPOGRAPHY_VARIANTS;

// Component props
export interface TypographyProps extends Omit<TextProps, 'style'> {
  // Content
  children: React.ReactNode;
  
  // Typography
  variant?: TypographyVariant;
  color?: string;
  align?: 'left' | 'center' | 'right' | 'justify';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  
  // Accessibility
  accessibilityLevel?: 'normal' | 'large' | 'extraLarge';
  semanticLevel?: 1 | 2 | 3 | 4 | 5 | 6; // For headings
  
  // Behavior
  selectable?: boolean;
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
  
  // Styling
  style?: any;
  gutterBottom?: boolean;
  gutterTop?: boolean;
  
  // Testing
  testID?: string;
}

export const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body1',
  color,
  align = 'left',
  transform = 'none',
  accessibilityLevel = 'normal',
  semanticLevel,
  selectable = true,
  numberOfLines,
  ellipsizeMode = 'tail',
  style,
  gutterBottom = false,
  gutterTop = false,
  testID,
  ...textProps
}) => {
  // Hooks
  const { colors } = useHighContrastColors();
  const { processText, settings } = useCognitiveAccessibility();

  // Get typography variant styles
  const variantStyles = TYPOGRAPHY_VARIANTS[variant];

  // Process text for cognitive accessibility
  const processedText = typeof children === 'string' ? processText(children) : children;

  // Calculate accessible font size
  const accessibleFontSize = getAccessibleFontSize(
    variantStyles.fontSize,
    accessibilityLevel === 'normal' ? 'normal' :
    accessibilityLevel === 'large' ? 'large' : 'extraLarge'
  );

  // Get effective color
  const effectiveColor = color || colors?.text?.primary || '#333333';

  // Generate accessibility props
  const getAccessibilityProps = () => {
    const props: any = {};

    // Set accessibility role for headings
    if (variant.startsWith('h') || semanticLevel) {
      props.accessibilityRole = 'header';
      props.accessibilityLevel = semanticLevel || parseInt(variant.charAt(1)) || 1;
    } else {
      props.accessibilityRole = 'text';
    }

    // Add accessibility label if text is truncated
    if (numberOfLines && typeof children === 'string') {
      props.accessibilityLabel = children;
    }

    return props;
  };

  // Generate styles
  const textStyles = [
    styles.base,
    {
      fontSize: accessibleFontSize,
      fontFamily: variantStyles.fontFamily,
      fontWeight: variantStyles.fontWeight,
      lineHeight: variantStyles.lineHeight,
      letterSpacing: variantStyles.letterSpacing,
      color: effectiveColor,
      textAlign: align,
      textTransform: transform,
    },
    gutterTop && styles.gutterTop,
    gutterBottom && styles.gutterBottom,
    style,
  ].filter(Boolean);

  // Warn about accessibility issues in development
  if (__DEV__ && !isTextSizeAccessible(accessibleFontSize)) {
    console.warn(`Typography: Font size ${accessibleFontSize}px may not be accessible`);
  }

  return (
    <Text
      style={textStyles}
      selectable={selectable}
      numberOfLines={numberOfLines}
      ellipsizeMode={ellipsizeMode}
      testID={testID}
      {...getAccessibilityProps()}
      {...textProps}
    >
      {processedText}
    </Text>
  );
};

// Specialized typography components for common use cases
export const Heading: React.FC<Omit<TypographyProps, 'variant'> & {
  level: 1 | 2 | 3 | 4 | 5 | 6;
}> = ({ level, ...props }) => (
  <Typography
    variant={`h${level}` as TypographyVariant}
    semanticLevel={level}
    gutterBottom
    {...props}
  />
);

export const Body: React.FC<Omit<TypographyProps, 'variant'> & {
  size?: 'small' | 'medium';
}> = ({ size = 'medium', ...props }) => (
  <Typography
    variant={size === 'small' ? 'body2' : 'body1'}
    gutterBottom
    {...props}
  />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography
    variant="caption"
    {...props}
  />
);

export const Label: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography
    variant="label"
    {...props}
  />
);

export const Subtitle: React.FC<Omit<TypographyProps, 'variant'> & {
  level?: 1 | 2;
}> = ({ level = 1, ...props }) => (
  <Typography
    variant={level === 1 ? 'subtitle1' : 'subtitle2'}
    gutterBottom
    {...props}
  />
);

export const Overline: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography
    variant="overline"
    {...props}
  />
);

// Display component for large text
export const Display: React.FC<Omit<TypographyProps, 'variant'> & {
  size?: 'small' | 'medium' | 'large';
}> = ({ size = 'medium', ...props }) => {
  const variantMap = {
    small: 'h2',
    medium: 'h1',
    large: 'h1',
  } as const;

  return (
    <Typography
      variant={variantMap[size]}
      gutterBottom
      {...props}
    />
  );
};

// Code component for monospace text
export const Code: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography
    variant="code"
    selectable
    {...props}
  />
);

const styles = StyleSheet.create({
  base: {
    // Base styles applied to all typography
  },
  gutterTop: {
    marginTop: 16,
  },
  gutterBottom: {
    marginBottom: 16,
  },
});

// Export all components
export default Typography;
export {
  Heading,
  Body,
  Caption,
  Label,
  Subtitle,
  Overline,
  Display,
  Code,
};
