{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "deepFreezeAndThrowOnMutationInDev", "object", "__DEV__", "isFrozen", "isSealed", "keys", "hasOwnProperty", "prototype", "i", "length", "key", "call", "get", "identity", "bind", "set", "throwOnImmutableMutation", "freeze", "seal", "Error", "JSON", "stringify", "_default"], "sources": ["deepFreezeAndThrowOnMutationInDev.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\n/**\n * If your application is accepting different values for the same field over\n * time and is doing a diff on them, you can either (1) create a copy or\n * (2) ensure that those values are not mutated behind two passes.\n * This function helps you with (2) by freezing the object and throwing if\n * the user subsequently modifies the value.\n *\n * There are two caveats with this function:\n *   - If the call site is not in strict mode, it will only throw when\n *     mutating existing fields, adding a new one\n *     will unfortunately fail silently :(\n *   - If the object is already frozen or sealed, it will not continue the\n *     deep traversal and will leave leaf nodes unfrozen.\n *\n * Freezing the object and adding the throw mechanism is expensive and will\n * only be used in DEV.\n */\nfunction deepFreezeAndThrowOnMutationInDev<T: {...} | Array<mixed>>(\n  object: T,\n): T {\n  if (__DEV__) {\n    if (\n      typeof object !== 'object' ||\n      object === null ||\n      Object.isFrozen(object) ||\n      Object.isSealed(object)\n    ) {\n      return object;\n    }\n\n    // $FlowFixMe[not-an-object] `object` can be an array, but Object.keys works with arrays too\n    const keys = Object.keys((object: {...} | Array<mixed>));\n    // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n    const hasOwnProperty = Object.prototype.hasOwnProperty;\n\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      if (hasOwnProperty.call(object, key)) {\n        Object.defineProperty(object, key, {\n          get: identity.bind(null, object[key]),\n        });\n        Object.defineProperty(object, key, {\n          set: throwOnImmutableMutation.bind(null, key),\n        });\n      }\n    }\n\n    Object.freeze(object);\n    Object.seal(object);\n\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      if (hasOwnProperty.call(object, key)) {\n        deepFreezeAndThrowOnMutationInDev(object[key]);\n      }\n    }\n  }\n  return object;\n}\n\n/* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n * LTI update could not be added via codemod */\nfunction throwOnImmutableMutation(key: empty, value) {\n  throw Error(\n    'You attempted to set the key `' +\n      key +\n      '` with the value `' +\n      JSON.stringify(value) +\n      '` on an object that is meant to be immutable ' +\n      'and has been frozen.',\n  );\n}\n\nfunction identity(value: mixed) {\n  return value;\n}\n\nexport default deepFreezeAndThrowOnMutationInDev;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAmBb,SAASC,iCAAiCA,CACxCC,MAAS,EACN;EACH,IAAIC,OAAO,EAAE;IACX,IACE,OAAOD,MAAM,KAAK,QAAQ,IAC1BA,MAAM,KAAK,IAAI,IACfN,MAAM,CAACQ,QAAQ,CAACF,MAAM,CAAC,IACvBN,MAAM,CAACS,QAAQ,CAACH,MAAM,CAAC,EACvB;MACA,OAAOA,MAAM;IACf;IAGA,IAAMI,IAAI,GAAGV,MAAM,CAACU,IAAI,CAAEJ,MAA6B,CAAC;IAExD,IAAMK,cAAc,GAAGX,MAAM,CAACY,SAAS,CAACD,cAAc;IAEtD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAME,GAAG,GAAGL,IAAI,CAACG,CAAC,CAAC;MACnB,IAAIF,cAAc,CAACK,IAAI,CAACV,MAAM,EAAES,GAAG,CAAC,EAAE;QACpCf,MAAM,CAACC,cAAc,CAACK,MAAM,EAAES,GAAG,EAAE;UACjCE,GAAG,EAAEC,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAEb,MAAM,CAACS,GAAG,CAAC;QACtC,CAAC,CAAC;QACFf,MAAM,CAACC,cAAc,CAACK,MAAM,EAAES,GAAG,EAAE;UACjCK,GAAG,EAAEC,wBAAwB,CAACF,IAAI,CAAC,IAAI,EAAEJ,GAAG;QAC9C,CAAC,CAAC;MACJ;IACF;IAEAf,MAAM,CAACsB,MAAM,CAAChB,MAAM,CAAC;IACrBN,MAAM,CAACuB,IAAI,CAACjB,MAAM,CAAC;IAEnB,KAAK,IAAIO,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,EAAC,EAAE,EAAE;MACpC,IAAME,IAAG,GAAGL,IAAI,CAACG,EAAC,CAAC;MACnB,IAAIF,cAAc,CAACK,IAAI,CAACV,MAAM,EAAES,IAAG,CAAC,EAAE;QACpCV,iCAAiC,CAACC,MAAM,CAACS,IAAG,CAAC,CAAC;MAChD;IACF;EACF;EACA,OAAOT,MAAM;AACf;AAIA,SAASe,wBAAwBA,CAACN,GAAU,EAAEZ,KAAK,EAAE;EACnD,MAAMqB,KAAK,CACT,gCAAgC,GAC9BT,GAAG,GACH,oBAAoB,GACpBU,IAAI,CAACC,SAAS,CAACvB,KAAK,CAAC,GACrB,+CAA+C,GAC/C,sBACJ,CAAC;AACH;AAEA,SAASe,QAAQA,CAACf,KAAY,EAAE;EAC9B,OAAOA,KAAK;AACd;AAAC,IAAAwB,QAAA,GAAAzB,OAAA,CAAAE,OAAA,GAEcC,iCAAiC", "ignoreList": []}