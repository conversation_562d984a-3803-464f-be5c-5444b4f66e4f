{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_defineProperty2", "stringifySafe", "invariant", "processTransform", "transform", "regex", "RegExp", "transformArray", "matches", "exec", "_getKeyAndValueFromCS", "_getKeyAndValueFromCSSTransform", "key", "undefined", "push", "__DEV__", "_validateTransforms", "args", "_args$match", "argsWithUnitsRegex", "match", "map", "Number", "parsedArgs", "missingUnitOfMeasurement", "unitOfMeasurement", "length", "arg<PERSON><PERSON><PERSON>", "isNaN", "for<PERSON>ach", "transformation", "keys", "console", "error", "_validateTransform", "getValue", "multivalueTransforms", "indexOf", "Array", "isArray", "endsWith", "_default"], "sources": ["processTransform.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\nconst stringifySafe = require('../Utilities/stringifySafe').default;\nconst invariant = require('invariant');\n\n/**\n * Generate a transform matrix based on the provided transforms, and use that\n * within the style object instead.\n *\n * This allows us to provide an API that is similar to CSS, where transforms may\n * be applied in an arbitrary order, and yet have a universal, singular\n * interface to native code.\n */\nfunction processTransform(\n  transform: Array<Object> | string,\n): Array<Object> | Array<number> {\n  if (typeof transform === 'string') {\n    const regex = new RegExp(/(\\w+)\\(([^)]+)\\)/g);\n    const transformArray: Array<Object> = [];\n    let matches;\n\n    while ((matches = regex.exec(transform))) {\n      const {key, value} = _getKeyAndValueFromCSSTransform(\n        matches[1],\n        matches[2],\n      );\n\n      if (value !== undefined) {\n        transformArray.push({[key]: value});\n      }\n    }\n    transform = transformArray;\n  }\n\n  if (__DEV__) {\n    _validateTransforms(transform);\n  }\n\n  return transform;\n}\n\nconst _getKeyAndValueFromCSSTransform: (\n  key: string,\n  args: string,\n) => {key: string, value?: Array<string | number> | number | string} = (\n  key,\n  args,\n) => {\n  const argsWithUnitsRegex = new RegExp(/([+-]?\\d+(\\.\\d+)?)([a-zA-Z]+|%)?/g);\n\n  switch (key) {\n    case 'matrix':\n      return {key, value: args.match(/[+-]?\\d+(\\.\\d+)?/g)?.map(Number)};\n    case 'translate':\n    case 'translate3d':\n      const parsedArgs = [];\n      let missingUnitOfMeasurement = false;\n\n      let matches;\n      while ((matches = argsWithUnitsRegex.exec(args))) {\n        const value = Number(matches[1]);\n        const unitOfMeasurement = matches[3];\n\n        if (value !== 0 && !unitOfMeasurement) {\n          missingUnitOfMeasurement = true;\n        }\n\n        if (unitOfMeasurement === '%') {\n          parsedArgs.push(`${value}%`);\n        } else {\n          parsedArgs.push(value);\n        }\n      }\n\n      if (__DEV__) {\n        invariant(\n          !missingUnitOfMeasurement,\n          `Transform with key ${key} must have units unless the provided value is 0, found %s`,\n          `${key}(${args})`,\n        );\n\n        if (key === 'translate') {\n          invariant(\n            parsedArgs?.length === 1 || parsedArgs?.length === 2,\n            'Transform with key translate must be an string with 1 or 2 parameters, found %s: %s',\n            parsedArgs?.length,\n            `${key}(${args})`,\n          );\n        } else {\n          invariant(\n            parsedArgs?.length === 3,\n            'Transform with key translate3d must be an string with 3 parameters, found %s: %s',\n            parsedArgs?.length,\n            `${key}(${args})`,\n          );\n        }\n      }\n\n      if (parsedArgs?.length === 1) {\n        parsedArgs.push(0);\n      }\n\n      return {key: 'translate', value: parsedArgs};\n    case 'translateX':\n    case 'translateY':\n    case 'perspective':\n      const argMatches = argsWithUnitsRegex.exec(args);\n\n      if (!argMatches?.length) {\n        return {key, value: undefined};\n      }\n\n      const value = Number(argMatches[1]);\n      const unitOfMeasurement = argMatches[3];\n\n      if (__DEV__) {\n        invariant(\n          value === 0 || unitOfMeasurement,\n          `Transform with key ${key} must have units unless the provided value is 0, found %s`,\n          `${key}(${args})`,\n        );\n      }\n\n      return {key, value};\n\n    default:\n      return {key, value: !isNaN(args) ? Number(args) : args};\n  }\n};\n\nfunction _validateTransforms(transform: Array<Object>): void {\n  transform.forEach(transformation => {\n    const keys = Object.keys(transformation);\n    invariant(\n      keys.length === 1,\n      'You must specify exactly one property per transform object. Passed properties: %s',\n      stringifySafe(transformation),\n    );\n    const key = keys[0];\n    const value = transformation[key];\n    if (key === 'matrix' && transform.length > 1) {\n      console.error(\n        'When using a matrix transform, you must specify exactly one transform object. Passed transform: ' +\n          stringifySafe(transform),\n      );\n    }\n    _validateTransform(key, value, transformation);\n  });\n}\n\nfunction _validateTransform(\n  key: string,\n  value: any | number | string,\n  transformation: any,\n) {\n  invariant(\n    !value.getValue,\n    'You passed an Animated.Value to a normal component. ' +\n      'You need to wrap that component in an Animated. For example, ' +\n      'replace <View /> by <Animated.View />.',\n  );\n\n  const multivalueTransforms = ['matrix', 'translate'];\n  if (multivalueTransforms.indexOf(key) !== -1) {\n    invariant(\n      Array.isArray(value),\n      'Transform with key of %s must have an array as the value: %s',\n      key,\n      stringifySafe(transformation),\n    );\n  }\n  switch (key) {\n    case 'matrix':\n      invariant(\n        value.length === 9 || value.length === 16,\n        'Matrix transform must have a length of 9 (2d) or 16 (3d). ' +\n          'Provided matrix has a length of %s: %s',\n        /* $FlowFixMe[prop-missing] (>=0.84.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.84 was deployed. To\n         * see the error, delete this comment and run Flow. */\n        value.length,\n        stringifySafe(transformation),\n      );\n      break;\n    case 'translate':\n      invariant(\n        value.length === 2 || value.length === 3,\n        'Transform with key translate must be an array of length 2 or 3, found %s: %s',\n        /* $FlowFixMe[prop-missing] (>=0.84.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.84 was deployed. To\n         * see the error, delete this comment and run Flow. */\n        value.length,\n        stringifySafe(transformation),\n      );\n      break;\n    case 'rotateX':\n    case 'rotateY':\n    case 'rotateZ':\n    case 'rotate':\n    case 'skewX':\n    case 'skewY':\n      invariant(\n        typeof value === 'string',\n        'Transform with key of \"%s\" must be a string: %s',\n        key,\n        stringifySafe(transformation),\n      );\n      invariant(\n        value.indexOf('deg') > -1 || value.indexOf('rad') > -1,\n        'Rotate transform must be expressed in degrees (deg) or radians ' +\n          '(rad): %s',\n        stringifySafe(transformation),\n      );\n      break;\n    case 'perspective':\n      invariant(\n        typeof value === 'number',\n        'Transform with key of \"%s\" must be a number: %s',\n        key,\n        stringifySafe(transformation),\n      );\n      invariant(\n        value !== 0,\n        'Transform with key of \"%s\" cannot be zero: %s',\n        key,\n        stringifySafe(transformation),\n      );\n      break;\n    case 'translateX':\n    case 'translateY':\n      invariant(\n        typeof value === 'number' ||\n          (typeof value === 'string' && value.endsWith('%')),\n        'Transform with key of \"%s\" must be number or a percentage. Passed value: %s.',\n        key,\n        stringifySafe(transformation),\n      );\n      break;\n    case 'scale':\n    case 'scaleX':\n    case 'scaleY':\n      invariant(\n        typeof value === 'number',\n        'Transform with key of \"%s\" must be a number: %s',\n        key,\n        stringifySafe(transformation),\n      );\n      break;\n    default:\n      invariant(\n        false,\n        'Invalid transform %s: %s',\n        key,\n        stringifySafe(transformation),\n      );\n  }\n}\n\nexport default processTransform;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEb,IAAMO,aAAa,GAAGP,OAAO,6BAA6B,CAAC,CAACK,OAAO;AACnE,IAAMG,SAAS,GAAGR,OAAO,CAAC,WAAW,CAAC;AAUtC,SAASS,gBAAgBA,CACvBC,SAAiC,EACF;EAC/B,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,mBAAmB,CAAC;IAC7C,IAAMC,cAA6B,GAAG,EAAE;IACxC,IAAIC,OAAO;IAEX,OAAQA,OAAO,GAAGH,KAAK,CAACI,IAAI,CAACL,SAAS,CAAC,EAAG;MACxC,IAAAM,qBAAA,GAAqBC,+BAA+B,CAClDH,OAAO,CAAC,CAAC,CAAC,EACVA,OAAO,CAAC,CAAC,CACX,CAAC;QAHMI,IAAG,GAAAF,qBAAA,CAAHE,GAAG;QAAEd,KAAK,GAAAY,qBAAA,CAALZ,KAAK;MAKjB,IAAIA,KAAK,KAAKe,SAAS,EAAE;QACvBN,cAAc,CAACO,IAAI,KAAAd,gBAAA,CAAAD,OAAA,MAAGa,IAAG,EAAGd,KAAK,CAAC,CAAC;MACrC;IACF;IACAM,SAAS,GAAGG,cAAc;EAC5B;EAEA,IAAIQ,OAAO,EAAE;IACXC,mBAAmB,CAACZ,SAAS,CAAC;EAChC;EAEA,OAAOA,SAAS;AAClB;AAEA,IAAMO,+BAG8D,GAAG,SAHjEA,+BAG8DA,CAClEC,GAAG,EACHK,IAAI,EACD;EAAA,IAAAC,WAAA;EACH,IAAMC,kBAAkB,GAAG,IAAIb,MAAM,CAAC,mCAAmC,CAAC;EAE1E,QAAQM,GAAG;IACT,KAAK,QAAQ;MACX,OAAO;QAACA,GAAG,EAAHA,GAAG;QAAEd,KAAK,GAAAoB,WAAA,GAAED,IAAI,CAACG,KAAK,CAAC,mBAAmB,CAAC,qBAA/BF,WAAA,CAAiCG,GAAG,CAACC,MAAM;MAAC,CAAC;IACnE,KAAK,WAAW;IAChB,KAAK,aAAa;MAChB,IAAMC,UAAU,GAAG,EAAE;MACrB,IAAIC,wBAAwB,GAAG,KAAK;MAEpC,IAAIhB,OAAO;MACX,OAAQA,OAAO,GAAGW,kBAAkB,CAACV,IAAI,CAACQ,IAAI,CAAC,EAAG;QAChD,IAAMnB,MAAK,GAAGwB,MAAM,CAACd,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,IAAMiB,kBAAiB,GAAGjB,OAAO,CAAC,CAAC,CAAC;QAEpC,IAAIV,MAAK,KAAK,CAAC,IAAI,CAAC2B,kBAAiB,EAAE;UACrCD,wBAAwB,GAAG,IAAI;QACjC;QAEA,IAAIC,kBAAiB,KAAK,GAAG,EAAE;UAC7BF,UAAU,CAACT,IAAI,CAAC,GAAGhB,MAAK,GAAG,CAAC;QAC9B,CAAC,MAAM;UACLyB,UAAU,CAACT,IAAI,CAAChB,MAAK,CAAC;QACxB;MACF;MAEA,IAAIiB,OAAO,EAAE;QACXb,SAAS,CACP,CAACsB,wBAAwB,EACzB,sBAAsBZ,GAAG,2DAA2D,EACpF,GAAGA,GAAG,IAAIK,IAAI,GAChB,CAAC;QAED,IAAIL,GAAG,KAAK,WAAW,EAAE;UACvBV,SAAS,CACP,CAAAqB,UAAU,oBAAVA,UAAU,CAAEG,MAAM,MAAK,CAAC,IAAI,CAAAH,UAAU,oBAAVA,UAAU,CAAEG,MAAM,MAAK,CAAC,EACpD,qFAAqF,EACrFH,UAAU,oBAAVA,UAAU,CAAEG,MAAM,EAClB,GAAGd,GAAG,IAAIK,IAAI,GAChB,CAAC;QACH,CAAC,MAAM;UACLf,SAAS,CACP,CAAAqB,UAAU,oBAAVA,UAAU,CAAEG,MAAM,MAAK,CAAC,EACxB,kFAAkF,EAClFH,UAAU,oBAAVA,UAAU,CAAEG,MAAM,EAClB,GAAGd,GAAG,IAAIK,IAAI,GAChB,CAAC;QACH;MACF;MAEA,IAAI,CAAAM,UAAU,oBAAVA,UAAU,CAAEG,MAAM,MAAK,CAAC,EAAE;QAC5BH,UAAU,CAACT,IAAI,CAAC,CAAC,CAAC;MACpB;MAEA,OAAO;QAACF,GAAG,EAAE,WAAW;QAAEd,KAAK,EAAEyB;MAAU,CAAC;IAC9C,KAAK,YAAY;IACjB,KAAK,YAAY;IACjB,KAAK,aAAa;MAChB,IAAMI,UAAU,GAAGR,kBAAkB,CAACV,IAAI,CAACQ,IAAI,CAAC;MAEhD,IAAI,EAACU,UAAU,YAAVA,UAAU,CAAED,MAAM,GAAE;QACvB,OAAO;UAACd,GAAG,EAAHA,GAAG;UAAEd,KAAK,EAAEe;QAAS,CAAC;MAChC;MAEA,IAAMf,KAAK,GAAGwB,MAAM,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;MACnC,IAAMF,iBAAiB,GAAGE,UAAU,CAAC,CAAC,CAAC;MAEvC,IAAIZ,OAAO,EAAE;QACXb,SAAS,CACPJ,KAAK,KAAK,CAAC,IAAI2B,iBAAiB,EAChC,sBAAsBb,GAAG,2DAA2D,EACpF,GAAGA,GAAG,IAAIK,IAAI,GAChB,CAAC;MACH;MAEA,OAAO;QAACL,GAAG,EAAHA,GAAG;QAAEd,KAAK,EAALA;MAAK,CAAC;IAErB;MACE,OAAO;QAACc,GAAG,EAAHA,GAAG;QAAEd,KAAK,EAAE,CAAC8B,KAAK,CAACX,IAAI,CAAC,GAAGK,MAAM,CAACL,IAAI,CAAC,GAAGA;MAAI,CAAC;EAC3D;AACF,CAAC;AAED,SAASD,mBAAmBA,CAACZ,SAAwB,EAAQ;EAC3DA,SAAS,CAACyB,OAAO,CAAC,UAAAC,cAAc,EAAI;IAClC,IAAMC,IAAI,GAAGpC,MAAM,CAACoC,IAAI,CAACD,cAAc,CAAC;IACxC5B,SAAS,CACP6B,IAAI,CAACL,MAAM,KAAK,CAAC,EACjB,mFAAmF,EACnFzB,aAAa,CAAC6B,cAAc,CAC9B,CAAC;IACD,IAAMlB,GAAG,GAAGmB,IAAI,CAAC,CAAC,CAAC;IACnB,IAAMjC,KAAK,GAAGgC,cAAc,CAAClB,GAAG,CAAC;IACjC,IAAIA,GAAG,KAAK,QAAQ,IAAIR,SAAS,CAACsB,MAAM,GAAG,CAAC,EAAE;MAC5CM,OAAO,CAACC,KAAK,CACX,kGAAkG,GAChGhC,aAAa,CAACG,SAAS,CAC3B,CAAC;IACH;IACA8B,kBAAkB,CAACtB,GAAG,EAAEd,KAAK,EAAEgC,cAAc,CAAC;EAChD,CAAC,CAAC;AACJ;AAEA,SAASI,kBAAkBA,CACzBtB,GAAW,EACXd,KAA4B,EAC5BgC,cAAmB,EACnB;EACA5B,SAAS,CACP,CAACJ,KAAK,CAACqC,QAAQ,EACf,sDAAsD,GACpD,+DAA+D,GAC/D,wCACJ,CAAC;EAED,IAAMC,oBAAoB,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;EACpD,IAAIA,oBAAoB,CAACC,OAAO,CAACzB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC5CV,SAAS,CACPoC,KAAK,CAACC,OAAO,CAACzC,KAAK,CAAC,EACpB,8DAA8D,EAC9Dc,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;EACH;EACA,QAAQlB,GAAG;IACT,KAAK,QAAQ;MACXV,SAAS,CACPJ,KAAK,CAAC4B,MAAM,KAAK,CAAC,IAAI5B,KAAK,CAAC4B,MAAM,KAAK,EAAE,EACzC,4DAA4D,GAC1D,wCAAwC,EAI1C5B,KAAK,CAAC4B,MAAM,EACZzB,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF,KAAK,WAAW;MACd5B,SAAS,CACPJ,KAAK,CAAC4B,MAAM,KAAK,CAAC,IAAI5B,KAAK,CAAC4B,MAAM,KAAK,CAAC,EACxC,8EAA8E,EAI9E5B,KAAK,CAAC4B,MAAM,EACZzB,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,OAAO;IACZ,KAAK,OAAO;MACV5B,SAAS,CACP,OAAOJ,KAAK,KAAK,QAAQ,EACzB,iDAAiD,EACjDc,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD5B,SAAS,CACPJ,KAAK,CAACuC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAIvC,KAAK,CAACuC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EACtD,iEAAiE,GAC/D,WAAW,EACbpC,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF,KAAK,aAAa;MAChB5B,SAAS,CACP,OAAOJ,KAAK,KAAK,QAAQ,EACzB,iDAAiD,EACjDc,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD5B,SAAS,CACPJ,KAAK,KAAK,CAAC,EACX,+CAA+C,EAC/Cc,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF,KAAK,YAAY;IACjB,KAAK,YAAY;MACf5B,SAAS,CACP,OAAOJ,KAAK,KAAK,QAAQ,IACtB,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC0C,QAAQ,CAAC,GAAG,CAAE,EACpD,8EAA8E,EAC9E5B,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,QAAQ;MACX5B,SAAS,CACP,OAAOJ,KAAK,KAAK,QAAQ,EACzB,iDAAiD,EACjDc,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;MACD;IACF;MACE5B,SAAS,CACP,KAAK,EACL,0BAA0B,EAC1BU,GAAG,EACHX,aAAa,CAAC6B,cAAc,CAC9B,CAAC;EACL;AACF;AAAC,IAAAW,QAAA,GAAA5C,OAAA,CAAAE,OAAA,GAEcI,gBAAgB", "ignoreList": []}