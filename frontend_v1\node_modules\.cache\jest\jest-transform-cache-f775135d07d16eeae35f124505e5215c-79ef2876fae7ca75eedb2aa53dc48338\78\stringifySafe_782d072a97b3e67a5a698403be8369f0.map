{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "createStringifySafeWithLimits", "limits", "_limits$maxDepth", "max<PERSON><PERSON><PERSON>", "Number", "POSITIVE_INFINITY", "_limits$maxStringLimi", "maxStringLimit", "_limits$maxArrayLimit", "maxArrayLimit", "_limits$maxObjectKeys", "maxObjectKeysLimit", "stack", "replacer", "key", "value", "length", "shift", "truncatedString", "substring", "retval", "Array", "isArray", "slice", "concat", "invariant", "keys", "Object", "k", "truncatedKey", "unshift", "stringifySafe", "arg", "undefined", "toString", "e", "Error", "name", "message", "ret", "JSON", "stringify", "E", "_default", "exports", "default"], "sources": ["stringifySafe.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nimport invariant from 'invariant';\n\n/**\n * Tries to stringify with JSON.stringify and toString, but catches exceptions\n * (e.g. from circular objects) and always returns a string and never throws.\n */\nexport function createStringifySafeWithLimits(limits: {\n  maxDepth?: number,\n  maxStringLimit?: number,\n  maxArrayLimit?: number,\n  maxObjectKeysLimit?: number,\n}): mixed => string {\n  const {\n    maxDepth = Number.POSITIVE_INFINITY,\n    maxStringLimit = Number.POSITIVE_INFINITY,\n    maxArrayLimit = Number.POSITIVE_INFINITY,\n    maxObjectKeysLimit = Number.POSITIVE_INFINITY,\n  } = limits;\n  const stack: Array<mixed> = [];\n  /* $FlowFixMe[missing-this-annot] The 'this' type annotation(s) required by\n   * Flow's LTI update could not be added via codemod */\n  function replacer(key: string, value: mixed): mixed {\n    while (stack.length && this !== stack[0]) {\n      stack.shift();\n    }\n\n    if (typeof value === 'string') {\n      const truncatedString = '...(truncated)...';\n      if (value.length > maxStringLimit + truncatedString.length) {\n        return value.substring(0, maxStringLimit) + truncatedString;\n      }\n      return value;\n    }\n    if (typeof value !== 'object' || value === null) {\n      return value;\n    }\n\n    let retval: mixed = value;\n    if (Array.isArray(value)) {\n      if (stack.length >= maxDepth) {\n        retval = `[ ... array with ${value.length} values ... ]`;\n      } else if (value.length > maxArrayLimit) {\n        retval = value\n          .slice(0, maxArrayLimit)\n          .concat([\n            `... extra ${value.length - maxArrayLimit} values truncated ...`,\n          ]);\n      }\n    } else {\n      // Add refinement after Array.isArray call.\n      invariant(typeof value === 'object', 'This was already found earlier');\n      let keys = Object.keys(value);\n      if (stack.length >= maxDepth) {\n        retval = `{ ... object with ${keys.length} keys ... }`;\n      } else if (keys.length > maxObjectKeysLimit) {\n        // Return a sample of the keys.\n        retval = ({}: {[string]: mixed});\n        for (let k of keys.slice(0, maxObjectKeysLimit)) {\n          retval[k] = value[k];\n        }\n        const truncatedKey = '...(truncated keys)...';\n        retval[truncatedKey] = keys.length - maxObjectKeysLimit;\n      }\n    }\n    stack.unshift(retval);\n    return retval;\n  }\n\n  return function stringifySafe(arg: mixed): string {\n    if (arg === undefined) {\n      return 'undefined';\n    } else if (arg === null) {\n      return 'null';\n    } else if (typeof arg === 'function') {\n      try {\n        return arg.toString();\n      } catch (e) {\n        return '[function unknown]';\n      }\n    } else if (arg instanceof Error) {\n      return arg.name + ': ' + arg.message;\n    } else {\n      // Perform a try catch, just in case the object has a circular\n      // reference or stringify throws for some other reason.\n      try {\n        const ret = JSON.stringify(arg, replacer);\n        if (ret === undefined) {\n          return '[\"' + typeof arg + '\" failed to stringify]';\n        }\n        return ret;\n      } catch (e) {\n        if (typeof arg.toString === 'function') {\n          try {\n            // $FlowFixMe[incompatible-use] : toString shouldn't take any arguments in general.\n            return arg.toString();\n          } catch (E) {}\n        }\n      }\n    }\n    return '[\"' + typeof arg + '\" failed to stringify]';\n  };\n}\n\nconst stringifySafe: mixed => string = createStringifySafeWithLimits({\n  maxDepth: 10,\n  maxStringLimit: 100,\n  maxArrayLimit: 50,\n  maxObjectKeysLimit: 50,\n});\n\nexport default stringifySafe;\n"], "mappings": ";;;;;;AAUA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMO,SAASC,6BAA6BA,CAACC,MAK7C,EAAmB;EAClB,IAAAC,gBAAA,GAKID,MAAM,CAJRE,QAAQ;IAARA,QAAQ,GAAAD,gBAAA,cAAGE,MAAM,CAACC,iBAAiB,GAAAH,gBAAA;IAAAI,qBAAA,GAIjCL,MAAM,CAHRM,cAAc;IAAdA,cAAc,GAAAD,qBAAA,cAAGF,MAAM,CAACC,iBAAiB,GAAAC,qBAAA;IAAAE,qBAAA,GAGvCP,MAAM,CAFRQ,aAAa;IAAbA,aAAa,GAAAD,qBAAA,cAAGJ,MAAM,CAACC,iBAAiB,GAAAG,qBAAA;IAAAE,qBAAA,GAEtCT,MAAM,CADRU,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGN,MAAM,CAACC,iBAAiB,GAAAK,qBAAA;EAE/C,IAAME,KAAmB,GAAG,EAAE;EAG9B,SAASC,QAAQA,CAACC,GAAW,EAAEC,KAAY,EAAS;IAClD,OAAOH,KAAK,CAACI,MAAM,IAAI,IAAI,KAAKJ,KAAK,CAAC,CAAC,CAAC,EAAE;MACxCA,KAAK,CAACK,KAAK,CAAC,CAAC;IACf;IAEA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAMG,eAAe,GAAG,mBAAmB;MAC3C,IAAIH,KAAK,CAACC,MAAM,GAAGT,cAAc,GAAGW,eAAe,CAACF,MAAM,EAAE;QAC1D,OAAOD,KAAK,CAACI,SAAS,CAAC,CAAC,EAAEZ,cAAc,CAAC,GAAGW,eAAe;MAC7D;MACA,OAAOH,KAAK;IACd;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C,OAAOA,KAAK;IACd;IAEA,IAAIK,MAAa,GAAGL,KAAK;IACzB,IAAIM,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;MACxB,IAAIH,KAAK,CAACI,MAAM,IAAIb,QAAQ,EAAE;QAC5BiB,MAAM,GAAG,oBAAoBL,KAAK,CAACC,MAAM,eAAe;MAC1D,CAAC,MAAM,IAAID,KAAK,CAACC,MAAM,GAAGP,aAAa,EAAE;QACvCW,MAAM,GAAGL,KAAK,CACXQ,KAAK,CAAC,CAAC,EAAEd,aAAa,CAAC,CACvBe,MAAM,CAAC,CACN,aAAaT,KAAK,CAACC,MAAM,GAAGP,aAAa,uBAAuB,CACjE,CAAC;MACN;IACF,CAAC,MAAM;MAEL,IAAAgB,kBAAS,EAAC,OAAOV,KAAK,KAAK,QAAQ,EAAE,gCAAgC,CAAC;MACtE,IAAIW,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACX,KAAK,CAAC;MAC7B,IAAIH,KAAK,CAACI,MAAM,IAAIb,QAAQ,EAAE;QAC5BiB,MAAM,GAAG,qBAAqBM,IAAI,CAACV,MAAM,aAAa;MACxD,CAAC,MAAM,IAAIU,IAAI,CAACV,MAAM,GAAGL,kBAAkB,EAAE;QAE3CS,MAAM,GAAI,CAAC,CAAqB;QAChC,KAAK,IAAIQ,CAAC,IAAIF,IAAI,CAACH,KAAK,CAAC,CAAC,EAAEZ,kBAAkB,CAAC,EAAE;UAC/CS,MAAM,CAACQ,CAAC,CAAC,GAAGb,KAAK,CAACa,CAAC,CAAC;QACtB;QACA,IAAMC,YAAY,GAAG,wBAAwB;QAC7CT,MAAM,CAACS,YAAY,CAAC,GAAGH,IAAI,CAACV,MAAM,GAAGL,kBAAkB;MACzD;IACF;IACAC,KAAK,CAACkB,OAAO,CAACV,MAAM,CAAC;IACrB,OAAOA,MAAM;EACf;EAEA,OAAO,SAASW,aAAaA,CAACC,GAAU,EAAU;IAChD,IAAIA,GAAG,KAAKC,SAAS,EAAE;MACrB,OAAO,WAAW;IACpB,CAAC,MAAM,IAAID,GAAG,KAAK,IAAI,EAAE;MACvB,OAAO,MAAM;IACf,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MACpC,IAAI;QACF,OAAOA,GAAG,CAACE,QAAQ,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,oBAAoB;MAC7B;IACF,CAAC,MAAM,IAAIH,GAAG,YAAYI,KAAK,EAAE;MAC/B,OAAOJ,GAAG,CAACK,IAAI,GAAG,IAAI,GAAGL,GAAG,CAACM,OAAO;IACtC,CAAC,MAAM;MAGL,IAAI;QACF,IAAMC,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACT,GAAG,EAAEnB,QAAQ,CAAC;QACzC,IAAI0B,GAAG,KAAKN,SAAS,EAAE;UACrB,OAAO,IAAI,GAAG,OAAOD,GAAG,GAAG,wBAAwB;QACrD;QACA,OAAOO,GAAG;MACZ,CAAC,CAAC,OAAOJ,CAAC,EAAE;QACV,IAAI,OAAOH,GAAG,CAACE,QAAQ,KAAK,UAAU,EAAE;UACtC,IAAI;YAEF,OAAOF,GAAG,CAACE,QAAQ,CAAC,CAAC;UACvB,CAAC,CAAC,OAAOQ,CAAC,EAAE,CAAC;QACf;MACF;IACF;IACA,OAAO,IAAI,GAAG,OAAOV,GAAG,GAAG,wBAAwB;EACrD,CAAC;AACH;AAEA,IAAMD,aAA8B,GAAG/B,6BAA6B,CAAC;EACnEG,QAAQ,EAAE,EAAE;EACZI,cAAc,EAAE,GAAG;EACnBE,aAAa,EAAE,EAAE;EACjBE,kBAAkB,EAAE;AACtB,CAAC,CAAC;AAAC,IAAAgC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEYd,aAAa", "ignoreList": []}