/**
 * Confirmation Dialog Context Provider
 *
 * Provides consistent confirmation dialogs throughout the application
 * following Nielsen's heuristic for error prevention and user control.
 *
 * Features:
 * - Customizable confirmation dialogs
 * - Destructive action warnings
 * - Accessibility support
 * - Promise-based API
 * - Multiple dialog types
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  BackHandler,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDesignSystem } from './DesignSystemContext';

// Dialog types
export type ConfirmationDialogType = 'default' | 'destructive' | 'warning' | 'info';

// Dialog configuration
export interface ConfirmationConfig {
  title: string;
  message: string;
  type?: ConfirmationDialogType;
  confirmText?: string;
  cancelText?: string;
  icon?: string;
  destructive?: boolean;
  showCancel?: boolean;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
}

// Context interface
interface ConfirmationContextType {
  showConfirmation: (config: ConfirmationConfig) => Promise<boolean>;
  hideConfirmation: () => void;
  isVisible: boolean;
}

// Create context
const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

// Provider component
export const ConfirmationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { theme } = useDesignSystem();
  const [isVisible, setIsVisible] = useState(false);
  const [config, setConfig] = useState<ConfirmationConfig | null>(null);
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);

  // Show confirmation dialog
  const showConfirmation = useCallback((dialogConfig: ConfirmationConfig): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfig(dialogConfig);
      setResolvePromise(() => resolve);
      setIsVisible(true);
    });
  }, []);

  // Hide confirmation dialog
  const hideConfirmation = useCallback(() => {
    setIsVisible(false);
    setConfig(null);
    if (resolvePromise) {
      resolvePromise(false);
      setResolvePromise(null);
    }
  }, [resolvePromise]);

  // Handle confirm
  const handleConfirm = useCallback(async () => {
    if (config?.onConfirm) {
      try {
        await config.onConfirm();
      } catch (error) {
        console.error('Confirmation action failed:', error);
      }
    }
    
    setIsVisible(false);
    if (resolvePromise) {
      resolvePromise(true);
      setResolvePromise(null);
    }
    setConfig(null);
  }, [config, resolvePromise]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    if (config?.onCancel) {
      config.onCancel();
    }
    
    setIsVisible(false);
    if (resolvePromise) {
      resolvePromise(false);
      setResolvePromise(null);
    }
    setConfig(null);
  }, [config, resolvePromise]);

  // Handle back button on Android
  React.useEffect(() => {
    if (Platform.OS === 'android' && isVisible) {
      const onBackPress = () => {
        handleCancel();
        return true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }
  }, [isVisible, handleCancel]);

  // Get dialog styles based on type
  const getDialogStyles = useCallback(() => {
    if (!config) return {};

    const type = config.type || 'default';
    
    switch (type) {
      case 'destructive':
        return {
          iconColor: theme.colors.status?.error || '#DC2626',
          confirmButtonColor: theme.colors.status?.error || '#DC2626',
        };
      case 'warning':
        return {
          iconColor: theme.colors.status?.warning || '#F59E0B',
          confirmButtonColor: theme.colors.status?.warning || '#F59E0B',
        };
      case 'info':
        return {
          iconColor: theme.colors.status?.info || '#3B82F6',
          confirmButtonColor: theme.colors.status?.info || '#3B82F6',
        };
      default:
        return {
          iconColor: theme.colors.primary?.default || '#5A7A63',
          confirmButtonColor: theme.colors.primary?.default || '#5A7A63',
        };
    }
  }, [config, theme]);

  const dialogStyles = getDialogStyles();

  // Context value
  const contextValue: ConfirmationContextType = {
    showConfirmation,
    hideConfirmation,
    isVisible,
  };

  return (
    <ConfirmationContext.Provider value={contextValue}>
      {children}
      
      {/* Confirmation Modal */}
      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
        accessibilityViewIsModal
      >
        <View style={styles.overlay}>
          <View style={[styles.dialog, { backgroundColor: theme.colors.background?.primary }]}>
            {/* Icon */}
            {config?.icon && (
              <View style={styles.iconContainer}>
                <Ionicons
                  name={config.icon as any}
                  size={48}
                  color={dialogStyles.iconColor}
                />
              </View>
            )}

            {/* Title */}
            <Text
              style={[
                styles.title,
                theme.typography.styles.h4,
                { color: theme.colors.text?.primary },
              ]}
              accessibilityRole="header"
            >
              {config?.title}
            </Text>

            {/* Message */}
            <Text
              style={[
                styles.message,
                theme.typography.styles.body1,
                { color: theme.colors.text?.secondary },
              ]}
            >
              {config?.message}
            </Text>

            {/* Buttons */}
            <View style={styles.buttonContainer}>
              {config?.showCancel !== false && (
                <TouchableOpacity
                  style={[
                    styles.button,
                    styles.cancelButton,
                    { borderColor: theme.colors.border?.primary },
                  ]}
                  onPress={handleCancel}
                  accessibilityRole="button"
                  accessibilityLabel={config?.cancelText || 'Cancel'}
                >
                  <Text
                    style={[
                      styles.buttonText,
                      { color: theme.colors.text?.primary },
                    ]}
                  >
                    {config?.cancelText || 'Cancel'}
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[
                  styles.button,
                  styles.confirmButton,
                  { backgroundColor: dialogStyles.confirmButtonColor },
                ]}
                onPress={handleConfirm}
                accessibilityRole="button"
                accessibilityLabel={config?.confirmText || 'Confirm'}
              >
                <Text
                  style={[
                    styles.buttonText,
                    styles.confirmButtonText,
                  ]}
                >
                  {config?.confirmText || 'Confirm'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ConfirmationContext.Provider>
  );
};

// Hook to use confirmation dialogs
export const useConfirmation = (): ConfirmationContextType => {
  const context = useContext(ConfirmationContext);
  
  if (context === undefined) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider');
  }
  
  return context;
};

// Convenience hooks for specific dialog types
export const useDestructiveConfirmation = () => {
  const { showConfirmation } = useConfirmation();
  
  return useCallback((title: string, message: string, onConfirm?: () => void) => {
    return showConfirmation({
      title,
      message,
      type: 'destructive',
      icon: 'warning',
      confirmText: 'Delete',
      destructive: true,
      onConfirm,
    });
  }, [showConfirmation]);
};

export const useWarningConfirmation = () => {
  const { showConfirmation } = useConfirmation();
  
  return useCallback((title: string, message: string, onConfirm?: () => void) => {
    return showConfirmation({
      title,
      message,
      type: 'warning',
      icon: 'alert-circle',
      confirmText: 'Continue',
      onConfirm,
    });
  }, [showConfirmation]);
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dialog: {
    borderRadius: 16,
    padding: 24,
    minWidth: 280,
    maxWidth: 400,
    width: '100%',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 16,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  message: {
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  confirmButton: {
    // Background color set dynamically
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#FFFFFF',
  },
});

export default ConfirmationProvider;
