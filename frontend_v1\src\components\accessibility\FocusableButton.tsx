/**
 * Focusable Button Component
 *
 * Enhanced button component with comprehensive keyboard navigation
 * and accessibility support for power users and assistive technologies.
 *
 * Features:
 * - Keyboard navigation support
 * - Focus management
 * - Screen reader optimization
 * - Touch and keyboard activation
 * - Visual focus indicators
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef, useCallback, useState } from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  Platform,
  AccessibilityRole,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';

// Button props interface
export interface FocusableButtonProps {
  // Content
  title: string;
  onPress: () => void;
  
  // Styling
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  
  // Icon
  icon?: string;
  iconPosition?: 'left' | 'right';
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  
  // Keyboard navigation
  focusable?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  
  // Testing
  testID?: string;
  
  // Custom styling
  style?: any;
  textStyle?: any;
}

export const FocusableButton: React.FC<FocusableButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  icon,
  iconPosition = 'left',
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  focusable = true,
  autoFocus = false,
  onFocus,
  onBlur,
  testID,
  style,
  textStyle,
}) => {
  // State
  const [isFocused, setIsFocused] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  // Refs
  const buttonRef = useRef<any>(null);

  // Motor accessibility hooks
  const touchTargetStyles = useTouchTargetStyles({ width: 100, height: 40 });
  const triggerHapticFeedback = useHapticFeedback();

  // Handle press
  const handlePress = useCallback(() => {
    if (!disabled) {
      triggerHapticFeedback('light');
      onPress();
    }
  }, [disabled, onPress, triggerHapticFeedback]);

  // Handle focus
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    if (onFocus) {
      onFocus();
    }
  }, [onFocus]);

  // Handle blur
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    if (onBlur) {
      onBlur();
    }
  }, [onBlur]);

  // Handle press in
  const handlePressIn = useCallback(() => {
    setIsPressed(true);
  }, []);

  // Handle press out
  const handlePressOut = useCallback(() => {
    setIsPressed(false);
  }, []);

  // Auto focus effect
  React.useEffect(() => {
    if (autoFocus && buttonRef.current && buttonRef.current.focus) {
      buttonRef.current.focus();
    }
  }, [autoFocus]);

  // Get button styles
  const buttonStyles = React.useMemo(() => {
    const baseStyles = [
      styles.button,
      styles[`${variant}Button`],
      styles[`${size}Button`],
      touchTargetStyles, // Apply motor accessibility sizing
    ];

    if (disabled) {
      baseStyles.push(styles.disabledButton);
    }

    if (isFocused) {
      baseStyles.push(styles.focusedButton);
    }

    if (isPressed) {
      baseStyles.push(styles.pressedButton);
    }

    if (style) {
      baseStyles.push(style);
    }

    return baseStyles;
  }, [variant, size, disabled, isFocused, isPressed, style, touchTargetStyles]);

  // Get text styles
  const textStyles = React.useMemo(() => {
    const baseStyles = [styles.text, styles[`${variant}Text`], styles[`${size}Text`]];
    
    if (disabled) {
      baseStyles.push(styles.disabledText);
    }
    
    if (textStyle) {
      baseStyles.push(textStyle);
    }
    
    return baseStyles;
  }, [variant, size, disabled, textStyle]);

  // Get icon color
  const iconColor = React.useMemo(() => {
    if (disabled) {
      return '#999';
    }
    
    switch (variant) {
      case 'primary':
        return '#FFFFFF';
      case 'secondary':
        return '#5A7A63';
      case 'ghost':
        return '#5A7A63';
      default:
        return '#5A7A63';
    }
  }, [variant, disabled]);

  // Get icon size
  const iconSize = React.useMemo(() => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  }, [size]);

  // Render icon
  const renderIcon = () => {
    if (!icon) return null;
    
    return (
      <Ionicons
        name={icon as any}
        size={iconSize}
        color={iconColor}
        style={[
          iconPosition === 'left' ? styles.iconLeft : styles.iconRight,
        ]}
        accessibilityElementsHidden={true}
        importantForAccessibility="no"
      />
    );
  };

  return (
    <TouchableOpacity
      ref={buttonRef}
      style={buttonStyles}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onFocus={handleFocus}
      onBlur={handleBlur}
      disabled={disabled}
      accessible={focusable}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        disabled,
        busy: false,
      }}
      testID={testID}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {icon && iconPosition === 'left' && renderIcon()}
        <Text style={textStyles} numberOfLines={1}>
          {title}
        </Text>
        {icon && iconPosition === 'right' && renderIcon()}
      </View>
      
      {/* Focus indicator for keyboard navigation */}
      {isFocused && Platform.OS === 'web' && (
        <View style={styles.focusIndicator} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minHeight: 44, // Minimum touch target
    position: 'relative',
  },
  
  // Variant styles
  primaryButton: {
    backgroundColor: '#5A7A63',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#5A7A63',
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  
  // Size styles
  smallButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 32,
  },
  mediumButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 44,
  },
  largeButton: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    minHeight: 48,
  },
  
  // State styles
  disabledButton: {
    opacity: 0.5,
  },
  focusedButton: {
    borderWidth: 2,
    borderColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  pressedButton: {
    transform: [{ scale: 0.98 }],
  },
  
  // Content
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Text styles
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryText: {
    color: '#FFFFFF',
  },
  secondaryText: {
    color: '#5A7A63',
  },
  ghostText: {
    color: '#5A7A63',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  disabledText: {
    opacity: 0.7,
  },
  
  // Icon styles
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
  
  // Focus indicator
  focusIndicator: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    pointerEvents: 'none',
  },
});

export default FocusableButton;
