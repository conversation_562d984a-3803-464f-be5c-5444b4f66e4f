/**
 * FormInput Component - Enhanced Form Input with Validation
 *
 * Component Contract:
 * - Extends basic Input with advanced form validation
 * - Supports real-time validation with debouncing
 * - Provides visual feedback for validation states
 * - Includes built-in validation rules for common patterns
 * - Supports custom validation functions
 * - Handles password visibility toggle
 * - Responsive design with proper touch targets
 * - Full accessibility support
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  TextInputProps,
  TextStyle,
  ViewStyle,
} from 'react-native';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getResponsiveIconSize,
} from '../../utils/responsiveUtils';
import { Icon } from '../ui/IconLibrary';
import {
  FocusUtils,
  TouchTargetUtils,
  ScreenReaderUtils,
  ColorContrastUtils,
  WCAG_STANDARDS,
} from '../../utils/accessibilityUtils';
import {
  generateFormFieldAccessibilityProps,
  generateErrorMessageProps,
  generateHelperTextProps,
  generateLabelProps,
  type FormFieldType,
} from '../../utils/formAccessibilityUtils';

// Enhanced Error Handling
import { ValidationError } from '../ui/ErrorHandling';

// Validation rule types
export type ValidationRule =
  | 'required'
  | 'email'
  | 'phone'
  | 'password'
  | 'confirmPassword'
  | 'name'
  | 'minLength'
  | 'maxLength'
  | 'custom';

export interface ValidationConfig {
  rule: ValidationRule;
  message: string;
  value?: any; // For rules like minLength, maxLength, confirmPassword
  validator?: (value: string) => boolean; // For custom validation
}

export interface FormInputProps extends Omit<TextInputProps, 'style'> {
  /** Input label */
  label?: string;
  /** Placeholder text */
  placeholder?: string;
  /** Input value */
  value?: string;
  /** Text change handler */
  onChangeText?: (text: string) => void;
  /** Validation rules */
  validationRules?: ValidationConfig[];
  /** Show validation error immediately */
  showError?: boolean;
  /** Custom error message override */
  error?: string;
  /** Multiple validation errors */
  errors?: string[];
  /** Input type for specialized behavior */
  inputType?: 'text' | 'email' | 'password' | 'phone' | 'name';
  /** Disabled state */
  disabled?: boolean;
  /** Required field indicator */
  required?: boolean;
  /** Helper text */
  helperText?: string;
  /** Custom style overrides */
  style?: TextStyle;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  /** Validation callback */
  onValidation?: (isValid: boolean, error?: string) => void;
  /** Debounce validation delay in ms */
  validationDelay?: number;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  placeholder,
  value = '',
  onChangeText,
  validationRules = [],
  showError = false,
  error: externalError,
  errors: externalErrors,
  inputType = 'text',
  disabled = false,
  required = false,
  helperText,
  style,
  containerStyle,
  labelStyle,
  onValidation,
  validationDelay = 300,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [validationError, setValidationError] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [validationTimeout, setValidationTimeout] =
    useState<NodeJS.Timeout | null>(null);

  // Generate enhanced accessibility props safely
  const currentError = externalError || (hasInteracted && showError ? validationError : '');
  const accessibilityProps = React.useMemo(() => {
    try {
      return generateFormFieldAccessibilityProps({
        id: props.testID || 'form-input',
        label,
        placeholder,
        helperText,
        errorMessage: currentError,
        required,
        disabled,
        fieldType: inputType as FormFieldType,
        value,
      });
    } catch (error) {
      console.warn('Error generating accessibility props:', error);
      return {
        accessibilityLabel: label || placeholder,
        accessibilityHint: helperText,
        accessibilityState: { disabled, invalid: !!currentError },
        ids: {
          fieldId: 'form-input',
          labelId: 'form-input-label',
          errorId: 'form-input-error',
          helperId: 'form-input-helper',
        },
      };
    }
  }, [label, placeholder, helperText, currentError, required, disabled, inputType, value, props.testID]);

  // Validation functions
  const validateRequired = (val: string): boolean => val.trim().length > 0;

  const validateEmail = (val: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(val);
  };

  const validatePhone = (val: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(val.replace(/[\s\-\(\)]/g, ''));
  };

  const validatePassword = (val: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(val);
  };

  const validateName = (val: string): boolean => {
    const nameRegex = /^[a-zA-Z\s]{2,50}$/;
    return nameRegex.test(val.trim());
  };

  const runValidation = useCallback(
    (val: string): string => {
      for (const config of validationRules) {
        let isValid = true;

        switch (config.rule) {
          case 'required':
            isValid = validateRequired(val);
            break;
          case 'email':
            isValid = val === '' || validateEmail(val);
            break;
          case 'phone':
            isValid = val === '' || validatePhone(val);
            break;
          case 'password':
            isValid = val === '' || validatePassword(val);
            break;
          case 'name':
            isValid = val === '' || validateName(val);
            break;
          case 'minLength':
            isValid = val.length >= (config.value || 0);
            break;
          case 'maxLength':
            isValid = val.length <= (config.value || 100);
            break;
          case 'confirmPassword':
            isValid = val === config.value;
            break;
          case 'custom':
            isValid = config.validator ? config.validator(val) : true;
            break;
        }

        if (!isValid) {
          return config.message;
        }
      }

      return '';
    },
    [validationRules],
  );

  // Debounced validation
  useEffect(() => {
    if (validationTimeout) {
      clearTimeout(validationTimeout);
    }

    const timeout = setTimeout(() => {
      const error = runValidation(value);
      setValidationError(error);
      onValidation?.(error === '', error);
    }, validationDelay);

    setValidationTimeout(timeout);

    return () => {
      if (validationTimeout) {
        clearTimeout(validationTimeout);
      }
    };
  }, [value, runValidation, validationDelay, onValidation]);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    setHasInteracted(true);

    // Announce focus to screen readers for better accessibility
    if (label) {
      const announcement = ScreenReaderUtils.generateAccessibleLabel(
        `${label} input field`,
        required ? 'required' : undefined,
        currentError ? 'has error' : undefined,
        helperText
      );
      ScreenReaderUtils.announce(announcement, 'low');
    }

    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);

    // Announce validation results to screen readers
    if (hasInteracted && currentError) {
      ScreenReaderUtils.announce(`Error: ${currentError}`, 'high');
    }

    onBlur?.(e);
  };

  const handleChangeText = (text: string) => {
    setHasInteracted(true);
    onChangeText?.(text);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Get input props based on type
  const getInputProps = () => {
    const baseProps = {
      autoCapitalize: 'none' as const,
      autoCorrect: false,
    };

    switch (inputType) {
      case 'email':
        return {
          ...baseProps,
          keyboardType: 'email-address' as const,
          textContentType: 'emailAddress' as const,
        };
      case 'password':
        return {
          ...baseProps,
          secureTextEntry: !showPassword,
          textContentType: 'password' as const,
        };
      case 'phone':
        return {
          ...baseProps,
          keyboardType: 'phone-pad' as const,
          textContentType: 'telephoneNumber' as const,
        };
      case 'name':
        return {
          autoCapitalize: 'words' as const,
          autoCorrect: true,
          textContentType: 'name' as const,
        };
      default:
        return baseProps;
    }
  };

  // Determine colors based on state with WCAG-compliant focus
  const getBorderColor = () => {
    if (currentError) return Colors.status.error;
    if (isFocused) return '#5A7A63'; // WCAG-compliant sage green for focus
    return Colors.border.primary;
  };

  const getTextColor = () => {
    if (disabled) return Colors.text.disabled;
    return Colors.text.primary;
  };

  const isValid = !currentError && hasInteracted;

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Enhanced Label with ARIA associations */}
      {label && (
        <View style={styles.labelContainer}>
          <Text
            style={[styles.label, labelStyle]}
            {...generateLabelProps(label, accessibilityProps.ids?.labelId || 'label', required)}
            accessibilityRole="text"
            accessibilityLabel={required ? `${label}, required field` : label}
          >
            {label}
            {required && (
              <Text
                style={styles.required}
                accessibilityLabel="required"
                accessibilityRole="text">
                {' *'}
              </Text>
            )}
          </Text>
        </View>
      )}

      {/* Input Container */}
      <View
        style={[
          styles.inputContainer,
          { borderColor: getBorderColor() },
          isFocused && styles.inputContainerFocused,
          currentError && styles.inputContainerError,
          disabled && styles.inputContainerDisabled,
          // Apply enhanced WCAG-compliant focus indicator styles
          FocusUtils.getFocusIndicatorStyle(isFocused, {}, {
            color: currentError ? Colors.semantic.error : '#5A7A63', // WCAG-compliant focus color
            width: 3, // WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
            offset: 2, // WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET,
          }),
        ]}>
        <TextInput
          style={[styles.input, { color: getTextColor() }, style]}
          placeholder={placeholder}
          placeholderTextColor={Colors.text.placeholder}
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled}
          // Enhanced WCAG-compliant accessibility props
          {...accessibilityProps}
          // Ensure minimum touch target
          hitSlop={TouchTargetUtils.calculateHitSlop(44)}
          {...getInputProps()}
          {...props}
        />

        {/* Password Toggle */}
        {inputType === 'password' && (
          <TouchableOpacity
            style={[
              styles.passwordToggle,
              // Ensure minimum touch target size
              { minWidth: 44, minHeight: 44 } // WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE
            ]}
            onPress={togglePasswordVisibility}
            accessibilityLabel={
              showPassword ? 'Hide password' : 'Show password'
            }
            accessibilityRole="button"
            accessibilityHint="Toggles password visibility"
            hitSlop={TouchTargetUtils.calculateHitSlop(20)}>
            <Icon
              name={showPassword ? 'hidePassword' : 'showPassword'}
              size={getResponsiveIconSize(20)}
              color={Colors.text.secondary}
            />
          </TouchableOpacity>
        )}

        {/* Validation Icon */}
        {hasInteracted && !disabled && (
          <View style={styles.validationIcon}>
            {currentError ? (
              <Icon
                name="error"
                size={getResponsiveIconSize(16)}
                color={Colors.status.error}
              />
            ) : isValid ? (
              <Icon
                name="success"
                size={getResponsiveIconSize(16)}
                color={Colors.status.success}
              />
            ) : null}
          </View>
        )}
      </View>

      {/* Enhanced Error Display */}
      {(currentError || externalErrors) && (
        <ValidationError
          errors={externalErrors || (currentError ? [currentError] : [])}
          field={label}
          testID={`${props.testID || 'form-input'}-validation-error`}
        />
      )}
      {helperText && !currentError && (
        <Text
          style={[styles.helperText, styles.normalHelperText]}
          {...generateHelperTextProps(helperText, accessibilityProps.ids?.helperId || 'helper')}
        >
          {helperText}
        </Text>
      )}
    </View>
  );
};

const styles = {
  container: {
    marginBottom: getResponsiveSpacing(16),
  } as ViewStyle,
  labelContainer: {
    marginBottom: getResponsiveSpacing(8),
  } as ViewStyle,
  label: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600' as const,
    color: Colors.text.primary,
  } as TextStyle,
  required: {
    color: Colors.status.error,
  } as TextStyle,
  inputContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    borderWidth: 1,
    borderRadius: 12,
    backgroundColor: Colors.background.primary,
    minHeight: 48,
    paddingHorizontal: getResponsiveSpacing(16),
  } as ViewStyle,
  inputContainerFocused: {
    borderWidth: 2,
    shadowColor: Colors.primary.sage,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  } as ViewStyle,
  inputContainerError: {
    borderColor: Colors.status.error,
  } as ViewStyle,
  inputContainerDisabled: {
    backgroundColor: Colors.background.disabled,
    opacity: 0.6,
  } as ViewStyle,
  input: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    paddingVertical: getResponsiveSpacing(12),
  } as TextStyle,
  passwordToggle: {
    padding: getResponsiveSpacing(4),
    marginLeft: getResponsiveSpacing(8),
  } as ViewStyle,
  validationIcon: {
    marginLeft: getResponsiveSpacing(8),
  } as ViewStyle,
  helperText: {
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(4),
    marginLeft: getResponsiveSpacing(4),
  } as TextStyle,
  errorText: {
    color: Colors.status.error,
  } as TextStyle,
  normalHelperText: {
    color: Colors.text.secondary,
  } as TextStyle,
};

export default FormInput;
