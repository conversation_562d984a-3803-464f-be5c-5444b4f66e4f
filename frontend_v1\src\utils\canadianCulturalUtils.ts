/**
 * Canadian Cultural Adaptation Utilities
 *
 * Utilities for adapting UI/UX to Canadian cultural preferences,
 * local market needs, and regional variations.
 *
 * Features:
 * - Regional cultural preferences
 * - Canadian business etiquette
 * - Local market adaptations
 * - Seasonal considerations
 * - Cultural sensitivity
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { CanadianProvince } from './canadianPaymentUtils';

// Canadian cultural regions
export const CANADIAN_CULTURAL_REGIONS = {
  ATLANTIC: ['NL', 'PE', 'NS', 'NB'],
  QUEBEC: ['QC'],
  ONTARIO: ['ON'],
  PRAIRIES: ['MB', 'SK', 'AB'],
  BRITISH_COLUMBIA: ['BC'],
  TERRITORIES: ['YT', 'NT', 'NU'],
} as const;

export type CanadianCulturalRegion = keyof typeof CANADIAN_CULTURAL_REGIONS;

// Cultural preferences by region
export interface RegionalPreferences {
  region: CanadianCulturalRegion;
  communicationStyle: 'direct' | 'polite' | 'formal' | 'casual';
  businessHours: {
    weekdays: { start: string; end: string };
    saturday: { start: string; end: string } | null;
    sunday: { start: string; end: string } | null;
  };
  preferredContactMethods: string[];
  culturalConsiderations: string[];
  seasonalFactors: string[];
  localTerminology: Record<string, string>;
}

// Regional cultural preferences
export const REGIONAL_PREFERENCES: Record<CanadianCulturalRegion, RegionalPreferences> = {
  ATLANTIC: {
    region: 'ATLANTIC',
    communicationStyle: 'polite',
    businessHours: {
      weekdays: { start: '08:00', end: '17:00' },
      saturday: { start: '09:00', end: '17:00' },
      sunday: null,
    },
    preferredContactMethods: ['phone', 'email', 'in-person'],
    culturalConsiderations: [
      'Strong community values',
      'Emphasis on personal relationships',
      'Maritime hospitality traditions',
      'Weather-dependent scheduling',
    ],
    seasonalFactors: [
      'Winter weather impacts (Nov-Mar)',
      'Tourism season considerations (Jun-Sep)',
      'Fishing season schedules',
    ],
    localTerminology: {
      'apartment': 'flat',
      'soda': 'soft drink',
      'shopping_cart': 'buggy',
    },
  },
  QUEBEC: {
    region: 'QUEBEC',
    communicationStyle: 'formal',
    businessHours: {
      weekdays: { start: '08:30', end: '17:00' },
      saturday: { start: '09:00', end: '17:00' },
      sunday: { start: '12:00', end: '17:00' },
    },
    preferredContactMethods: ['email', 'phone', 'text'],
    culturalConsiderations: [
      'French language preference',
      'Cultural preservation values',
      'Family-oriented society',
      'Respect for traditions',
      'Bill 101 compliance requirements',
    ],
    seasonalFactors: [
      'Winter festival season (Jan-Mar)',
      'Construction holiday (last 2 weeks of July)',
      'Back-to-school emphasis (Sep)',
      'Holiday season traditions (Dec)',
    ],
    localTerminology: {
      'email': 'courriel',
      'weekend': 'fin de semaine',
      'parking': 'stationnement',
      'shopping': 'magasinage',
    },
  },
  ONTARIO: {
    region: 'ONTARIO',
    communicationStyle: 'direct',
    businessHours: {
      weekdays: { start: '09:00', end: '17:00' },
      saturday: { start: '10:00', end: '18:00' },
      sunday: { start: '11:00', end: '17:00' },
    },
    preferredContactMethods: ['email', 'text', 'app', 'phone'],
    culturalConsiderations: [
      'Multicultural awareness',
      'Business efficiency focus',
      'Technology adoption',
      'Urban vs rural differences',
    ],
    seasonalFactors: [
      'Cottage season (May-Sep)',
      'Winter sports season (Dec-Mar)',
      'Back-to-school rush (Sep)',
      'Holiday shopping (Nov-Dec)',
    ],
    localTerminology: {
      'cottage': 'cottage',
      'hydro': 'electricity',
      'the_six': 'Toronto',
    },
  },
  PRAIRIES: {
    region: 'PRAIRIES',
    communicationStyle: 'casual',
    businessHours: {
      weekdays: { start: '08:00', end: '17:00' },
      saturday: { start: '09:00', end: '17:00' },
      sunday: { start: '12:00', end: '17:00' },
    },
    preferredContactMethods: ['phone', 'email', 'text'],
    culturalConsiderations: [
      'Strong work ethic',
      'Community cooperation',
      'Agricultural awareness',
      'Weather resilience',
      'Indigenous cultural respect',
    ],
    seasonalFactors: [
      'Harvest season (Aug-Oct)',
      'Extreme winter conditions (Dec-Feb)',
      'Seeding season (Apr-May)',
      'Stampede season (Jul)',
    ],
    localTerminology: {
      'bunnyhugs': 'hoodie',
      'slough': 'pond',
      'grid_road': 'rural_road',
    },
  },
  BRITISH_COLUMBIA: {
    region: 'BRITISH_COLUMBIA',
    communicationStyle: 'casual',
    businessHours: {
      weekdays: { start: '09:00', end: '17:00' },
      saturday: { start: '10:00', end: '18:00' },
      sunday: { start: '11:00', end: '17:00' },
    },
    preferredContactMethods: ['app', 'email', 'text', 'phone'],
    culturalConsiderations: [
      'Environmental consciousness',
      'Outdoor lifestyle',
      'Tech-savvy population',
      'Health and wellness focus',
      'Indigenous cultural respect',
    ],
    seasonalFactors: [
      'Ski season (Dec-Apr)',
      'Hiking season (May-Oct)',
      'Rain season (Oct-Mar)',
      'Festival season (Jun-Sep)',
    ],
    localTerminology: {
      'the_mainland': 'Vancouver_area',
      'the_island': 'Vancouver_Island',
      'lower_mainland': 'Metro_Vancouver',
    },
  },
  TERRITORIES: {
    region: 'TERRITORIES',
    communicationStyle: 'polite',
    businessHours: {
      weekdays: { start: '08:30', end: '16:30' },
      saturday: { start: '10:00', end: '16:00' },
      sunday: null,
    },
    preferredContactMethods: ['phone', 'email', 'radio'],
    culturalConsiderations: [
      'Indigenous cultural respect',
      'Remote community challenges',
      'Extreme weather conditions',
      'Limited infrastructure',
      'Strong community bonds',
    ],
    seasonalFactors: [
      'Extreme winter darkness (Nov-Jan)',
      'Midnight sun season (May-Jul)',
      'Ice road season (Dec-Mar)',
      'Traditional hunting seasons',
    ],
    localTerminology: {
      'the_north': 'territories',
      'outside': 'southern_canada',
      'bush': 'wilderness',
    },
  },
};

// Canadian holidays and observances
export const CANADIAN_HOLIDAYS = {
  NATIONAL: [
    { name: 'New Year\'s Day', date: '01-01', type: 'statutory' },
    { name: 'Good Friday', date: 'easter-2', type: 'statutory' },
    { name: 'Easter Monday', date: 'easter+1', type: 'federal' },
    { name: 'Victoria Day', date: 'may-monday-3', type: 'statutory' },
    { name: 'Canada Day', date: '07-01', type: 'statutory' },
    { name: 'Labour Day', date: 'september-monday-1', type: 'statutory' },
    { name: 'Thanksgiving', date: 'october-monday-2', type: 'statutory' },
    { name: 'Remembrance Day', date: '11-11', type: 'federal' },
    { name: 'Christmas Day', date: '12-25', type: 'statutory' },
    { name: 'Boxing Day', date: '12-26', type: 'statutory' },
  ],
  PROVINCIAL: {
    QC: [
      { name: 'Saint-Jean-Baptiste Day', date: '06-24', type: 'provincial' },
    ],
    AB: [
      { name: 'Family Day', date: 'february-monday-3', type: 'provincial' },
    ],
    BC: [
      { name: 'Family Day', date: 'february-monday-2', type: 'provincial' },
      { name: 'BC Day', date: 'august-monday-1', type: 'provincial' },
    ],
    ON: [
      { name: 'Family Day', date: 'february-monday-3', type: 'provincial' },
    ],
  },
};

// Canadian business etiquette
export const CANADIAN_BUSINESS_ETIQUETTE = {
  COMMUNICATION: {
    greetings: ['Hello', 'Good morning', 'Good afternoon', 'Bonjour (Quebec)'],
    politeness: ['Please', 'Thank you', 'You\'re welcome', 'Excuse me', 'Sorry'],
    formality: 'moderate', // Generally less formal than UK, more than US
    directness: 'moderate', // Polite but clear
  },
  SCHEDULING: {
    punctuality: 'important',
    advanceNotice: '24-48 hours',
    cancellationNotice: '2-4 hours minimum',
    reschedulingFlexibility: 'moderate',
  },
  PAYMENT: {
    preferredMethods: ['credit_card', 'debit_card', 'interac_etransfer'],
    tippingCulture: {
      restaurants: '15-20%',
      services: '10-15%',
      delivery: '10-15%',
    },
    taxInclusive: false, // Prices typically exclude tax
  },
};

/**
 * Get cultural region for a province
 */
export const getCulturalRegion = (province: CanadianProvince): CanadianCulturalRegion => {
  for (const [region, provinces] of Object.entries(CANADIAN_CULTURAL_REGIONS)) {
    if (provinces.includes(province)) {
      return region as CanadianCulturalRegion;
    }
  }
  return 'ONTARIO'; // Default fallback
};

/**
 * Get regional preferences for a province
 */
export const getRegionalPreferences = (province: CanadianProvince): RegionalPreferences => {
  const region = getCulturalRegion(province);
  return REGIONAL_PREFERENCES[region];
};

/**
 * Get culturally appropriate greeting based on region and time
 */
export const getCulturalGreeting = (
  province: CanadianProvince,
  timeOfDay: 'morning' | 'afternoon' | 'evening',
  language: 'en' | 'fr' = 'en'
): string => {
  const region = getCulturalRegion(province);
  
  if (region === 'QUEBEC' && language === 'fr') {
    switch (timeOfDay) {
      case 'morning': return 'Bonjour';
      case 'afternoon': return 'Bonjour';
      case 'evening': return 'Bonsoir';
    }
  }
  
  // English greetings
  switch (timeOfDay) {
    case 'morning': return 'Good morning';
    case 'afternoon': return 'Good afternoon';
    case 'evening': return 'Good evening';
  }
};

/**
 * Get business hours for a region
 */
export const getRegionalBusinessHours = (province: CanadianProvince) => {
  const preferences = getRegionalPreferences(province);
  return preferences.businessHours;
};

/**
 * Check if current time is within business hours
 */
export const isWithinBusinessHours = (
  province: CanadianProvince,
  currentTime: Date = new Date()
): boolean => {
  const businessHours = getRegionalBusinessHours(province);
  const dayOfWeek = currentTime.getDay(); // 0 = Sunday, 6 = Saturday
  const timeString = currentTime.toTimeString().slice(0, 5); // HH:MM format
  
  let todayHours;
  if (dayOfWeek === 0) { // Sunday
    todayHours = businessHours.sunday;
  } else if (dayOfWeek === 6) { // Saturday
    todayHours = businessHours.saturday;
  } else { // Weekdays
    todayHours = businessHours.weekdays;
  }
  
  if (!todayHours) return false;
  
  return timeString >= todayHours.start && timeString <= todayHours.end;
};

/**
 * Get preferred contact methods for a region
 */
export const getPreferredContactMethods = (province: CanadianProvince): string[] => {
  const preferences = getRegionalPreferences(province);
  return preferences.preferredContactMethods;
};

/**
 * Get cultural considerations for a region
 */
export const getCulturalConsiderations = (province: CanadianProvince): string[] => {
  const preferences = getRegionalPreferences(province);
  return preferences.culturalConsiderations;
};

/**
 * Get seasonal factors for a region
 */
export const getSeasonalFactors = (province: CanadianProvince): string[] => {
  const preferences = getRegionalPreferences(province);
  return preferences.seasonalFactors;
};

/**
 * Get local terminology for a region
 */
export const getLocalTerminology = (province: CanadianProvince): Record<string, string> => {
  const preferences = getRegionalPreferences(province);
  return preferences.localTerminology;
};

/**
 * Adapt text for local terminology
 */
export const adaptTextForRegion = (text: string, province: CanadianProvince): string => {
  const terminology = getLocalTerminology(province);
  let adaptedText = text;
  
  Object.entries(terminology).forEach(([standard, local]) => {
    const regex = new RegExp(`\\b${standard}\\b`, 'gi');
    adaptedText = adaptedText.replace(regex, local);
  });
  
  return adaptedText;
};

/**
 * Get appropriate communication style for region
 */
export const getCommunicationStyle = (province: CanadianProvince): 'direct' | 'polite' | 'formal' | 'casual' => {
  const preferences = getRegionalPreferences(province);
  return preferences.communicationStyle;
};

/**
 * Format service description for cultural context
 */
export const formatServiceForCulture = (
  serviceDescription: string,
  province: CanadianProvince,
  language: 'en' | 'fr' = 'en'
): string => {
  let formatted = adaptTextForRegion(serviceDescription, province);
  const style = getCommunicationStyle(province);
  
  // Adjust tone based on communication style
  switch (style) {
    case 'formal':
      // Add more formal language markers
      formatted = formatted.replace(/\bwe'll\b/gi, 'we will');
      formatted = formatted.replace(/\bcan't\b/gi, 'cannot');
      break;
    case 'casual':
      // Add more casual language markers
      formatted = formatted.replace(/\bwill not\b/gi, 'won\'t');
      formatted = formatted.replace(/\bcannot\b/gi, 'can\'t');
      break;
  }
  
  return formatted;
};

/**
 * Get culturally appropriate error messages
 */
export const getCulturalErrorMessage = (
  errorType: string,
  province: CanadianProvince,
  language: 'en' | 'fr' = 'en'
): string => {
  const style = getCommunicationStyle(province);
  
  const messages = {
    network_error: {
      direct: 'Connection failed. Check your internet.',
      polite: 'We\'re having trouble connecting. Please check your internet connection.',
      formal: 'We apologize for the inconvenience. Please verify your internet connection.',
      casual: 'Oops! Connection hiccup. Check your wifi?',
    },
    validation_error: {
      direct: 'Fix the highlighted fields.',
      polite: 'Please review and correct the highlighted fields.',
      formal: 'Kindly review and correct the information in the highlighted fields.',
      casual: 'Looks like a few fields need a quick fix!',
    },
  };
  
  const errorMessages = messages[errorType as keyof typeof messages];
  if (!errorMessages) return 'An error occurred.';
  
  return errorMessages[style] || errorMessages.polite;
};

/**
 * Check if it's a Canadian holiday
 */
export const isCanadianHoliday = (date: Date, province?: CanadianProvince): boolean => {
  // This would need a proper holiday calculation library
  // For now, return false as placeholder
  return false;
};

/**
 * Get weather-appropriate service recommendations
 */
export const getWeatherConsiderations = (province: CanadianProvince, season: 'spring' | 'summer' | 'fall' | 'winter'): string[] => {
  const region = getCulturalRegion(province);
  
  const considerations = {
    ATLANTIC: {
      winter: ['Weather delays possible', 'Indoor services preferred', 'Storm cancellation policy'],
      spring: ['Mud season considerations', 'Variable weather'],
      summer: ['Tourism season busy', 'Outdoor services popular'],
      fall: ['Hurricane season awareness', 'Shorter daylight hours'],
    },
    QUEBEC: {
      winter: ['Snow removal required', 'Extreme cold precautions', 'Holiday season busy'],
      spring: ['Construction season begins', 'Pothole season'],
      summer: ['Construction holiday impact', 'Festival season'],
      fall: ['Back-to-school rush', 'Leaf season tourism'],
    },
    ONTARIO: {
      winter: ['Snow clearing needed', 'Cottage closures', 'Holiday shopping rush'],
      spring: ['Cottage opening season', 'Spring cleaning demand'],
      summer: ['Cottage season peak', 'Vacation scheduling'],
      fall: ['Back-to-school busy', 'Winterization services'],
    },
    PRAIRIES: {
      winter: ['Extreme cold warnings', 'Block heater services', 'Snow removal critical'],
      spring: ['Flooding possible', 'Seeding season busy'],
      summer: ['Harvest preparation', 'Fair season'],
      fall: ['Harvest season priority', 'Winter preparation'],
    },
    BRITISH_COLUMBIA: {
      winter: ['Rain season services', 'Ski season busy', 'Mild temperatures'],
      spring: ['Cherry blossom season', 'Hiking season begins'],
      summer: ['Fire season precautions', 'Outdoor activity peak'],
      fall: ['Rain season returns', 'Storm season'],
    },
    TERRITORIES: {
      winter: ['Extreme cold protocols', 'Limited daylight', 'Ice road season'],
      spring: ['Breakup season challenges', 'Flooding risks'],
      summer: ['Midnight sun advantage', 'Construction season'],
      fall: ['Freeze-up preparations', 'Hunting season'],
    },
  };
  
  return considerations[region]?.[season] || [];
};

export default {
  CANADIAN_CULTURAL_REGIONS,
  REGIONAL_PREFERENCES,
  CANADIAN_HOLIDAYS,
  CANADIAN_BUSINESS_ETIQUETTE,
  getCulturalRegion,
  getRegionalPreferences,
  getCulturalGreeting,
  getRegionalBusinessHours,
  isWithinBusinessHours,
  getPreferredContactMethods,
  getCulturalConsiderations,
  getSeasonalFactors,
  getLocalTerminology,
  adaptTextForRegion,
  getCommunicationStyle,
  formatServiceForCulture,
  getCulturalErrorMessage,
  isCanadianHoliday,
  getWeatherConsiderations,
};
