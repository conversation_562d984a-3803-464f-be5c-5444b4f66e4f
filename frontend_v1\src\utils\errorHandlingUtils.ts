/**
 * Error Handling Utilities
 *
 * Comprehensive error handling utilities for graceful error management,
 * user-friendly error messages, and robust error recovery mechanisms.
 *
 * Features:
 * - Error classification
 * - User-friendly messaging
 * - Error recovery strategies
 * - Logging and reporting
 * - Accessibility compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Error types
export type ErrorType = 
  | 'network'
  | 'validation'
  | 'authentication'
  | 'authorization'
  | 'not_found'
  | 'server'
  | 'client'
  | 'timeout'
  | 'offline'
  | 'unknown';

// Error severity levels
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Error context
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp: Date;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

// Application error interface
export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code?: string | number;
  context: ErrorContext;
  originalError?: Error;
  recoverable: boolean;
  retryable: boolean;
  suggestions: string[];
}

// Error recovery action
export interface ErrorRecoveryAction {
  label: string;
  action: () => void | Promise<void>;
  primary?: boolean;
}

// Error boundary state
export interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorId: string | null;
  retryCount: number;
}

/**
 * Generate unique error ID
 */
export const generateErrorId = (): string => {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Classify error type based on error object
 */
export const classifyError = (error: any): ErrorType => {
  if (!error) return 'unknown';
  
  // Network errors
  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    return 'network';
  }
  
  // Timeout errors
  if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {
    return 'timeout';
  }
  
  // HTTP status code based classification
  if (error.status || error.response?.status) {
    const status = error.status || error.response.status;
    
    if (status === 401) return 'authentication';
    if (status === 403) return 'authorization';
    if (status === 404) return 'not_found';
    if (status >= 400 && status < 500) return 'client';
    if (status >= 500) return 'server';
  }
  
  // Validation errors
  if (error.name === 'ValidationError' || error.type === 'validation') {
    return 'validation';
  }
  
  // Offline errors
  if (error.message?.includes('offline') || error.code === 'OFFLINE') {
    return 'offline';
  }
  
  return 'unknown';
};

/**
 * Determine error severity
 */
export const getErrorSeverity = (error: any, type: ErrorType): ErrorSeverity => {
  // Critical errors that break core functionality
  if (type === 'authentication' || type === 'server') {
    return 'critical';
  }
  
  // High severity errors that significantly impact user experience
  if (type === 'network' || type === 'timeout' || type === 'authorization') {
    return 'high';
  }
  
  // Medium severity errors that cause inconvenience
  if (type === 'validation' || type === 'not_found') {
    return 'medium';
  }
  
  // Low severity errors that are minor issues
  return 'low';
};

/**
 * Generate user-friendly error message
 */
export const getUserFriendlyMessage = (type: ErrorType, originalMessage?: string): string => {
  const messages: Record<ErrorType, string> = {
    network: 'Unable to connect to the internet. Please check your connection and try again.',
    validation: 'Please check your input and try again.',
    authentication: 'Your session has expired. Please sign in again.',
    authorization: 'You don\'t have permission to perform this action.',
    not_found: 'The requested information could not be found.',
    server: 'We\'re experiencing technical difficulties. Please try again later.',
    client: 'Something went wrong with your request. Please try again.',
    timeout: 'The request took too long to complete. Please try again.',
    offline: 'You appear to be offline. Please check your internet connection.',
    unknown: 'An unexpected error occurred. Please try again.',
  };
  
  return messages[type] || messages.unknown;
};

/**
 * Generate error suggestions
 */
export const getErrorSuggestions = (type: ErrorType): string[] => {
  const suggestions: Record<ErrorType, string[]> = {
    network: [
      'Check your internet connection',
      'Try switching between WiFi and mobile data',
      'Restart your router if using WiFi',
    ],
    validation: [
      'Double-check all required fields',
      'Ensure email addresses are valid',
      'Check password requirements',
    ],
    authentication: [
      'Sign in again with your credentials',
      'Reset your password if needed',
      'Contact support if the problem persists',
    ],
    authorization: [
      'Contact your administrator for access',
      'Ensure you\'re signed in to the correct account',
      'Try refreshing the page',
    ],
    not_found: [
      'Check the URL for typos',
      'Go back and try again',
      'Use the search function to find what you need',
    ],
    server: [
      'Wait a few minutes and try again',
      'Check our status page for updates',
      'Contact support if the issue persists',
    ],
    client: [
      'Refresh the page and try again',
      'Clear your browser cache',
      'Try using a different browser',
    ],
    timeout: [
      'Check your internet connection speed',
      'Try again with a better connection',
      'Break large requests into smaller ones',
    ],
    offline: [
      'Check your internet connection',
      'Try again when you\'re back online',
      'Some features may work offline',
    ],
    unknown: [
      'Try refreshing the page',
      'Restart the app',
      'Contact support if the problem continues',
    ],
  };
  
  return suggestions[type] || suggestions.unknown;
};

/**
 * Create application error object
 */
export const createAppError = (
  error: any,
  context: Partial<ErrorContext> = {}
): AppError => {
  const type = classifyError(error);
  const severity = getErrorSeverity(error, type);
  const userMessage = getUserFriendlyMessage(type, error.message);
  const suggestions = getErrorSuggestions(type);
  
  return {
    id: generateErrorId(),
    type,
    severity,
    message: error.message || 'Unknown error',
    userMessage,
    code: error.code || error.status,
    context: {
      timestamp: new Date(),
      ...context,
    },
    originalError: error,
    recoverable: type !== 'critical',
    retryable: ['network', 'timeout', 'server'].includes(type),
    suggestions,
  };
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: AppError): boolean => {
  return error.retryable && error.retryCount < 3;
};

/**
 * Get retry delay based on attempt count
 */
export const getRetryDelay = (attemptCount: number): number => {
  // Exponential backoff: 1s, 2s, 4s, 8s...
  return Math.min(1000 * Math.pow(2, attemptCount), 10000);
};

/**
 * Log error for debugging and monitoring
 */
export const logError = (error: AppError): void => {
  const logData = {
    id: error.id,
    type: error.type,
    severity: error.severity,
    message: error.message,
    code: error.code,
    context: error.context,
    stack: error.originalError?.stack,
  };
  
  // In development, log to console
  if (__DEV__) {
    console.group(`🚨 Error [${error.severity.toUpperCase()}]`);
    console.error('Error Details:', logData);
    console.error('Original Error:', error.originalError);
    console.groupEnd();
  }
  
  // In production, send to error reporting service
  // Example: Sentry, Bugsnag, etc.
  // errorReportingService.captureError(logData);
};

/**
 * Handle async operation with error handling
 */
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context: Partial<ErrorContext> = {}
): Promise<{ data: T | null; error: AppError | null }> => {
  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    const appError = createAppError(error, context);
    logError(appError);
    return { data: null, error: appError };
  }
};

/**
 * Retry operation with exponential backoff
 */
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  context: Partial<ErrorContext> = {}
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw createAppError(error, { ...context, retryCount: attempt });
      }
      
      const delay = getRetryDelay(attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw createAppError(lastError, context);
};

/**
 * Create error recovery actions
 */
export const createRecoveryActions = (error: AppError): ErrorRecoveryAction[] => {
  const actions: ErrorRecoveryAction[] = [];
  
  // Retry action for retryable errors
  if (error.retryable) {
    actions.push({
      label: 'Try Again',
      action: () => {
        // This would be implemented by the component using the error
        window.location.reload();
      },
      primary: true,
    });
  }
  
  // Go back action
  actions.push({
    label: 'Go Back',
    action: () => {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // Navigate to home or safe route
        window.location.href = '/';
      }
    },
  });
  
  // Contact support for critical errors
  if (error.severity === 'critical') {
    actions.push({
      label: 'Contact Support',
      action: () => {
        // Open support contact method
        window.open('mailto:<EMAIL>?subject=Error Report&body=' + 
          encodeURIComponent(`Error ID: ${error.id}\nMessage: ${error.message}`));
      },
    });
  }
  
  return actions;
};

/**
 * Format error for display
 */
export const formatErrorForDisplay = (error: AppError): {
  title: string;
  message: string;
  suggestions: string[];
  actions: ErrorRecoveryAction[];
} => {
  const titles: Record<ErrorSeverity, string> = {
    low: 'Minor Issue',
    medium: 'Something Went Wrong',
    high: 'Connection Problem',
    critical: 'Service Unavailable',
  };
  
  return {
    title: titles[error.severity],
    message: error.userMessage,
    suggestions: error.suggestions,
    actions: createRecoveryActions(error),
  };
};

/**
 * Error boundary helper
 */
export const createErrorBoundaryState = (): ErrorBoundaryState => ({
  hasError: false,
  error: null,
  errorId: null,
  retryCount: 0,
});

export default {
  generateErrorId,
  classifyError,
  getErrorSeverity,
  getUserFriendlyMessage,
  getErrorSuggestions,
  createAppError,
  isRetryableError,
  getRetryDelay,
  logError,
  withErrorHandling,
  retryOperation,
  createRecoveryActions,
  formatErrorForDisplay,
  createErrorBoundaryState,
};
