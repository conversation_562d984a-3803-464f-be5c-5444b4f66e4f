/**
 * Exit Confirmation Hook
 *
 * Provides exit confirmation functionality to prevent accidental app exits
 * and data loss, following Nielsen's heuristic for user control and freedom.
 *
 * Features:
 * - Back button handling
 * - Unsaved changes detection
 * - Customizable exit messages
 * - Accessibility support
 * - Platform-specific behavior
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useEffect, useCallback, useRef } from 'react';
import { BackHandler, Platform, Alert } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useConfirmation } from '../contexts/ConfirmationContext';

// Exit confirmation configuration
export interface ExitConfirmationConfig {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  enabled?: boolean;
  checkUnsavedChanges?: () => boolean;
  onExit?: () => void | Promise<void>;
  customHandler?: () => boolean | Promise<boolean>;
}

// Default configuration
const defaultConfig: Required<ExitConfirmationConfig> = {
  title: 'Exit App',
  message: 'Are you sure you want to exit the app?',
  confirmText: 'Exit',
  cancelText: 'Stay',
  enabled: true,
  checkUnsavedChanges: () => false,
  onExit: () => {},
  customHandler: () => false,
};

export const useExitConfirmation = (config: ExitConfirmationConfig = {}) => {
  const navigation = useNavigation();
  const { showConfirmation } = useConfirmation();
  const configRef = useRef({ ...defaultConfig, ...config });
  const lastBackPressRef = useRef<number>(0);

  // Update config ref when config changes
  useEffect(() => {
    configRef.current = { ...defaultConfig, ...config };
  }, [config]);

  // Handle back button press
  const handleBackPress = useCallback(async (): Promise<boolean> => {
    const currentConfig = configRef.current;

    // If disabled, allow default behavior
    if (!currentConfig.enabled) {
      return false;
    }

    // Check if we can go back in navigation
    if (navigation.canGoBack()) {
      // If there are unsaved changes, show confirmation
      if (currentConfig.checkUnsavedChanges()) {
        const confirmed = await showConfirmation({
          title: 'Unsaved Changes',
          message: 'You have unsaved changes. Are you sure you want to go back?',
          type: 'warning',
          icon: 'alert-circle',
          confirmText: 'Discard',
          cancelText: 'Stay',
        });

        if (confirmed) {
          navigation.goBack();
        }
        return true; // Prevent default back behavior
      }
      
      // No unsaved changes, allow normal back navigation
      return false;
    }

    // We're at the root screen, handle app exit
    return handleAppExit();
  }, [navigation, showConfirmation]);

  // Handle app exit
  const handleAppExit = useCallback(async (): Promise<boolean> => {
    const currentConfig = configRef.current;

    // Use custom handler if provided
    if (currentConfig.customHandler) {
      const result = await currentConfig.customHandler();
      return result;
    }

    // Check for unsaved changes
    if (currentConfig.checkUnsavedChanges()) {
      const confirmed = await showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes that will be lost. Are you sure you want to exit?',
        type: 'warning',
        icon: 'alert-circle',
        confirmText: 'Exit',
        cancelText: 'Stay',
      });

      if (confirmed && currentConfig.onExit) {
        await currentConfig.onExit();
      }
      
      return !confirmed; // Return true to prevent exit if not confirmed
    }

    // Double-tap to exit on Android
    if (Platform.OS === 'android') {
      const now = Date.now();
      const timeDiff = now - lastBackPressRef.current;
      
      if (timeDiff < 2000) {
        // Second tap within 2 seconds, exit app
        if (currentConfig.onExit) {
          await currentConfig.onExit();
        }
        return false; // Allow exit
      } else {
        // First tap, show toast and update timestamp
        lastBackPressRef.current = now;
        Alert.alert('', 'Press back again to exit', [{ text: 'OK' }]);
        return true; // Prevent exit
      }
    }

    // iOS or other platforms - show confirmation
    const confirmed = await showConfirmation({
      title: currentConfig.title,
      message: currentConfig.message,
      confirmText: currentConfig.confirmText,
      cancelText: currentConfig.cancelText,
      type: 'default',
      icon: 'exit',
    });

    if (confirmed && currentConfig.onExit) {
      await currentConfig.onExit();
    }

    return !confirmed; // Return true to prevent exit if not confirmed
  }, [showConfirmation]);

  // Set up back handler when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android') {
        const subscription = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
        return () => subscription.remove();
      }
    }, [handleBackPress])
  );

  // Return control functions
  return {
    handleBackPress,
    handleAppExit,
    updateConfig: (newConfig: Partial<ExitConfirmationConfig>) => {
      configRef.current = { ...configRef.current, ...newConfig };
    },
  };
};

// Hook for form exit confirmation
export const useFormExitConfirmation = (hasUnsavedChanges: boolean) => {
  return useExitConfirmation({
    checkUnsavedChanges: () => hasUnsavedChanges,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes that will be lost. Are you sure you want to leave?',
    confirmText: 'Discard',
    cancelText: 'Stay',
  });
};

// Hook for booking flow exit confirmation
export const useBookingExitConfirmation = (isInProgress: boolean) => {
  return useExitConfirmation({
    enabled: isInProgress,
    title: 'Cancel Booking',
    message: 'Are you sure you want to cancel your booking? Your progress will be lost.',
    confirmText: 'Cancel Booking',
    cancelText: 'Continue Booking',
    checkUnsavedChanges: () => isInProgress,
  });
};

// Hook for destructive action confirmation
export const useDestructiveActionConfirmation = () => {
  const { showConfirmation } = useConfirmation();

  return useCallback(async (
    title: string,
    message: string,
    action: () => void | Promise<void>
  ): Promise<boolean> => {
    const confirmed = await showConfirmation({
      title,
      message,
      type: 'destructive',
      icon: 'trash',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: action,
    });

    return confirmed;
  }, [showConfirmation]);
};

// Hook for navigation confirmation with unsaved changes
export const useNavigationConfirmation = (hasUnsavedChanges: boolean) => {
  const navigation = useNavigation();
  const { showConfirmation } = useConfirmation();

  const confirmNavigation = useCallback(async (
    routeName: string,
    params?: any
  ): Promise<void> => {
    if (hasUnsavedChanges) {
      const confirmed = await showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to leave this page?',
        type: 'warning',
        icon: 'alert-circle',
        confirmText: 'Leave',
        cancelText: 'Stay',
      });

      if (confirmed) {
        navigation.navigate(routeName as never, params as never);
      }
    } else {
      navigation.navigate(routeName as never, params as never);
    }
  }, [navigation, hasUnsavedChanges, showConfirmation]);

  return { confirmNavigation };
};

export default useExitConfirmation;
