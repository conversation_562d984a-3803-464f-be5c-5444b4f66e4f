/**
 * Optimized Image Component
 *
 * High-performance image component with lazy loading, optimization,
 * progressive loading, and accessibility features.
 *
 * Features:
 * - Automatic image optimization
 * - Lazy loading with intersection observer
 * - Progressive loading with placeholders
 * - Error handling and fallbacks
 * - Accessibility compliance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Animated,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Typography, Body } from '../typography/Typography';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useAnimation } from '../../contexts/AnimationContext';
import {
  optimizeImageForDisplay,
  generatePlaceholder,
  monitorImagePerformance,
  ImageLoadingState,
  OptimizedImage as OptimizedImageType,
} from '../../utils/imageOptimizationUtils';

// Component props
export interface OptimizedImageProps {
  // Image source
  source: { uri: string } | number;
  
  // Dimensions
  width?: number;
  height?: number;
  aspectRatio?: number;
  
  // Optimization options
  quality?: number;
  format?: string;
  enableLazyLoading?: boolean;
  enableProgressiveLoading?: boolean;
  enableCaching?: boolean;
  
  // Placeholder options
  placeholder?: string;
  placeholderColor?: string;
  showLoadingIndicator?: boolean;
  
  // Styling
  style?: any;
  imageStyle?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  borderRadius?: number;
  
  // Interaction
  onPress?: () => void;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  alt?: string;
  
  // Testing
  testID?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  width,
  height,
  aspectRatio,
  quality = 0.8,
  format,
  enableLazyLoading = true,
  enableProgressiveLoading = true,
  enableCaching = true,
  placeholder,
  placeholderColor = '#F0F0F0',
  showLoadingIndicator = true,
  style,
  imageStyle,
  resizeMode = 'cover',
  borderRadius,
  onPress,
  onLoad,
  onError,
  onLoadStart,
  onLoadEnd,
  accessibilityLabel,
  accessibilityHint,
  alt,
  testID,
}) => {
  // Hooks
  const { colors } = useHighContrastColors();
  const { shouldAnimate } = useAnimation();

  // State
  const [loadingState, setLoadingState] = useState<ImageLoadingState>({
    isLoading: false,
    isLoaded: false,
    hasError: false,
    progress: 0,
  });
  const [optimizedImage, setOptimizedImage] = useState<OptimizedImageType | null>(null);
  const [isInView, setIsInView] = useState(!enableLazyLoading);

  // Refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const performanceMonitor = useRef(monitorImagePerformance());
  const containerRef = useRef<View>(null);

  // Get image URI
  const getImageUri = useCallback((): string => {
    if (typeof source === 'number') {
      return Image.resolveAssetSource(source).uri;
    }
    return source.uri;
  }, [source]);

  // Handle image optimization
  useEffect(() => {
    const optimizeImage = async () => {
      if (!isInView) return;

      const uri = getImageUri();
      if (!uri) return;

      try {
        const optimized = await optimizeImageForDisplay(
          uri,
          width,
          height,
          {
            quality,
            format,
            enableCaching,
          }
        );
        setOptimizedImage(optimized);
      } catch (error) {
        console.error('Image optimization failed:', error);
        setLoadingState(prev => ({ ...prev, hasError: true, error: error as Error }));
      }
    };

    optimizeImage();
  }, [isInView, getImageUri, width, height, quality, format, enableCaching]);

  // Handle image loading
  const handleLoadStart = useCallback(() => {
    setLoadingState(prev => ({ ...prev, isLoading: true, hasError: false }));
    performanceMonitor.current.recordLoad();
    onLoadStart?.();
  }, [onLoadStart]);

  const handleLoad = useCallback(() => {
    setLoadingState(prev => ({ ...prev, isLoaded: true, isLoading: false }));
    
    if (shouldAnimate) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(1);
    }
    
    onLoad?.();
  }, [onLoad, shouldAnimate, fadeAnim]);

  const handleError = useCallback((error: any) => {
    setLoadingState(prev => ({ 
      ...prev, 
      hasError: true, 
      isLoading: false,
      error: error.nativeEvent?.error || new Error('Image load failed'),
    }));
    performanceMonitor.current.recordError();
    onError?.(error);
  }, [onError]);

  const handleLoadEnd = useCallback(() => {
    setLoadingState(prev => ({ ...prev, isLoading: false }));
    onLoadEnd?.();
  }, [onLoadEnd]);

  // Calculate container dimensions
  const getContainerDimensions = () => {
    const containerStyle: any = {};
    
    if (width) containerStyle.width = width;
    if (height) containerStyle.height = height;
    if (aspectRatio) containerStyle.aspectRatio = aspectRatio;
    if (borderRadius) containerStyle.borderRadius = borderRadius;
    
    return containerStyle;
  };

  // Get placeholder source
  const getPlaceholderSource = () => {
    if (placeholder) {
      return { uri: placeholder };
    }
    
    if (width && height) {
      return { uri: generatePlaceholder(width, height, placeholderColor) };
    }
    
    return null;
  };

  // Render loading indicator
  const renderLoadingIndicator = () => {
    if (!showLoadingIndicator || !loadingState.isLoading) return null;

    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="small"
          color={colors?.primary?.default || '#5A7A63'}
        />
      </View>
    );
  };

  // Render error state
  const renderErrorState = () => {
    if (!loadingState.hasError) return null;

    return (
      <View style={[
        styles.errorContainer,
        { backgroundColor: colors?.background?.secondary || '#F8F9FA' },
        getContainerDimensions(),
      ]}>
        <Ionicons
          name="image-outline"
          size={32}
          color={colors?.text?.tertiary || '#999'}
          style={styles.errorIcon}
        />
        <Body
          color={colors?.text?.tertiary}
          style={styles.errorText}
        >
          Failed to load image
        </Body>
      </View>
    );
  };

  // Render placeholder
  const renderPlaceholder = () => {
    if (loadingState.isLoaded || loadingState.hasError) return null;
    if (!enableProgressiveLoading) return null;

    const placeholderSource = getPlaceholderSource();
    if (!placeholderSource) return null;

    return (
      <Image
        source={placeholderSource}
        style={[
          styles.placeholder,
          imageStyle,
          getContainerDimensions(),
        ]}
        resizeMode={resizeMode}
      />
    );
  };

  // Render main image
  const renderMainImage = () => {
    if (loadingState.hasError) return null;
    if (!optimizedImage && !isInView) return null;

    const imageSource = optimizedImage 
      ? { uri: optimizedImage.uri }
      : typeof source === 'number' 
        ? source 
        : source;

    return (
      <Animated.View style={{ opacity: fadeAnim }}>
        <Image
          source={imageSource}
          style={[
            styles.image,
            imageStyle,
            getContainerDimensions(),
          ]}
          resizeMode={resizeMode}
          onLoadStart={handleLoadStart}
          onLoad={handleLoad}
          onError={handleError}
          onLoadEnd={handleLoadEnd}
          accessibilityLabel={accessibilityLabel || alt}
          accessibilityHint={accessibilityHint}
          accessible={!!accessibilityLabel || !!alt}
        />
      </Animated.View>
    );
  };

  // Render content
  const renderContent = () => (
    <View
      ref={containerRef}
      style={[
        styles.container,
        getContainerDimensions(),
        style,
      ]}
      testID={testID}
    >
      {renderPlaceholder()}
      {renderMainImage()}
      {renderLoadingIndicator()}
      {renderErrorState()}
    </View>
  );

  // Handle press
  const handlePress = useCallback(() => {
    if (onPress && !loadingState.isLoading && !loadingState.hasError) {
      onPress();
    }
  }, [onPress, loadingState.isLoading, loadingState.hasError]);

  // Render with or without press handler
  if (onPress) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.8}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || alt}
        accessibilityHint={accessibilityHint || 'Tap to view image'}
        disabled={loadingState.isLoading || loadingState.hasError}
      >
        {renderContent()}
      </TouchableOpacity>
    );
  }

  return renderContent();
};

// Specialized image components
export const AvatarImage: React.FC<Omit<OptimizedImageProps, 'aspectRatio' | 'resizeMode'>> = (props) => (
  <OptimizedImage
    {...props}
    aspectRatio={1}
    resizeMode="cover"
    borderRadius={props.width ? props.width / 2 : 25}
    placeholderColor="#E0E0E0"
  />
);

export const ThumbnailImage: React.FC<OptimizedImageProps> = (props) => (
  <OptimizedImage
    {...props}
    quality={0.6}
    enableProgressiveLoading={true}
    showLoadingIndicator={false}
  />
);

export const HeroImage: React.FC<OptimizedImageProps> = (props) => (
  <OptimizedImage
    {...props}
    quality={0.9}
    enableLazyLoading={false}
    enableProgressiveLoading={true}
    showLoadingIndicator={true}
  />
);

export const GalleryImage: React.FC<OptimizedImageProps> = (props) => (
  <OptimizedImage
    {...props}
    quality={0.8}
    enableLazyLoading={true}
    enableProgressiveLoading={true}
    resizeMode="cover"
  />
);

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    opacity: 0.5,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    minHeight: 100,
  },
  errorIcon: {
    marginBottom: 8,
  },
  errorText: {
    textAlign: 'center',
    fontSize: 12,
  },
});

export default OptimizedImage;
