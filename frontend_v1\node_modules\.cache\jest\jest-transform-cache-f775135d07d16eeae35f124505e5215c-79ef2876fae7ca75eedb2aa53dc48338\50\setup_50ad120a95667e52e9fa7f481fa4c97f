60ff58f3e9afd86d7141940d7ec8198c
_getJestObj().mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
_getJestObj().mock('react-native', function () {
  var RN = jest.requireActual('react-native');
  return Object.assign({}, RN, {
    PixelRatio: Object.assign({}, RN.PixelRatio, {
      getFontScale: jest.fn(function () {
        return 1;
      }),
      get: jest.fn(function () {
        return 2;
      })
    })
  });
});
_getJestObj().mock('react-native/Libraries/Utilities/Dimensions', function () {
  return {
    get: jest.fn(function () {
      return {
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1
      };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };
});
_getJestObj().mock('react-native/Libraries/Utilities/Platform', function () {
  return {
    OS: 'ios',
    Version: '14.0',
    select: jest.fn(function (obj) {
      return obj.ios;
    })
  };
});
_getJestObj().mock('expo-status-bar', function () {
  return {
    StatusBar: 'StatusBar',
    setStatusBarStyle: jest.fn(),
    setStatusBarBackgroundColor: jest.fn()
  };
});
_getJestObj().mock('expo-constants', function () {
  return {
    default: {
      statusBarHeight: 44,
      deviceName: 'iPhone',
      platform: {
        ios: {
          platform: 'ios'
        }
      },
      appOwnership: 'standalone',
      expoVersion: '45.0.0'
    }
  };
});
_getJestObj().mock('expo-font', function () {
  return {
    loadAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    isLoaded: jest.fn(function () {
      return true;
    }),
    isLoading: jest.fn(function () {
      return false;
    })
  };
});
_getJestObj().mock('expo-linear-gradient', function () {
  return {
    LinearGradient: 'LinearGradient'
  };
});
_getJestObj().mock('expo-haptics', function () {
  return {
    impactAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    notificationAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    selectionAsync: jest.fn(function () {
      return Promise.resolve();
    }),
    ImpactFeedbackStyle: {
      Light: 'light',
      Medium: 'medium',
      Heavy: 'heavy'
    },
    NotificationFeedbackType: {
      Success: 'success',
      Warning: 'warning',
      Error: 'error'
    }
  };
});
_getJestObj().mock('expo-crypto', function () {
  return {
    randomUUID: jest.fn(function () {
      return 'mock-uuid-1234-5678-9012';
    }),
    digestStringAsync: jest.fn(function () {
      return Promise.resolve('mock-hash');
    })
  };
});
_getJestObj().mock('expo-location', function () {
  return {
    requestForegroundPermissionsAsync: jest.fn(function () {
      return Promise.resolve({
        status: 'granted'
      });
    }),
    getCurrentPositionAsync: jest.fn(function () {
      return Promise.resolve({
        coords: {
          latitude: 43.6532,
          longitude: -79.3832,
          accuracy: 10,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          speed: 0
        },
        timestamp: Date.now()
      });
    }),
    watchPositionAsync: jest.fn(function () {
      return Promise.resolve({
        remove: jest.fn()
      });
    })
  };
});
_getJestObj().mock('expo-local-authentication', function () {
  return {
    hasHardwareAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    supportedAuthenticationTypesAsync: jest.fn(function () {
      return Promise.resolve([1, 2]);
    }),
    isEnrolledAsync: jest.fn(function () {
      return Promise.resolve(true);
    }),
    authenticateAsync: jest.fn(function () {
      return Promise.resolve({
        success: true,
        error: undefined
      });
    }),
    AuthenticationType: {
      FINGERPRINT: 1,
      FACIAL_RECOGNITION: 2
    }
  };
});
_getJestObj().mock('react-i18next', function () {
  return {
    useTranslation: function useTranslation() {
      return {
        t: function t(key, options) {
          if (options && typeof options === 'object') {
            var result = key;
            Object.keys(options).forEach(function (optionKey) {
              result = result.replace(`{{${optionKey}}}`, options[optionKey]);
            });
            return result;
          }
          return key;
        },
        i18n: {
          language: 'en',
          changeLanguage: jest.fn(function () {
            return Promise.resolve();
          })
        }
      };
    },
    initReactI18next: {
      type: '3rdParty',
      init: jest.fn()
    }
  };
});
_getJestObj().mock('zustand', function () {
  return {
    create: jest.fn(function (fn) {
      var store = fn(function () {
        return {};
      }, function () {
        return {};
      });
      return function () {
        return store;
      };
    })
  };
});
_getJestObj().mock('@react-navigation/native', function () {
  return {
    useNavigation: function useNavigation() {
      return {
        navigate: jest.fn(),
        goBack: jest.fn(),
        reset: jest.fn(),
        setParams: jest.fn(),
        dispatch: jest.fn(),
        isFocused: jest.fn(function () {
          return true;
        }),
        canGoBack: jest.fn(function () {
          return true;
        }),
        getId: jest.fn(function () {
          return 'mock-route-id';
        }),
        getParent: jest.fn(),
        getState: jest.fn(function () {
          return {
            index: 0,
            routes: [{
              name: 'Home',
              key: 'home-key'
            }]
          };
        })
      };
    },
    useRoute: function useRoute() {
      return {
        key: 'mock-route-key',
        name: 'MockScreen',
        params: {}
      };
    },
    useFocusEffect: jest.fn(),
    useIsFocused: jest.fn(function () {
      return true;
    }),
    NavigationContainer: function NavigationContainer(_ref) {
      var children = _ref.children;
      return children;
    },
    createNavigationContainerRef: jest.fn(function () {
      return {
        current: {
          navigate: jest.fn(),
          reset: jest.fn(),
          goBack: jest.fn()
        }
      };
    })
  };
});
_getJestObj().mock('@react-navigation/stack', function () {
  return {
    createStackNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref2) {
          var children = _ref2.children;
          return children;
        },
        Screen: function Screen(_ref3) {
          var children = _ref3.children;
          return children;
        }
      };
    }),
    CardStyleInterpolators: {
      forHorizontalIOS: {},
      forVerticalIOS: {},
      forModalPresentationIOS: {}
    },
    TransitionPresets: {
      SlideFromRightIOS: {},
      ModalSlideFromBottomIOS: {}
    }
  };
});
_getJestObj().mock('@react-navigation/bottom-tabs', function () {
  return {
    createBottomTabNavigator: jest.fn(function () {
      return {
        Navigator: function Navigator(_ref4) {
          var children = _ref4.children;
          return children;
        },
        Screen: function Screen(_ref5) {
          var children = _ref5.children;
          return children;
        }
      };
    })
  };
});
Object.defineProperty(exports, "__esModule", {
  value: true
});
require("react-native-gesture-handler/jestSetup");
require("@testing-library/jest-native/extend-expect");
var _testingUtils = require("../utils/testingUtils");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
(0, _testingUtils.setupTestEnvironment)({
  enableAccessibilityTesting: true,
  enablePerformanceTesting: true,
  mockNetworkRequests: true,
  mockLocationServices: true,
  mockNotifications: true,
  logLevel: 'warn'
});
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
afterEach(function () {
  jest.clearAllMocks();
});
process.on('unhandledRejection', function (reason, promise) {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
jest.setTimeout(10000);
beforeEach(function () {
  jest.useFakeTimers();
});
afterEach(function () {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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