/**
 * Bundle Analyzer Component
 *
 * Development tool for analyzing bundle size, performance metrics,
 * and optimization opportunities in real-time.
 *
 * Features:
 * - Real-time bundle analysis
 * - Performance metrics display
 * - Optimization recommendations
 * - Component size tracking
 * - Memory usage monitoring
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Typography, Heading, Body } from '../typography/Typography';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles } from '../../contexts/MotorAccessibilityContext';
import {
  usePerformanceMonitoring,
  getPerformanceMetrics,
  getBundleSizeRecommendations,
  getCodeSplittingRecommendations,
  analyzeBundleSize,
} from '../../utils/bundleOptimizationUtils';

// Component props
export interface BundleAnalyzerProps {
  visible: boolean;
  onClose: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const BundleAnalyzer: React.FC<BundleAnalyzerProps> = ({
  visible,
  onClose,
  autoRefresh = true,
  refreshInterval = 5000,
}) => {
  // Hooks
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();
  const { metrics, recommendations, codeSplittingRecommendations } = usePerformanceMonitoring();

  // State
  const [activeTab, setActiveTab] = useState<'overview' | 'components' | 'recommendations'>('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Auto-refresh metrics
  useEffect(() => {
    if (!autoRefresh || !visible) return;

    const interval = setInterval(() => {
      setRefreshing(true);
      setTimeout(() => setRefreshing(false), 500);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, visible, refreshInterval]);

  // Manual refresh
  const handleRefresh = () => {
    setRefreshing(true);
    analyzeBundleSize();
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Format file size
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  // Get size color based on threshold
  const getSizeColor = (bytes: number): string => {
    if (bytes > 5 * 1024 * 1024) return colors?.status?.error || '#FF6B6B'; // > 5MB
    if (bytes > 2 * 1024 * 1024) return colors?.status?.warning || '#FFB347'; // > 2MB
    return colors?.status?.success || '#4CAF50';
  };

  // Render tab button
  const renderTabButton = (
    tab: 'overview' | 'components' | 'recommendations',
    label: string,
    icon: string
  ) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: activeTab === tab 
            ? colors?.primary?.default 
            : colors?.background?.secondary,
          borderColor: colors?.border?.primary,
        },
        touchTargetStyles,
      ]}
      onPress={() => setActiveTab(tab)}
      accessibilityRole="tab"
      accessibilityState={{ selected: activeTab === tab }}
      accessibilityLabel={label}
    >
      <Ionicons
        name={icon as any}
        size={16}
        color={activeTab === tab ? colors?.text?.inverse : colors?.text?.secondary}
        style={styles.tabIcon}
      />
      
      <Typography
        variant="caption"
        color={activeTab === tab ? colors?.text?.inverse : colors?.text?.secondary}
        style={styles.tabLabel}
      >
        {label}
      </Typography>
    </TouchableOpacity>
  );

  // Render overview tab
  const renderOverviewTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Bundle Size Card */}
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="cube"
            size={24}
            color={colors?.primary?.default}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Bundle Size
          </Heading>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Total Size:</Body>
          <Typography
            variant="subtitle1"
            color={getSizeColor(metrics?.bundleSize || 0)}
            style={styles.metricValue}
          >
            {formatSize(metrics?.bundleSize || 0)}
          </Typography>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Components:</Body>
          <Typography
            variant="subtitle1"
            color={colors?.text?.primary}
            style={styles.metricValue}
          >
            {metrics?.componentCount || 0}
          </Typography>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Assets:</Body>
          <Typography
            variant="subtitle1"
            color={colors?.text?.primary}
            style={styles.metricValue}
          >
            {metrics?.assetCount || 0}
          </Typography>
        </View>
      </View>

      {/* Performance Card */}
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="speedometer"
            size={24}
            color={colors?.primary?.default}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Performance
          </Heading>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Load Time:</Body>
          <Typography
            variant="subtitle1"
            color={colors?.text?.primary}
            style={styles.metricValue}
          >
            {metrics?.loadTime || 0}ms
          </Typography>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Memory Usage:</Body>
          <Typography
            variant="subtitle1"
            color={colors?.text?.primary}
            style={styles.metricValue}
          >
            {formatSize(metrics?.memoryUsage || 0)}
          </Typography>
        </View>
        
        <View style={styles.metricRow}>
          <Body color={colors?.text?.secondary}>Last Updated:</Body>
          <Typography
            variant="caption"
            color={colors?.text?.tertiary}
            style={styles.metricValue}
          >
            {metrics?.timestamp ? new Date(metrics.timestamp).toLocaleTimeString() : 'Never'}
          </Typography>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="flash"
            size={24}
            color={colors?.primary?.default}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Quick Actions
          </Heading>
        </View>
        
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: colors?.primary?.light, borderColor: colors?.primary?.default },
            touchTargetStyles,
          ]}
          onPress={handleRefresh}
          accessibilityRole="button"
          accessibilityLabel="Refresh analysis"
        >
          <Ionicons
            name="refresh"
            size={16}
            color={colors?.primary?.default}
            style={styles.actionIcon}
          />
          <Typography variant="button" color={colors?.primary?.default}>
            Refresh Analysis
          </Typography>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: colors?.background?.primary, borderColor: colors?.border?.primary },
            touchTargetStyles,
          ]}
          onPress={() => console.log('Export report')}
          accessibilityRole="button"
          accessibilityLabel="Export report"
        >
          <Ionicons
            name="download"
            size={16}
            color={colors?.text?.primary}
            style={styles.actionIcon}
          />
          <Typography variant="button" color={colors?.text?.primary}>
            Export Report
          </Typography>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  // Render recommendations tab
  const renderRecommendationsTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Bundle Recommendations */}
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="bulb"
            size={24}
            color={colors?.status?.warning}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Bundle Optimization
          </Heading>
        </View>
        
        {recommendations.length > 0 ? (
          recommendations.map((recommendation, index) => (
            <View key={index} style={styles.recommendationItem}>
              <Ionicons
                name="arrow-forward"
                size={16}
                color={colors?.text?.secondary}
                style={styles.recommendationIcon}
              />
              <Body color={colors?.text?.secondary} style={styles.recommendationText}>
                {recommendation}
              </Body>
            </View>
          ))
        ) : (
          <Body color={colors?.text?.tertiary} style={styles.noRecommendations}>
            No optimization recommendations at this time.
          </Body>
        )}
      </View>

      {/* Code Splitting Recommendations */}
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="git-branch"
            size={24}
            color={colors?.primary?.default}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Code Splitting
          </Heading>
        </View>
        
        {codeSplittingRecommendations.map((rec, index) => (
          <View key={index} style={styles.recommendationItem}>
            <View style={styles.recommendationHeader}>
              <Typography
                variant="subtitle2"
                color={colors?.text?.primary}
                style={styles.recommendationTitle}
              >
                {rec.strategy.replace(/_/g, ' ').toUpperCase()}
              </Typography>
              
              <View style={[
                styles.priorityBadge,
                { backgroundColor: rec.priority === 'high' ? colors?.status?.error : 
                                  rec.priority === 'medium' ? colors?.status?.warning : 
                                  colors?.status?.success }
              ]}>
                <Typography variant="caption" color={colors?.text?.inverse}>
                  {rec.priority.toUpperCase()}
                </Typography>
              </View>
            </View>
            
            <Body color={colors?.text?.secondary} style={styles.recommendationDescription}>
              {rec.description}
            </Body>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  // Render components tab
  const renderComponentsTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={[styles.card, { backgroundColor: colors?.background?.secondary }]}>
        <View style={styles.cardHeader}>
          <Ionicons
            name="layers"
            size={24}
            color={colors?.primary?.default}
            style={styles.cardIcon}
          />
          <Heading level={4} color={colors?.text?.primary}>
            Component Analysis
          </Heading>
        </View>
        
        <Body color={colors?.text?.tertiary} style={styles.comingSoon}>
          Detailed component analysis coming soon...
        </Body>
      </View>
    </ScrollView>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors?.background?.primary }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors?.border?.primary }]}>
          <View style={styles.headerContent}>
            <Ionicons
              name="analytics"
              size={24}
              color={colors?.primary?.default}
              style={styles.headerIcon}
            />
            <Heading level={3} color={colors?.text?.primary}>
              Bundle Analyzer
            </Heading>
          </View>
          
          <TouchableOpacity
            style={[styles.closeButton, touchTargetStyles]}
            onPress={onClose}
            accessibilityRole="button"
            accessibilityLabel="Close analyzer"
          >
            <Ionicons
              name="close"
              size={24}
              color={colors?.text?.secondary}
            />
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          {renderTabButton('overview', 'Overview', 'pie-chart')}
          {renderTabButton('components', 'Components', 'layers')}
          {renderTabButton('recommendations', 'Tips', 'bulb')}
        </View>

        {/* Content */}
        <View style={styles.content}>
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'components' && renderComponentsTab()}
          {activeTab === 'recommendations' && renderRecommendationsTab()}
        </View>

        {/* Refresh Indicator */}
        {refreshing && (
          <View style={[styles.refreshIndicator, { backgroundColor: colors?.primary?.default }]}>
            <Typography variant="caption" color={colors?.text?.inverse}>
              Refreshing...
            </Typography>
          </View>
        )}
      </View>
    </Modal>
  );
};

// Development-only floating button to open analyzer
export const BundleAnalyzerButton: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const { colors } = useHighContrastColors();

  if (!__DEV__) return null;

  return (
    <>
      <TouchableOpacity
        style={[
          styles.floatingButton,
          { backgroundColor: colors?.primary?.default }
        ]}
        onPress={() => setVisible(true)}
        accessibilityRole="button"
        accessibilityLabel="Open bundle analyzer"
      >
        <Ionicons
          name="analytics"
          size={24}
          color={colors?.text?.inverse}
        />
      </TouchableOpacity>

      <BundleAnalyzer
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 12,
  },
  closeButton: {
    padding: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabLabel: {
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardIcon: {
    marginRight: 12,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricValue: {
    fontWeight: '600',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  recommendationItem: {
    marginBottom: 12,
  },
  recommendationIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  recommendationText: {
    flex: 1,
    lineHeight: 20,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  recommendationTitle: {
    flex: 1,
    fontWeight: '600',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  recommendationDescription: {
    lineHeight: 18,
  },
  noRecommendations: {
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
  comingSoon: {
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 32,
  },
  refreshIndicator: {
    position: 'absolute',
    top: 100,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 100,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
});

export default BundleAnalyzer;
