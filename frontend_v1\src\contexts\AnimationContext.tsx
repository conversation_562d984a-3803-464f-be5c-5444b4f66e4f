/**
 * Animation Context Provider
 *
 * Provides global animation settings and utilities throughout
 * the application with accessibility and performance considerations.
 *
 * Features:
 * - Global animation settings
 * - Accessibility compliance
 * - Performance optimization
 * - Reduced motion support
 * - Animation presets
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Platform, AccessibilityInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { shouldReduceMotion, ANIMATION_DURATIONS } from '../utils/animationUtils';

// Animation settings interface
export interface AnimationSettings {
  enabled: boolean;
  reducedMotion: boolean;
  duration: keyof typeof ANIMATION_DURATIONS;
  enableHaptics: boolean;
  enableSounds: boolean;
  staggerDelay: number;
}

// Animation context interface
interface AnimationContextType {
  // Settings
  settings: AnimationSettings;
  
  // Controls
  enableAnimations: () => void;
  disableAnimations: () => void;
  toggleAnimations: () => void;
  setReducedMotion: (enabled: boolean) => void;
  setAnimationDuration: (duration: keyof typeof ANIMATION_DURATIONS) => void;
  
  // Utilities
  shouldAnimate: () => boolean;
  getDuration: (baseDuration?: keyof typeof ANIMATION_DURATIONS) => number;
  getStaggerDelay: () => number;
  
  // Presets
  applyAccessibilityPreset: () => void;
  applyPerformancePreset: () => void;
  applyDefaultPreset: () => void;
  
  // Reset
  resetToDefaults: () => void;
}

// Default settings
const DEFAULT_SETTINGS: AnimationSettings = {
  enabled: true,
  reducedMotion: false,
  duration: 'normal',
  enableHaptics: true,
  enableSounds: false,
  staggerDelay: 100,
};

// Create context
const AnimationContext = createContext<AnimationContextType | undefined>(undefined);

// Storage key
const ANIMATION_SETTINGS_KEY = '@vierla_animation_settings';

// Provider props
interface AnimationProviderProps {
  children: React.ReactNode;
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  // State
  const [settings, setSettings] = useState<AnimationSettings>(DEFAULT_SETTINGS);

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const saved = await AsyncStorage.getItem(ANIMATION_SETTINGS_KEY);
        if (saved) {
          const parsedSettings = JSON.parse(saved);
          setSettings({ ...DEFAULT_SETTINGS, ...parsedSettings });
        } else {
          // Check system accessibility settings
          await detectSystemPreferences();
        }
      } catch (error) {
        console.warn('Failed to load animation settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Detect system accessibility preferences
  const detectSystemPreferences = useCallback(async () => {
    try {
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        const isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled?.();
        
        if (isReduceMotionEnabled) {
          setSettings(prev => ({
            ...prev,
            reducedMotion: true,
            duration: 'fast',
            staggerDelay: 50,
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to detect system accessibility preferences:', error);
    }
  }, []);

  // Save settings
  const saveSettings = useCallback(async (newSettings: AnimationSettings) => {
    try {
      await AsyncStorage.setItem(ANIMATION_SETTINGS_KEY, JSON.stringify(newSettings));
    } catch (error) {
      console.warn('Failed to save animation settings:', error);
    }
  }, []);

  // Update settings helper
  const updateSettings = useCallback((updates: Partial<AnimationSettings>) => {
    setSettings(prev => {
      const newSettings = { ...prev, ...updates };
      saveSettings(newSettings);
      return newSettings;
    });
  }, [saveSettings]);

  // Enable animations
  const enableAnimations = useCallback(() => {
    updateSettings({ enabled: true });
  }, [updateSettings]);

  // Disable animations
  const disableAnimations = useCallback(() => {
    updateSettings({ enabled: false });
  }, [updateSettings]);

  // Toggle animations
  const toggleAnimations = useCallback(() => {
    updateSettings({ enabled: !settings.enabled });
  }, [settings.enabled, updateSettings]);

  // Set reduced motion
  const setReducedMotion = useCallback((enabled: boolean) => {
    updateSettings({ 
      reducedMotion: enabled,
      duration: enabled ? 'fast' : 'normal',
      staggerDelay: enabled ? 50 : 100,
    });
  }, [updateSettings]);

  // Set animation duration
  const setAnimationDuration = useCallback((duration: keyof typeof ANIMATION_DURATIONS) => {
    updateSettings({ duration });
  }, [updateSettings]);

  // Check if animations should be enabled
  const shouldAnimate = useCallback((): boolean => {
    if (!settings.enabled) return false;
    if (settings.reducedMotion) return false;
    if (shouldReduceMotion()) return false;
    return true;
  }, [settings.enabled, settings.reducedMotion]);

  // Get animation duration
  const getDuration = useCallback((baseDuration?: keyof typeof ANIMATION_DURATIONS): number => {
    const durationKey = baseDuration || settings.duration;
    return ANIMATION_DURATIONS[durationKey];
  }, [settings.duration]);

  // Get stagger delay
  const getStaggerDelay = useCallback((): number => {
    return settings.staggerDelay;
  }, [settings.staggerDelay]);

  // Apply accessibility preset
  const applyAccessibilityPreset = useCallback(() => {
    updateSettings({
      enabled: true,
      reducedMotion: true,
      duration: 'fast',
      enableHaptics: true,
      enableSounds: false,
      staggerDelay: 50,
    });
  }, [updateSettings]);

  // Apply performance preset
  const applyPerformancePreset = useCallback(() => {
    updateSettings({
      enabled: true,
      reducedMotion: false,
      duration: 'fast',
      enableHaptics: false,
      enableSounds: false,
      staggerDelay: 50,
    });
  }, [updateSettings]);

  // Apply default preset
  const applyDefaultPreset = useCallback(() => {
    updateSettings(DEFAULT_SETTINGS);
  }, [updateSettings]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    saveSettings(DEFAULT_SETTINGS);
  }, [saveSettings]);

  // Listen for system accessibility changes
  useEffect(() => {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      const subscription = AccessibilityInfo.addEventListener(
        'reduceMotionChanged',
        (isReduceMotionEnabled: boolean) => {
          setReducedMotion(isReduceMotionEnabled);
        }
      );

      return () => subscription?.remove();
    }
  }, [setReducedMotion]);

  // Context value
  const contextValue: AnimationContextType = {
    // Settings
    settings,
    
    // Controls
    enableAnimations,
    disableAnimations,
    toggleAnimations,
    setReducedMotion,
    setAnimationDuration,
    
    // Utilities
    shouldAnimate,
    getDuration,
    getStaggerDelay,
    
    // Presets
    applyAccessibilityPreset,
    applyPerformancePreset,
    applyDefaultPreset,
    
    // Reset
    resetToDefaults,
  };

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

// Hook to use animation context
export const useAnimation = (): AnimationContextType => {
  const context = useContext(AnimationContext);
  
  if (context === undefined) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  
  return context;
};

// Convenience hooks
export const useAnimationSettings = () => {
  const { settings, shouldAnimate, getDuration, getStaggerDelay } = useAnimation();
  
  return {
    settings,
    shouldAnimate: shouldAnimate(),
    duration: getDuration(),
    staggerDelay: getStaggerDelay(),
  };
};

export const useAnimationControls = () => {
  const {
    enableAnimations,
    disableAnimations,
    toggleAnimations,
    setReducedMotion,
    setAnimationDuration,
  } = useAnimation();
  
  return {
    enable: enableAnimations,
    disable: disableAnimations,
    toggle: toggleAnimations,
    setReducedMotion,
    setDuration: setAnimationDuration,
  };
};

export const useAnimationPresets = () => {
  const {
    applyAccessibilityPreset,
    applyPerformancePreset,
    applyDefaultPreset,
    resetToDefaults,
  } = useAnimation();
  
  return {
    accessibility: applyAccessibilityPreset,
    performance: applyPerformancePreset,
    default: applyDefaultPreset,
    reset: resetToDefaults,
  };
};

export default AnimationProvider;
