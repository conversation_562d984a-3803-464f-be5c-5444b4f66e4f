/**
 * Responsive Context Provider
 *
 * Provides responsive design capabilities and screen information
 * throughout the application with real-time updates.
 *
 * Features:
 * - Screen dimension tracking
 * - Breakpoint management
 * - Device type detection
 * - Orientation handling
 * - Responsive utilities
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Dimensions, Platform } from 'react-native';
import { getDeviceType, getScreenDimensions, getSafeAreaInsets } from '../utils/responsiveUtils';

// Screen size breakpoints
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,
} as const;

export type BreakpointKey = keyof typeof BREAKPOINTS;
export type DeviceType = 'phone' | 'tablet' | 'desktop';
export type Orientation = 'portrait' | 'landscape';

// Responsive value type
export type ResponsiveValue<T> = T | Partial<Record<BreakpointKey, T>>;

// Screen information interface
export interface ScreenInfo {
  width: number;
  height: number;
  scale: number;
  fontScale: number;
  deviceType: DeviceType;
  orientation: Orientation;
  breakpoint: BreakpointKey;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

// Responsive context interface
interface ResponsiveContextType {
  // Screen information
  screen: ScreenInfo;
  
  // Responsive utilities
  getResponsiveValue: <T>(value: ResponsiveValue<T>) => T;
  isBreakpoint: (breakpoint: BreakpointKey) => boolean;
  isBreakpointUp: (breakpoint: BreakpointKey) => boolean;
  isBreakpointDown: (breakpoint: BreakpointKey) => boolean;
  isBreakpointBetween: (min: BreakpointKey, max: BreakpointKey) => boolean;
  
  // Device checks
  isPhone: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLandscape: boolean;
  isPortrait: boolean;
  
  // Layout utilities
  getColumns: (config: ResponsiveValue<number>) => number;
  getSpacing: (config: ResponsiveValue<number>) => number;
  getPadding: (config: ResponsiveValue<number>) => number;
  
  // Adaptive sizing
  scaleSize: (size: number) => number;
  scaleFontSize: (size: number) => number;
}

// Create context
const ResponsiveContext = createContext<ResponsiveContextType | undefined>(undefined);

// Provider props
interface ResponsiveProviderProps {
  children: React.ReactNode;
}

export const ResponsiveProvider: React.FC<ResponsiveProviderProps> = ({ children }) => {
  // Get current breakpoint from width
  const getBreakpoint = (width: number): BreakpointKey => {
    if (width >= BREAKPOINTS.xxl) return 'xxl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  };

  // Initialize screen info
  const initializeScreenInfo = (): ScreenInfo => {
    const dimensions = Dimensions.get('window');
    const scale = Dimensions.get('screen').scale;
    const fontScale = Dimensions.get('screen').fontScale;
    
    return {
      width: dimensions.width,
      height: dimensions.height,
      scale,
      fontScale,
      deviceType: getDeviceType(),
      orientation: dimensions.width > dimensions.height ? 'landscape' : 'portrait',
      breakpoint: getBreakpoint(dimensions.width),
      safeAreaInsets: getSafeAreaInsets(),
    };
  };

  // State
  const [screen, setScreen] = useState<ScreenInfo>(initializeScreenInfo);

  // Update screen info on dimension changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window, screen: screenDims }) => {
      setScreen({
        width: window.width,
        height: window.height,
        scale: screenDims.scale,
        fontScale: screenDims.fontScale,
        deviceType: getDeviceType(),
        orientation: window.width > window.height ? 'landscape' : 'portrait',
        breakpoint: getBreakpoint(window.width),
        safeAreaInsets: getSafeAreaInsets(),
      });
    });

    return () => subscription?.remove();
  }, []);

  // Get responsive value based on current breakpoint
  const getResponsiveValue = useCallback(<T,>(value: ResponsiveValue<T>): T => {
    if (typeof value !== 'object' || value === null || Array.isArray(value)) {
      return value as T;
    }

    const responsiveObj = value as Partial<Record<BreakpointKey, T>>;
    
    // Try current breakpoint first
    if (responsiveObj[screen.breakpoint] !== undefined) {
      return responsiveObj[screen.breakpoint] as T;
    }

    // Fallback to smaller breakpoints
    const breakpointOrder: BreakpointKey[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(screen.breakpoint);

    for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
      const fallbackBreakpoint = breakpointOrder[i];
      if (responsiveObj[fallbackBreakpoint] !== undefined) {
        return responsiveObj[fallbackBreakpoint] as T;
      }
    }

    // Return first available value
    const firstValue = Object.values(responsiveObj)[0];
    return firstValue as T;
  }, [screen.breakpoint]);

  // Breakpoint utilities
  const isBreakpoint = useCallback((breakpoint: BreakpointKey): boolean => {
    return screen.breakpoint === breakpoint;
  }, [screen.breakpoint]);

  const isBreakpointUp = useCallback((breakpoint: BreakpointKey): boolean => {
    return screen.width >= BREAKPOINTS[breakpoint];
  }, [screen.width]);

  const isBreakpointDown = useCallback((breakpoint: BreakpointKey): boolean => {
    return screen.width < BREAKPOINTS[breakpoint];
  }, [screen.width]);

  const isBreakpointBetween = useCallback((min: BreakpointKey, max: BreakpointKey): boolean => {
    return screen.width >= BREAKPOINTS[min] && screen.width < BREAKPOINTS[max];
  }, [screen.width]);

  // Device type checks
  const isPhone = screen.deviceType === 'phone';
  const isTablet = screen.deviceType === 'tablet';
  const isDesktop = screen.deviceType === 'desktop';
  const isLandscape = screen.orientation === 'landscape';
  const isPortrait = screen.orientation === 'portrait';

  // Layout utilities
  const getColumns = useCallback((config: ResponsiveValue<number>): number => {
    return getResponsiveValue(config);
  }, [getResponsiveValue]);

  const getSpacing = useCallback((config: ResponsiveValue<number>): number => {
    return getResponsiveValue(config);
  }, [getResponsiveValue]);

  const getPadding = useCallback((config: ResponsiveValue<number>): number => {
    return getResponsiveValue(config);
  }, [getResponsiveValue]);

  // Adaptive sizing
  const scaleSize = useCallback((size: number): number => {
    const baseWidth = 375; // iPhone X width as base
    const scale = Math.min(screen.width / baseWidth, 1.3); // Cap at 1.3x
    return Math.round(size * scale);
  }, [screen.width]);

  const scaleFontSize = useCallback((size: number): number => {
    return Math.round(size * screen.fontScale);
  }, [screen.fontScale]);

  // Context value
  const contextValue: ResponsiveContextType = {
    // Screen information
    screen,
    
    // Responsive utilities
    getResponsiveValue,
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    isBreakpointBetween,
    
    // Device checks
    isPhone,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    
    // Layout utilities
    getColumns,
    getSpacing,
    getPadding,
    
    // Adaptive sizing
    scaleSize,
    scaleFontSize,
  };

  return (
    <ResponsiveContext.Provider value={contextValue}>
      {children}
    </ResponsiveContext.Provider>
  );
};

// Hook to use responsive context
export const useResponsive = (): ResponsiveContextType => {
  const context = useContext(ResponsiveContext);
  
  if (context === undefined) {
    throw new Error('useResponsive must be used within a ResponsiveProvider');
  }
  
  return context;
};

// Convenience hooks
export const useBreakpoint = () => {
  const { screen, isBreakpoint, isBreakpointUp, isBreakpointDown, isBreakpointBetween } = useResponsive();
  
  return {
    current: screen.breakpoint,
    is: isBreakpoint,
    up: isBreakpointUp,
    down: isBreakpointDown,
    between: isBreakpointBetween,
  };
};

export const useDeviceType = () => {
  const { screen, isPhone, isTablet, isDesktop } = useResponsive();
  
  return {
    type: screen.deviceType,
    isPhone,
    isTablet,
    isDesktop,
  };
};

export const useOrientation = () => {
  const { screen, isLandscape, isPortrait } = useResponsive();
  
  return {
    orientation: screen.orientation,
    isLandscape,
    isPortrait,
  };
};

export const useScreenDimensions = () => {
  const { screen } = useResponsive();
  
  return {
    width: screen.width,
    height: screen.height,
    scale: screen.scale,
    fontScale: screen.fontScale,
    safeAreaInsets: screen.safeAreaInsets,
  };
};

export const useResponsiveValue = <T,>(value: ResponsiveValue<T>): T => {
  const { getResponsiveValue } = useResponsive();
  return getResponsiveValue(value);
};

export const useAdaptiveSize = () => {
  const { scaleSize, scaleFontSize } = useResponsive();
  
  return {
    scaleSize,
    scaleFontSize,
  };
};

export default ResponsiveProvider;
