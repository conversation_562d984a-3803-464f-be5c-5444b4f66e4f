/**
 * Animated Button Component
 *
 * Enhanced button component with smooth animations and micro-interactions
 * for improved user experience and visual feedback.
 *
 * Features:
 * - Press animations
 * - Loading states
 * - Success/Error feedback
 * - Accessibility compliance
 * - Customizable animations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef, useCallback, useEffect } from 'react';
import {
  TouchableOpacity,
  Animated,
  StyleSheet,
  Platform,
  AccessibilityInfo,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';
import { Typography } from '../typography/Typography';
import {
  createPressAnimation,
  createLoadingAnimation,
  pulse,
  shake,
  shouldReduceMotion,
  ANIMATION_DURATIONS,
} from '../../utils/animationUtils';

// Button states
export type ButtonState = 'idle' | 'loading' | 'success' | 'error';

// Component props
export interface AnimatedButtonProps {
  // Content
  title: string;
  icon?: string;
  iconPosition?: 'left' | 'right';
  
  // Behavior
  onPress: () => void;
  disabled?: boolean;
  state?: ButtonState;
  
  // Styling
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  style?: any;
  
  // Animation
  enableAnimations?: boolean;
  pressScale?: number;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  
  // Testing
  testID?: string;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  title,
  icon,
  iconPosition = 'left',
  onPress,
  disabled = false,
  state = 'idle',
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  style,
  enableAnimations = true,
  pressScale = 0.95,
  accessibilityLabel,
  accessibilityHint,
  testID,
}) => {
  // Animation values
  const scaleValue = useRef(new Animated.Value(1)).current;
  const rotationValue = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const shakeValue = useRef(new Animated.Value(0)).current;

  // Hooks
  const { colors } = useHighContrastColors();
  const touchTargetStyles = useTouchTargetStyles();
  const triggerHapticFeedback = useHapticFeedback();

  // Press animation handlers
  const { pressIn, pressOut } = createPressAnimation(scaleValue, pressScale);

  // Handle press in
  const handlePressIn = useCallback(() => {
    if (!disabled && enableAnimations && !shouldReduceMotion()) {
      pressIn();
    }
    triggerHapticFeedback('light');
  }, [disabled, enableAnimations, pressIn, triggerHapticFeedback]);

  // Handle press out
  const handlePressOut = useCallback(() => {
    if (!disabled && enableAnimations && !shouldReduceMotion()) {
      pressOut();
    }
  }, [disabled, enableAnimations, pressOut]);

  // Handle press
  const handlePress = useCallback(() => {
    if (!disabled && state !== 'loading') {
      triggerHapticFeedback('medium');
      onPress();
    }
  }, [disabled, state, onPress, triggerHapticFeedback]);

  // Loading animation
  useEffect(() => {
    if (state === 'loading' && enableAnimations && !shouldReduceMotion()) {
      const loadingAnimation = createLoadingAnimation(rotationValue);
      loadingAnimation.start();
      
      return () => loadingAnimation.stop();
    } else {
      rotationValue.setValue(0);
    }
  }, [state, enableAnimations, rotationValue]);

  // Success animation
  useEffect(() => {
    if (state === 'success' && enableAnimations && !shouldReduceMotion()) {
      const successAnimation = pulse(scaleValue, 1.1);
      successAnimation.start(() => {
        // Announce success to screen readers
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          AccessibilityInfo.announceForAccessibility('Action completed successfully');
        }
      });
    }
  }, [state, enableAnimations, scaleValue]);

  // Error animation
  useEffect(() => {
    if (state === 'error' && enableAnimations && !shouldReduceMotion()) {
      const errorAnimation = shake(shakeValue, 8);
      errorAnimation.start(() => {
        // Announce error to screen readers
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          AccessibilityInfo.announceForAccessibility('Action failed. Please try again.');
        }
      });
    }
  }, [state, enableAnimations, shakeValue]);

  // Get button styles based on variant and state
  const getButtonStyles = () => {
    const baseStyles = [styles.button, styles[`${size}Button`]];
    
    if (fullWidth) {
      baseStyles.push(styles.fullWidth);
    }
    
    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyles.push({
          backgroundColor: disabled ? colors?.button?.disabled || '#CCC' : colors?.primary?.default || '#5A7A63',
        });
        break;
      case 'secondary':
        baseStyles.push({
          backgroundColor: disabled ? colors?.button?.disabled || '#CCC' : colors?.secondary?.default || '#F0F0F0',
          borderWidth: 1,
          borderColor: colors?.border?.primary || '#DDD',
        });
        break;
      case 'outline':
        baseStyles.push({
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor: disabled ? colors?.button?.disabled || '#CCC' : colors?.primary?.default || '#5A7A63',
        });
        break;
      case 'ghost':
        baseStyles.push({
          backgroundColor: 'transparent',
        });
        break;
    }
    
    // State-specific styles
    if (state === 'loading') {
      baseStyles.push(styles.loadingButton);
    }
    
    return baseStyles;
  };

  // Get text color
  const getTextColor = () => {
    if (disabled) return colors?.text?.disabled || '#999';
    
    switch (variant) {
      case 'primary':
        return colors?.text?.inverse || '#FFFFFF';
      case 'secondary':
        return colors?.text?.primary || '#333';
      case 'outline':
        return colors?.primary?.default || '#5A7A63';
      case 'ghost':
        return colors?.primary?.default || '#5A7A63';
      default:
        return colors?.text?.primary || '#333';
    }
  };

  // Get icon size based on button size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  // Render loading icon
  const renderLoadingIcon = () => {
    if (state !== 'loading') return null;
    
    return (
      <Animated.View
        style={[
          styles.iconContainer,
          {
            transform: [
              {
                rotate: rotationValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }),
              },
            ],
          },
        ]}
      >
        <Ionicons
          name="refresh"
          size={getIconSize()}
          color={getTextColor()}
        />
      </Animated.View>
    );
  };

  // Render icon
  const renderIcon = () => {
    if (state === 'loading') return renderLoadingIcon();
    if (!icon) return null;
    
    let iconName = icon;
    if (state === 'success') iconName = 'checkmark';
    if (state === 'error') iconName = 'close';
    
    return (
      <View style={styles.iconContainer}>
        <Ionicons
          name={iconName as any}
          size={getIconSize()}
          color={getTextColor()}
        />
      </View>
    );
  };

  // Get button content
  const getButtonTitle = () => {
    switch (state) {
      case 'loading':
        return 'Loading...';
      case 'success':
        return 'Success!';
      case 'error':
        return 'Try Again';
      default:
        return title;
    }
  };

  return (
    <TouchableOpacity
      style={[touchTargetStyles, style]}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || state === 'loading'}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        disabled: disabled || state === 'loading',
        busy: state === 'loading',
      }}
      testID={testID}
    >
      <Animated.View
        style={[
          getButtonStyles(),
          {
            transform: [
              { scale: scaleValue },
              { translateX: shakeValue },
            ],
            opacity: opacityValue,
          },
        ]}
      >
        {iconPosition === 'left' && renderIcon()}
        
        <Typography
          variant="button"
          color={getTextColor()}
          style={[
            styles.buttonText,
            icon && iconPosition === 'left' && styles.textWithLeftIcon,
            icon && iconPosition === 'right' && styles.textWithRightIcon,
          ]}
        >
          {getButtonTitle()}
        </Typography>
        
        {iconPosition === 'right' && renderIcon()}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  smallButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  mediumButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  largeButton: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 10,
  },
  fullWidth: {
    width: '100%',
  },
  loadingButton: {
    opacity: 0.8,
  },
  buttonText: {
    textAlign: 'center',
  },
  textWithLeftIcon: {
    marginLeft: 8,
  },
  textWithRightIcon: {
    marginRight: 8,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default AnimatedButton;
