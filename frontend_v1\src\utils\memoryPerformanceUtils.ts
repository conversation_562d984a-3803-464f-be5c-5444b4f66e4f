/**
 * Memory & Performance Monitoring Utilities
 *
 * Comprehensive utilities for monitoring memory usage, detecting leaks,
 * and tracking performance metrics in React Native applications.
 *
 * Features:
 * - Memory usage monitoring
 * - Memory leak detection
 * - Performance profiling
 * - Component lifecycle tracking
 * - Automated cleanup
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';

// Performance monitoring configuration
export interface PerformanceMonitoringConfig {
  enableMemoryMonitoring: boolean;
  enableLeakDetection: boolean;
  enablePerformanceProfiling: boolean;
  enableComponentTracking: boolean;
  memoryThresholdMB: number;
  leakDetectionInterval: number; // milliseconds
  performanceSampleRate: number; // 0-1
  maxComponentInstances: number;
}

// Memory metrics
export interface MemoryMetrics {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  usedMemoryMB: number;
  totalMemoryMB: number;
  memoryUsagePercent: number;
  timestamp: number;
}

// Performance metrics
export interface PerformanceMetrics {
  fps: number;
  frameDrops: number;
  renderTime: number;
  jsThreadUsage: number;
  uiThreadUsage: number;
  bridgeUsage: number;
  timestamp: number;
}

// Component lifecycle tracking
export interface ComponentLifecycleMetrics {
  componentName: string;
  instanceCount: number;
  mountTime: number;
  unmountTime: number;
  renderCount: number;
  lastRenderTime: number;
  memoryUsage: number;
  isActive: boolean;
}

// Memory leak detection result
export interface MemoryLeakDetection {
  hasLeaks: boolean;
  suspiciousComponents: string[];
  memoryGrowthRate: number;
  recommendations: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Default configuration
const DEFAULT_CONFIG: PerformanceMonitoringConfig = {
  enableMemoryMonitoring: __DEV__,
  enableLeakDetection: __DEV__,
  enablePerformanceProfiling: __DEV__,
  enableComponentTracking: __DEV__,
  memoryThresholdMB: 100,
  leakDetectionInterval: 30000, // 30 seconds
  performanceSampleRate: 0.1, // 10%
  maxComponentInstances: 50,
};

// Global tracking variables
const memoryHistory: MemoryMetrics[] = [];
const performanceHistory: PerformanceMetrics[] = [];
const componentRegistry = new Map<string, ComponentLifecycleMetrics>();
const activeTimers = new Set<NodeJS.Timeout>();
const activeIntervals = new Set<NodeJS.Timeout>();
const activeListeners = new Map<string, Function>();

let monitoringInterval: NodeJS.Timeout | null = null;
let leakDetectionInterval: NodeJS.Timeout | null = null;

/**
 * Get current memory metrics
 */
export const getCurrentMemoryMetrics = (): MemoryMetrics => {
  let metrics: MemoryMetrics;

  if (Platform.OS === 'web' && 'memory' in performance) {
    const memory = (performance as any).memory;
    metrics = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usedMemoryMB: memory.usedJSHeapSize / (1024 * 1024),
      totalMemoryMB: memory.totalJSHeapSize / (1024 * 1024),
      memoryUsagePercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      timestamp: Date.now(),
    };
  } else {
    // For React Native, we'll estimate based on component count and other factors
    const estimatedUsage = componentRegistry.size * 1024 * 1024; // Rough estimate
    metrics = {
      usedJSHeapSize: estimatedUsage,
      totalJSHeapSize: estimatedUsage * 1.5,
      jsHeapSizeLimit: 512 * 1024 * 1024, // 512MB limit estimate
      usedMemoryMB: estimatedUsage / (1024 * 1024),
      totalMemoryMB: (estimatedUsage * 1.5) / (1024 * 1024),
      memoryUsagePercent: (estimatedUsage / (512 * 1024 * 1024)) * 100,
      timestamp: Date.now(),
    };
  }

  return metrics;
};

/**
 * Get current performance metrics
 */
export const getCurrentPerformanceMetrics = (): PerformanceMetrics => {
  // In a real implementation, these would be measured using native modules
  // or performance APIs. For now, we'll provide mock data.
  return {
    fps: 60 - Math.random() * 5, // Simulate slight FPS variations
    frameDrops: Math.floor(Math.random() * 3),
    renderTime: 8 + Math.random() * 8, // 8-16ms
    jsThreadUsage: Math.random() * 30, // 0-30%
    uiThreadUsage: Math.random() * 20, // 0-20%
    bridgeUsage: Math.random() * 10, // 0-10%
    timestamp: Date.now(),
  };
};

/**
 * Track component lifecycle
 */
export const trackComponentLifecycle = (
  componentName: string,
  event: 'mount' | 'unmount' | 'render'
): void => {
  if (!DEFAULT_CONFIG.enableComponentTracking) return;

  const now = Date.now();
  const existing = componentRegistry.get(componentName);

  switch (event) {
    case 'mount':
      if (existing) {
        existing.instanceCount++;
        existing.mountTime = now;
        existing.isActive = true;
      } else {
        componentRegistry.set(componentName, {
          componentName,
          instanceCount: 1,
          mountTime: now,
          unmountTime: 0,
          renderCount: 0,
          lastRenderTime: now,
          memoryUsage: 0,
          isActive: true,
        });
      }
      break;

    case 'unmount':
      if (existing) {
        existing.instanceCount = Math.max(0, existing.instanceCount - 1);
        existing.unmountTime = now;
        existing.isActive = existing.instanceCount > 0;
      }
      break;

    case 'render':
      if (existing) {
        existing.renderCount++;
        existing.lastRenderTime = now;
      }
      break;
  }
};

/**
 * Start memory monitoring
 */
export const startMemoryMonitoring = (): void => {
  if (!DEFAULT_CONFIG.enableMemoryMonitoring || monitoringInterval) return;

  monitoringInterval = setInterval(() => {
    const memoryMetrics = getCurrentMemoryMetrics();
    const performanceMetrics = getCurrentPerformanceMetrics();

    // Store metrics (keep last 100 entries)
    memoryHistory.push(memoryMetrics);
    performanceHistory.push(performanceMetrics);

    if (memoryHistory.length > 100) {
      memoryHistory.shift();
    }
    if (performanceHistory.length > 100) {
      performanceHistory.shift();
    }

    // Check for memory threshold breach
    if (memoryMetrics.usedMemoryMB > DEFAULT_CONFIG.memoryThresholdMB) {
      console.warn(`Memory usage high: ${memoryMetrics.usedMemoryMB.toFixed(2)}MB`);
    }

    // Check for performance issues
    if (performanceMetrics.fps < 55) {
      console.warn(`Low FPS detected: ${performanceMetrics.fps.toFixed(1)}`);
    }
  }, 1000);

  activeIntervals.add(monitoringInterval);
};

/**
 * Stop memory monitoring
 */
export const stopMemoryMonitoring = (): void => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    activeIntervals.delete(monitoringInterval);
    monitoringInterval = null;
  }
};

/**
 * Start memory leak detection
 */
export const startLeakDetection = (): void => {
  if (!DEFAULT_CONFIG.enableLeakDetection || leakDetectionInterval) return;

  leakDetectionInterval = setInterval(() => {
    const leakDetection = detectMemoryLeaks();
    
    if (leakDetection.hasLeaks) {
      console.warn('Memory leaks detected:', leakDetection);
      
      if (leakDetection.severity === 'critical') {
        // Trigger automatic cleanup
        performAutomaticCleanup();
      }
    }
  }, DEFAULT_CONFIG.leakDetectionInterval);

  activeIntervals.add(leakDetectionInterval);
};

/**
 * Stop memory leak detection
 */
export const stopLeakDetection = (): void => {
  if (leakDetectionInterval) {
    clearInterval(leakDetectionInterval);
    activeIntervals.delete(leakDetectionInterval);
    leakDetectionInterval = null;
  }
};

/**
 * Detect memory leaks
 */
export const detectMemoryLeaks = (): MemoryLeakDetection => {
  const suspiciousComponents: string[] = [];
  const recommendations: string[] = [];
  
  // Check for components with high instance counts
  for (const [name, metrics] of componentRegistry.entries()) {
    if (metrics.instanceCount > DEFAULT_CONFIG.maxComponentInstances) {
      suspiciousComponents.push(name);
    }
  }

  // Calculate memory growth rate
  let memoryGrowthRate = 0;
  if (memoryHistory.length >= 2) {
    const recent = memoryHistory.slice(-10);
    const older = memoryHistory.slice(-20, -10);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.usedMemoryMB, 0) / recent.length;
    const olderAvg = older.reduce((sum, m) => sum + m.usedMemoryMB, 0) / older.length;
    
    memoryGrowthRate = ((recentAvg - olderAvg) / olderAvg) * 100;
  }

  // Generate recommendations
  if (suspiciousComponents.length > 0) {
    recommendations.push(`High instance count for: ${suspiciousComponents.join(', ')}`);
  }
  
  if (memoryGrowthRate > 10) {
    recommendations.push('Memory usage growing rapidly, check for leaks');
  }
  
  if (activeTimers.size > 20) {
    recommendations.push('Many active timers, ensure proper cleanup');
  }
  
  if (activeListeners.size > 50) {
    recommendations.push('Many active listeners, ensure proper removal');
  }

  // Determine severity
  let severity: MemoryLeakDetection['severity'] = 'low';
  if (memoryGrowthRate > 20 || suspiciousComponents.length > 5) {
    severity = 'high';
  } else if (memoryGrowthRate > 15 || suspiciousComponents.length > 3) {
    severity = 'medium';
  }
  
  const currentMemory = getCurrentMemoryMetrics();
  if (currentMemory.memoryUsagePercent > 80) {
    severity = 'critical';
  }

  return {
    hasLeaks: suspiciousComponents.length > 0 || memoryGrowthRate > 10,
    suspiciousComponents,
    memoryGrowthRate,
    recommendations,
    severity,
  };
};

/**
 * Perform automatic cleanup
 */
export const performAutomaticCleanup = (): void => {
  console.log('Performing automatic cleanup...');
  
  // Clear old timers
  activeTimers.forEach(timer => {
    clearTimeout(timer);
  });
  activeTimers.clear();
  
  // Clear old intervals (except monitoring ones)
  activeIntervals.forEach(interval => {
    if (interval !== monitoringInterval && interval !== leakDetectionInterval) {
      clearInterval(interval);
    }
  });
  
  // Remove old listeners
  activeListeners.clear();
  
  // Clean up component registry
  const now = Date.now();
  for (const [name, metrics] of componentRegistry.entries()) {
    if (!metrics.isActive && (now - metrics.unmountTime) > 60000) { // 1 minute
      componentRegistry.delete(name);
    }
  }
  
  // Trigger garbage collection if available
  if (global.gc) {
    global.gc();
  }
};

/**
 * Get memory usage summary
 */
export const getMemoryUsageSummary = () => {
  const current = getCurrentMemoryMetrics();
  const components = Array.from(componentRegistry.values());
  
  return {
    current,
    totalComponents: components.length,
    activeComponents: components.filter(c => c.isActive).length,
    highInstanceComponents: components.filter(c => c.instanceCount > 10),
    memoryHistory: memoryHistory.slice(-10),
    activeTimers: activeTimers.size,
    activeIntervals: activeIntervals.size,
    activeListeners: activeListeners.size,
  };
};

/**
 * Get performance summary
 */
export const getPerformanceSummary = () => {
  const current = getCurrentPerformanceMetrics();
  const recent = performanceHistory.slice(-10);
  
  const avgFps = recent.length > 0 
    ? recent.reduce((sum, p) => sum + p.fps, 0) / recent.length 
    : current.fps;
    
  const avgRenderTime = recent.length > 0
    ? recent.reduce((sum, p) => sum + p.renderTime, 0) / recent.length
    : current.renderTime;

  return {
    current,
    averageFps: avgFps,
    averageRenderTime: avgRenderTime,
    performanceHistory: recent,
    frameDrops: recent.reduce((sum, p) => sum + p.frameDrops, 0),
  };
};

/**
 * Register timer for tracking
 */
export const registerTimer = (timer: NodeJS.Timeout): void => {
  activeTimers.add(timer);
};

/**
 * Unregister timer
 */
export const unregisterTimer = (timer: NodeJS.Timeout): void => {
  activeTimers.delete(timer);
};

/**
 * Register event listener for tracking
 */
export const registerListener = (key: string, listener: Function): void => {
  activeListeners.set(key, listener);
};

/**
 * Unregister event listener
 */
export const unregisterListener = (key: string): void => {
  activeListeners.delete(key);
};

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = () => {
  const [memoryMetrics, setMemoryMetrics] = React.useState<MemoryMetrics | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = React.useState<PerformanceMetrics | null>(null);
  const [leakDetection, setLeakDetection] = React.useState<MemoryLeakDetection | null>(null);

  React.useEffect(() => {
    startMemoryMonitoring();
    startLeakDetection();

    const updateInterval = setInterval(() => {
      setMemoryMetrics(getCurrentMemoryMetrics());
      setPerformanceMetrics(getCurrentPerformanceMetrics());
      setLeakDetection(detectMemoryLeaks());
    }, 2000);

    return () => {
      clearInterval(updateInterval);
      stopMemoryMonitoring();
      stopLeakDetection();
    };
  }, []);

  return {
    memoryMetrics,
    performanceMetrics,
    leakDetection,
    memoryUsageSummary: getMemoryUsageSummary(),
    performanceSummary: getPerformanceSummary(),
    cleanup: performAutomaticCleanup,
  };
};

/**
 * Component lifecycle hook
 */
export const useComponentLifecycleTracking = (componentName: string) => {
  React.useEffect(() => {
    trackComponentLifecycle(componentName, 'mount');
    
    return () => {
      trackComponentLifecycle(componentName, 'unmount');
    };
  }, [componentName]);

  React.useEffect(() => {
    trackComponentLifecycle(componentName, 'render');
  });
};

export default {
  getCurrentMemoryMetrics,
  getCurrentPerformanceMetrics,
  trackComponentLifecycle,
  startMemoryMonitoring,
  stopMemoryMonitoring,
  startLeakDetection,
  stopLeakDetection,
  detectMemoryLeaks,
  performAutomaticCleanup,
  getMemoryUsageSummary,
  getPerformanceSummary,
  registerTimer,
  unregisterTimer,
  registerListener,
  unregisterListener,
  usePerformanceMonitor,
  useComponentLifecycleTracking,
  DEFAULT_CONFIG,
};
