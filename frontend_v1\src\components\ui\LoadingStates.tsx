/**
 * Loading States Component - Comprehensive Loading UI Components
 *
 * Component Contract:
 * - Provides various loading states for different contexts
 * - Supports customizable loading messages and animations
 * - Implements accessibility and responsive design
 * - Includes skeleton loading for better UX
 * - Supports different loading sizes and styles
 * - Integrates with app theme and colors
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Animated,
  Dimensions,
  ViewStyle,
  TextStyle,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

// Types
interface LoadingProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
}

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: ViewStyle;
}

// Main Loading Component
export const Loading: React.FC<LoadingProps> = ({
  message = 'Loading...',
  size = 'medium',
  color = Colors.sage600,
  style,
  textStyle,
  testID = 'loading-indicator',
}) => {
  const getSizeValue = () => {
    switch (size) {
      case 'small':
        return 'small' as const;
      case 'large':
        return 'large' as const;
      default:
        return 'small' as const;
    }
  };

  return (
    <View style={[styles.container, style]} testID={testID}>
      <ActivityIndicator
        size={getSizeValue()}
        color={color}
        accessibilityLabel="Loading"
      />
      {message && (
        <Text style={[styles.message, textStyle]} accessibilityLabel={message}>
          {message}
        </Text>
      )}
    </View>
  );
};

// Full Screen Loading
export const FullScreenLoading: React.FC<LoadingProps> = ({
  message = 'Loading...',
  size = 'large',
  color = Colors.sage600,
  testID = 'fullscreen-loading',
}) => {
  return (
    <View style={styles.fullScreenContainer} testID={testID}>
      <ActivityIndicator
        size={size === 'small' ? 'small' : 'large'}
        color={color}
        accessibilityLabel="Loading"
      />
      <Text style={styles.fullScreenMessage} accessibilityLabel={message}>
        {message}
      </Text>
    </View>
  );
};

// Inline Loading (for lists, cards, etc.)
export const InlineLoading: React.FC<LoadingProps> = ({
  message = 'Loading...',
  size = 'small',
  color = Colors.sage600,
  style,
  testID = 'inline-loading',
}) => {
  return (
    <View style={[styles.inlineContainer, style]} testID={testID}>
      <ActivityIndicator
        size={size === 'large' ? 'small' : 'small'}
        color={color}
        accessibilityLabel="Loading"
      />
      <Text style={styles.inlineMessage} accessibilityLabel={message}>
        {message}
      </Text>
    </View>
  );
};

// Button Loading State
export const ButtonLoading: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  color?: string;
}> = ({
  loading,
  children,
  loadingText = 'Loading...',
  color = Colors.text.onPrimary,
}) => {
  if (loading) {
    return (
      <View style={styles.buttonLoadingContainer}>
        <ActivityIndicator
          size="small"
          color={color}
          accessibilityLabel="Loading"
        />
        <Text
          style={[styles.buttonLoadingText, { color }]}
          accessibilityLabel={loadingText}>
          {loadingText}
        </Text>
      </View>
    );
  }

  return <>{children}</>;
};

// Skeleton Loading Component
export const SkeletonLoader: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]),
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.background.secondary, Colors.border.light],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
      accessibilityLabel="Loading content"
    />
  );
};

// Skeleton Card (for lists)
export const SkeletonCard: React.FC<{ style?: ViewStyle }> = ({ style }) => {
  return (
    <View style={[styles.skeletonCard, style]}>
      <SkeletonLoader width={60} height={60} borderRadius={30} />
      <View style={styles.skeletonCardContent}>
        <SkeletonLoader width="80%" height={16} />
        <SkeletonLoader width="60%" height={14} style={{ marginTop: 8 }} />
        <SkeletonLoader width="40%" height={12} style={{ marginTop: 8 }} />
      </View>
    </View>
  );
};

// List Loading (multiple skeleton cards)
export const ListLoading: React.FC<{
  count?: number;
  style?: ViewStyle;
}> = ({ count = 3, style }) => {
  return (
    <View style={[styles.listContainer, style]}>
      {Array.from({ length: count }, (_, index) => (
        <SkeletonCard key={index} style={{ marginBottom: 16 }} />
      ))}
    </View>
  );
};

// Loading Overlay (for modals, screens)
export const LoadingOverlay: React.FC<LoadingProps> = ({
  message = 'Loading...',
  size = 'large',
  color = Colors.sage600,
  testID = 'loading-overlay',
}) => {
  return (
    <View style={styles.overlayContainer} testID={testID}>
      <View style={styles.overlayContent}>
        <ActivityIndicator
          size={size === 'small' ? 'small' : 'large'}
          color={color}
          accessibilityLabel="Loading"
        />
        <Text style={styles.overlayMessage} accessibilityLabel={message}>
          {message}
        </Text>
      </View>
    </View>
  );
};

// Progress Bar Component
export const ProgressBar: React.FC<{
  progress: number; // 0-100
  height?: number;
  color?: string;
  backgroundColor?: string;
  animated?: boolean;
  showPercentage?: boolean;
  style?: ViewStyle;
  testID?: string;
}> = ({
  progress,
  height = 4,
  color = Colors.primary.sage,
  backgroundColor = Colors.background.tertiary,
  animated = true,
  showPercentage = false,
  style,
  testID = 'progress-bar',
}) => {
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      progressAnim.setValue(progress);
    }
  }, [progress, animated, progressAnim]);

  return (
    <View style={[styles.progressContainer, style]} testID={testID}>
      <View style={[styles.progressTrack, { height, backgroundColor }]}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              height,
              backgroundColor: color,
              width: animated
                ? progressAnim.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                    extrapolate: 'clamp',
                  })
                : `${Math.max(0, Math.min(100, progress))}%`,
            },
          ]}
        />
      </View>
      {showPercentage && (
        <Text style={styles.progressText} testID={`${testID}-percentage`}>
          {Math.round(Math.max(0, Math.min(100, progress)))}%
        </Text>
      )}
    </View>
  );
};

// Enhanced Progress Bar with Steps
export const SteppedProgressBar: React.FC<{
  currentStep: number;
  totalSteps: number;
  stepLabels?: string[];
  color?: string;
  backgroundColor?: string;
  style?: ViewStyle;
  testID?: string;
}> = ({
  currentStep,
  totalSteps,
  stepLabels,
  color = Colors.primary.sage,
  backgroundColor = Colors.background.tertiary,
  style,
  testID = 'stepped-progress-bar',
}) => {
  const progress = (currentStep / totalSteps) * 100;

  return (
    <View style={[styles.steppedProgressContainer, style]} testID={testID}>
      {stepLabels && (
        <View style={styles.stepLabelsContainer}>
          {stepLabels.map((label, index) => (
            <Text
              key={index}
              style={[
                styles.stepLabel,
                index < currentStep && styles.stepLabelCompleted,
                index === currentStep && styles.stepLabelActive,
              ]}
              accessibilityLabel={`Step ${index + 1}: ${label}`}
            >
              {label}
            </Text>
          ))}
        </View>
      )}
      <View style={styles.stepsContainer}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <View
            key={index}
            style={[
              styles.stepIndicator,
              index < currentStep && { backgroundColor: color },
              index === currentStep && { backgroundColor: color, opacity: 0.7 },
            ]}
          />
        ))}
      </View>
      <ProgressBar
        progress={progress}
        color={color}
        backgroundColor={backgroundColor}
        animated={true}
        showPercentage={false}
        testID={`${testID}-progress`}
      />
    </View>
  );
};

// Enhanced Skeleton Components for Different Content Types
export const SkeletonText: React.FC<{
  lines?: number;
  width?: string | number;
  height?: number;
  style?: ViewStyle;
}> = ({ lines = 1, width = '100%', height = 16, style }) => {
  return (
    <View style={[styles.skeletonTextContainer, style]}>
      {Array.from({ length: lines }, (_, index) => (
        <SkeletonLoader
          key={index}
          width={index === lines - 1 ? '70%' : width}
          height={height}
          style={{ marginBottom: index < lines - 1 ? 8 : 0 }}
        />
      ))}
    </View>
  );
};

export const SkeletonImage: React.FC<{
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: ViewStyle;
}> = ({ width = 100, height = 100, borderRadius = 8, style }) => {
  return (
    <SkeletonLoader
      width={width}
      height={height}
      borderRadius={borderRadius}
      style={style}
    />
  );
};

export const SkeletonButton: React.FC<{
  width?: number | string;
  height?: number;
  style?: ViewStyle;
}> = ({ width = 120, height = 40, style }) => {
  return (
    <SkeletonLoader
      width={width}
      height={height}
      borderRadius={20}
      style={style}
    />
  );
};

// System Status Indicator
export const SystemStatusIndicator: React.FC<{
  status: 'online' | 'offline' | 'syncing' | 'error';
  message?: string;
  showIcon?: boolean;
  style?: ViewStyle;
  testID?: string;
}> = ({
  status,
  message,
  showIcon = true,
  style,
  testID = 'system-status',
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          color: Colors.success,
          icon: 'checkmark-circle',
          defaultMessage: 'Connected',
        };
      case 'offline':
        return {
          color: Colors.error,
          icon: 'close-circle',
          defaultMessage: 'Offline',
        };
      case 'syncing':
        return {
          color: Colors.warning,
          icon: 'sync',
          defaultMessage: 'Syncing...',
        };
      case 'error':
        return {
          color: Colors.error,
          icon: 'warning',
          defaultMessage: 'Connection Error',
        };
      default:
        return {
          color: Colors.textSecondary,
          icon: 'help-circle',
          defaultMessage: 'Unknown Status',
        };
    }
  };

  const config = getStatusConfig();
  const displayMessage = message || config.defaultMessage;

  return (
    <View style={[styles.statusContainer, style]} testID={testID}>
      {showIcon && (
        <Ionicons
          name={config.icon as any}
          size={16}
          color={config.color}
          style={styles.statusIcon}
        />
      )}
      <Text
        style={[styles.statusText, { color: config.color }]}
        accessibilityLabel={`System status: ${displayMessage}`}
      >
        {displayMessage}
      </Text>
    </View>
  );
};

// Refresh Loading (for pull-to-refresh)
export const RefreshLoading: React.FC<{
  refreshing: boolean;
  onRefresh: () => void;
  children: React.ReactNode;
  tintColor?: string;
  title?: string;
}> = ({ refreshing, onRefresh, children, tintColor = Colors.primary.sage, title }) => {
  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={tintColor}
          title={title}
          titleColor={tintColor}
        />
      }
    >
      {children}
    </ScrollView>
  );
};

// Styles
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing(16),
  },
  message: {
    fontSize: getResponsiveFontSize(16),
    color: Colors.text.secondary,
    marginTop: getResponsiveSpacing(12),
    textAlign: 'center',
  },
  fullScreenContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.background.primary,
    padding: getResponsiveSpacing(20),
  },
  fullScreenMessage: {
    fontSize: getResponsiveFontSize(18),
    color: Colors.text.secondary,
    marginTop: getResponsiveSpacing(16),
    textAlign: 'center',
  },
  inlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing(8),
  },
  inlineMessage: {
    fontSize: getResponsiveFontSize(14),
    color: Colors.text.secondary,
    marginLeft: getResponsiveSpacing(8),
  },
  buttonLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonLoadingText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginLeft: getResponsiveSpacing(8),
  },
  skeletonCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getResponsiveSpacing(16),
    backgroundColor: Colors.background.primary,
    borderRadius: getResponsiveSpacing(12),
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  skeletonCardContent: {
    flex: 1,
    marginLeft: getResponsiveSpacing(16),
  },
  listContainer: {
    padding: getResponsiveSpacing(16),
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  overlayContent: {
    backgroundColor: Colors.background.primary,
    padding: getResponsiveSpacing(24),
    borderRadius: getResponsiveSpacing(12),
    alignItems: 'center',
    minWidth: width * 0.3,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  overlayMessage: {
    fontSize: getResponsiveFontSize(16),
    color: Colors.text.primary,
    marginTop: getResponsiveSpacing(12),
    textAlign: 'center',
  },
  // Progress Bar Styles
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    borderRadius: 2,
  },
  progressText: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
    marginTop: getResponsiveSpacing(4),
    fontWeight: '500',
  },
  // System Status Styles
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
  },
  statusIcon: {
    marginRight: getResponsiveSpacing(6),
  },
  statusText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
  },
  // Stepped Progress Styles
  steppedProgressContainer: {
    marginVertical: getResponsiveSpacing(8),
  },
  stepLabelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing(8),
  },
  stepLabel: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
    textAlign: 'center',
    flex: 1,
  },
  stepLabelCompleted: {
    color: Colors.primary.sage,
    fontWeight: '600',
  },
  stepLabelActive: {
    color: Colors.primary.sage,
    fontWeight: '500',
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing(8),
    paddingHorizontal: getResponsiveSpacing(4),
  },
  stepIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.background.tertiary,
    marginHorizontal: getResponsiveSpacing(2),
  },
  // Skeleton Text Styles
  skeletonTextContainer: {
    marginVertical: getResponsiveSpacing(4),
  },
});
