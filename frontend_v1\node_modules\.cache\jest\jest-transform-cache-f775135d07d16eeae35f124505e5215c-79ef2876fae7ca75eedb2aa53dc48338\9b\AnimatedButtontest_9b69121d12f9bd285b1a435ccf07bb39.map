{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_Animated<PERSON><PERSON><PERSON>", "_jsxRuntime", "TestWrapper", "_ref", "children", "jsx", "Fragment", "describe", "defaultProps", "title", "onPress", "jest", "fn", "testID", "beforeEach", "clearAllMocks", "it", "_render", "render", "AnimatedButton", "Object", "assign", "getByTestId", "getByText", "expect", "toBeTruthy", "_render2", "variant", "onPressMock", "_render3", "fireEvent", "press", "toHaveBeenCalledTimes", "_render4", "disabled", "not", "toHaveBeenCalled", "_render5", "accessibilityLabel", "button", "props", "accessibilityRole", "toBe"], "sources": ["AnimatedButton.test.tsx"], "sourcesContent": ["/**\n * AnimatedButton Component Tests\n *\n * Basic test suite for the AnimatedButton component.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport { render, fireEvent } from '@testing-library/react-native';\nimport { AnimatedButton } from '../../components/animation/AnimatedButton';\n\n// Mock providers\nconst TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  return <>{children}</>;\n};\n\ndescribe('AnimatedButton', () => {\n  const defaultProps = {\n    title: 'Test Button',\n    onPress: jest.fn(),\n    testID: 'animated-button',\n  };\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Rendering', () => {\n    it('renders correctly with default props', () => {\n      const { getByTestId, getByText } = render(\n        <TestWrapper>\n          <AnimatedButton {...defaultProps} />\n        </TestWrapper>\n      );\n\n      expect(getByTestId('animated-button')).toBeTruthy();\n      expect(getByText('Test Button')).toBeTruthy();\n    });\n\n    it('renders with different variants', () => {\n      const { getByTestId } = render(\n        <TestWrapper>\n          <AnimatedButton\n            {...defaultProps}\n            variant=\"primary\"\n            testID=\"button-primary\"\n          />\n        </TestWrapper>\n      );\n\n      expect(getByTestId('button-primary')).toBeTruthy();\n    });\n\n  });\n\n  describe('Functionality', () => {\n    it('calls onPress when pressed', () => {\n      const onPressMock = jest.fn();\n      const { getByTestId } = render(\n        <TestWrapper>\n          <AnimatedButton\n            {...defaultProps}\n            onPress={onPressMock}\n          />\n        </TestWrapper>\n      );\n\n      fireEvent.press(getByTestId('animated-button'));\n      expect(onPressMock).toHaveBeenCalledTimes(1);\n    });\n\n    it('does not call onPress when disabled', () => {\n      const onPressMock = jest.fn();\n      const { getByTestId } = render(\n        <TestWrapper>\n          <AnimatedButton\n            {...defaultProps}\n            onPress={onPressMock}\n            disabled={true}\n          />\n        </TestWrapper>\n      );\n\n      fireEvent.press(getByTestId('animated-button'));\n      expect(onPressMock).not.toHaveBeenCalled();\n    });\n\n  });\n\n  describe('Accessibility', () => {\n    it('has proper accessibility properties', () => {\n      const { getByTestId } = render(\n        <TestWrapper>\n          <AnimatedButton\n            {...defaultProps}\n            accessibilityLabel=\"Custom accessibility label\"\n          />\n        </TestWrapper>\n      );\n\n      const button = getByTestId('animated-button');\n      expect(button.props.accessibilityRole).toBe('button');\n    });\n  });\n});\n"], "mappings": ";AASA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAA2E,IAAAG,WAAA,GAAAH,OAAA;AAG3E,IAAMI,WAAoD,GAAG,SAAvDA,WAAoDA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACtE,OAAO,IAAAH,WAAA,CAAAI,GAAA,EAAAJ,WAAA,CAAAK,QAAA;IAAAF,QAAA,EAAGA;EAAQ,CAAG,CAAC;AACxB,CAAC;AAEDG,QAAQ,CAAC,gBAAgB,EAAE,YAAM;EAC/B,IAAMC,YAAY,GAAG;IACnBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;IAClBC,MAAM,EAAE;EACV,CAAC;EAEDC,UAAU,CAAC,YAAM;IACfH,IAAI,CAACI,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFR,QAAQ,CAAC,WAAW,EAAE,YAAM;IAC1BS,EAAE,CAAC,sCAAsC,EAAE,YAAM;MAC/C,IAAAC,OAAA,GAAmC,IAAAC,mBAAM,EACvC,IAAAjB,WAAA,CAAAI,GAAA,EAACH,WAAW;UAAAE,QAAA,EACV,IAAAH,WAAA,CAAAI,GAAA,EAACL,eAAA,CAAAmB,cAAc,EAAAC,MAAA,CAAAC,MAAA,KAAKb,YAAY,CAAG;QAAC,CACzB,CACf,CAAC;QAJOc,WAAW,GAAAL,OAAA,CAAXK,WAAW;QAAEC,SAAS,GAAAN,OAAA,CAATM,SAAS;MAM9BC,MAAM,CAACF,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACG,UAAU,CAAC,CAAC;MACnDD,MAAM,CAACD,SAAS,CAAC,aAAa,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEFT,EAAE,CAAC,iCAAiC,EAAE,YAAM;MAC1C,IAAAU,QAAA,GAAwB,IAAAR,mBAAM,EAC5B,IAAAjB,WAAA,CAAAI,GAAA,EAACH,WAAW;UAAAE,QAAA,EACV,IAAAH,WAAA,CAAAI,GAAA,EAACL,eAAA,CAAAmB,cAAc,EAAAC,MAAA,CAAAC,MAAA,KACTb,YAAY;YAChBmB,OAAO,EAAC,SAAS;YACjBd,MAAM,EAAC;UAAgB,EACxB;QAAC,CACS,CACf,CAAC;QAROS,WAAW,GAAAI,QAAA,CAAXJ,WAAW;MAUnBE,MAAM,CAACF,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAACG,UAAU,CAAC,CAAC;IACpD,CAAC,CAAC;EAEJ,CAAC,CAAC;EAEFlB,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BS,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAMY,WAAW,GAAGjB,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7B,IAAAiB,QAAA,GAAwB,IAAAX,mBAAM,EAC5B,IAAAjB,WAAA,CAAAI,GAAA,EAACH,WAAW;UAAAE,QAAA,EACV,IAAAH,WAAA,CAAAI,GAAA,EAACL,eAAA,CAAAmB,cAAc,EAAAC,MAAA,CAAAC,MAAA,KACTb,YAAY;YAChBE,OAAO,EAAEkB;UAAY,EACtB;QAAC,CACS,CACf,CAAC;QAPON,WAAW,GAAAO,QAAA,CAAXP,WAAW;MASnBQ,sBAAS,CAACC,KAAK,CAACT,WAAW,CAAC,iBAAiB,CAAC,CAAC;MAC/CE,MAAM,CAACI,WAAW,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEFhB,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAMY,WAAW,GAAGjB,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7B,IAAAqB,QAAA,GAAwB,IAAAf,mBAAM,EAC5B,IAAAjB,WAAA,CAAAI,GAAA,EAACH,WAAW;UAAAE,QAAA,EACV,IAAAH,WAAA,CAAAI,GAAA,EAACL,eAAA,CAAAmB,cAAc,EAAAC,MAAA,CAAAC,MAAA,KACTb,YAAY;YAChBE,OAAO,EAAEkB,WAAY;YACrBM,QAAQ,EAAE;UAAK,EAChB;QAAC,CACS,CACf,CAAC;QAROZ,WAAW,GAAAW,QAAA,CAAXX,WAAW;MAUnBQ,sBAAS,CAACC,KAAK,CAACT,WAAW,CAAC,iBAAiB,CAAC,CAAC;MAC/CE,MAAM,CAACI,WAAW,CAAC,CAACO,GAAG,CAACC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;EAEJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BS,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAAqB,QAAA,GAAwB,IAAAnB,mBAAM,EAC5B,IAAAjB,WAAA,CAAAI,GAAA,EAACH,WAAW;UAAAE,QAAA,EACV,IAAAH,WAAA,CAAAI,GAAA,EAACL,eAAA,CAAAmB,cAAc,EAAAC,MAAA,CAAAC,MAAA,KACTb,YAAY;YAChB8B,kBAAkB,EAAC;UAA4B,EAChD;QAAC,CACS,CACf,CAAC;QAPOhB,WAAW,GAAAe,QAAA,CAAXf,WAAW;MASnB,IAAMiB,MAAM,GAAGjB,WAAW,CAAC,iBAAiB,CAAC;MAC7CE,MAAM,CAACe,MAAM,CAACC,KAAK,CAACC,iBAAiB,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}