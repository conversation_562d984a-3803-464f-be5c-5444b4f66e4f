/**
 * Bundle Optimization Utilities
 *
 * Utilities for bundle size optimization, code splitting, and performance
 * monitoring in React Native applications.
 *
 * Features:
 * - Bundle size analysis
 * - Code splitting strategies
 * - Lazy loading utilities
 * - Performance monitoring
 * - Memory optimization
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';

// Bundle analysis configuration
export interface BundleAnalysisConfig {
  enableAnalysis: boolean;
  trackComponentSizes: boolean;
  trackAssetSizes: boolean;
  reportThreshold: number; // KB
  enableLogging: boolean;
}

// Performance metrics
export interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  memoryUsage: number;
  componentCount: number;
  assetCount: number;
  timestamp: number;
}

// Lazy loading configuration
export interface LazyLoadConfig {
  enabled: boolean;
  preloadDistance: number; // screens
  cacheSize: number; // number of components to keep in memory
  enablePrefetch: boolean;
}

// Code splitting strategies
export const CODE_SPLITTING_STRATEGIES = {
  ROUTE_BASED: 'route_based',
  COMPONENT_BASED: 'component_based',
  FEATURE_BASED: 'feature_based',
  VENDOR_BASED: 'vendor_based',
} as const;

export type CodeSplittingStrategy = typeof CODE_SPLITTING_STRATEGIES[keyof typeof CODE_SPLITTING_STRATEGIES];

// Bundle optimization configuration
const BUNDLE_CONFIG: BundleAnalysisConfig = {
  enableAnalysis: __DEV__,
  trackComponentSizes: __DEV__,
  trackAssetSizes: true,
  reportThreshold: 100, // 100KB
  enableLogging: __DEV__,
};

// Lazy loading configuration
const LAZY_LOAD_CONFIG: LazyLoadConfig = {
  enabled: true,
  preloadDistance: 1,
  cacheSize: 5,
  enablePrefetch: true,
};

// Component registry for tracking
const componentRegistry = new Map<string, {
  size: number;
  loadTime: number;
  lastUsed: number;
  usageCount: number;
}>();

// Asset registry for tracking
const assetRegistry = new Map<string, {
  size: number;
  type: string;
  cached: boolean;
  lastUsed: number;
}>();

/**
 * Track component performance metrics
 */
export const trackComponentMetrics = (
  componentName: string,
  size: number,
  loadTime: number
): void => {
  if (!BUNDLE_CONFIG.trackComponentSizes) return;

  const existing = componentRegistry.get(componentName);
  const now = Date.now();

  componentRegistry.set(componentName, {
    size,
    loadTime,
    lastUsed: now,
    usageCount: existing ? existing.usageCount + 1 : 1,
  });

  if (BUNDLE_CONFIG.enableLogging && size > BUNDLE_CONFIG.reportThreshold * 1024) {
    console.warn(`Large component detected: ${componentName} (${(size / 1024).toFixed(2)}KB)`);
  }
};

/**
 * Track asset performance metrics
 */
export const trackAssetMetrics = (
  assetPath: string,
  size: number,
  type: string,
  cached: boolean = false
): void => {
  if (!BUNDLE_CONFIG.trackAssetSizes) return;

  assetRegistry.set(assetPath, {
    size,
    type,
    cached,
    lastUsed: Date.now(),
  });

  if (BUNDLE_CONFIG.enableLogging && size > BUNDLE_CONFIG.reportThreshold * 1024) {
    console.warn(`Large asset detected: ${assetPath} (${(size / 1024).toFixed(2)}KB)`);
  }
};

/**
 * Get current performance metrics
 */
export const getPerformanceMetrics = (): PerformanceMetrics => {
  const componentSizes = Array.from(componentRegistry.values()).reduce(
    (total, comp) => total + comp.size, 0
  );
  
  const assetSizes = Array.from(assetRegistry.values()).reduce(
    (total, asset) => total + asset.size, 0
  );

  return {
    bundleSize: componentSizes + assetSizes,
    loadTime: 0, // Would be measured in real implementation
    memoryUsage: 0, // Would be measured in real implementation
    componentCount: componentRegistry.size,
    assetCount: assetRegistry.size,
    timestamp: Date.now(),
  };
};

/**
 * Create lazy loading wrapper for components
 */
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFunction: () => Promise<{ default: T }>,
  componentName: string,
  fallback?: React.ComponentType
): React.LazyExoticComponent<T> => {
  if (!LAZY_LOAD_CONFIG.enabled) {
    // If lazy loading is disabled, return a wrapper that loads immediately
    return React.lazy(importFunction);
  }

  const startTime = Date.now();
  
  return React.lazy(async () => {
    try {
      const module = await importFunction();
      const loadTime = Date.now() - startTime;
      
      // Track loading metrics
      trackComponentMetrics(componentName, 0, loadTime); // Size would be measured differently
      
      if (BUNDLE_CONFIG.enableLogging) {
        console.log(`Lazy loaded component: ${componentName} (${loadTime}ms)`);
      }
      
      return module;
    } catch (error) {
      console.error(`Failed to lazy load component: ${componentName}`, error);
      throw error;
    }
  });
};

/**
 * Preload components for better UX
 */
export const preloadComponent = async (
  importFunction: () => Promise<{ default: React.ComponentType<any> }>,
  componentName: string
): Promise<void> => {
  if (!LAZY_LOAD_CONFIG.enablePrefetch) return;

  try {
    const startTime = Date.now();
    await importFunction();
    const loadTime = Date.now() - startTime;
    
    trackComponentMetrics(`${componentName}_preload`, 0, loadTime);
    
    if (BUNDLE_CONFIG.enableLogging) {
      console.log(`Preloaded component: ${componentName} (${loadTime}ms)`);
    }
  } catch (error) {
    console.warn(`Failed to preload component: ${componentName}`, error);
  }
};

/**
 * Clean up unused components from cache
 */
export const cleanupComponentCache = (): void => {
  const now = Date.now();
  const maxAge = 5 * 60 * 1000; // 5 minutes
  
  for (const [componentName, metrics] of componentRegistry.entries()) {
    if (now - metrics.lastUsed > maxAge && metrics.usageCount === 1) {
      componentRegistry.delete(componentName);
      
      if (BUNDLE_CONFIG.enableLogging) {
        console.log(`Cleaned up unused component: ${componentName}`);
      }
    }
  }
};

/**
 * Get bundle size recommendations
 */
export const getBundleSizeRecommendations = (): string[] => {
  const recommendations: string[] = [];
  const metrics = getPerformanceMetrics();
  
  // Check overall bundle size
  if (metrics.bundleSize > 5 * 1024 * 1024) { // 5MB
    recommendations.push('Consider implementing more aggressive code splitting');
  }
  
  // Check component count
  if (metrics.componentCount > 100) {
    recommendations.push('Consider lazy loading more components');
  }
  
  // Check for large components
  const largeComponents = Array.from(componentRegistry.entries())
    .filter(([_, metrics]) => metrics.size > BUNDLE_CONFIG.reportThreshold * 1024)
    .map(([name]) => name);
  
  if (largeComponents.length > 0) {
    recommendations.push(`Optimize large components: ${largeComponents.join(', ')}`);
  }
  
  // Check for large assets
  const largeAssets = Array.from(assetRegistry.entries())
    .filter(([_, metrics]) => metrics.size > BUNDLE_CONFIG.reportThreshold * 1024)
    .map(([path]) => path);
  
  if (largeAssets.length > 0) {
    recommendations.push(`Optimize large assets: ${largeAssets.join(', ')}`);
  }
  
  return recommendations;
};

/**
 * Optimize images for bundle size
 */
export const optimizeImageForBundle = (
  imageUri: string,
  maxWidth: number = 800,
  quality: number = 0.8
): string => {
  // In a real implementation, this would integrate with image optimization services
  // For now, return the original URI with optimization parameters
  
  if (imageUri.startsWith('http')) {
    // External image - add optimization parameters
    const separator = imageUri.includes('?') ? '&' : '?';
    return `${imageUri}${separator}w=${maxWidth}&q=${Math.round(quality * 100)}`;
  }
  
  // Local image - return as-is (would be optimized at build time)
  return imageUri;
};

/**
 * Create optimized asset loader
 */
export const createAssetLoader = (assetPath: string, type: 'image' | 'video' | 'audio' | 'document') => {
  return {
    load: async () => {
      const startTime = Date.now();
      
      try {
        // Simulate asset loading (in real implementation, this would load the actual asset)
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const loadTime = Date.now() - startTime;
        trackAssetMetrics(assetPath, 0, type); // Size would be measured in real implementation
        
        return { uri: assetPath, loadTime };
      } catch (error) {
        console.error(`Failed to load asset: ${assetPath}`, error);
        throw error;
      }
    },
    preload: async () => {
      if (LAZY_LOAD_CONFIG.enablePrefetch) {
        return this.load();
      }
    },
  };
};

/**
 * Monitor memory usage
 */
export const monitorMemoryUsage = (): void => {
  if (!__DEV__) return;
  
  // In React Native, we can't directly access memory usage like in web browsers
  // This would integrate with native modules or performance monitoring tools
  
  const interval = setInterval(() => {
    const componentCount = componentRegistry.size;
    const assetCount = assetRegistry.size;
    
    if (componentCount > 50 || assetCount > 100) {
      console.warn(`High memory usage detected: ${componentCount} components, ${assetCount} assets`);
      cleanupComponentCache();
    }
  }, 30000); // Check every 30 seconds
  
  // Clean up interval on app background/close
  return () => clearInterval(interval);
};

/**
 * Get code splitting recommendations
 */
export const getCodeSplittingRecommendations = (): Array<{
  strategy: CodeSplittingStrategy;
  description: string;
  priority: 'high' | 'medium' | 'low';
}> => {
  const recommendations = [];
  const metrics = getPerformanceMetrics();
  
  if (metrics.bundleSize > 3 * 1024 * 1024) { // 3MB
    recommendations.push({
      strategy: CODE_SPLITTING_STRATEGIES.ROUTE_BASED,
      description: 'Split code by routes to reduce initial bundle size',
      priority: 'high' as const,
    });
  }
  
  if (metrics.componentCount > 75) {
    recommendations.push({
      strategy: CODE_SPLITTING_STRATEGIES.COMPONENT_BASED,
      description: 'Lazy load large or rarely used components',
      priority: 'medium' as const,
    });
  }
  
  recommendations.push({
    strategy: CODE_SPLITTING_STRATEGIES.VENDOR_BASED,
    description: 'Separate vendor libraries from application code',
    priority: 'medium' as const,
  });
  
  return recommendations;
};

/**
 * Create performance monitoring hook
 */
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics | null>(null);
  
  React.useEffect(() => {
    const updateMetrics = () => {
      setMetrics(getPerformanceMetrics());
    };
    
    updateMetrics();
    const interval = setInterval(updateMetrics, 10000); // Update every 10 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  return {
    metrics,
    recommendations: getBundleSizeRecommendations(),
    codeSplittingRecommendations: getCodeSplittingRecommendations(),
  };
};

/**
 * Bundle size analyzer for development
 */
export const analyzeBundleSize = (): void => {
  if (!__DEV__ || !BUNDLE_CONFIG.enableAnalysis) return;
  
  const metrics = getPerformanceMetrics();
  const recommendations = getBundleSizeRecommendations();
  
  console.group('📦 Bundle Size Analysis');
  console.log(`Total Bundle Size: ${(metrics.bundleSize / 1024 / 1024).toFixed(2)}MB`);
  console.log(`Components: ${metrics.componentCount}`);
  console.log(`Assets: ${metrics.assetCount}`);
  
  if (recommendations.length > 0) {
    console.group('💡 Recommendations');
    recommendations.forEach(rec => console.log(`• ${rec}`));
    console.groupEnd();
  }
  
  console.groupEnd();
};

// Auto-start monitoring in development
if (__DEV__) {
  monitorMemoryUsage();
  
  // Analyze bundle size on app start
  setTimeout(analyzeBundleSize, 5000);
}

export default {
  CODE_SPLITTING_STRATEGIES,
  trackComponentMetrics,
  trackAssetMetrics,
  getPerformanceMetrics,
  createLazyComponent,
  preloadComponent,
  cleanupComponentCache,
  getBundleSizeRecommendations,
  optimizeImageForBundle,
  createAssetLoader,
  monitorMemoryUsage,
  getCodeSplittingRecommendations,
  usePerformanceMonitoring,
  analyzeBundleSize,
};
